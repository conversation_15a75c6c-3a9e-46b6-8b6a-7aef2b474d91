server {
    listen 80;
    server_name annotation.platform.local; # 可以替换为实际域名

    # 访问日志和错误日志配置
    access_log /var/log/nginx/annotation-platform-access.log;
    error_log /var/log/nginx/annotation-platform-error.log;

    # 静态资源缓存设置
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        root /usr/share/nginx/html/intelligent-annotation-platform/dist;
        expires 7d;
        add_header Cache-Control "public, max-age=604800";
    }

    # 前端静态资源
    location / {
        root /usr/share/nginx/html/intelligent-annotation-platform/dist;
        index index.html;
        try_files $uri $uri/ /index.html; # 支持前端路由
    }

    # API 反向代理
    location /api/ {
        proxy_pass http://backend-service:3000/; # 后端服务地址
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_read_timeout 60s;
        proxy_send_timeout 60s;
    }

    # 无人货柜 API 反向代理
    location /unmanned/ {
        proxy_pass http://unmanned-service:3001/unmanned/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_read_timeout 60s;
        proxy_send_timeout 60s;
    }

    # WebSocket 支持
    location /ws/ {
        proxy_pass http://websocket-service:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # 安全相关配置
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Frame-Options SAMEORIGIN;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # 限制请求大小，适用于视频上传
    client_max_body_size 100M;
}