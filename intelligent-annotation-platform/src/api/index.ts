import axios from 'axios';
import type { AxiosRequestConfig } from 'axios';
const prefix = import.meta.env.VITE_APP_API_PREFIX;
const reqUrl = import.meta.env.VITE_APP_API_BASE_URL

// 创建axios实例
const request = axios.create({
  baseURL:  `${reqUrl}${prefix}`, // 从环境变量获取API前缀
  timeout: 5000, // 请求超时时间
});

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 从localStorage获取token信息
    const accessToken = localStorage.getItem('access_token');
    const tokenType = localStorage.getItem('token_type');
    
    // 如果token存在，添加到请求头
    if (accessToken && tokenType) {
      config.headers.Authorization = `${tokenType} ${accessToken}`;
    }
    
    // 如果是表单提交，确保Content-Type正确设置
    if (config.headers['Content-Type'] === 'application/x-www-form-urlencoded') {
      if (config.data instanceof URLSearchParams) {
        // 已经是URLSearchParams格式，无需处理
        return config;
      }
      // 将普通对象转换为URLSearchParams格式
      if (config.data && typeof config.data === 'object') {
        config.data = new URLSearchParams(config.data);
      }
    }
    return config;
  },
  (error) => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    const res = response.data;
    // 根据后端约定的状态码判断请求是否成功
    if (response.status === 200) {
      return res;
    }
    // 处理其他状态码
    const error = new Error(res.message || '请求失败') as any;
    error.code = res.code;
    return Promise.reject(error);
  },
  (error) => {
    let message = '网络错误';
    if (error.response) {
      // HTTP 错误状态码处理
      switch (error.response.status) {
        case 401:
          message = '未授权，请重新登录';
          // 清除用户凭证
          localStorage.removeItem('access_token');
          localStorage.removeItem('token_type');
          localStorage.removeItem('user_info');
          
          // 使用事件总线通知应用处理登出逻辑，而不是直接跳转
          // 这样可以由应用决定如何处理重定向，更加灵活
          window.dispatchEvent(new CustomEvent('auth:unauthorized'));
          break;
        case 403:
          message = '拒绝访问';
          break;
        case 404:
          message = '请求错误，未找到该资源';
          break;
        case 500:
          message = '服务器错误';
          break;
        default:
          message = `请求失败: ${error.response.status}`;
      }
    } else if (error.request) {
      // 请求已经发出，但没有收到响应
      message = '网络异常，请检查网络连接';
    } else if (error.code === 'ECONNABORTED') {
      message = '请求超时，请重试';
    }
    console.error(message, error);
    return Promise.reject(error);
  }
);

// 封装GET请求
export const get = async <T = any>(
  url: string,
  params?: any,
  config?: AxiosRequestConfig
): Promise<T> => {
  try {
    const response = await request.get(url, { params, ...config });
    return response as T;
  } catch (error) {
    throw error;
  }
};

// 封装POST请求
export const post = async <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<T> => {
  try {
    const response = await request.post(url, data, config);
    return response as T;
  } catch (error) {
    throw error;
  }
};

export default request;