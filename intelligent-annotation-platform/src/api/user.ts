import { post, get } from './index';

// 用户登录接口
export const login = async(data: {
  username: string;
  password: string;
}) => {
  try {
    const formData = new URLSearchParams();
    formData.append('username', data.username);
    formData.append('password', data.password);
    
    const res = await post<{
      access_token: string;
      token_type: string;
    }>('/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }); 
    // 存储token信息
    if (res.access_token && res.token_type) {
      localStorage.setItem('access_token', res.access_token);
      localStorage.setItem('token_type', res.token_type);
    }
    
    return res;
  } catch (error) {
    return Promise.reject(error);
  }
}

// 用户角色接口返回类型
interface Role {
  id: number;
  name: string;
}

// 获取用户角色接口
export const getUserRole = async () => {
  try {
    const res = await get<Role[]>('/auth/users/roles');
    return res;
  } catch (error) {
    return Promise.reject(error);
  }
}