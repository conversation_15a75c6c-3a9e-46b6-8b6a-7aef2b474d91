import { get, post } from './index';

/**
 * 系统情况接口返回数据类型
 */
export interface SystemInfo {
  /** 任务池数量 */
  taskPoolCount: number;
  /** 在线人数 */
  onlineUsers: number;
}

/**
 * 审核原因类型
 */
export interface ReasonItem {
  /** 原因ID */
  id: number;
  /** 原因名称 */
  name: string;
  /** 原因类型：0-普通，1-警告 */
  type: number;
}

/**
 * 审核任务类型
 */
export interface TaskItem {
  /** 订单ID */
  order_id: number;
  /** 任务ID */
  task_id: number;
  /** 视频URL */
  video_url: string;
  /** 推荐商品列表 */
  recommended_skus: Array<SkuItem>;
}

/**
 * 商品SKU类型
 */
export interface SkuItem {
  /** SKU ID */
  id: number;
  /** 商品名称 */
  name: string;
  /** 商品规格 */
  spec: string;
  /** 商品图片 */
  image: string;
  /** 商品分类 */
  category: string;
  /** 置信度 */
  confidence?: number;
  /** 热销度 */
  popularity?: number;
}

/**
 * 订单详情类型
 */
export interface OrderDetail {
  /** 订单ID */
  order_id: number;
  /** 任务ID */
  task_id: number;
  /** 订单号 */
  display_order_id?: string | number;
  /** 创建时间 */
  created_at: string;
  /** 审核人 */
  checker: string;
  /** 审核时间 */
  checked_at: string;
  /** SKU数量 */
  sku_num: number;
  /** 已选数量 */
  selected_num: number;
  /** 抽检人 */
  reviewer: string;
  /** 原因 */
  reason: string;
  /** 状态 */
  status: string;
  /** 视频URL */
  video_url: string;
  /** 已选商品 */
  selected_skus: Array<SkuItem>;
}

/**
 * 抽检列表项类型
 */
export interface OrderListItem {
  /** 订单ID */
  order_id: number;
  /** 任务ID */
  task_id: number;
  /** 创建时间 */
  created_at: string;
  /** 审核人 */
  checker: string;
  /** 审核时间 */
  checked_at: string;
  /** SKU数量 */
  sku_num: number;
  /** 已选数量 */
  selected_num: number;
  /** 抽检人 */
  reviewer: string;
  /** 原因 */
  reason: string;
  /** 状态 */
  status: string;
}

/**
 * 抽检统计信息
 */
export interface ReviewStats {
  /** 当日抽检总数 */
  total: number;
  /** 合格数量 */
  qualified: number;
  /** 不合格数量 */
  unqualified: number;
}

/**
 * @description 获取系统情况，任务池数量和在线人数
 * @returns {Promise<SystemInfo>} 系统情况
 */
export const getSystemInfo = async () => {
  return get('/unmanned/system');
}

/**
 * @description 获取审核原因列表
 * @returns {Promise<Array<ReasonItem>>} 审核原因列表
 */
export const getReasonList = async () => {
  return get('/unmanned/reason');
}

/**
 * @description 获取单个无人审核任务
 * @returns {Promise<TaskItem>} 审核任务
 */
export const getTask = async () => {
  return get('/unmanned/task');
}

/**
 * @description 获取SKU详情
 * @param {number} skuId - SKU ID
 * @returns {Promise<SkuItem>} SKU详情
 */
export const getSkuDetail = async(skuId: number) => {
  return get('/unmanned/sku', { sku_id: skuId });
}

/**
 * @description 获取订单详情
 * @param {number} taskId - 任务ID
 * @returns {Promise<OrderDetail>} 订单详情
 */
export const getOrderDetail = async(taskId: number) => {
  return get<OrderDetail>('/unmanned/order', { task_id: taskId });
}

/**
 * @description 获取当日抽检情况
 * @returns {Promise<ReviewStats>} 抽检统计
 */
export const getReviewStats = async() => {
  return get<ReviewStats>('/unmanned/review/stat');
}

/**
 * @description 获取订单抽检列表
 * @param {Object} params - 查询参数
 * @param {string} [params.display_order_id] - 订单ID
 * @param {string} [params.task_id] - 任务ID
 * @param {string} [params.status] - 状态
 * @param {string} [params.reviewer] - 审核人
 * @param {string} [params.reason] - 原因/异常类型
 * @param {[string, string]} [params.date_range] - 日期范围
 * @param {number} params.page - 页码
 * @param {number} params.page_size - 每页条数
 * 
 * @returns {Promise<{data: Object, total_num: number}>} 订单列表和总数
 */
export function getOrderList(params: {
  display_order_id?: string;
  task_id?: string;
  status?: string;
  reviewer?: string;
  date_range?: [string, string];
  page: number;
  page_size: number;
  reason?: string;
}) {
  return get<{data: object, total_num: number}>('/unmanned/order/list', params);
}

/**
 * @description 提交审核任务
 * @param {Object} data - 提交数据
 * @param {number} data.order_id - 订单ID
 * @param {number} data.task_id - 任务ID
 * @param {Array<number>} data.skus - 已选商品ID列表
 * @param {Array<number>} data.reason_ids - 原因ID列表
 * @param {string} [data.other_reason] - 其他原因
 * @param {boolean} data.stop - 是否结束任务
 */
export const submitTask = async(data: any) => {
  return post<{code: number, message: string, data: object}>('/unmanned/task', data);
}

/**
 * @description 提交抽检结果 合格/不合格
 * @param {Object} data - 提交数据
 * @param {number} data.task_id - 任务ID
 * @param {string} data.status - 状态：qualified(合格)、unqualified(不合格)
 * @param {string} [data.reason] - 不合格原因
 * @param {boolean} [data.ok] -  是否不合格
 * @returns {Promise<{success: boolean, message: string}>} 提交结果
 */
export const submitReview = async(data: {
  task_id: number;
  status: string;
  reason?: string;
  ok?: boolean;
}) => {
  return post<{code: number, message: string}>('/unmanned/review', data);
}
/**
 * @description 获取抽检状态
 */
export const getReviewStatus = async() => {
  return get('/unmanned/review/status');
}

/**
 * @description 导出抽检列表
 * @param {Object} params - 查询参数
 * @param {string} [params.display_orderId] - 订单ID
 * @param {string} [params.taskId] - 任务ID
 * @param {string} [params.status] - 状态
 * @param {string} [params.checker] - 审核人
 * @param {string} [params.reason] - 原因/异常类型
 * @param {Array} [params.dateRange] - 日期范围
 * @param {string} [params.sortField] - 排序字段
 * @param {string} [params.sortOrder] - 排序方向
 * @returns {Promise<BlobPart>} 导出文件二进制数据
 */
export function exportInspectionList(params: any) {
  return get<BlobPart>('/unmanned/order/list/export', params, { responseType: 'blob' });
}