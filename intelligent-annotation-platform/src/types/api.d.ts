// 为API模块提供类型声明
declare module '@/api/unmanned' {
  // 导出所有unmanned.ts中的类型和函数
  export * from '../api/unmanned';
}

declare module '@/api/user' {
  // 导出所有user.ts中的类型和函数
  export * from '../api/user';
}

declare module '@/services/videoService' {
  // 导出所有videoService.ts中的类型和函数
  export * from '../services/videoService';
}

declare module '@/components/ExportMenu.vue' {
  import { DefineComponent } from 'vue';
  const component: DefineComponent<{}, {}, any>;
  export default component;
}

declare module '@/components/ProductDetailPopover.vue' {
  import { DefineComponent } from 'vue';
  const component: DefineComponent<{}, {}, any>;
  export default component;
}

declare module '@/mock' {
  // 导出所有mock/index.ts中的内容
  export * from '../mock/index';
}