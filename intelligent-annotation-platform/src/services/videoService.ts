/**
 * 视频处理服务
 * 用于处理视频解码和关键帧提取等功能
 */

// 创建WebWorker实例
let videoWorker: Worker | null = null;

// 初始化WebWorker
function initWorker() {
  if (!videoWorker) {
    videoWorker = new Worker(new URL('../workers/videoProcessor.js', import.meta.url), { type: 'module' });
  }
  return videoWorker;
}

/**
 * 提取视频关键帧
 * @param {Blob | File} videoFile - 视频文件
 * @param {number} frameCount - 需要提取的关键帧数量
 * @returns {Promise<Array<{id: number, timestamp: number, imageData: string}>>} 关键帧数据
 */
export function extractKeyframes(videoFile: Blob | File, frameCount: number = 5): Promise<Array<{id: number, timestamp: number, imageData: string}>> {
  return new Promise((resolve, reject) => {
    const worker = initWorker();
    
    // 处理Worker返回的消息
    const messageHandler = (e: MessageEvent) => {
      const { type, data } = e.data;
      
      if (type === 'keyframesExtracted') {
        if (data.success) {
          worker.removeEventListener('message', messageHandler);
          resolve(data.frames);
        } else {
          worker.removeEventListener('message', messageHandler);
          reject(new Error(data.message || 'Failed to extract keyframes'));
        }
      } else if (type === 'error') {
        worker.removeEventListener('message', messageHandler);
        reject(new Error(data.error || 'Unknown error'));
      }
    };
    
    worker.addEventListener('message', messageHandler);
    
    // 将视频文件转换为ArrayBuffer
    const reader = new FileReader();
    reader.onload = function() {
      // 发送消息到Worker
      worker.postMessage({
        type: 'extractKeyframes',
        data: {
          videoBuffer: reader.result,
          frameCount
        }
      });
    };
    reader.onerror = function() {
      reject(new Error('Failed to read video file'));
    };
    reader.readAsArrayBuffer(videoFile);
  });
}

/**
 * 解码视频
 * @param {Blob | File} videoFile - 视频文件
 * @param {string} format - 视频格式
 * @returns {Promise<{duration: number, width: number, height: number, fps: number}>} 视频元数据
 */
export function decodeVideo(videoFile: Blob | File, format: string): Promise<{duration: number, width: number, height: number, fps: number}> {
  return new Promise((resolve, reject) => {
    const worker = initWorker();
    
    // 处理Worker返回的消息
    const messageHandler = (e: MessageEvent) => {
      const { type, data } = e.data;
      
      if (type === 'videoDecoded') {
        if (data.success) {
          worker.removeEventListener('message', messageHandler);
          resolve(data.metadata);
        } else {
          worker.removeEventListener('message', messageHandler);
          reject(new Error(data.message || 'Failed to decode video'));
        }
      } else if (type === 'error') {
        worker.removeEventListener('message', messageHandler);
        reject(new Error(data.error || 'Unknown error'));
      }
    };
    
    worker.addEventListener('message', messageHandler);
    
    // 将视频文件转换为ArrayBuffer
    const reader = new FileReader();
    reader.onload = function() {
      // 发送消息到Worker
      worker.postMessage({
        type: 'decodeVideo',
        data: {
          videoBuffer: reader.result,
          format
        }
      });
    };
    reader.onerror = function() {
      reject(new Error('Failed to read video file'));
    };
    reader.readAsArrayBuffer(videoFile);
  });
}

/**
 * 从视频元素中捕获当前帧
 * @param {HTMLVideoElement} videoElement - 视频元素
 * @param {number} quality - 图像质量 (0-1)
 * @returns {string} 捕获的帧的base64编码
 */
export function captureVideoFrame(videoElement: HTMLVideoElement, quality: number = 0.8): string {
  const canvas = document.createElement('canvas');
  canvas.width = videoElement.videoWidth;
  canvas.height = videoElement.videoHeight;
  
  const ctx = canvas.getContext('2d');
  if (!ctx) throw new Error('Failed to get canvas context');
  
  ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
  return canvas.toDataURL('image/jpeg', quality);
}

/**
 * 使用requestVideoFrameCallback捕获视频帧
 * 注意：此API仅在支持的浏览器中可用
 * @param {HTMLVideoElement} videoElement - 视频元素
 * @param {Function} callback - 回调函数，接收捕获的帧
 */
export function captureFramesWithCallback(videoElement: HTMLVideoElement, callback: (frameData: string, metadata: any) => void): () => void {
  if (!('requestVideoFrameCallback' in HTMLVideoElement.prototype)) {
    throw new Error('requestVideoFrameCallback not supported in this browser');
  }
  
  const canvas = document.createElement('canvas');
  canvas.width = videoElement.videoWidth;
  canvas.height = videoElement.videoHeight;
  
  const ctx = canvas.getContext('2d');
  if (!ctx) throw new Error('Failed to get canvas context');
  
  let rafId: number;
  
  const captureFrame = (_now: number, metadata: any) => {
    ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
    const frameData = canvas.toDataURL('image/jpeg', 0.8);
    callback(frameData, metadata);
    
    // 继续请求下一帧
    rafId = videoElement.requestVideoFrameCallback(captureFrame);
  };
  
  // 开始捕获
  rafId = videoElement.requestVideoFrameCallback(captureFrame);
  
  // 返回停止函数
  return () => {
    if (rafId) {
      videoElement.cancelVideoFrameCallback(rafId);
    }
  };
}

/**
 * 销毁WebWorker
 */
export function destroyWorker(): void {
  if (videoWorker) {
    videoWorker.terminate();
    videoWorker = null;
  }
}