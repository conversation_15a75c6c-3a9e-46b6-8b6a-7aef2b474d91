<template>
  <div class="min-w-[1250px] min-h-screen h-screen bg-[#fafafc] overflow-y-auto relative">
    <!-- 身份切换按钮 -->
    <div v-if="hasMultipleRoles" class="identity-switch-btn" @click="switchToReview">
      <img src="/images/change_role.png" class="w-5 h-5"/>
      <span class="inline-block w-[10px]">切换审核</span>
    </div>
    <!-- 页面标题 -->
    <div class="page-title mb-2 py-5 pl-9 rounded bg-white h-15">
      <h1 class="text-[14px] font-500">抽检管理</h1>
    </div>
    <div class="p-4 rounded-[8px]">
          <!-- 筛选区域 -->
        <a-form layout="horizontal" class="filter-form p-4 bg-white rounded shadow-sm flex justify-between">
            <div class="flex flex-wrap w-[85%]">
                <div class="w-1/3 pr-2 h-8">
                    <a-form-item label="订单ID">
                        <a-input v-model:value="filters.display_order_id" placeholder="请输入" style="max-width: 224px;" allowClear/>
                    </a-form-item>
                </div>
                <div class="w-1/3 pr-2 h-8">
                    <a-form-item label="任务ID">
                        <a-input v-model:value="filters.task_id" placeholder="请输入" style="max-width: 234px;" allowClear/>
                    </a-form-item>
                </div>
                <div class="w-1/3 pr-2 h-8">
                    <a-form-item label="抽检状态">
                        <a-select v-model:value="filters.status" style="width: 100%; max-width: 224px;" placeholder="请选择" allowClear>
                          <a-select-option v-for="(item,index) in reviewStatus" :value="index">{{ item }}</a-select-option>
                          <!-- <a-select-option value="2">合格</a-select-option> -->
                          <!-- <a-select-option value="3">不合格</a-select-option> -->
                          <!-- <a-select-option value="1">待抽检</a-select-option> -->
                        </a-select>
                    </a-form-item>
                </div>
                <div class="w-1/3 pr-2 h-8">
                    <a-form-item label="审核人">
                        <a-input v-model:value="filters.checker" placeholder="请输入" style="max-width: 224px;" allowClear/>
                    </a-form-item>
                </div>
                <div class="w-1/3 pr-2 h-8">
                    <a-form-item label="审核时间">
                        <a-range-picker v-model:value="filters.dateRange" :placeholder="['开始日期', '结束日期']" style="width: 100%; max-width: 234px;" />
                    </a-form-item>
                    <!-- <a-form-item label="审核时间">
                      <a-range-picker 
                        v-model:value="filters.dateRange" 
                        :placeholder="['开始日期', '结束日期']" 
                        style="width: 100%; max-width: 234px;"
                        show-time
                        format="YYYY-MM-DD HH:mm:ss"
                      />
                    </a-form-item> -->
                </div>
                <div class="w-1/3 pr-2 h-8">
                    <a-form-item label="异常类型">
                        <a-select v-model:value="filters.reason" style="width: 100%; max-width: 224px;" placeholder="请选择" allowClear>
                          <a-select-option v-for="(item,index) in reasonList" :value="item.id" :key="index">{{ item.name }}</a-select-option>
                          <!-- <a-select-option value="1">其他警告</a-select-option>
                          <a-select-option value="2">故意遮挡</a-select-option>
                          <a-select-option value="3">异物拿放</a-select-option>
                          <a-select-option value="4">无法识别</a-select-option>
                          <a-select-option value="5">疑似设备故障</a-select-option>
                          <a-select-option value="6">未拿商品</a-select-option> -->
                        </a-select>
                    </a-form-item>
                </div>
            </div>
            <div class="flex flex-col justify-center items-end whitespace-nowrap">
                <a-form-item class="mb-0">
                    <ExportMenu class="ml-2 float-right" :onExport="(field: string, order: boolean) => {
                      pagination.sortField = field;
                      pagination.order_desc = order
                      exportList();
                    }" /><br/>
                    <div class="mt-4">
                        <a-button @click.stop="resetFilters">重 置</a-button>
                        <a-button class="bg-[#005290] text-[#fff] ml-4" @click.stop="handleSearch">查 询</a-button>
                    </div>
                </a-form-item>
            </div>
        </a-form>

        <!-- 订单列表 -->
        <div class="mb-4 bg-white rounded shadow-sm px-4 pb-4">
          <div class="statistics-summary mb-4 flex justify-between items-center p-4 statistics-box">
            <div class="flex items-center gap-2 font-400">
              <img src="/images/info-icon.png" class="info-icon" />
              <span class="text-[14px]">当日抽检: <span>{{ reviewStats.total_num }}条，</span></span>
              <span class="text-[14px]">合格: <span class="text-success">{{ reviewStats.ok_num }}条</span>，</span>
              <span class="text-[14px]">不合格: <span class="text-error">{{ reviewStats.no_num }}条</span></span>
            </div>
            <div class="shortcut-link relative">
              <a href="javascript:void(0)">查看快捷键</a>
              <div class="shortcut-tooltip p-4">
                <div class="tooltip-title">列表&详情</div>
                <div class="tooltip-content">
                  <div class="tooltip-item">
                    <span class="desc">视角切换</span>
                    <span class="key">ALT+TAB</span>
                  </div>
                  <div class="tooltip-item">
                    <span class="desc">播放/暂停</span>
                    <span class="key">空格键</span>
                  </div>
                  <div class="tooltip-item">
                    <span class="desc">快进</span>
                    <span class="key">右键</span>
                  </div>
                  <div class="tooltip-item">
                    <span class="desc">倒退</span>
                    <span class="key">左键</span>
                  </div>
                  <div class="tooltip-item">
                    <span class="desc">提高倍速</span>
                    <span class="key">ALT+1</span>
                  </div>
                  <div class="tooltip-item">
                    <span class="desc">降低倍速</span>
                    <span class="key">ALT+2</span>
                  </div>
                  <div class="tooltip-item">
                    <span class="desc">退出详情</span>
                    <span class="key">ESC</span>
                  </div>
                  <!-- <div class="tooltip-item">
                    <span class="desc">列表上下切换</span>
                    <span class="key">上键/下键</span>
                  </div> -->
                </div>
              </div>
            </div>
          </div>
        
          <a-table
              :columns="columns"
              :data-source="dataSource"
              :pagination="pagination"
              @change="handleTableChange"
              class="inspection-table"
              :row-class-name="rowClassName"
              :custom-row="customRow"
              :locale="{ filterTitle: '筛选', filterConfirm: '确定', filterReset: '重置', filterEmptyText: '无筛选项', emptyText: '暂无数据', triggerDesc: '点击降序', triggerAsc: '点击升序', cancelSort: '取消排序' }"
          >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'status'">
                    <span :class="{
                    'text-success': record.status === '合格',
                    'text-error': record.status === '不合格',
                    'text-warning': record.status === '待抽检'
                    }">
                    {{ getStatusText(record.status) }}
                    </span>
                </template>
              </template>
          </a-table>
        </div>
    </div>

    <!-- 订单详情抽屉 -->
    <a-drawer
      v-model:open="drawerVisible"
      title="订单详情"
      placement="right"
      width="800"
      @close="closeDrawer"
    >
      <template v-if="selectedOrder">
        <!-- 视频播放区域 -->
        <div class="video-container bg-white p-4 rounded">
          <div class="video-player relative mb-4">
            <video ref="videoRef" 
              class="w-full h-100 border rounded" :src="(selectedOrder as any)?.video_url" controls autoplay
              v-show="!showKeyframe">
            </video>
            <canvas ref="canvasRef" class="hidden"></canvas>
            <!-- 关键帧图片显示区域 -->
            <div v-if="showKeyframe && keyframeImages.length > 0" class="w-full h-100 border rounded p-8">
              <div class="keyframe-grid grid grid-cols-4 gap-8 pt-6">
                <div v-for="(frame, index) in keyframeImages[currentKeyframeIndex]" :key="index" 
                  class="keyframe-item cursor-pointer border-2 border-transparent hover:border-[#005290] rounded overflow-hidden transition-all duration-300">
                  <img 
                    :src="frame" 
                    class="w-full h-full object-cover" 
                    alt="关键帧图片" 
                  />
                </div>
              </div>
            </div>
            <div class="video-speed-indicator absolute top-4 right-4 bg-black bg-opacity-70 text-white px-2 py-1 rounded" v-if="videoRef && !showKeyframe">
              {{ playbackRate.toFixed(2) }}x
            </div>
            <div class="video-controls flex justify-start py-4">
              <div class="flex gap-4">
                <a-button 
                  class="btn-filter" 
                  :class="{ active: currentAngle === 1 }" 
                  @click="switchAngle(1)">视角1</a-button>
                <a-button 
                  class="btn-filter" 
                  :class="{ active: currentAngle === 2 }" 
                  @click="switchAngle(2)">视角2</a-button>
                <a-button 
                  class="btn-filter" 
                  :class="{ active: currentAngle === 3 }" 
                  @click="switchAngle(3)">关键帧 1</a-button>
                <a-button 
                  class="btn-filter" 
                  :class="{ active: currentAngle === 4 }" 
                  @click="switchAngle(4)">关键帧 2</a-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 选中商品区域 -->
        <div class="p-4 rounded mt-[-24px] mb-4 bg-white shadow-sm">
          <div class="flex justify-between items-center mb-4 justify-start items-center gap-2 pb-3 border-b border-b-solid border-b-[#D5D5D5]/50">
            <h3 class="font-500 text-[14px]">
              <span class="title-decorator"></span>
              选中商品 <span class="text-black">({{ (selectedOrder as any)?.sku_num || 0 }}种，{{ (selectedOrder as any)?.selected_num || 0 }}件)</span>
            </h3>
          </div>
          
          <div class="selected-product-list max-h-[300px] overflow-y-auto">
            <div class="flex flex-wrap gap-4 p-2">
              <!-- 商品项 -->
              <div v-for="(sku, index) in (selectedOrder as any)?.selected_skus || []" :key="index" class="selected-product-item relative border border-gray-200 overflow-hidden p-2 w-[110px]">
                <div class="product-badge absolute top-1 right-1 bg-red-500 text-white text-xs px-1 rounded">{{ sku.quantity }}</div>
                <img :src="sku.image || '/images/商品详情1.png'" class="w-full h-[80px] object-contain" />
                <div class="product-name text-[10px] mt-2 line-clamp-2 overflow-ellipsis">{{ sku.name }}</div>
                <div class="product-spec text-[10px]">{{ sku.spec }}</div>
              </div>
            </div>
          </div>
        </div>
        <!-- 底部操作区域 -->
        <div class="flex justify-between items-center m-4 bg-white fixed-bottom">
          <div class="flex flex-col flex-1 p-3">
            <div class="flex flex-row gap-2 mb-4">
              <a-button class="bg-[#FF4D4F] text-[#fff]">{{ (selectedOrder as any)?.reason || '其他警告' }}</a-button>
            </div>
            <div class="mb-4" v-if="(selectedOrder as any)?.reason === '其他警告'">
              {{ (selectedOrder as any)?.other_reason }}
            </div>
          </div>
        </div>
        <div class="flex float-right mt-4 gap-4 fixed-bottom-buttons">
          <a-button class="review-btn border-[#FF4D4F] text-[#FF4D4F]" type="default" @click="showUnqualifiedConfirm">不合格</a-button>
          <a-button class="review-btn bg-[#005290]" type="primary" @click="showQualifiedConfirm">合格</a-button>
        </div>
      </template>
    </a-drawer>
  </div>
</template>

<script setup lang="ts">
import { h, ref, reactive, onMounted, computed, watch } from 'vue'
import { onKeyStroke, useMagicKeys } from '@vueuse/core'
import type { TableColumnsType } from 'ant-design-vue'
import { message, Modal } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import ExportMenu from '@/components/ExportMenu.vue'
import { 
  getReviewStats, 
  getReviewStatus, 
  getOrderList, 
  getReasonList, 
  getOrderDetail, 
  submitReview, 
  exportInspectionList 
} from '../../api/unmanned'
import dayjs from 'dayjs';
const formatDate = (date: any) => dayjs(date).format('YYYY-MM-DD HH:mm:ss');
// 筛选条件
const filters = reactive({
  display_order_id: null,
  task_id: null,
  status: null,
  reason: null, 
  checker: null,
  start_at: '',
  end_at: '',
  dateRange: [],
})

// 当前选中行的key
const selectedRowKey = ref('')

// 重置筛选条件
const resetFilters = () => {
  filters.display_order_id = null
  filters.task_id = null
  filters.status = null
  filters.reason = null
  filters.checker = null
  filters.start_at = ''
  filters.end_at = ''
  filters.dateRange = []
  
  // 重置分页和排序
  pagination.page = 1
  pagination.order_by = null
  pagination.order_desc = false
  
  // 重新获取数据
  fetchInspectionList()
}

// 搜索按钮处理
const handleSearch = () => {
  pagination.page = 1 // 搜索时重置到第一页
  fetchInspectionList()
}
const reasonList = ref<Array<{ id: number; name: string }>>([])
// 获取异常原因列表
const fetchReasonList = async () => {
  try {
    const response = await getReasonList()
    if (response && response.data) {
        console.log(response.data);
        reasonList.value = response.data
    }
  } catch (error) {
    console.error('获取异常原因列表失败:', error)
    message.error('获取异常原因列表失败，请稍后重试')
  }
}
const reviewStatus = ref([])
const fetchReviewStatus = async () => {
  try {
    const res = await getReviewStatus()
    if (!res.code) {
      console.log(res.data);
      reviewStatus.value = res.data
    }
  } catch (error) {
    console.error('获取抽检状态列表失败:', error)
    message.error('获取抽检状态列表失败，请稍后重试')
  }
}
// 组件挂载时加载数据和初始化视频处理
onMounted(() => {
  fetchInspectionList()
  fetchReviewStats() // 获取抽检统计数据
  fetchReviewStatus() // 获取抽检状态
  fetchReasonList() // 获取异常类型列表
  
  // 初始化视频事件监听
  if (videoRef.value) {
    // 设置默认播放速度
    videoRef.value.playbackRate = playbackRate.value
    
    // 添加视频加载事件监听器，确保每次视频加载完成后都设置为1.5倍速
    videoRef.value.addEventListener('loadeddata', () => {
      // 确保videoRef.value存在再设置播放速率
      if (videoRef.value) {
        videoRef.value.playbackRate = playbackRate.value
      }
    })
  }
  
  // 默认选中第一行
  if (dataSource.value && dataSource.value.length > 0) {
    selectedRowKey.value = dataSource.value[0].orderId
  }
})

// 表格行类名
const rowClassName = (record: any) => {
  // 确保选中行的背景色持续显示，直到用户悬停在其他行上
  return record.orderId === selectedRowKey.value ? 'selected-row' : ''
}

// 表格行点击和悬停事件
const customRow = (record: any) => {
  return {
    onClick: () => {
      selectedRowKey.value = record.orderId
      // showOrderDetail(record)
    },
    onMouseenter: () => {
      // 鼠标悬停时更新选中行
      selectedRowKey.value = record.orderId
    }
  }
}

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '订单ID',
    dataIndex: 'display_order_id',
    key: 'display_order_id',
    sorter: false,
    width: 120,
  },
  {
    title: '任务ID',
    dataIndex: 'task_id',
    key: 'task_id',
    sorter: false,
    width: 120
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    sorter: true,
    width: 120
  },
  {
    title: '审核人',
    dataIndex: 'checker',
    key: 'checker',
    width: 100
  },
  {
    title: '审核时间',
    dataIndex: 'checked_at',
    key: 'checked_at',
    sorter: true,
    width: 120
  },
  {
    title: '审核时长',
    dataIndex: 'check_sec',
    key: 'check_sec',
    sorter: true,
    width: 100,
    customRender: ({ text }) => text+'s',
    align: 'center'
  },
  {
    title: '品类/数量',
    dataIndex: 'sku_num',
    key: 'sku_num',
    width: 100,
    customRender: ({ record }) => `${record.sku_num || 0} / ${record.selected_num || 0}`,
    align: 'center'
  },
  {
    title: '抽检人',
    dataIndex: 'reviewer',
    key: 'reviewer',
    width: 100
  },
  {
    title: '异常类型',
    dataIndex: 'reason',
    key: 'reason',
    width: 120,
    ellipsis: true
  },
  {
    title: '抽检状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    align: 'center'
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 100,
    align: 'center',
    customRender: ({ record }) => {
      return h(
        'a',
        {
          style: { color: '#005290', cursor: 'pointer' },
          onClick: () => showOrderDetail(record),
        },
        '查看'
      );
    }
  }
]
// 表格数据
const dataSource = ref<any[]>([])
// 分页配置
const pagination = reactive({
  total: 0,
  page: 1,
  pageSize: 20,
  totalPage: 1,
  showSizeChanger: false,
  sortField: '',
  sortOrder: '',
  order_by: null as string | null,  // 添加缺失的属性
  order_desc: false
})
// 监听 dateRange 变化
watch(() => filters.dateRange, (newVal) => {
  if (newVal && newVal.length === 2) {
    filters.start_at = formatDate(newVal[0]);
    filters.end_at = formatDate(newVal[1]);
  }
}, { immediate: true }); // immediate 让 watch 在初始化时立即执行

// 获取抽检列表数据
const fetchInspectionList = (params?: any) => {  
  // 构建请求参数
  const queryParams = {
    page: pagination.page,
    order_id: filters.display_order_id ? Number(filters.display_order_id) : null,
    task_id: filters.task_id ? Number(filters.task_id) : null,
    status: filters.status ?? null,
    reason: filters.reason ?? null,
    checker: filters.checker ?? null,
    start_at: filters.start_at ? formatDate(filters.start_at) : null,
    end_at: filters.end_at ? formatDate(filters.end_at) : null,
    order_by: pagination.sortField || 'created_at',
    order_desc: false,
    ...params
};  
  // 调用API获取数据
  getOrderList(queryParams).then((res:any) => {
    const _respone = res.data?.orders.map((item: any) => {
      return {
        ...item,
        action: '查看'
      }
    })
    dataSource.value = _respone || []
    pagination.total = res.data?.total_num || 0
  }).catch((err: any) => {
    console.error('获取抽检列表失败:', err)
    message.error('获取数据失败，请稍后重试')
  })
}

// 视频引用
const videoRef = ref<HTMLVideoElement | null>(null)
const currentAngle = ref(1) // 当前视角，默认视角1
const playbackRate = ref(1.5) // 当前播放速度
const canvasRef = ref<HTMLCanvasElement | null>(null)
// 关键帧数据
const keyframeImages = ref<string[][]>([]) // 存储关键帧图片URL
const showKeyframe = ref<boolean>(false) // 是否显示关键帧
const currentKeyframeIndex = ref<number>(0) // 当前显示的关键帧索引

// 导航到上一个订单
const navigateToPreviousOrder = () => {
  if (!dataSource.value || dataSource.value.length === 0) return
  
  const currentIndex = dataSource.value.findIndex(item => item.orderId === selectedRowKey.value)
  if (currentIndex > 0) {
    const previousOrder = dataSource.value[currentIndex - 1]
    selectedRowKey.value = previousOrder.orderId
    
    // 确保选中行在视图中可见
    const rowElement = document.querySelector(`.selected-row`)
    if (rowElement) {
      rowElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  }
}

// 导航到下一个订单
const navigateToNextOrder = () => {
  if (!dataSource.value || dataSource.value.length === 0) return
  
  const currentIndex = dataSource.value.findIndex(item => item.orderId === selectedRowKey.value)
  if (currentIndex < dataSource.value.length - 1) {
    const nextOrder = dataSource.value[currentIndex + 1]
    selectedRowKey.value = nextOrder.orderId
    
    // 确保选中行在视图中可见
    const rowElement = document.querySelector(`.selected-row`)
    if (rowElement) {
      rowElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  }
}

// 倍速控制方法
const increasePlaybackRate = () => {
  if (videoRef.value && playbackRate.value < 2.0) {
    playbackRate.value = Math.min(2.0, playbackRate.value + 0.25)
    videoRef.value.playbackRate = playbackRate.value
  }
}

const decreasePlaybackRate = () => {
  if (videoRef.value && playbackRate.value > 0.5) {
    playbackRate.value = Math.max(0.5, playbackRate.value - 0.25)
    videoRef.value.playbackRate = playbackRate.value
  }
}

const currentVideoUrl = ref<string | undefined>(undefined)
// 切换视角
const switchAngle = (angle: number) => {
  currentAngle.value = angle
  // 判断是否是关键帧视角
  if (angle === 3 || angle === 4) {
    // 设置当前关键帧索引（关键帧1对应索引0，关键帧2对应索引1）
    currentKeyframeIndex.value = angle - 3
    // 显示关键帧
    showKeyframe.value = true
    if (videoRef.value) {
      videoRef.value.pause()
    }
  } else {
    // 显示视频
    showKeyframe.value = false
    // 如果有多个视频源，可以在这里切换视频源
    if (videoRef.value && currentVideoUrl.value) {
      videoRef.value.src = currentVideoUrl.value
      videoRef.value.load()
      videoRef.value.play()
    }
  }
}

// 抽屉控制
const drawerVisible = ref(false)
const selectedOrder = ref<any>(null)

// 表格变化处理
const handleTableChange = (pag: any, filters: any, sorter: any) => {
  console.log('Table change:', { pag, filters, sorter })
  
  // 更新分页信息
  pagination.page = pag.current
  pagination.pageSize = pag.pageSize
  
  // 处理排序参数
  let sortField = null
  let sortOrder = true
  if (sorter.field && sorter.order) {
    sortField = sorter.field
    sortOrder = sorter.order === 'ascend' ? true : false
  }
  
  // 获取筛选后的数据
  fetchInspectionList({
    ...filters,
    page: pagination.page,
    pageSize: pagination.pageSize,
    order_by: sortField || "created_at",
    order_desc: sortOrder
  })
}

// 显示订单详情
const showOrderDetail = (record: any) => {
  selectedRowKey.value = record.orderId
  selectedOrder.value = null// 先清空之前的数据  
  // 清空关键帧图片
  keyframeImages.value = []
  drawerVisible.value = true
  // 显示加载状态
  // const loadingMessage = message.loading('正在加载订单详情...', 0)
  
  // 调用API获取订单详情
  getOrderDetail(record.task_id).then((res: any) => {
    // 处理API返回的数据
    if (res.data) {
      // 构建订单详情对象
      selectedOrder.value = {
        orderId: res.data.order_id,
        display_order_id: res.data.display_order_id,
        task_id: res.data.task_id,
        video_url: res.data.videos && res.data.videos.length > 0 ? res.data.videos[0] : '',
        sku_num: res.data.skus ? res.data.skus.length : 0,
        selected_num: res.data.skus ? res.data.skus.reduce((total: number, sku: any) => total + (sku.number || 1), 0) : 0,
        reason: res.data.reason || '',
        other_reason: res.data.other_reason || '',
        selected_skus: res.data.skus ? res.data.skus.map((sku: any) => ({
          id: sku.id,
          name: sku.name,
          spec: sku.spec || '',
          image: sku.cover || '',
          quantity: sku.number || 1
        })) : []
      }
      //  // 如果有视频链接，设置视频源
      // if (res.data.videos && res.data.videos.length > 0) {
      //   currentVideoUrl.value = res.data.videos[0]
      //   if (videoRef.value) {
      //     videoRef.value.src = currentVideoUrl.value || ''
      //     videoRef.value.load()
      //     videoRef.value.play()
      //   }
      // }
      // 如果有关键帧数据，设置关键帧图片
      if (res.data.key_frames && res.data.key_frames.length > 0) {
        keyframeImages.value = res.data.key_frames
        // 默认显示视频，不显示关键帧
        showKeyframe.value = false
        currentKeyframeIndex.value = 0
      } else {
        // 如果没有关键帧数据，清空关键帧图片数组
        keyframeImages.value = []
      }
      // 处理关键帧图片
      // if (res.data.key_frames && res.data.key_frames.length > 0) {
      //   // 将关键帧图片添加到keyframeImages数组
      //   res.data.key_frames.forEach((frameGroup: any) => {
      //     if (Array.isArray(frameGroup) && frameGroup.length > 0) {
      //       keyframeImages.value.push(frameGroup[0])
      //     }
      //   })
      // }
    }
    
    // 关闭加载提示
    // loadingMessage()
  }).catch((err: any) => {
    console.error('获取订单详情失败:', err)
    message.error('获取订单详情失败，请稍后重试')
    // loadingMessage()
  })
}

// 关闭抽屉
const closeDrawer = () => {
  drawerVisible.value = false
  selectedOrder.value = null
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    2: '合格',
    3: '不合格',
    1: '特抽检'
  }
  return statusMap[status] || status
}

// 导出抽检列表
const exportList = () => {
  // 显示加载中提示
  const loadingKey = 'exportLoading';
  // message.loading({ content: '正在导出数据...', key: loadingKey, duration: 0 });
  
  // 构建导出参数，使用当前筛选条件
  const exportParams = {
    order_id: filters.display_order_id ?? null,
    task_id: filters.task_id ?? null,
    status: filters.status ?? null,
    reason: filters.reason ?? null,
    checker: filters.checker ?? null,
    start_at: filters.start_at ?? null,
    end_at: filters.end_at ?? null,
    order_by: pagination.sortField,
    order_desc: false
  }
  
  // 调用导出API
  exportInspectionList(exportParams).then((res: BlobPart) => {
    // 创建下载链接
    const blob = new Blob([res], { type: 'application/vnd.ms-excel' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `抽检记录_${new Date().toISOString().split('T')[0]}.xlsx`
    link.click()
    URL.revokeObjectURL(link.href)
    message.success({ content: '导出成功', key: loadingKey })
  }).catch((err: any) => {
    console.error('导出失败:', err)
    message.error({ content: '导出失败，请稍后重试', key: loadingKey })
  })
}

// 显示合格确认弹窗
const showQualifiedConfirm = () => {
  Modal.confirm({
    title: '提示',
    content: '是否确定订单抽检合格？',
    okText: '确定',
    cancelText: '取消',
    onOk() {
      // 调用接口更新订单状态为合格
      updateOrderStatus('qualified')
    }
  })
}

// 显示不合格确认弹窗
const showUnqualifiedConfirm = () => {
  Modal.confirm({
    title: '提示',
    content: '是否确定订单抽检不合格？',
    okText: '确定',
    cancelText: '取消',
    onOk() {
      // 调用接口更新订单状态为不合格
      updateOrderStatus('unqualified')
    }
  })
}

// 更新订单状态
const updateOrderStatus = (status: string) => {
  if (!selectedOrder.value) {
    message.error('未选择订单')
    return
  }
  
  // 构建更新参数
  const updateData = {
    task_id: (selectedOrder.value as any).task_id,
    status: status,
    reason: (selectedOrder.value as any).reason,
    ok: status === 'qualified'? true : false
  }
  
  // 调用API更新订单状态
  submitReview(updateData).then((res: any) => {
    if (!res.code) {
      // 更新成功后关闭抽屉
      closeDrawer()
      // 刷新列表
      fetchInspectionList()
      // 刷新统计数据
      fetchReviewStats()
      // 显示成功提示
      message.success(`订单状态已更新为${status === 'qualified' ? '合格' : '不合格'}`)
    }
  })
}

// 路由实例
const router = useRouter()

// 判断是否有多个角色
const hasMultipleRoles = computed(() => {
  const userRoles = JSON.parse(localStorage.getItem('user_roles') || '[]');
  return userRoles.some((role: { name: string }) => role.name === 'review') && userRoles.some((role: { name: string }) => role.name === 'check');
});

// 切换到审核页面
const switchToReview = () => {
  Modal.confirm({
    title: '提示',
    content: '是否确认切换到审核页面？',
    okText: '确定',
    cancelText: '取消',
    onOk() {
      router.push('/review')
    }
  })
}

// 抽检统计数据
const reviewStats = reactive({
  total_num: 0,
  ok_num: 0,
  no_num: 0
})

// 获取抽检统计数据
const fetchReviewStats = () => {
  getReviewStats().then((res:any) => {
    if (res && res.data) {
      // 根据API返回的数据结构进行映射
      reviewStats.total_num = res.data.total_num || 0
      reviewStats.ok_num = res.data.ok_num || 0
      reviewStats.no_num = res.data.no_num || 0
    }
  }).catch((err: any) => {
    console.error('获取抽检统计数据失败:', err)
  })
}

// 使用VueUse实现快捷键功能
// 空格键：播放/暂停
onKeyStroke(' ', (e) => {
  if (drawerVisible.value && videoRef.value) {
    e.preventDefault()
    if (videoRef.value.paused) {
      videoRef.value.play()
    } else {
      videoRef.value.pause()
    }
  } else if (selectedRowKey.value && dataSource.value) {
    e.preventDefault()
    const selectedRecord = dataSource.value.find(item => item.orderId === selectedRowKey.value)
    if (selectedRecord) {
      showOrderDetail(selectedRecord)
    }
  }
})

// 右键：快进
onKeyStroke('ArrowRight', (e) => {
  if (drawerVisible.value && videoRef.value) {
    e.preventDefault()
    videoRef.value.currentTime += 5
  }
})

// 左键：倒退
onKeyStroke('ArrowLeft', (e) => {
  if (drawerVisible.value && videoRef.value) {
    e.preventDefault()
    videoRef.value.currentTime -= 5
  }
})

// ESC键：退出详情
onKeyStroke('Escape', (e) => {
  if (drawerVisible.value) {
    e.preventDefault()
    closeDrawer()
  }
})

// 上键：切换上一个订单
// 上键：切换上一个订单
onKeyStroke('ArrowUp', (e) => {
  if (!drawerVisible.value && dataSource.value.length > 0) {
    e.preventDefault()
    navigateToPreviousOrder()
  }
})

// 下键：切换下一个订单
onKeyStroke('ArrowDown', (e) => {
  if (!drawerVisible.value && dataSource.value.length > 0) {
    e.preventDefault()
    navigateToNextOrder()
  }
})

// 使用useMagicKeys处理组合键
const { alt, ctrl } = useMagicKeys()

// Ctrl+Tab：视角切换
watch(ctrl, (pressed) => {
  if (pressed && drawerVisible.value) {
    onKeyStroke('Tab', (e) => {
      console.log('ctrl + tab 111');
      e.preventDefault()
      const nextAngle = currentAngle.value % 4 + 1
      switchAngle(nextAngle)
    })
  }
})

// Alt+1：提高倍速
watch(alt, (pressed) => {
  if (pressed) {
    onKeyStroke('1', (e) => {
      e.preventDefault()
      console.log('alt + 1')
      increasePlaybackRate()
    })
    onKeyStroke('2', (e) => {
      e.preventDefault()
      console.log('alt + 2')
      decreasePlaybackRate()
    })
    // onKeyStroke('1', (e) => {
    //   if (e.altKey && drawerVisible.value && videoRef.value) {
    //     e.preventDefault()
    //     increasePlaybackRate()
    //   }
    // })
    // // Alt+2：降低倍速
    // onKeyStroke('2', (e) => {
    //   if (e.altKey && drawerVisible.value && videoRef.value) {
    //     e.preventDefault()
    //     decreasePlaybackRate()
    //   }
    // })
  }
})

</script>

<style scoped>
.page-title {
  color: #333;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
  background: rgb(255, 255, 255);
}

.filter-form {
  border-radius: 4px;
}

/* 筛选区域标签对齐样式 */
.filter-form :deep(.ant-form-item-label) {
  min-width: 80px;
  text-align: right;
}

.filter-form :deep(.ant-form-item) {
  margin-bottom: 16px;
}

.statistics-summary {
  font-size: 14px;
  color: #666;
  box-sizing: border-box;
  border: 1px solid rgb(186, 224, 255);
  border-radius: 4px;
  background: rgb(230, 244, 255);
}

.info-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-color: #1677FFA6;
  border-radius: 50%;
  color: #fff
}

.shortcut-link a {
  color: #005290;
  text-decoration: none;
}

.statistics :deep(.ant-statistic-title) {
  font-size: 16px;
}

.statistics :deep(.ant-statistic-content) {
  font-size: 24px;
}

.text-success :deep(.ant-statistic-content-value),
.text-success {
  color: #52c41a;
}

.text-error :deep(.ant-statistic-content-value),
.text-error {
  color: #ff4d4f;
}

.text-warning {
  color: #005290;
}

.inspection-table :deep(.ant-table-thead > tr > th) {
  background-color: #f5f5f5;
  font-weight: 500;
  height: 48px;
  padding: 8px 16px;
  line-height: 32px;
}

.inspection-table :deep(.ant-table-tbody > tr > td) {
  height: 48px;
  padding: 8px 16px;
  line-height: 32px;
}

/* 订单详情抽屉样式 */
.order-info {
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.05);
}

/* 选中行样式 */
.selected-row {
  background-color: #E6F4FF !important;
}

/* 表格行悬停样式 */
.inspection-table :deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5;
}

.product-badge {
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 20px;
  background-color: #fa5151;
  color: white;
  border-top-right-radius: 4px;
  border-bottom-left-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  z-index: 1;
}

.selected-product-item {
  height: auto;
  position: relative;
  transition: all 0.3s;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.25);
  background: rgb(255, 255, 255);
}

.exception-btn {
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 4px 12px;
  font-size: 12px;
  transition: all 0.3s;
}

.exception-btn:hover {
  border-color: #005290;
  color: #005290;
}

.exception-btn.active {
  background-color: #e6f7ff;
  border-color: #005290;
  color: #005290;
}

.review-btn {
  padding: 6px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.3s;
}

.review-btn.qualified {
  background-color: #52c41a;
  color: white;
}

.review-btn.unqualified {
  background-color: #f5222d;
  color: white;
}
/* 底部固定样式 */
.fixed-bottom {
  border-radius: 6px;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
  background: rgb(255, 255, 255);
}

.fixed-bottom-buttons {
  position: fixed;
  bottom: 0;
  right: 0;
  z-index: 101;
  padding: 16px;
  background-color: white;
  width: 800px;
  display: flex;
  justify-content: flex-end;
  box-sizing: border-box;
}

/* 为抽屉内容添加底部内边距，避免内容被固定底部区域遮挡 */
:deep(.ant-drawer-body) {
  padding-bottom: 120px;
}

/* 视频播放控制样式 */
.video-container {
  border-radius: 8px;
  overflow: hidden;
}

.video-player {
  position: relative;
}

.video-speed-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 10;
}

.video-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
}

.btn-speed {
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-speed:hover {
  border-color: #005290;
  color: #005290;
}

.speed-value {
  padding: 0 8px;
  font-size: 14px;
}

.keyframe-preview {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.keyframe-item {
  border: 2px solid transparent;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
}

.keyframe-item:hover {
  border-color: #005290;
}

.keyframe-item.active {
  border-color: #005290;
}

/* 快捷键提示样式 */
.shortcut-link {
  position: relative;
}

.shortcut-tooltip {
  display: none;
  position: absolute;
  top: 100%;
  right: 0;
  width: 280px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  margin-top: 10px;
  border: 1px solid #eaeaea;
  animation: fadeIn 0.2s ease-in-out;
}

.shortcut-link:hover .shortcut-tooltip {
  display: block;
}

.tooltip-title {
  font-size: 14px;
  font-weight: 500;
  padding: 10px 15px;
  color: #000;
  text-align: left;
  margin-left: -10px;
}

.tooltip-content {
  padding: 10px 0;
  max-height: 300px;
  overflow-y: auto;
  background: rgb(245, 245, 245);
  border-radius: 5px;
  margin: 5px;
}

.tooltip-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 8px;
  padding-right: 8px;
}

.tooltip-item .key {
  background-color: #f0f2f5;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #8c8c8c;
  min-width: 80px;
  text-align: right;
  line-height: 16px;
  font-weight: 400;
  font-family: PingFang SC;
}

.tooltip-item .desc {
  color: #1f1f1f;
  text-align: left;
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  font-family: PingFang SC;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>