<template>
  <div class="flex w-[100vw] h-[100vh] overflow-hidden">
    <div class="login-left flex-center">
      <img src="/images/login_left.png" alt="login decoration" class="login-decoration object-cover" />
    </div>
    
    <div class="login-right flex-center">
      <div class="login-form">

        <h2 class="login-title mb-8">登录您的账号</h2>

        <a-form
          :model="loginForm"
          :rules="loginRules"
          ref="loginFormRef"
          layout="vertical"
          @keyup.enter="loginSubmit"
        >
          <a-form-item label="账号" name="username">
            <a-input v-model:value="loginForm.username" placeholder="请输入账号" size="large">
              <template #prefix>
                <user-outlined />
              </template>
            </a-input>
          </a-form-item>

          <a-form-item label="密码" name="password">
            <a-input-password v-model:value="loginForm.password" placeholder="请输入密码" size="large">
              <template #prefix>
                <lock-outlined />
              </template>
            </a-input-password>
          </a-form-item>
          
          <a-form-item>
            <a-button type="primary" size="large" block @click="loginSubmit" :disabled="loading">
              <span class="text-white flex items-center justify-center">
                  <a-spin v-if="loading" size="small" class="mr-2" />
                  登录
                </span>
            </a-button>
          </a-form-item>
        </a-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Login page component
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue';
import { login, getUserRole } from '../../api/user';

// 引入Mock服务
import '@/mock';

const router = useRouter();
const loginFormRef = ref();
const loading = ref(false);

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: ''
});

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入账号', trigger: 'blur' },
    { min: 3, max: 20, message: '账号长度应为3-20个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度应为6-20个字符', trigger: 'blur' }
  ]
};

// 登录提交
const loginSubmit = () => {
  loginFormRef.value.validate().then(() => {
    // 设置加载状态
    loading.value = true;
    
    // 调用登录接口
    login({
      username: loginForm.username,
      password: loginForm.password
    }).then((res: any) => {
      if (res) {
        // 存储token和用户信息
        localStorage.setItem('access_token', res.access_token);
        localStorage.setItem('token_type', res.token_type);
        // 登录成功
        message.success("登录成功");
        // 获取用户角色并跳转到对应页面
        getUserRole().then((roles: any) => {
          // 存储用户角色信息
          localStorage.setItem('user_roles', JSON.stringify(roles));
          // 默认跳转到抽检页面
          router.push('/inspection');
        }).catch((error: Error) => {
          console.error('获取用户角色失败:', error);
          message.error('获取用户角色失败，请重新登录');
        });
      } else {
        // 登录失败
        message.error(res.message);
      }
    }).catch((error: any) => {
      console.error('登录失败:', error);
      message.error('登录失败，请稍后重试');
    }).finally(() => {
      // 无论成功失败，都关闭加载状态
      loading.value = false;
    });
  }).catch((error: any) => {
    console.log('表单验证失败:', error);
  });
}
</script>

<style scoped>
.login-container {
  width: 100vw;
  height: 100vh;
}

.login-left {
  flex: 1;
  background-color: #f0f7ff;
  position: relative;
}

.login-decoration {
  width: 100%;
  height: 100%;
  position: absolute;
}

.login-right {
  flex: 1;
  background-color: white;
}

.login-form {
  width: 360px;
  height: 400px;
  border-radius: 4px;
  box-shadow: 4px 4px 10px 0px rgba(37, 79, 243, 0.1);
  background: rgb(255, 255, 255);
  padding: 20px;
}

.login-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}
</style>