<template>
  <div class="w-full h-full">
    <!-- 身份切换按钮 -->
    <div v-if="hasMultipleRoles" class="identity-switch-btn" @click="switchToInspection">
      <img src="/images/change_role.png" class="w-5 h-5"/>
      <span class="inline-block w-[10px]">切换抽检</span>
    </div>
    <!-- 顶部订单信息 -->
    <div ref="headerRef" class="order-info flex items-center justify-between p-6 rounded bg-[#f0f2f5] min-w-[1024px]" :class="{ 'fixed-header': isHeaderFixed }">
      <div class="flex items-center gap-20 text-sm">
        <!-- 等待任务中... -->
        <template v-if="!hasTask">
          <div class="font-500 text-[14px] w-100">等待任务中...</div>
        </template>
        <!-- 有新任务上线了 -->
        <template v-else-if="hasSubmitEnd">
          <span class="w-3 h-3 bg-primary rounded-[100%]"></span>
          <div class="font-500 text-[14px] w-100 text-primary ml-[-70px]">{{ orderInfo.taskPool ? '有新任务上线了':'无任务在线'  }}</div>
        </template>
        <template v-else>
          <div class="ml-4 text-[#333] font-sans text-[14px] font-400 leading-none tracking-none text-left">订单ID: <span>{{ orderInfo.orderId }}</span></div>
          <div class="text-[#333] font-sans text-[14px] font-400 leading-none tracking-none text-left">任务ID: <span>{{ orderInfo.taskId }}</span></div>
        </template> 
        <div class="flex items-center gap-4">
            <div class="text-[14px] font-400 leading-20 tracking-none text-left">任务池: <span class="text-green">{{ orderInfo.taskPool }}</span></div>|
            <div class="text-[14px] font-400 leading-20 tracking-none text-left">今日完成: <span class="text-success">{{ orderInfo.completedToday }}</span></div>|
            <div class="text-[14px] font-400 leading-20 tracking-none text-left">异常提交: <span class="text-red">{{ orderInfo.exceptionMode }}</span></div>
        </div>
        <div class="text-[14px] font-400 leading-20 tracking-none text-left">在线人数: <span class="font-400">{{ orderInfo.onlineUsers }}</span></div>&nbsp;&nbsp;
        <div class="text-[14px] font-400 leading-20 tracking-none text-left" v-if="!hasSubmitEnd">倒计时: <span class="text-red">{{ orderInfo.remainSeconds }}</span></div>
      </div>
    </div>

    <div class="flex flex-row w-full h-[100vh] bg-[#fafafc]" :style="isHeaderFixed ? { 'padding-top': headerHeight + 'px' } : {}">
      <!-- 缺省页面 -->
      <div v-if="!hasTask" class="flex-1 flex flex-col items-center justify-center shadow-custom p-4">
        <img src="/images/default_task.png" alt="暂无任务" class="w-[300px] mb-4">
        <p class="text-[16px] text-[#666] mb-4">暂无任务</p>
        <a-button type="primary" @click.stop="refreshTask">刷新任务</a-button>
      </div>
      <div v-else-if="hasSubmitEnd" class="flex-1 flex flex-col items-center justify-center p-4">
        <img src="/images/waiting.png" alt="暂无任务" class="w-[300px] mb-4">
        <p class="text-[16px] text-[#666] mb-4">休息一下</p>
        <a-button type="default" @click.stop="refreshTask" class="w-30 h-10 border-2 border-solid"
        :class="orderInfo.taskPool > 0 ? 'text-[#005290] border-[#005290]':''" :disabled="orderInfo.taskPool === 0">
          开始审核&nbsp;&nbsp;
          <img src="/images/arrow_right.png" class="w-4 h-4 ml-2 mb-1"/>
        </a-button>
      </div>
      <!-- 任务内容区域 -->
      <template v-else>
        <!-- 左侧视频区域 -->
        <div class="bg-[#fafafc] flex-1 border-r border-r-solid border-r-[#d5d5d5]/50 pb-10 flex flex-col">
          <div>
            <div class="video-player relative min-h-[400px]">
              <video ref="videoRef" 
                class="w-full min-h-[400px] max-h-[800px] bg-black aspect-video" 
                :src="currentVideoUrl" controls autoplay
                v-show="!showKeyframe">
              </video>
              <!-- 关键帧图片展示区域 -->
              <div v-if="showKeyframe && keyframeImages.length > 0" 
                class="w-full bg-white p-4 aspect-video max-h-[800px]">
                <div class="keyframe-grid grid grid-cols-4 gap-8 pt-6">
                  <div v-for="(frame, index) in keyframeImages[currentKeyframeIndex]" :key="index" 
                    class="keyframe-item cursor-pointer border-2 border-transparent hover:border-[#005290] rounded overflow-hidden transition-all duration-300">
                    <img :src="frame" class="w-full h-full object-contain" alt="关键帧图片"  @mouseenter="showProductZoom($event, frame)" @mouseleave="hideProductZoom" />
                  </div>
                </div>
              </div>

              <div class="video-speed-indicator absolute top-4 right-4 bg-black bg-opacity-70 text-white px-2 py-1 rounded" v-if="videoRef && !showKeyframe">
                {{ playbackRate.toFixed(2) }}x
              </div>
            </div>
            <div class="video-controls flex justify-start p-4 mb-4">
                <div class="flex gap-4">
                  <a-button 
                    class="btn-filter" 
                    :class="{ active: currentAngle === 1 }" 
                    @click="switchAngle(1)">视角1</a-button>
                  <a-button 
                    class="btn-filter" 
                    :class="{ active: currentAngle === 2 }" 
                    @click="switchAngle(2)">视角2</a-button>
                  <a-button 
                    class="btn-filter" 
                    :class="{ active: currentAngle === 3 }" 
                    @click="switchAngle(3)">关键帧 1</a-button>
                  <a-button 
                    class="btn-filter" 
                    :class="{ active: currentAngle === 4 }" 
                    @click="switchAngle(4)">关键帧 2</a-button>
                </div>
            </div>
          </div>
        
          <!-- 已选商品区域 -->
          <div class="h-[240px] max-h-[500px] pr-2 mt-[-24px]">
              <div class="selected-products px-4 pt-4">
                <div class="flex justify-start items-center gap-2 pb-3 border-b border-b-solid border-b-[#D5D5D5]/50">
                    <span class="title-decorator"></span>
                    <h3 class="font-500 text-[14px] mt-2">已选商品</h3>
                    <span class="text-black text-[14px]">({{ productStats.totalTypes }}种，{{ productStats.totalItems }}件)</span>
                </div>

                <div class="h-[180px] max-h-[400px] overflow-y-auto pt-4 rounded-[4px]">
                    <div class="flex flex-wrap gap-6 ml-2 pb-2">
                    <div 
                        v-for="product in selectedProducts" 
                        :key="product.id"
                        class="selected-product-item relative border border-gray-300 overflow-hidden"
                    >
                        <div class="product-badge">{{ product.quantity }}</div>
                        <img :src="product.image" class="w-20 h-24 product-image object-contain"/>
                        <div class="product-name h-6 font-500 text-[10px] line-clamp-2 overflow-ellipsis px-2 mb-2">{{ product.name }}</div>
                        
                        <div class="product-overlay">
                        <div class="flex flex-col space-y-2 items-center justify-center">
                            <div class="flex items-center h-5">
                              <button class="btn-quantity cursor-pointer" @click="adjustQuantity(product.id, -1)">-</button>
                              <span class="quantity bg-[#f0f0f0] text-black h-5">{{ product.quantity }}</span>
                              <button class="btn-quantity cursor-pointer" @click="adjustQuantity(product.id, 1)">+</button>
                            </div>
                            <button class="btn-remove w-[74px] rounded-[4px] text-[10px] p-2 cursor-pointer" @click="removeProduct(product.id)">取消已选</button>
                        </div>
                        </div>
                    </div>
                    </div>
                </div>
              </div>
          </div>
        
          <!-- 底部按钮区域 -->
          <div class="bottom-action-area flex justify-between items-center mx-4 mt-2 p-4">
              <div class="flex flex-col flex-1 gap-3 my-4 mr-2 border-r-1 border-r-dashed border-[#D5D5D5]">
              <div class="flex flex-row gap-2 grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 min-w-[475px]">
                  <button 
                      v-for="exception in exceptionTypes" 
                      :key="exception.id"
                      class="exception-btn px-2"
                      :class="{ active: exception.active && exception.type === 1, active1: exception.active && exception.type === 0, lastbtn: exception.type === 0}"
                      @click="toggleException(exception.id)">
                      {{ exception.name }}
                  </button>
              </div> 
              <div class="mb-4 mt-4">
                  <a-input 
                      v-model:value="otherReason" 
                      placeholder="请输入其他原因..."
                      :maxlength="50"
                      :disabled="exceptionTypes&&!exceptionTypes[0].active"
                      class="w-97% h-10" @input="limitReason"/>
              </div>
              </div>
              <div class="flex flex-col justify-between gap-4 ml-3">
                <a-button class="submit-btn submit-end flex flex-row justify-between" @click.stop="submitCheck(true)">
                  <span class="whitespace-nowrap">提交并结束</span> 
                  <span class="ml-2 text-xs whitespace-nowrap">A</span>
                </a-button>
                <button class="submit-btn submit-next flex flex-row min-w-[137px]" @click.stop="submitCheck(false)">
                  <span class="whitespace-nowrap">提交并下一条</span>
                  <span class="ml-4 text-xs whitespace-nowrap">D</span>
                </button>
              </div>
          </div>
        </div>

        <!-- 右侧商品选择区域 -->
        <div class="w-[760px] bg-[#fafafc] p-4">
          <div class="flex justify-between items-center mb-4 border-b border-b-solid border-b-[#D5D5D5]/50 pb-2">
            <div class="flex-center">
              <span class="title-decorator"></span>
              <h3 class="font-500 text-[14px] mt-2">待选商品</h3>
            </div>
            <a-button class="flex items-center shortcut-link relative h-8 mr-4 border border-solid border-[rgb(0,82,144)] rounded-[4px] hover:bg-[rgba(0,82,144,0.05)]">
              <img src="/images/kuaijie.svg" class="w-4 h-4 mr-2"/>
              <span class="text-[rgb(0,82,144)] font-['思源黑体'] text-xs font-normal leading-8 text-center h-8 block">查看快捷键</span>
              <div class="shortcut-tooltip p-4">
                <div class="tooltip-title">视图区域</div>
                <div class="tooltip-content">
                  <!-- <div class="tooltip-item">
                    <span class="desc">视角切换</span>
                    <span class="key">CTRL+TAB</span>
                  </div> -->
                  <div class="tooltip-item">
                    <span class="desc">播放/暂停</span>
                    <span class="key">空格键</span>
                  </div>
                  <div class="tooltip-item">
                    <span class="desc">快进</span>
                    <span class="key">右键</span>
                  </div>
                  <div class="tooltip-item">
                    <span class="desc">倒退</span>
                    <span class="key">左键</span>
                  </div>
                  <div class="tooltip-item">
                    <span class="desc">提高倍速</span>
                    <span class="key">ALT+1</span>
                  </div>
                  <div class="tooltip-item">
                    <span class="desc">降低倍速</span>
                    <span class="key">ALT+2</span>
                  </div>
                </div>
                <div class="tooltip-title">待选区域</div>
                <div class="tooltip-content">
                  <div class="tooltip-item">
                    <span class="desc">切换推荐分类</span>
                    <span class="key">ALT+TAB</span>
                  </div>
                  <!-- <div class="tooltip-item">
                    <span class="desc">品类检索</span>
                    <span class="key">ALT+F</span>
                  </div> -->
                  <!-- <div class="tooltip-item">
                    <span class="desc">商品详情</span>
                    <span class="key">悬浮+S</span>
                  </div> -->
                </div>
                <div class="tooltip-title">已选区域</div>
                <div class="tooltip-content">
                  <!-- <div class="tooltip-item">
                    <span class="desc">增加/减少商品</span>
                    <span class="key">ALT+滚轮</span>
                  </div> -->
                  <div class="tooltip-item">
                    <span class="desc">选取分类标签</span>
                    <span class="key">CTRL+1~6</span>
                  </div>
                  <div class="tooltip-item">
                    <span class="desc">提交并结束</span>
                    <span class="key">A</span>
                  </div>
                  <div class="tooltip-item">
                    <span class="desc">提交并下一条</span>
                    <span class="key">D</span>
                  </div>
                </div>
              </div>
            </a-button>
          </div>
          
          <div class="flex justify-between px-4 mb-4">
            <div class="flex gap-6">
              <a-button 
                v-for="category in productCategories" 
                :key="category.id"
                class="btn-filter h-8" 
                :class="{ active: category.active }"
                @click="switchCategory(category.id)"
              >{{ category.name }}</a-button>
            </div>
            <div class="relative rounded-[4px]">
              <a-input 
                v-model:value="searchKeyword"
                type="text"
                ref="searchInputRef"
                class="w-[220px] h-8 p-2 text-[14px] border border-solid border-[#dcdcdc] rounded" 
                placeholder="品类检索"
                allow-clear
              />
              <span class="i-carbon-search absolute left-3 top-2.5 text-gray-400"></span>
            </div>
          </div>
          
          <div class="grid md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-2 max-h-[800px] overflow-y-auto pb-4 px-2">
            <div 
              v-for="product in availableProducts"
              :key="product.id" 
              class="product-item cursor-pointer hover:shadow-md transition-shadow overflow-hidden border border-gray-200 mx-auto"
              @click.stop="addProduct(product)"
            >
              <div class="relative p-1">
                <img :src="product.image" class="w-24 h-24 m-2 object-contain"/>
                <div class="product-badge" v-show="product.quantity > 0">{{ product.quantity }}</div>
              </div>
              <div class="font-500 h-6 text-[10px] line-clamp-2 overflow-ellipsis px-2">{{ product.name }}</div>
              <div 
                v-if="product.confidence !== undefined && product.confidence >= 0" 
                class="confidence-badge mt-2 w-full h-5 leading-4 text-center relative" 
                :class="{ 'high-confidence': product.confidence >= 90, 'low-confidence': product.confidence < 90 }"
              >
                <span>置信度: {{ product.confidence?.toFixed(1) }}%</span>
                <div class="confidence-hover" @mouseenter.stop="openProductDetail($event, product.id)" @mouseleave.stop="closeProductDetail">查看详情</div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
    <!-- 商品详情悬浮框 -->
    <ProductDetailPopover 
      :visible="showProductDetail" 
      :product-id="currentProductId" 
      :position="popoverPosition"
      @close="closeProductDetail" 
    />
    
    <!-- 商品图片放大悬浮框 -->
    <ProductZoomPopover
      :visible="isProductZoomVisible"
      :image-url="zoomImageUrl"
      :position="zoomPosition || { top: 0, left: 0 }"
      @close="hideProductZoom"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, watch, computed, nextTick } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import ProductDetailPopover from '@/components/ProductDetailPopover.vue'
import ProductZoomPopover from '@/components/ProductZoomPopover.vue'
import { getTask, getSystemInfo, getReasonList, submitTask } from '../../api/unmanned'
import { onKeyStroke, useMagicKeys, whenever, useEventListener } from '@vueuse/core'
// import VideoPlayer from 'vue-video-player'
// import 'video.js/dist/video-js.css'

// 顶部悬浮相关
const isHeaderFixed = ref<boolean>(false)
const headerHeight = ref<number>(0)
const headerRef = ref<HTMLElement | null>(null)

// 订单信息
const orderInfo = reactive({
  orderId: '234123840182034',
  taskId: '234123840182034',
  taskPool: 0,
  completedToday: 0,
  exceptionMode: 0,
  onlineUsers: 0,
  remainSeconds: 0
})

// 视频引用
const videoRef = ref<HTMLVideoElement | null>(null)
const currentAngle = ref<number>(1) // 当前视角，默认视角1
const playbackRate = ref<number>(1.5) // 当前播放速度
const searchInputRef = ref<HTMLInputElement | null>(null)
// 关键帧数据
const keyframeImages = ref<string[][]>([]) // 存储关键帧图片URL
const showKeyframe = ref<boolean>(false) // 是否显示关键帧
const currentKeyframeIndex = ref<number>(0) // 当前显示的关键帧索引
// 商品数据
interface Product {
  id: number
  name: string
  spec: string
  image: string
  confidence?: number
  quantity: number
}
const limitReason = () => {
  if (otherReason.value.length > 50) {
    otherReason.value = otherReason.value.slice(0, 50);
  }
};
// 商品详情弹窗控制
const showProductDetail = ref<boolean>(false)
const currentProductId = ref<number | undefined>(undefined)
const popoverPosition = ref<{ top: number; left: number } | undefined>(undefined)
const currentVideoUrl = ref<string | undefined>(undefined)

// 商品图片放大弹窗控制
const isProductZoomVisible = ref<boolean>(false)
const zoomImageUrl = ref<string>('')
const zoomPosition = ref<{ top: number; left: number } | undefined>(undefined)
// 待选商品列表
const availableProducts = ref<Product[]>([
])

// 已选商品列表
const selectedProducts = ref<Product[]>([
  { id: 1, name: '合味道猪骨汤面', spec: '300ML', image: '/images/商品详情1.png', quantity: 1 },
  { id: 2, name: '合味道鸡汤面', spec: '300ML', image: '/images/商品详情1.png', quantity: 2 },
  { id: 3, name: '合味道牛肉面', spec: '300ML', image: '/images/商品详情1.png', quantity: 1 },
])

// 异常标记
const exceptionTypes = ref<Array<{id: number, name: string, active: boolean, type: number}>>([])

// 其他警告原因
const otherReason = ref<string>('')

// 商品分类
const productCategories = ref([
  { id: 1, name: '算法推荐', active: true },
  { id: 2, name: '铺货推荐', active: false },
  { id: 3, name: '全部货品', active: false },
])

// 搜索关键词
const searchKeyword = ref<string>('')

// 原始商品列表，用于搜索筛选
const originalProducts = ref<Product[]>([])

// 计算属性：商品统计
const productStats = reactive({
  get totalTypes() {
    return selectedProducts.value.length
  },
  get totalItems() {
    return selectedProducts.value.reduce((sum, product) => sum + product.quantity, 0)
  }
})

// 监听搜索关键词变化，筛选商品
watch(searchKeyword, (newValue) => {
  if (!newValue) {
    // 如果搜索关键词为空，根据当前选中的分类显示商品
    const activeCategory = productCategories.value.find(category => category.active);
    if (activeCategory) {
      switchCategory(activeCategory.id);
    }
  } else {
    // 否则根据关键词在所有商品中筛选（商品名称或规格的模糊匹配）
    const keyword = newValue.toLowerCase()
    // 从所有商品中搜索，而不仅仅是当前分类
    availableProducts.value = allProducts.value.filter(product => {
      return product.name.toLowerCase().includes(keyword) || 
          (product.spec && product.spec.toLowerCase().includes(keyword))
    })
  }
})

// 路由实例
const router = useRouter()

// 判断是否有多个角色
const hasMultipleRoles = computed(() => {
  const userRoles = JSON.parse(localStorage.getItem('user_roles') || '[]');
  return userRoles.some((role: { name: string }) => role.name === 'review') 
    && userRoles.some((role: { name: string }) => role.name === 'check');
});

// 任务状态
const hasTask = ref<boolean>(false);

// 获取异常原因列表
const fetchReasonList = async () => {
  try {
    const response = await getReasonList()
    if (response && response.data) {
      // 更新异常原因列表
      exceptionTypes.value = response.data.map((item: any, index: number) => {
        const isLastItem = index === response.data.length - 1;
        return {
          id: item.id,
          name: item.name,
          active: false,
          type: isLastItem ? 0 : 1
        };
      });
    }
  } catch (error) {
    console.error('获取异常原因列表失败:', error)
    message.error('获取异常原因列表失败，请稍后重试')
  }
}

// 获取系统情况
const fetchSystemInfo = async () => {
  try {
    const response = await getSystemInfo()
    if (response && response.data) {
      // 更新任务池数量和在线人数
      orderInfo.taskPool = response.data.total || 0
      orderInfo.onlineUsers = response.data.online || 0
      orderInfo.completedToday = response.data.today_done || 0
      orderInfo.exceptionMode = response.data.today_exception || 0
    }
  } catch (error) {
    console.error('获取系统情况失败:', error)
  }
}

const algorithmsProducts = ref<Product[]>([])
const recommendedProducts = ref<Product[]>([])
const allProducts = ref<Product[]>([])
// 刷新任务
const refreshTask = async () => {
  try {
    // 调用获取任务的API
    const response = await getTask()
    if (response && response.data) {
      hasTask.value = true
      // 更新任务相关数据
      if (response.data) {
        // 更新订单信息
        orderInfo.orderId = response.data.display_order_id || '-'
        orderInfo.taskId = response.data.task_id || '-'
        orderInfo.remainSeconds = response.data.remain_sec || 0
        // 启动倒计时
        startCountdown();
      }
      
      // 如果有视频链接，设置视频源
      if (response.data.videos && response.data.videos.length > 0) {
        currentVideoUrl.value = response.data.videos[0]
        if (videoRef.value) {
          videoRef.value.src = currentVideoUrl.value || ''
          videoRef.value.load()
          videoRef.value.play()
        }
      }
      
      // 如果有关键帧数据，设置关键帧图片
      if (response.data.key_frames && response.data.key_frames.length > 0) {
        keyframeImages.value = response.data.key_frames
        // 默认显示视频，不显示关键帧
        showKeyframe.value = false
        currentKeyframeIndex.value = 0
      } else {
        // 如果没有关键帧数据，清空关键帧图片数组
        keyframeImages.value = []
      }
      
      // 如果有商品数据，更新待选商品列表
      if (response.data) {
        const products = response.data.algorithms.map((product: 
          { id: number; name: string; spec?: string; cover?: string; number?: number }) => ({
          id: product.id,
          name: product.name,
          image: product.cover || '/images/商品详情1.png',
          confidence: product.number || 0,
          quantity: ''
        }))
        // Bind sales data to the recommended products section
        const _sales = response.data.sales.map((item: 
        { id: number; name: string; cover: string; number: number }) => ({
          id: item.id,
          name: item.name,
          cover: item.cover,
          confidence: item.number,
          image: item.cover, // Using cover as image
          quantity: '' // Default quantity
        }))

        // Bind full data to the all products section
        const _full = response.data.full.map((item: 
        { id: number; name: string; cover: string; number: number }) => ({
          id: item.id,
          name: item.name,
          cover: item.cover,
          confidence: item.number,
          image: item.cover, // Using cover as image
          quantity: '' // Default quantity
        }))
        algorithmsProducts.value = [...products]
        recommendedProducts.value = [..._sales]
        allProducts.value = [..._full]
        // 保存原始商品列表用于搜索
        originalProducts.value = [...products]
        availableProducts.value = [...products]
      }
      
      // 清空已选商品
      selectedProducts.value = []
      // 清空异常标记
      exceptionTypes.value.forEach(type => {
        type.active = false
      })

      // 清空其他警告原因
      otherReason.value = ''
      // 清空搜索关键词
      searchKeyword.value = ''
      // message.success('任务加载成功')
      hasSubmitEnd.value = false
    } else {
      hasTask.value = false
      hasSubmitEnd.value = false
      message.info('暂无可用任务')
    }
  } catch (error) {
    console.error('获取任务失败:', error)
    message.error('获取任务失败，请稍后重试')
  }
}
// 定义倒计时计时器变量
let countdownTimer: any = null;

// 启动倒计时
const startCountdown = () => {
  // 清除已有的计时器
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
  
  // 设置新的计时器
  countdownTimer = setInterval(() => {
    if (orderInfo.remainSeconds > 0) {
      orderInfo.remainSeconds--;
    } else {
      // 时间到时清除计时器
      if (countdownTimer) {
        hasSubmitEnd.value = true
        clearInterval(countdownTimer);
        countdownTimer = null;
      }
    }
  }, 1000);
};
// 切换到抽检页面
const switchToInspection = () => {
  Modal.confirm({
    title: '提示',
    content: '是否确认切换到抽检页面？',
    okText: '确定',
    cancelText: '取消',
    onOk() {
      router.push('/inspection')
    }
  })
}

// 生命周期钩子
onMounted(() => {
  // 初始化视频播放器
  if (videoRef.value) {
    // 设置默认倍速为1.5
    videoRef.value.playbackRate = playbackRate.value
    
    // 添加视频加载事件监听器，确保每次视频加载完成后都设置为1.5倍速
    videoRef.value.addEventListener('loadeddata', () => {
      if (videoRef.value) {
        videoRef.value.playbackRate = playbackRate.value
      }
    })
    
    // 设置视频自动播放
    videoRef.value.autoplay = true
    switchAngle(1)
    // 使用VueUse处理键盘事件，不需要手动添加事件监听器
  }
  
  // 获取顶部区域高度
  if (headerRef.value) {
    headerHeight.value = headerRef.value.offsetHeight
  }
  
  // 加载任务数据
  refreshTask()
  
  // 获取系统情况
  fetchSystemInfo()
  // 获取异常原因列表
  fetchReasonList()
  
  // 定时刷新系统情况
  const systemInfoInterval = setInterval(fetchSystemInfo, 30000) // 每30秒刷新一次
  
  // 组件卸载时清除定时器
  onUnmounted(() => {
    clearInterval(systemInfoInterval)
    // 清除倒计时计时器
    if (countdownTimer) {
      clearInterval(countdownTimer);
      countdownTimer = null;
    }
  })
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})

// 滚动事件处理
const handleScroll = () => {
  if (headerRef.value) {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    const contentHeight = document.documentElement.scrollHeight
    const viewportHeight = window.innerHeight
    
    // 只有当页面内容高度大于视窗高度时才启用悬浮效果
    if (contentHeight > viewportHeight) {
      isHeaderFixed.value = scrollTop > headerRef.value.offsetTop
    } else {
      isHeaderFixed.value = false
    }
  }
}

// 使用VueUse实现快捷键功能
// 空格键：播放/暂停
onKeyStroke(' ', (e) => {
  if (videoRef.value) {
    e.preventDefault()
    if (videoRef.value.paused) {
      playVideo()
    } else {
      pauseVideo()
    }
  }
})

// 左键：倒退
onKeyStroke('ArrowLeft', (e) => {
  if (videoRef.value) {
    e.preventDefault()
    videoRef.value.currentTime -= 5
  }
})

// 右键：快进
onKeyStroke('ArrowRight', (e) => {
  if (videoRef.value) {
    e.preventDefault()
    videoRef.value.currentTime += 5
  }
})

// 使用useMagicKeys处理组合键
const { alt } = useMagicKeys()
// Alt+1：倍速切换
watch(alt, (pressed) => {
  if (pressed){
    onKeyStroke('1', (e) => {
      e.preventDefault()
      increasePlaybackRate()
    })
    onKeyStroke('2', (e) => {
      e.preventDefault()
      decreasePlaybackRate()
    })
    // Alt+F：搜索
    onKeyStroke('F', (e) => {
      if (e.target && 
        ((e.target as HTMLElement).tagName === "INPUT" || 
        (e.target as HTMLElement).tagName === "TEXTAREA")) {
        return; // 避免在输入框里按D时触发
      }
      e.preventDefault()      // 输入框获取焦点
      // 确保输入框存在并获取焦点
      if (searchInputRef.value) {
        // 使用 nextTick 确保 DOM 更新后再获取焦点
        nextTick(() => {
          // console.log('alt+ F')
          searchInputRef.value?.focus()
          // 如果是 Ant Design 组件，可能需要访问其内部 input 元素
          const inputElement = (searchInputRef.value as any)?.$el?.querySelector('input')
          if (inputElement) {
            inputElement.focus()
          }
        })
      }
    })
  }
})
useEventListener('keydown', (event) => {
  if (event.altKey && event.key.toLowerCase() === 'f') {
    event.preventDefault() // 阻止浏览器默认搜索行为
    searchInputRef.value?.focus()
  }
})
const keys = useMagicKeys()
// 切换商品分类
const switchCategory = (categoryId: number) => {
  productCategories.value.forEach(category => {
    category.active = category.id === categoryId
  })
  // 如果有搜索关键词，不改变当前显示的商品列表
  if (searchKeyword.value) {
    return;
  }
  // Filter products based on the selected category
  if (categoryId === 1) {
    availableProducts.value = algorithmsProducts.value
    originalProducts.value = algorithmsProducts.value
  } else if (categoryId === 2) {
    availableProducts.value = recommendedProducts.value
    originalProducts.value = recommendedProducts.value
  } else if (categoryId === 3) {
    availableProducts.value = allProducts.value
    originalProducts.value = allProducts.value
  } else {
    // 默认显示算法推荐商品
    availableProducts.value = algorithmsProducts.value
    originalProducts.value = algorithmsProducts.value
  }
}
// 监听 Ctrl + 1 ~ Ctrl + 6
watch(
  () => [
    keys['Ctrl+1'].value,
    keys['Ctrl+2'].value,
    keys['Ctrl+3'].value,
    keys['Ctrl+4'].value,
    keys['Ctrl+5'].value,
    keys['Ctrl+6'].value
  ],
  ([ctrl1, ctrl2, ctrl3, ctrl4, ctrl5, ctrl6]) => {
    if (ctrl1) toggleException(1)
    if (ctrl2) toggleException(2)
    if (ctrl3) toggleException(3)
    if (ctrl4) toggleException(4)
    if (ctrl5) toggleException(5)
    if (ctrl6) toggleException(6)
  }
)
watch(
  () => [
  keys['ALT+1'].value,
  keys['ALT+2'].value
],([alt1, alt2]) => {
  if (alt1) increasePlaybackRate()
  if (alt2) decreasePlaybackRate()
})
// 监听 Tab 键
onKeyStroke('Tab', (e) => {
  if (keys.ctrl.value) { // 只有按下 Ctrl 时才执行
    e.preventDefault() // 阻止浏览器切换标签页
    console.log('Ctrl + Tab detected')
    const nextAngle = (currentAngle.value % 4) + 1
    switchAngle(nextAngle)
  }
})
//监听 Alt+Tab 组合键
whenever(keys.alt_tab, () => {
  console.log('alt + Tab')
  // 获取当前 active 的索引
  let currentIndex = productCategories.value.findIndex(c => c.active)
  let nextIndex = (currentIndex + 1) % productCategories.value.length // 循环切换
  let nextCategory = productCategories.value[nextIndex]

  switchCategory(nextCategory.id)
})

// A键：提交并结束
onKeyStroke('a', (e) => {
  if (e.target && 
    ((e.target as HTMLElement).tagName === "INPUT" || 
    (e.target as HTMLElement).tagName === "TEXTAREA")) {
    return; // 避免在输入框里按A时触发
  }
  e.preventDefault()
  submitAndEnd()
}, { eventName: 'keydown' })

// D键：提交并下一条
onKeyStroke('d', (e) => {
  if (e.target && 
    ((e.target as HTMLElement).tagName === "INPUT" || 
    (e.target as HTMLElement).tagName === "TEXTAREA")) {
    return; // 避免在输入框里按D时触发
  }
  e.preventDefault()
  submitAndNext()
}, { eventName: 'keydown' })

// 监听 Alt + 滚轮事件
useEventListener('wheel', (event: WheelEvent) => {
  if (event.altKey) {
    event.preventDefault() // 阻止页面滚动
    const delta = event.deltaY > 0 ? -1 : 1 // 向下滚减少，向上滚增加

    // 这里假设当前选中的商品是 `selectedProducts` 的第一个
    if (selectedProducts.value.length > 0) {
      adjustQuantity(selectedProducts.value[0].id, delta)
    }
  }
})
// 视频控制方法
const playVideo = () => {
  if (videoRef.value) videoRef.value.play()
}

const pauseVideo = () => {
  if (videoRef.value) videoRef.value.pause()
}

// 倍速控制方法
const increasePlaybackRate = () => {
  if (videoRef.value && playbackRate.value < 2.0) {
    playbackRate.value = Math.min(2.0, playbackRate.value + 0.25)
    videoRef.value.playbackRate = playbackRate.value
  }
}

const decreasePlaybackRate = () => {
  if (videoRef.value && playbackRate.value > 0.5) {
    playbackRate.value = Math.max(0.5, playbackRate.value - 0.25)
    videoRef.value.playbackRate = playbackRate.value
  }
}

// 切换视角
const switchAngle = (angle: number) => {
  currentAngle.value = angle
  // 判断是否是关键帧视角
  if (angle === 3 || angle === 4) {
    // 设置当前关键帧索引（关键帧1对应索引0，关键帧2对应索引1）
    currentKeyframeIndex.value = angle - 3
    // 显示关键帧
    showKeyframe.value = true
    if (videoRef.value) {
      videoRef.value.pause()
    }
  } else {
    // 显示视频
    showKeyframe.value = false
    // 如果有多个视频源，可以在这里切换视频源
    if (videoRef.value && currentVideoUrl.value) {
      videoRef.value.src = currentVideoUrl.value
      videoRef.value.load()
      videoRef.value.play()
    }
  }
}

// 商品操作方法
const addProduct = (product: Product) => {
  const existingProduct = selectedProducts.value.find(p => p.id === product.id)
  
  if (existingProduct) {
    // 如果商品已在已选列表中，增加数量
    existingProduct.quantity++
    // message.success(`已添加商品: ${product.name}，数量 +1`)
  } else {
    // 否则添加到已选列表
    selectedProducts.value.push({
      ...product,
      quantity: 1
    })
    // message.success(`已添加商品: ${product.name}`)
  }
  
  // 同步更新availableProducts中对应商品的quantity
  const availableProduct = availableProducts.value.find(p => p.id === product.id)
  if (availableProduct) {
    // 如果在availableProducts中找到对应商品，更新其quantity
    if (existingProduct) {
      availableProduct.quantity = existingProduct.quantity
    } else {
      availableProduct.quantity = 1
    }
  }
}

const removeProduct = (productId: number) => {
  Modal.confirm({
    title: '确认移除',
    content: '确定要移除此商品吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      selectedProducts.value = selectedProducts.value.filter(p => p.id !== productId)
      
      // 重置availableProducts中对应商品的quantity
      const availableProduct = availableProducts.value.find(p => p.id === productId)
      if (availableProduct) {
        availableProduct.quantity = 0
      }
      
      message.success('已移除商品')
    }
  })
}

const adjustQuantity = (productId: number, delta: number) => {
  const product = selectedProducts.value.find(p => p.id === productId)
  if (product) {
    const newQuantity = product.quantity + delta
    if (newQuantity > 0) {
      product.quantity = newQuantity
      
      // 同步更新availableProducts中对应商品的quantity
      const availableProduct = availableProducts.value.find(p => p.id === productId)
      if (availableProduct) {
        availableProduct.quantity = newQuantity
      }
    } else {
      // 如果数量减到0，询问是否移除
      removeProduct(productId)
    }
  }
}

// 切换异常标记
const toggleException = (exceptionId: number) => {
  // 获取当前点击的异常标签
  const clickedException = exceptionTypes.value.find(e => e.id === exceptionId)
  
  // 如果当前点击的标签已经是激活状态，则取消选中
  if (clickedException && clickedException.active) {
    clickedException.active = false
  } else {
    // 否则，取消其他所有标签的选中状态，并选中当前标签
    exceptionTypes.value.forEach(exception => {
      exception.active = exception.id === exceptionId
    })
  }
}

const hasSubmitEnd = ref(false)
const submitCheck = (flag: boolean) => {
  Modal.confirm({
    title: '提示',
    content: flag ? '是否确认提交并结束？':'是否确认提交并下一条？',
    okText: '确定',
    cancelText: '取消',
    onOk() {
      flag ? submitAndEnd():submitAndNext()
    }
  })
}
// 提交方法
const submitAndEnd = () => {
  const  reasonId = exceptionTypes.value.filter(e => e.active).map(e => e.id)
  if(selectedProducts.value.length === 0&&reasonId.length === 0){
    message.error('请选择商品或者异常原因')
    return
  }
  // 检查是否选择了"其他警告"但未填写原因
  const otherWarning = exceptionTypes.value.find(e => e.id === 1 && e.active)
  // const reasonId = exceptionTypes.value.filter(e => e.active).map(e => e.id)
  // if(reasonId.length === 0){
  //   message.error('请选择异常原因')
  //   return
  // }
  if (otherWarning && (!otherReason.value || !otherReason.value.trim())) {
    message.error('请填写其他原因')
    return
  }
  const _skus = selectedProducts.value.map(product => ({
    id: product.id,
    number: product.quantity
  }));
  // 构建提交数据
  const submitData = {
    order_id: Number(orderInfo.orderId),
    task_id: Number(orderInfo.taskId),
    skus: _skus,
    reason_ids: reasonId,
    other_reason: otherReason.value || '',
    stop: true // 提交并结束
  }
  // 调用API提交任务
  submitTask(submitData).then((res: any) => {
    if (res && res.message === 'success') {
      // 提交订单
      message.success('订单已提交并结束')
      // 这里可以添加提交逻辑和跳转
      hasSubmitEnd.value = true
      // 重新加载系统情况
      fetchSystemInfo()
    } else {
      message.error(res?.message || '提交失败，请重试')
    }
  }).catch((error: any) => {
    console.error('提交任务失败:', error)
    message.error('提交失败，请重试')
  })
}

const submitAndNext = () => {
  const  reasonId = exceptionTypes.value.filter(e => e.active).map(e => e.id)
  if(selectedProducts.value.length === 0&&reasonId.length === 0){
    message.error('请选择商品或者异常原因')
    return
  }
  // 检查是否选择了"其他警告"但未填写原因
  const otherWarning = exceptionTypes.value.find(e => e.id === 1 && e.active)
  // if(reasonId.length === 0){
  //   message.error('请选择异常原因')
  //   return
  // }
  if (otherWarning && (!otherReason.value || !otherReason.value.trim())) {
    message.error('请填写其他原因')
    return
  }
  const _skus = selectedProducts.value.map(product => ({
    id: product.id,
    number: product.quantity
  }));
  // 构建提交数据
  const submitData = {
    order_id: Number(orderInfo.orderId),
    task_id: Number(orderInfo.taskId),
    skus: _skus,
    reason_ids: reasonId,
    other_reason: otherReason.value || '',
    stop: false // 提交并继续下一条
  }
  
  // 调用API提交任务
  submitTask(submitData).then((res: any) => {
    if (res && res.message === 'success') {
      message.success('订单已提交，正在加载下一条...')
      // 获取下一条任务
      refreshTask()
      otherReason.value = ''
      exceptionTypes.value.forEach(exception => {
        exception.active = false
      })
      // 刷新系统情况
      fetchSystemInfo()
    } else {
      message.error(res?.message || '提交失败，请重试')
    }
  }).catch((error: any) => {
    console.error('提交任务失败:', error)
    message.error('提交失败，请重试')
  })
}

// 打开商品详情
const openProductDetail = (event: MouseEvent, productId: number) => {
  const target = event.target as HTMLElement
  const rect = target.getBoundingClientRect()
  
  // 计算悬浮框位置，显示在鼠标位置右侧
  popoverPosition.value = {
    top: rect.top,
    left: rect.right + 10 // 显示在元素右侧，留出 10px 间距
  }
  
  currentProductId.value = productId
  showProductDetail.value = true
}

// 关闭商品详情
const closeProductDetail = () => {
  showProductDetail.value = false
}

// 显示商品图片放大
const showProductZoom = (event: MouseEvent, imageUrl: string) => {
  zoomImageUrl.value = imageUrl
  isProductZoomVisible.value = true
  // 计算弹出位置 - 设置在图片右下方
  if (event.target) {
    const target = event.target as HTMLElement
    const rect = target.getBoundingClientRect()
    zoomPosition.value = {
      top: rect.bottom + window.scrollY, // 使用bottom而不是top，定位在图片底部
      left: rect.left + (rect.width/2) + window.scrollX // 使用left+width/2使悬浮框水平居中于图片下方
    }
  }
}

// 隐藏商品图片放大
const hideProductZoom = () => {
  setTimeout(() => {
    isProductZoomVisible.value = false
  }, 5000) // 延迟100毫秒后隐藏
}
</script>

<style scoped>
.order-info {
  height: 60px;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
  background: rgb(255, 255, 255);
  width: 100%;
  z-index: 1000;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
}

.close-btn {
  color: #999;
  font-weight: bold;
}

.close-btn:hover {
  color: #666;
}

.btn-exception {
  background-color: #f0f0f0;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.btn-exception.active {
  background-color: #ff4d4f;
  color: white;
}

.btn-quantity {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-remove {
  color: #fff;
  background: #FA5151;
}

.btn-submit, .btn-submit-next {
  padding: 8px 24px;
  border-radius: 4px;
  font-size: 16px;
  background-color: #005290;
  color: white;
}

.btn-submit {
  background-color: #f0f0f0;
  color: #333;
}

.quantity {
  width: 24px;
  text-align: center;
}

.selected-product-item {
  width: 120px;
  height: auto;
  position: relative;
  transition: all 0.3s;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.25);
  background: rgb(255, 255, 255);
}

.product-item {
  width: 120px;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.25);
  background: rgb(255, 255, 255);  
}

.product-image {
  border-radius: 4px;
  margin: 10px 20px;
  transition: all 0.3s ease;
  cursor: zoom-in;
}

.product-image:hover {
  transform: scale(1.05);
  box-shadow: 0 0 5px rgba(0, 82, 144, 0.3);
}

.product-name {
  font-weight: 500;
  margin-top: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-spec {
  font-size: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: none;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  z-index: 2;
}

.selected-product-item:hover .product-overlay {
  display: flex;
}

.product-badge {
  position: absolute;
  top: 0;
  right: 0;
  width: 24px;
  height: 24px;
  background-color: #fa5151;
  color: white;
  border-top-right-radius: 0px;
  border-bottom-left-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 500;
  z-index: 1;
}

.confidence-badge {
  font-size: 10px;
  padding: 1px 4px;
  display: inline-block;
  position: relative;
}

.high-confidence {
  background-color: #005290;
  color: white;
}

.low-confidence {
  background-color: #fa5151;
  color: white;
}

.confidence-hover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #52c41a;
  color: white;
  display: none; /* 修改这里，默认不显示 */
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 500;
  cursor: pointer;
  z-index: 10;
}

.confidence-badge:hover .confidence-hover {
  display: flex; /* 修改这里，鼠标悬浮时显示 */
}

.btn-filter:hover:not(.active) {
  border-color: #005290;
  color: #005290;
}

.btn-submit, .btn-submit-next {
  padding: 10px 24px;
  border-radius: 4px;
  font-size: 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-submit {
  background-color: #f0f0f0;
  color: #333;
}

.btn-submit:hover {
  background-color: #e0e0e0;
}

.btn-submit-next {
  background-color: #005290;
  color: white;
}

.btn-submit-next:hover {
  background-color: #40a9ff;
}

.exception-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  border-radius: 4px;
  background: #fff;
  color: #FA5151;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  height: 32px;
  border: 1px solid #FA5151;
  color: rgb(250, 81, 81);
  line-height: 14px;
  letter-spacing: 0px;
  text-align: left;
}

.active1 {
  background-color: rgb(153, 153, 153) !important;
  color: #fff !important;
  border-radius: 4px;
  border: 1px solid #d5d5d5 !important;
}
.lastbtn {
  border: 1px solid rgb(213, 213, 213) !important;
  color: #999;
}
.exception-btn:hover {
  border-color: rgb(250, 81, 81);
}

.exception-btn.active {
  background-color: rgb(250, 81, 81);
  color: white;
  border: 1px solid rgb(250, 81, 81);
}

.submit-btn {
  display: flex;
  align-items: center;
  justify-content: between;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  min-width: 120px;
}

.submit-end {
  background-color: #fff;
  color: #005290;
  border: 1px solid #005290;
}

.submit-next {
  background-color: #005290;
  color: white;
  border: 1px solid #005290;
}

.bottom-action-area {
  border-radius: 4px;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
  background: rgb(255, 255, 255);
}

/* Add any additional styles for the video player */
.video-player-container {
  width: 100%;
  max-height: 800px;
  background-color: black;
}

.video-player {
  width: 100%;
  height: auto;
  min-height: 400px;
  max-height: 800px;
}
</style>