import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'login',
      component: () => import('../views/login/index.vue')
    },
    {
      path: '/home',
      name: 'home',
      component: () => import('../views/home/<USER>')
    },
    {
      path: '/inspection',
      name: 'inspection',
      component: () => import('../views/inspection/index.vue')
    },
    {
      path: '/review',
      name: 'review',
      component: () => import('../views/review/index.vue')
    }
  ]
})

export default router