/**
 * 快捷键提示框交互增强脚本
 * 解决鼠标悬停时提示框闪烁或立即消失的问题
 */

document.addEventListener('DOMContentLoaded', () => {
  // 延迟时间设置（毫秒）
  const HOVER_DELAY = 300;
  const LEAVE_DELAY = 500;
  
  let hoverTimeout = null;
  let leaveTimeout = null;
  
  // 查找所有快捷键按钮和提示框
  const initTooltips = () => {
    const shortcutElements = document.querySelectorAll('.shortcut-link, .shortcut-btn');
    
    shortcutElements.forEach(element => {
      const tooltip = element.querySelector('.shortcut-tooltip');
      if (!tooltip) return;
      
      // 鼠标进入按钮时
      element.addEventListener('mouseenter', () => {
        // 清除之前的离开延迟
        if (leaveTimeout) {
          clearTimeout(leaveTimeout);
          leaveTimeout = null;
        }
        
        // 设置显示延迟，避免鼠标快速经过时闪烁
        hoverTimeout = setTimeout(() => {
          tooltip.style.display = 'block';
        }, HOVER_DELAY);
      });
      
      // 鼠标离开按钮时
      element.addEventListener('mouseleave', (e) => {
        // 检查鼠标是否移动到了提示框上
        if (e.relatedTarget === tooltip || (tooltip.contains && tooltip.contains(e.relatedTarget))) {
          // 如果移到了提示框上，取消隐藏
          if (hoverTimeout) {
            clearTimeout(hoverTimeout);
            hoverTimeout = null;
          }
          return;
        }
        
        // 清除显示延迟
        if (hoverTimeout) {
          clearTimeout(hoverTimeout);
          hoverTimeout = null;
        }
        
        // 设置隐藏延迟
        leaveTimeout = setTimeout(() => {
          // 再次检查鼠标是否在提示框上
          if (tooltip.matches(':hover')) {
            return;
          }
          tooltip.style.display = 'none';
        }, LEAVE_DELAY);
      });
      
      // 鼠标进入提示框时
      tooltip.addEventListener('mouseenter', () => {
        // 清除隐藏延迟
        if (leaveTimeout) {
          clearTimeout(leaveTimeout);
          leaveTimeout = null;
        }
      });
      
      // 鼠标离开提示框时
      tooltip.addEventListener('mouseleave', (e) => {
        // 检查鼠标是否移回到了按钮上
        if (e.relatedTarget === element || (element.contains && element.contains(e.relatedTarget))) {
          return;
        }
        
        // 设置隐藏延迟
        leaveTimeout = setTimeout(() => {
          tooltip.style.display = 'none';
        }, LEAVE_DELAY);
      });
    });
  };
  
  // 初始化
  initTooltips();
  
  // 对于动态加载的内容，可以使用MutationObserver监听DOM变化并重新初始化
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.addedNodes.length) {
        initTooltips();
      }
    });
  });
  
  observer.observe(document.body, { childList: true, subtree: true });
});