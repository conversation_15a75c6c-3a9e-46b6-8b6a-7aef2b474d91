body {
    background-color: #fafafc;
}
.shadow-custom {
    box-shadow: 0 0px 2px rgba(0, 0, 0, 0.1);
}

.title-decorator {
    display: inline-block;
    width: 3px;
    height: 20px;
    background-color: #005290;
    margin-right: 8px;
    border-radius: 2px;
    vertical-align: middle;
}

.btn-filter {
    width: 83px;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 14px;
    color: #005290;
    border: 1px solid #005290;
}

.btn-filter.active {
    background-color: #005290;
    color: white;
}

/* 身份切换按钮样式 */
.identity-switch-btn {
    position: fixed;
    right: 0;
    top: 70%;
    transform: translateY(-50%);
    background-color: #005290;
    color: white;
    padding: 10px 6px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 999;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s;
}

.identity-switch-btn span {
    margin-top: 5px;
    font-size: 12px;
}