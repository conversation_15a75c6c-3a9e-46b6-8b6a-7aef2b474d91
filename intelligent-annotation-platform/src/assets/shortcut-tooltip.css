/* 快捷键提示框样式 */
.shortcut-link,
.shortcut-btn {
  position: relative;
  cursor: pointer;
}

.shortcut-tooltip {
  display: none;
  position: absolute;
  top: 100%;
  right: 0;
  width: 280px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  margin-top: 10px;
  border: 1px solid #eaeaea;
  animation: fadeIn 0.2s ease-in-out;
  /* 添加过渡效果 */
  transition: opacity 0.3s, visibility 0.3s;
}

/* 修改悬停逻辑，使提示框在鼠标移入按钮或提示框本身时都保持显示 */
.shortcut-link:hover .shortcut-tooltip,
.shortcut-btn:hover .shortcut-tooltip,
.shortcut-tooltip:hover {
  display: block;
}

.tooltip-title {
  font-size: 14px;
  font-weight: bold;
  padding: 10px 15px;
  color: #333;
  text-align: left;
  margin-left: -10px;
}

.tooltip-content {
  padding: 10px 0;
  max-height: 300px;
  overflow-y: auto;
  background: rgb(245, 245, 245);
  border-radius: 5px;
  margin: 5px;
}

.tooltip-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 8px;
  padding-right: 8px;
}

.tooltip-item:last-child {
  border-bottom: none;
}

.tooltip-item .key {
  background-color: #f0f2f5;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #8c8c8c;
  min-width: 80px;
  text-align: right;
  line-height: 16px;
  font-weight: 400;
  font-family: PingFang SC;
}

.tooltip-item .desc {
  color: #1f1f1f;
  text-align: left;
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  font-family: PingFang SC;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}