import { createApp } from 'vue'
import { createPinia } from 'pinia'
import Antd from 'ant-design-vue'
import { message } from 'ant-design-vue'
import router from './router'
import App from './App.vue'

// 引入样式
import 'ant-design-vue/dist/reset.css'
import 'uno.css'
import './assets/main.css'
import './assets/shortcut-tooltip.css'
import '@radix-ui/themes/styles.css';

// 引入快捷键提示框交互增强脚本
import './assets/shortcut-tooltip.js'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(Antd)

app.mount('#app')


// 监听未授权事件
window.addEventListener('auth:unauthorized', () => {
    // 使用Vue Router进行导航
    router.push('/');
    message.error('登录已过期，请重新登录');
});
