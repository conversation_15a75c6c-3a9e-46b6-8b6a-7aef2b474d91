// import Mock from 'mockjs';

// 设置响应延迟
// Mock.setup({
//   timeout: '200-600'
// });

// // 登录接口
// Mock.mock('/api/user/login', 'post', (options: {body: string}) => {
//   const { username, password } = JSON.parse(options.body);
  
//   // 模拟登录验证
//   if (username === 'admin' && password === '123456') {
//     return {
//       code: 200,
//       message: '登录成功',
//       data: {
//         token: 'mock-token-' + Date.now(),
//         userInfo: {
//           id: 1,
//           username: 'admin',
//           name: '管理员',
//           avatar: 'https://joeschmoe.io/api/v1/random',
//           roles: ['admin'],
//           type: 0
//         }
//       }
//     };
//   } else {
//     return {
//       code: 400,
//       message: '用户名或密码错误',
//       data: null
//     };
//   }
// });

export const dataSources = [
  {
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '0/0',
    checker: '',
    reason: '其他警告',
    status: 'pending'
  },
  {
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'unqualified'
  },
  {
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },
  {
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '',
    reason: '',
    status: 'pending'
  },
  {
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'unqualified'
  },
  {
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },
  {
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },
  {
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },
  {
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },
  {
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },
  {
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },
  {
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },
  {
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },
  {
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  },{
    orderId: '234123840182034',
    taskId: '234123840182034',
    created_at: '2024.12.30 12:23:43',
    inspector: '沈才',
    checked_at: '2024.12.30 12:23:43',
    checked_sec: '30s',
    sku_num: '3/24',
    checker: '丁一',
    reason: '',
    status: 'qualified'
  }
]

export default dataSources;