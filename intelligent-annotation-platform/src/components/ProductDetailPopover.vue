<template>
  <div class="product-detail-popover cursor-pointer" v-if="visible" @mouseenter="onMouseEnter" @mouseleave="onMouseLeave">
    <div class="popover-header">
      <!-- <span class="title-decorator"></span> -->
      <h3 class="font-500 text-[14px] mt-2">商品详情页面</h3>
      <!-- <button class="close-btn" @click="close">&times;</button> -->
    </div>
    <div class="popover-content p-5">
      <div v-if="loading" class="flex justify-center items-center h-40">
        <div class="loading-spinner"></div>
      </div>
      <div v-else-if="error" class="flex justify-center items-center h-40 text-red-500">
        {{ error }}
      </div>
      <div v-else-if="productDetail" class="product-detail-info">
        <div class="product-images-grid">
          <div 
            v-for="(item,index) in productDetail"
            :key="index"
            class="product-detail-image-container"
          >
            <img :src="item" class="product-detail-image p-2" />
          </div>
        </div>
      </div>
      <div v-else class="product-images-grid">
        <div 
          v-for="index in 6"
          :key="index"
          class="product-detail-image-container"
        >
          <img src="/images/商品详情1.png" class="product-detail-image p-2" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed, ref, watch, onMounted } from 'vue'
import { getSkuDetail } from '../api/unmanned'

const props = defineProps<{
  visible: boolean
  productId?: number
  position?: { top: number; left: number }
}>()

const emit = defineEmits(['close'])

// 商品详情数据
const productDetail = ref([])
const loading = ref<boolean>(false)
const error = ref<string>('')

// 计算悬浮框的位置，检测右侧空间是否足够
const popoverPosition = computed(() => {
  if (!props.position) return {}
  
  // 获取窗口宽度
  const windowWidth = window.innerWidth
  // 悬浮框宽度
  const popoverWidth = 418
  // 右侧边距
  const rightMargin = 20
  
  // 检查右侧空间是否足够
  const rightEdge = props.position.left + popoverWidth + rightMargin
  const isRightOverflow = rightEdge > windowWidth
  
  // 如果右侧空间不足，则显示在左侧
  if (isRightOverflow) {
    return {
      top: props.position.top + 24 + 'px',
      left: (props.position.left - 20) + 'px',
      transform: 'translateX(-100%)' // 向左偏移自身宽度
    }
  } else {
    // 右侧空间足够，显示在右侧
    return {
      top: props.position.top + 'px',
      left: props.position.left + 'px',
      transform: 'translateX(0)' // 不需要水平偏移
    }
  }
})

// 获取商品详情
const fetchProductDetail = async () => {
  if (!props.productId) return
  
  loading.value = true
  error.value = ''
  productDetail.value = []
  
  try {
    const res = await getSkuDetail(props.productId)
    // console.log('商品详情', res);
    
    if (!res.code) {
      productDetail.value = res?.data
    } else {
      error.value = '获取商品详情失败'
    }
  } catch (err) {
    console.error('获取商品详情失败:', err)
    error.value = '获取商品详情失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 监听visible变化，当显示时获取商品详情
watch(() => props.visible, (newVal) => {
  if (newVal && props.productId) {
    fetchProductDetail()
  }
})

// 组件挂载时，如果有productId且visible为true，则获取商品详情
onMounted(() => {
  if (props.visible && props.productId) {
    fetchProductDetail()
  }
})

const close = () => {
  emit('close')
}

// 鼠标进入悬浮框时，取消关闭计时器
const onMouseEnter = () => {
  // 保持悬浮框显示，不做任何操作
}

// 鼠标离开悬浮框时，关闭悬浮框
const onMouseLeave = () => {
  // 添加小延迟，避免鼠标从按钮移到悬浮框时闪烁
  setTimeout(() => {
    close()
  }, 500)
}
</script>

<style scoped>
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.product-detail-popover {
  position: absolute;
  width: 418px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  border: 1px solid #eaeaea;
  animation: fadeIn 0.2s ease-in-out;
  transition: opacity 0.3s, visibility 0.3s;
  top: v-bind('popoverPosition.top');
  left: v-bind('popoverPosition.left');
  transform: v-bind('popoverPosition.transform');
  pointer-events: auto;
}

.popover-header {
  display: flex;
  height: 48px;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
}

.title-decorator {
  display: inline-block;
  width: 3px;
  height: 16px;
  background-color: #005290;
  margin-right: 8px;
  border-radius: 2px;
}

.close-btn {
  margin-left: auto;
  font-size: 24px;
  color: #999;
  background: none;
  border: none;
  cursor: pointer;
}

.close-btn:hover {
  color: #666;
}

.popover-content {
  max-height: 500px;
  overflow-y: auto;
}

.product-images-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.product-detail-image-container {
  width: 110px;
  height: 118px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s;
}

.product-detail-image-container:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.product-detail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #005290;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.product-detail-info {
  width: 100%;
}
</style>