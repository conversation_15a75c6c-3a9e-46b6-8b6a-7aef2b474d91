<template>
    <div v-if="visible" class="product-zoom-popover" :style="positionStyle" @mouseleave="onPopoverLeave">
        <div class="zoom-content" @wheel.prevent="handleWheel">
            <img 
                :src="imageUrl" 
                class="zoomed-image" 
                :style="imageStyle" 
                alt="放大图片" 
                @mousedown="startDrag"
                @mousemove="onDrag"
                @mouseup="stopDrag"
                @mouseleave="stopDrag"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed, ref, watch } from 'vue'

const props = defineProps<{
    visible: boolean
    imageUrl: string
    position: { top: number; left: number }
}>()

const emit = defineEmits(['close'])

// 监听visible变化，重置状态
watch(() => props.visible, (newValue) => {
    if (newValue) {
        // 当弹窗显示时，重置缩放和位置
        zoomScale.value = 1
        imagePosition.value = { x: 0, y: 0 }
    }
})

// 缩放比例
const zoomScale = ref(1)
const MIN_ZOOM = 0.5
const MAX_ZOOM = 3

// 图片拖拽相关状态
const isDragging = ref(false)
const dragStartX = ref(0)
const dragStartY = ref(0)
const imagePosition = ref({ x: 0, y: 0 })

// 处理鼠标滚轮事件
const handleWheel = (event: WheelEvent) => {
    // 向下滚动 (deltaY > 0) 缩小图片
    // 向上滚动 (deltaY < 0) 放大图片
    const delta = event.deltaY > 0 ? -0.1 : 0.1
    
    // 计算新的缩放比例
    const newScale = Math.max(MIN_ZOOM, Math.min(MAX_ZOOM, zoomScale.value + delta))
    zoomScale.value = newScale
}

// 开始拖拽
const startDrag = (event: MouseEvent) => {
    isDragging.value = true
    dragStartX.value = event.clientX
    dragStartY.value = event.clientY
}

// 拖拽中
const onDrag = (event: MouseEvent) => {
    if (!isDragging.value) return
    
    const deltaX = event.clientX - dragStartX.value
    const deltaY = event.clientY - dragStartY.value
    
    imagePosition.value.x += deltaX
    imagePosition.value.y += deltaY
    
    dragStartX.value = event.clientX
    dragStartY.value = event.clientY
}

// 停止拖拽
const stopDrag = () => {
    isDragging.value = false
}

// 鼠标离开悬浮框时关闭
const onPopoverLeave = () => {
    emit('close')
}

// 计算图片样式
const imageStyle = computed(() => {
    return {
        transform: `translate(${imagePosition.value.x}px, ${imagePosition.value.y}px) scale(${zoomScale.value})`,
        transition: isDragging.value ? 'none' : 'transform 0.1s ease',
        cursor: isDragging.value ? 'grabbing' : 'grab'
    }
})

// 计算弹出框位置样式
const positionStyle = computed(() => {
    if (!props.position) return {}

    // 获取窗口尺寸
    const windowWidth = window.innerWidth
    const windowHeight = window.innerHeight
    
    // 弹出框尺寸
    const popoverWidth = 400
    const popoverHeight = 400
    
    // 计算右下角位置
    let top = props.position.top
    let left = props.position.left + 20 // 向右偏移
    
    // 检查右侧边界
    if (left + popoverWidth > windowWidth) {
        // 如果右侧空间不足，则显示在左侧
        left = props.position.left - popoverWidth - 20
    }
    
    // 检查底部边界
    if (top + popoverHeight > windowHeight) {
        // 如果底部空间不足，则向上调整
        top = windowHeight - popoverHeight - 10
    }
    
    return {
        top: `${top}px`,
        left: `${left}px`,
        transform: 'none' // 移除原有的transform
    }
})
</script>

<style scoped>
.product-zoom-popover {
    position: fixed;
    z-index: 1000;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    opacity: 0;
    animation: fadeIn 0.2s ease forwards;
    border: 1px solid rgba(0, 82, 144, 0.2);
    transition: all 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.zoom-content {
    /* padding: 12px; */
    width: 400px;
    height: 400px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}



.zoomed-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 4px;
    transform-origin: center;
    will-change: transform;
    user-select: none;
    -webkit-user-drag: none;
}
</style>