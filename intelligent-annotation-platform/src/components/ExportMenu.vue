<template>
  <div class="export-menu-container relative">
    <a-button class="export-btn" @click="handleExport('time')">
      <download-outlined />导出
    </a-button>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue'
import { DownloadOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 定义props，接收父组件传入的导出函数
const props = defineProps({
  onExport: {
    type: Function,
    default: null
  }
})

const handleExport = (type: string) => {
  // 如果父组件传入了导出函数，则调用它并传递排序类型
  if (props.onExport) {
    // 设置排序字段和排序顺序
    // const sortConfig = {
    //   time: { field: 'created_at', order: true },
    //   checker: { field: 'checker', order: true },
    //   reviewer: { field: 'reviewer', order: true },
    //   status: { field: 'status', order: true }
    // }
    
    // const config = sortConfig[type] || sortConfig.time
    props.onExport("created_at", true)
  } else {
    console.log(`正在按${type}导出数据...`)
    message.info('导出功能未配置')
  }
}
</script>

<style scoped>
.export-menu-container {
  position: relative;
  display: inline-block;
  color: #fff;
}

.export-btn {
  position: relative;
  background-color: #005290;
  color: #fff;
}
</style>