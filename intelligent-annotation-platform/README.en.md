# Intelligent Annotation Platform

## Project Background

As a new retail terminal, unmanned cabinets need to support real-time product review, cabinet status monitoring, and order sampling functions. This project aims to improve operational efficiency by implementing standardized review processes, data visualization, and automated operations through a desktop work tool platform.

- **Target Users**: Client-side operation review management personnel

## Key Requirements

- Cross-platform (Windows/Linux) desktop support with Electron
- High-performance video processing (key frame extraction, fast-forward, screenshots)
- Complex review process visualization and permission hierarchy
- High real-time performance (task assignment, status synchronization)

## Technical Architecture

- **Frontend**: Vue 3 + TypeScript + Vite
- **Backend**: Electron + Node.js

## Core Functional Modules

### 1. User Permission Management

- Regular Users: Enter task workflow after login
- Admin Users: Enter inspection workflow after login
- Identity Switching: Users with both inspection and review permissions can switch identities

### 2. Product Review Process

- Available Products Module: Supports category filtering, keyword search, algorithm recommendations
- Selected Products Module: Supports product addition, quantity adjustment, exception marking
- Submission Process: Supports "Submit and End" and "Submit and Next" modes

### 3. Order Inspection Features

- List Display: Sorted by creation time in descending order, supports multiple sorting methods
- Detail View: Supports video shortcuts, order information display
- Data Statistics: Real-time statistics of daily inspections, qualified and unqualified quantities

### 4. Core Technical Implementation

#### 4.1 Inter-Process Communication (IPC)

Seamless integration of hardware devices (such as barcode scanners) with software

#### 4.2 Video Processing Performance Optimization

- Video Decoding: FFmpeg integration via WebAssembly, frontend calls WebWorker for asynchronous decoding
- Key Frame Extraction: Dynamic rendering using canvas + requestVideoFrameCallback

#### 4.3 Data Storage and Synchronization

- Local Data: Electron localStorage JSON file storage (disaster recovery backup)
- Cloud Synchronization: WebSocket long connection + data version number conflict resolution strategy

## Development and Building

### Development Environment Setup

```bash
# Install dependencies
npm install

# Local development
npm run dev
```

### Production Environment Packaging

```bash
# Windows platform packaging
npm run build:win

# Linux platform packaging
npm run build:linux
```

> Installation packages are generated in the /release directory

## Code Standards

- Naming Rules: Interface prefix (e.g., OrderItem), Store uses useXxxStore
- Git Commits: Follow Git Flow (feat/fix/docs, etc.)
- Documentation Requirements: Add JSDoc comments at module entry points, maintain TypeScript type definitions for key interfaces

## Future Plans

- Add page shortcuts
- Video playback speed optimization
- Task training module
- Peak-valley allocation rules
- Hot sales recommendation feature
- Review inspection process optimization
