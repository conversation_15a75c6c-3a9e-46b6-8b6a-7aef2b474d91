import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import UnoCSS from 'unocss/vite'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd());
  
  return {
    plugins: [
      vue(),
      UnoCSS()
    ],
    base: './',
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src')
      }
    },
    server: {
      host: true,
      port: 5173,
      // proxy: {
      //   [env.VITE_APP_API_PREFIX]: {
      //     target: env.VITE_APP_API_BASE_URL,
      //     changeOrigin: true,
      //     rewrite: (path) => path.replace(new RegExp(`^${env.VITE_APP_API_PREFIX}`), '')
      //   }
      // }
    },
    build: {
      minify: 'terser',
      chunkSizeWarningLimit: 1500,
      terserOptions: {
        compress: {
          // 生产环境下移除console
          drop_console: env.VITE_APP_ENV === 'production',
          drop_debugger: env.VITE_APP_ENV === 'production'
        }
      },
      outDir: 'dist',
      assetsDir: 'assets',
      rollupOptions: {
        output: {
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
          manualChunks: {
            'vendor': ['vue', 'vue-router', 'pinia']
          }
        }
      }
    }
  }
})