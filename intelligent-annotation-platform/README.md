# 智能标注平台 (Intelligent Annotation Platform)

## 项目背景

无人货柜作为新零售终端，需支持实时商品审核、货柜状态监控、订单抽检等功能。本项目旨在通过桌面端作业工具平台实现审核流程标准化、数据可视化与操作自动化，从而有效提高运营效率。

- **目标群体**：客户方运营审核管理人员

## 需求关键点

- 跨平台（Windows/Linux）桌面端支持 Electron
- 高性能视频处理（抽检视频的关键帧，快进、截图）
- 复杂审核流程可视化与权限分级
- 高实时性（任务分配、状态同步）

## 技术架构设计

- **前端**：Vue 3 + TypeScript + Vite
- **后端**：Electron + Node.js

## 核心功能模块

### 1. 用户权限管理

- 普通用户：登录后进入任务流程
- 管理用户：登录后进入抽检流程
- 身份切换：同时拥有抽查和审核权限的用户可切换身份

### 2. 商品审核流程

- 待选商品模块：支持分类筛选、关键词搜索、算法推荐
- 已选商品模块：支持商品添加、数量调整、异常标记
- 提交流程：支持「提交并结束」和「提交并下一条」两种模式

### 3. 订单抽检功能

- 列表展示：按创建时间倒序排列，支持多种排序方式
- 详情查看：支持视频快捷操作、订单信息展示
- 数据统计：实时统计当日抽检、合格、不合格数量

### 4. 核心技术实现

#### 4.1 跨进程通信（IPC）

支持硬件设备（如扫码枪）与软件的无缝集成

#### 4.2 视频处理性能优化

- 视频解码：通过 WebAssembly 集成 FFmpeg，前端调用 WebWorker 异步解码
- 关键帧截取：使用 canvas + requestVideoFrameCallback 动态渲染

#### 4.3 数据存储与同步

- 本地数据：Electron localStorage JSON 文件存储（容灾备份）
- 云同步：WebSocket 长连接 + 数据版本号冲突解决策略

## 开发与构建

### 开发环境配置

```bash
# 安装依赖
npm install

# 本地开发
npm run dev
```

### 生产环境打包

```bash
# Windows 平台打包
npm run build:win

# Linux 平台打包
npm run build:linux
```

> 生成安装包至 /release 目录

## 代码规范

- 命名规则：接口前缀（例：OrderItem），Store 使用 useXxxStore
- Git 提交：遵循 Git Flow（feat/fix/docs等）
- 文档要求：模块入口添加 JSDoc 注释，关键接口维护 TypeScript 类型定义

## 未来规划

- 页面添加快捷键
- 视频倍速优化
- 任务培训模块
- 峰谷分配规则
- 热销推荐功能
- 审核抽查流程优化
