{"name": "intelligent-annotation-platform", "private": true, "version": "0.0.0", "type": "module", "main": "electron/main.js", "scripts": {"dev": "vite --mode development", "dev:sit": "vite --mode sit", "dev:prd": "vite --mode production", "build": "vue-tsc -b && vite build", "build:dev": "vue-tsc -b && vite build --mode development", "build:sit": "vue-tsc -b && vite build --mode sit", "build:prd": "vue-tsc -b && vite build --mode production", "preview": "vite preview", "electron:dev": "cross-env NODE_ENV=development vite --mode development & wait-on tcp:5173 && electron .", "electron:build": "vue-tsc -b && vite build && electron-builder", "electron:preview": "vite preview & electron ."}, "dependencies": {"@radix-ui/themes": "^3.2.1", "@vueuse/core": "^13.0.0", "axios": "^1.8.2", "dayjs": "^1.11.13", "qs": "^6.14.0", "vue": "^3.5.13", "vue-video-player": "^6.0.0"}, "devDependencies": {"@ant-design/icons-vue": "^7.0.1", "@types/mockjs": "^1.0.10", "@types/node": "^22.13.10", "@unocss/reset": "^66.1.0-beta.3", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "ant-design-vue": "^4.2.6", "core-js": "^3.41.0", "cross-env": "^7.0.3", "electron": "^28.1.0", "electron-builder": "^24.9.1", "pinia": "^3.0.1", "terser": "^5.39.0", "typescript": "~5.7.2", "unocss": "^66.1.0-beta.3", "vite": "^6.2.0", "vite-plugin-electron": "^0.29.0", "vite-plugin-electron-renderer": "^0.14.6", "vue-router": "^4.5.0", "vue-tsc": "^2.2.4", "wait-on": "^7.2.0"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "build": {"appId": "com.intelligent.annotation", "productName": "智能标注平台", "directories": {"output": "dist_electron"}, "files": ["dist/**/*", "electron/**/*"], "mac": {"target": ["dmg"]}, "win": {"target": ["nsis"]}, "linux": {"target": ["AppImage"]}}, "pnpm": {"ignoredBuiltDependencies": ["core-js", "electron", "esbuild"]}}