1.1 背景
> 背景：无人货柜作为新零售终端，需支持实时商品审核、货柜状态监控、订单抽检等功能。
> 项目目标是通过桌面端作业工具平台实现审核流程标准化、数据可视化与操作自动化，从而有效提高运营效率。
- 目标群体：客户方运营审核管理人员

1.2 需求关键点

- 跨平台（Windows/Linux）桌面端支持 Electron
- 高性能视频处理（抽检视频的关键帧，快进、截图）
- 复杂审核流程可视化与权限分级
- 高实时性（任务分配、状态同步）

二、技术架构设计

- 前端：Vue + TypeScript + Vite
- 后端：Electron + Node.js

全局说明
1.账号体系使用标注工具的，区分普通用户、管理用户
1.1.普通用户:登录后进入任务流程
1.2.管理用户:登录后进入抽检流程
2.默认视频倍速1.5，默认待选商品分类-算法推荐
3.关闭页面，按超时处理任务，用户状态变更为中止
4.当接收与数据库重复的订单(异常单据重新进入审核)，
将订单放置队列的顶部5.悬浮的【身份切换】按钮显隐条件-同时拥有抽查和审核权限的用户，显示【身份切换】按钮

待选商品模块说明
1.顶部分类 和 检索说明
2.顶部分类: 默认加载【算法推荐】，点击切换或使用快捷键切换3检索说明:支持加空格筛选，空格条件为与
4.案例说明: 【合味道+ 空格+300ML】 满足合味道 与 满足300ml条件的货品5.算法排序说明:按置信度排序
6.铺货推荐说明:按商品热销度排序
7.【查看快捷键】悬浮显示 【快捷键说明】

已选商品模块说明
前置条件
1.商品未在已选商品内-点击【待选商品】商品标签红色框范围添加商品到已选商
品内，已选商品【种】+1，【件】+1
2.商品已在已选商品内-点击后已选商品数量+1，【件】+1

已选商品异常标签交互说明
1.异常标签为单选按钮
2.点击选中，再次点击取消选中，点击其他
异常按钮之前选中的取消选中
3.点击【其他警告】按钮，须填写其他原因才可提交

提交按钮说明

前置条件-任务未超时

【提交并结束】
选中异常标签: 选中【其他警告】标签需在提交时须判断是否填写其他原因，其他异常不判断，提交后当前任务的状态为【已完成】，
任务类型更改为【异常订单】，顶部异常任务+1， 显示【订单审核-中止接单】，审核员状态变更为中止，在线人数-1
未选中异常标签: 提交后当前任务的状态为【已完成】，任务类型不变，顶部今日完成+1，显示【订单审核-中止接单】，1审核员状态变更为中止，在线人数

【提交并下一条】-原理同上

不同点:提交后系统分配下一个任务，无任务时显示【缺省页】

已选商品标签交互说明
1.悬浮显示【加减商品数量】 和 【取消已选按钮】2.点击加减按钮同时商品标签右上角数量同步加减，【件】同步加减3.点击取消已选，
显示二次确认框，确认后将商品从已选商品中移除，【种】和【件】同步移除

抽查模块说明
1.如果 用户同时包含审核和抽检两种权限，页面数据要排除自己的审核的订单任务2.列表数据默认按创建时间倒序
3.导出数据按4种排序方式
4.订单详情抽屉【视频快捷方式】同【订单审核页】
4.1.关闭订单详情快捷键【ESC】
4.2.加入列表上下切换快捷键
5.点击订单ID【订单详情】从右侧移入
6.确认订单抽检状态后，同步增加【当日抽检】【合格】【不合格】的数量7.页面底部添加身份切换按钮，点击切换到不同页面
-
三、核心模块实现逻辑

3.1 跨进程通信（IPC）设计
// 主进程：硬件接口调用示例（扫码枪）  
ipcMain.handle('scanner:init', () => {  
    const scanner = require('scanner-driver');  
    scanner.on('data', (data) => {    
        mainWindow.webContents.send('scanner:data', data);
  });  
});  

// 渲染进程：监听扫码数据  
ipcRenderer.on('scanner:data', (_, data) => {  
    useOrderStore().scanProduct(data.code);  
});  

3.2 视频处理性能优化

- 视频解码：通过 WebAssembly 集成 FFmpeg
前端调用 WebWorker 异步解码
- 关键帧截取：使用 <canvas> + requestVideoFrameCallback 动态渲染
const video = document.getElementById('video');  
video.requestVideoFrameCallback((now, metadata) => {  
    canvasContext.drawImage(video, 0, 0);  
    const imageData = canvas.toDataURL('image/jpeg', 0.8);  
}); 

3.3 数据存储与同步
- 本地数据：Electron localStorage JSON 文件存储（容灾备份）
- 云同步：WebSocket 长连接 + 数据版本号冲突解决策略

---
四、安全性设计

五、开发与构建规范

5.1 代码规范
- 命名规则：接口前缀 （例：OrderItem），Store 使用 useXxxStore
- Git 提交：遵循 Git Flow（feat/fix/docs等）
- 文档要求：模块入口添加 JSDoc 注释，关键接口维护 TypeScript 类型定义

5.2 构建部署流程
本地开发
$ npm run dev  

生产打包（Windows/Linux）
$ npm run build:win  
$ npm run build:linux  

> 生成安装包至 /release 目录
> 依赖镜像加速配置：.npmrc 内指定国内镜像源（阿里云）

1.页面添加快捷键
2.视频倍速
3.实效性高
4.后续加入任务培训
5.峰谷分配规则 (后续考虑)
6.审核抽查-完成的任务 任务分类7.申述单据(单据类型不同)放入前置队列
8.加入热销推荐
9.抽检人员也需要做审核任务，在审核任务内加入一个抽检入口


### unmanned api 
Get 请求
0、 /unmanned/system 获取系统情况，任务池数量和在线人数
1、 /unmanned/reason  获取审核原因 如故意遮挡、异物拿放
2、 /unmanned/task 审核页面初始化加载时获取单个无人审核任务
3、 /unmanned/sku 获取 sku 详情页面，返回 sku 图片链接
4、 /unmanned/order 抽检详情。获取已完成审核订单的详情 入参 task_id
5、 /unmanned/review/stat 获取当日抽检情况 提交抽检的审核订单结果
6、 /unmanned/order/list 抽检页订单抽检列表数据
、、、
"order_id": 0,
"task_id": 0,
"created_at": "string",
"checker": "string",
"checked_at": "string",
"sku_num": 0,
"selected_num": 0,
"reviewer": "string",
"reason": "string",
"status": "string"
、、、

POST 请求
1、 /unmanned/task 提交审核任务。提交审核任务后下一条/结束

入参： 
    order_id, 
    task_id, 
    skus, array[]
    reason_ids,  array[]
    other_reason, string
    stop, booblean

2、/unmanned/review 提交抽检

### 前端部署
1. npm run build
前端项目部署 采用 Nginx 反向代理

2. npm run electron:build
