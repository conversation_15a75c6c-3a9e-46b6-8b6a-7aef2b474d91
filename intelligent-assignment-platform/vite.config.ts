import { defineConfig,loadEnv } from 'vite'
import UnoCSS from 'unocss/vite'
import vue from '@vitejs/plugin-vue'
import electron from 'vite-plugin-electron'
import { resolve } from 'path'
import viteCompression from 'vite-plugin-compression'
import viteImagemin from 'vite-plugin-imagemin'
import { visualizer } from 'rollup-plugin-visualizer';
import fs from 'fs'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  //配置中使用的环境变量
  const env = loadEnv(mode, process.cwd())
  console.log("env",env)
  return {
    plugins: [
        vue(),
        UnoCSS(),
        electron({
            // 路径地址为后续创建的主进程入口文件
            entry: './electron/main.js'
        }),
        viteCompression({
          verbose: true, // 控制台输出压缩结果
          disable: false, // 是否禁用
          threshold: 10240, // 只压缩大于10kb的文件
          algorithm: 'gzip', // 压缩算法
          ext: '.gz', // 生成的压缩包后缀 可配置 brotli: algorithm: 'brotliCompress', ext: '.br'
        }),
        viteImagemin({
          gifsicle: {
            optimizationLevel: 7,
            interlaced: false
          },
          optipng: {
            optimizationLevel: 7
          },
          mozjpeg: {
            quality: 20
          },
          pngquant: {
            quality: [0.8, 0.9],
            speed: 4
          },
          svgo: {
            plugins: [
              {
                name: 'removeViewBox'
              },
              {
                name: 'removeEmptyAttrs',
                active: false
              }
            ]
          }
        }),
        visualizer({
          gzipSize: true, //显示gzip后的大小
        })
    ],
    base: './',
    build: {
        //触发警告的 单个chunk 大小
        chunkSizeWarningLimit: 500,
        //配置后才能让编译后的vue路径被正确识别
        manifest: true,
        // 禁用 gzip 压缩大小报告
        reportCompressedSize: false,
        rollupOptions:{ //分块策略
          output:{
            manualChunks:{
              xlsx: ['xlsx'],
              antv: ['@antv/g2'],
              antd: ['ant-design-vue'],
              'vue-libs': ['vue', '@vueuse/core', 'vue-router']
            }
          },
        },
    },
    resolve: {
        alias: {
        '@': resolve(__dirname, './src')
        }
    },
    optimizeDeps: {
        // 告诉 Vite 排除预构建 electron，不然会出现 __diranme is not defined
        exclude: ['electron'],
    },
    // 关闭默认的终端清屏，避免错过在终端中打印的关键信息
    clearScreen: false,
    server: {
      https: {
          key: fs.readFileSync('./certs/localhost-key.pem'),
          cert: fs.readFileSync('./certs/localhost.pem'),
      },
      proxy: {
          '^/(sso|auth|validation)': {
            //获取数据的服务器地址设置
            target: env.VITE_APP_API,
            //需要代理跨域
            changeOrigin: true,
            secure: false,
          }
      },
      // 预热常用文件，提高初始页面加载速度，防止转换瀑布
      warmup: {
        clientFiles: ["./src/components/**/*.vue"]
      },
      // 禁用开发服务器错误的屏蔽
      hmr: {
        overlay: false
      },
    },
  }
})
