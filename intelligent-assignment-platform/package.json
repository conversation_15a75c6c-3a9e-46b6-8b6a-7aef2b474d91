{"name": "intelligent-assignment-platform", "version": "1.0.0", "description": "智能作业平台", "main": "./electron/main.js", "homepage": "git@************:nbcgai/intelligent-assignment-platform.git", "sideEffects": false, "dependencies": {"@ant-design/icons-vue": "7.0.1", "@antv/g2": "5.3.3", "@radix-ui/themes": "3.2.1", "axios": "1.8.4", "dayjs": "1.11.13", "electron-updater": "6.6.2", "fabric": "6.6.5", "vite-plugin-electron": "0.29.0", "vue": "3.5.13", "vue-i18n": "9.14.4", "xlsx": "0.18.5"}, "devDependencies": {"@commitlint/config-conventional": "19.8.0", "@vitejs/plugin-vue": "5.2.3", "@vueuse/core": "13.0.0", "ant-design-vue": "4.2.6", "commitlint": "19.8.0", "core-js": "3.41.0", "electron": "35.2.1", "electron-builder": "25.1.8", "electron-devtools-installer": "4.0.0", "eslint-formatter-gitlab": "3.0.0", "husky": "9.1.7", "mockjs": "1.1.0", "nodemon": "3.1.9", "pinia": "3.0.1", "rollup-plugin-visualizer": "6.0.3", "sass-embedded": "1.89.0", "stylelint-formatter-gitlab": "1.0.2", "typescript": "5.8.2", "unocss": "66.1.0-beta.8", "vite": "6.2.4", "vite-plugin-compression": "0.5.1", "vite-plugin-imagemin": "0.6.1", "vue-router": "4.5.0", "wait-on": "8.0.3"}, "scripts": {"dev": "vite --host", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "build:test": "vite build --mode test", "electron:build": "electron-builder -mwl", "electron:win-daily": "rimraf release && electron-builder --win --x64 --config.productName=intelligent-daily", "electron:win": "rimraf release && electron-builder --win --x64 --config.productName=intelligent", "electron:mac-daily": "rm -rf release && electron-builder --mac --x64 --config.productName=intelligent-daily", "electron:mac": "rm -rf release && electron-builder --mac --x64 --config.productName=intelligent", "electron:linux": "rm -rf release && electron-builder --linux --x64 --config.productName=intelligent", "preview": "vite preview", "electron:preview": "vite preview && electron-builder", "start": "nodemon --exec electron . --watch ./ --ext .js,.html,.css,.vue", "lint": "eslint --ext .ts . && stylelint --max-warnings 0 \"**/*.css\"", "lint:fix": "prettier --write . && eslint --ext .ts --fix . && stylelint --max-warnings 0 --fix \"**/*.css\""}, "keywords": ["electron", "vite", "vue"], "license": "ISC", "repository": {"type": "git", "url": "git@************:nbcgai/intelligent-assignment-platform.git"}, "build": {"productName": "intelligent", "appId": "com.iap.intelligent", "copyright": "iap.intelligent-assignment-platform © 2025", "compression": "maximum", "asar": true, "directories": {"output": "release/"}, "nsis": {"oneClick": false, "perMachine": true, "allowElevation": true, "allowToChangeInstallationDirectory": true, "installerIcon": "./build/icon.ico", "uninstallerIcon": "./build/icon.ico", "installerHeaderIcon": "./build/icon.ico", "deleteAppDataOnUninstall": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "intelligent"}, "win": {"icon": "./build/icon.ico", "artifactName": "${productName}-v${version}-${platform}-setup.${ext}", "target": [{"target": "nsis"}]}, "mac": {"icon": "./build/icon.png", "artifactName": "${productName}-v${version}-${platform}-setup.${ext}"}, "linux": {"icon": "./build/icon.png", "artifactName": "${productName}-v${version}-${platform}-setup.${ext}", "target": ["deb"], "maintainer": "maxiu <<EMAIL>>"}}}