# 智能作业平台 (Intelligent Assignment Platform)

## 项目概述

智能作业平台是一个基于Electron+Vue3+TypeScript+Vite构建的跨平台桌面应用程序，
旨在提供高效的业务管理和系统监控解决方案。平台支持Windows、macOS和Linux等多种操作系统，为企业提供统一的业务管理入口。

## 核心功能

- **业务管理**：业务配置、流程设置、技能管理、技能等级、等级分和任务监听
- **系统监控**：操作日志、用户状态管理
- **权限控制**：基于角色的权限分配，精细化的权限管理

## 系统架构设计

### 技术栈选型

#### 前端技术栈

- **框架**：Vue 3 + TypeScript
- **构建工具**：Vite
- **桌面应用框架**：Electron
- **UI组件库**：Ant Design Vue
- **状态管理**：Pinia
- **路由管理**：Vue Router
- **样式解决方案**：UnoCSS (原子化CSS)

#### 后端技术栈

- **运行环境**：Node.js (版本要求 ≥ 20)
- **进程通信**：Electron IPC
- **数据存储**：本地数据库 + 远程API交互

### 系统架构图

```
+------------------------------------------+
|              Electron 应用               |
|  +----------------+  +----------------+  |
|  |   主进程       |  |   渲染进程     |  |
|  | (Main Process) |  |(Renderer Process)| |
|  |                |<>|                |  |
|  | - 窗口管理     |  | - Vue 3 应用   |  |
|  | - 系统API调用  |  | - UI渲染      |  |
|  | - 进程间通信   |  | - 业务逻辑    |  |
|  +----------------+  +----------------+  |
+------------------------------------------+
           |                  |
+----------v--+      +---------v-----------+
| 本地存储    |      |     远程服务器       |
| - 配置信息  |      | - 业务数据API       |
| - 用户数据  |      | - 认证服务          |
+-------------+      +---------------------+
```

### 模块设计

#### 系统管理模块

1. **菜单管理**
   - 功能：配置系统菜单结构、权限和路由
   - 数据项：上级权限、权限名称、权限类型、路由路径、API接口、权限排序、角色选取、是否外链、是否技能权限、所属技能
   - 技术实现：基于树形结构的菜单配置，支持多级菜单和权限继承

2. **角色管理**
   - 功能：定义系统角色及其权限范围
   - 数据项：上级角色、角色名称、角色状态、角色描述、有效时长、权限配置、创建时间
   - 技术实现：基于RBAC（基于角色的访问控制）模型，实现权限的精细化管理

3. **部门管理**
   - 功能：组织架构维护和部门信息管理
   - 数据项：部门名称、部门状态、部门负责人、创建人、创建时间、更新人、更新时间
   - 技术实现：树形结构展示，支持部门的增删改查和层级管理

4. **用户管理**
   - 功能：系统用户信息维护和权限分配
   - 数据项：账号、创建时间、姓名、手机号、所属部门、上级负责人、角色权限、技能
   - 技术实现：用户信息的CRUD操作，与角色和部门的关联管理

5. **注册审核**
   - 功能：新用户注册的审核流程
   - 数据项：账号、手机号、审核意见、审核人、审核时间
   - 技术实现：审核流程的状态管理和操作记录

### 数据流设计

```
+-------------+     +-------------+     +-------------+
|  用户界面   |---->|  业务逻辑   |---->|  数据服务   |
| (Vue组件)   |<----|  (Stores)   |<----| (API/本地存储)|
+-------------+     +-------------+     +-------------+
```

1. **数据获取流程**：
   - 组件挂载时触发数据请求
   - Store层处理数据获取逻辑
   - API服务发送请求到后端
   - 数据返回并更新到Store
   - 组件响应式更新UI

2. **数据提交流程**：
   - 用户在UI上操作并提交数据
   - 组件触发Store的action
   - Store处理数据验证和转换
   - API服务将数据提交到后端
   - 后端处理并返回结果
   - Store更新状态
   - UI响应式更新

## 部署方案

### 开发环境

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 启动Electron应用
npm start
```

### 打包构建

```bash
# web环境打包
npm run build

# 构建 electron 应用并打包为安装包
npm run electron:build
```

构建产物：
- **web**：输出到 `dist` 目录
- **electron**：输出到 `dist` ,`release` 目录

构建后的应用将根据目标平台生成相应的安装包：
- Windows: NSIS安装包
- macOS: DMG安装包
- Linux: AppImage或deb包

## 未来规划

- 完善技能管理模块
- 增强业务流程配置的灵活性
- 优化用户界面和交互体验
- 增加数据分析和报表功能
- 提升系统性能和稳定性
- 增加自动化测试和CI/CD流程

### Vue 3组合式API的应用

系统广泛使用Vue 3的Composition API实现更清晰的代码组织和逻辑复用：

```javascript
// 用户管理组件示例
setup() {
  const users = ref([])
  const departments = ref([])
  
  const fetchUsers = async () => {
    // 获取用户数据逻辑
  }
  
  onMounted(fetchUsers)
  
  return { users, departments, fetchUsers }
}
```

### 权限控制实现

系统采用基于角色的访问控制 (RBAC) 模型，结合Vue Router的导航守卫实现权限控制：

```javascript
// 路由导航守卫
router.beforeEach((to, from, next) => {
  const hasPermission = checkPermission(to.meta.requiredPermissions)
  if (to.meta.requiresAuth && !hasPermission) {
    next({ path: '/unauthorized' })
  } else {
    next()
  }
})
```

## 开发团队

- 产品负责人：孙涛
- 技术负责人：吴瑞苗
- 前端开发：石为平/马秀
- 后端开发：吴瑞苗/王理想
- 测试：研发自测和产品 Review

---

© 2025 智能作业平台 - 版权所有