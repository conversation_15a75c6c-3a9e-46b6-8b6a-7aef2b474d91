const { app, BrowserWindow, ipcMain } = require('electron')
const { autoUpdater } = require('electron-updater')
const { join } = require('path')

// 屏蔽安全警告
// ectron Security Warning (Insecure Content-Security-Policy)
process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true'

// 定义全局窗口变量
let mainWindow;
const platform = process.platform;
let iconPath;

// 根据平台设置图标路径
if (platform === 'win32') {
  iconPath = join(__dirname, '../build/icon.ico');
} else {
  iconPath = join(__dirname, '../build/icon.png');
}

// 执行createWindow创建窗口
const createWindow = () => {
    mainWindow = new BrowserWindow({
        width: 1440,// 窗口宽度
        height: 1000,// 窗口高度
        title: 'intelligent-assignment-platform',// 应用标题
        icon: iconPath,
        webPreferences: {
            nodeIntegration: false,// 不直接开启node
            contextIsolation: true,// 开启上下文隔离
            preload: join(__dirname, './preload.js'),// 预加载脚本
        },
    })
    
    // 引入页面
    // development模式
    if(process.env.VITE_DEV_SERVER_URL) {
        mainWindow.loadURL(process.env.VITE_DEV_SERVER_URL)
        // 开启控制台
        mainWindow.webContents.openDevTools()
    }else {
        mainWindow.loadFile(join(__dirname, '../dist/index.html'))
        // 关闭控制台
        // mainWindow.webContents.closeDevTools()
        mainWindow.webContents.openDevTools()
        // 生产环境检查更新
        autoUpdater.checkForUpdatesAndNotify()
    }
}

// 自动更新相关事件
autoUpdater.on('update-available', () => {
    if (mainWindow) {
        mainWindow.webContents.send('update_available')
    }
})

// 下载进度
autoUpdater.on('download-progress', (progressObj) => {
    if (mainWindow) {
        mainWindow.webContents.send('downloadProgress', progressObj)
    }
})

// 下载完成
autoUpdater.on('update-downloaded', () => {
    if (mainWindow) {
        mainWindow.webContents.send('update_downloaded')
    }
})

ipcMain.on('check_for_updates', () => {
    autoUpdater.checkForUpdates()
})

// 关闭当前应用并安装更新
ipcMain.on('restart_app', () => {
    autoUpdater.quitAndInstall()
})

// Electron 会在初始化后并准备
app.whenReady().then(() => {
    createWindow()
    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) createWindow()
    })
    app.on('certificate-error', 
        (event, webContents, url, error, certificate, callback) => {
        event.preventDefault();
        callback(true); // 允许加载自签证书页面
    });
})

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') app.quit()
})
