const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron')

// 暴露版本信息和基本通信方法
contextBridge.exposeInMainWorld('versions', {
    node: () => process.versions.node,
    chrome: () => process.versions.chrome,
    electron: () => process.versions.electron,
    ping: () => ipcRenderer.invoke('ping')
})

// 暴露IPC通信接口，用于应用更新等功能
contextBridge.exposeInMainWorld('ipcRenderer', {
    // 发送消息到主进程
    send: (channel, ...args) => {
        // 白名单通道
        const validChannels = ['check_for_updates', 'restart_app']
        if (validChannels.includes(channel)) {
            ipcRenderer.send(channel, ...args)
        }
    },
    // 监听来自主进程的消息
    on: (channel, func) => {
        const validChannels = ['update_available', 'downloadProgress', 'update_downloaded']
        if (validChannels.includes(channel)) {
            // 移除旧的监听器，防止重复
            ipcRenderer.removeAllListeners(channel)
            // 添加新的监听器
            ipcRenderer.on(channel, (event, ...args) => func(...args))
        }
    }
})