/**
 * 渲染进程主文件
 * 负责与主进程通信并处理UI相关操作
 */

// 初始化应用程序，设置事件监听器和获取版本信息
const initApp = async () => {
  try {
    // 获取版本信息
    const nodeVersion = window.versions.node();
    const chromeVersion = window.versions.chrome();
    const electronVersion = window.versions.electron();

    console.log(`Node版本: ${nodeVersion}`);
    console.log(`Chrome版本: ${chromeVersion}`);
    console.log(`Electron版本: ${electronVersion}`);

    // 测试与主进程的通信
    const pingResponse = await testPingConnection();
    console.log(`主进程响应: ${pingResponse}`);

    // 设置自动更新相关事件监听
    setupUpdateListeners();
  } catch (error) {
    console.error("应用初始化失败:", error);
  }
};

/**
 * 测试与主进程的ping连接
 * @returns {Promise<string>} 主进程的响应
 */
const testPingConnection = async () => {
  try {
    return await window.versions.ping();
  } catch (error) {
    console.error("与主进程通信失败:", error);
    throw new Error("ping通信失败");
  }
};

/**
 * 设置应用更新相关的事件监听器
 */
const setupUpdateListeners = () => {
  // 监听有可用更新的事件
  window.ipcRenderer.on("update_available", () => {
    console.log("有新版本可用，正在下载...");
    // 这里可以显示更新提示UI
  });

  // 监听下载进度事件
  window.ipcRenderer.on("downloadProgress", (progressObj) => {
    console.log(`下载进度: ${progressObj.percent}%`);
    // 这里可以更新下载进度UI
  });

  // 监听更新下载完成事件
  window.ipcRenderer.on("update_downloaded", () => {
    console.log("更新已下载完成，准备安装...");
    // 这里可以显示安装提示UI

    // 可以提供一个按钮让用户确认重启安装
    // 用户确认后调用: window.ipcRenderer.send('restart_app');
  });
};

/**
 * 检查应用更新
 */
const checkForUpdates = () => {
  try {
    // 向主进程发送检查更新的请求
    window.ipcRenderer.send("check_for_updates");
    console.log("正在检查更新...");
  } catch (error) {
    console.error("检查更新失败:", error);
  }
};

/**
 * 重启应用并安装更新
 */
const restartAndInstallUpdate = () => {
  try {
    window.ipcRenderer.send("restart_app");
    console.log("正在重启应用并安装更新...");
  } catch (error) {
    console.error("重启应用失败:", error);
  }
};

// 初始化应用
initApp();

// 导出函数供其他模块使用
window.electronAPI = {
  checkForUpdates,
  restartAndInstallUpdate,
};

// 在控制台输出初始化完成信息
console.log("渲染进程初始化完成");
