<template>
  <a-config-provider :getPopupContainer="getPopupContainer">
    <router-view />
    <!-- 全局加载组件 -->
    <!-- <GlobalLoading /> -->
  </a-config-provider>
</template>

<script setup>
import { useRoute } from 'vue-router';
// import GlobalLoading from '@/components/common/GlobalLoading.vue';

const route = useRoute();

const getPopupContainer = (trigger) => {
  // 如果是model-validation的task-detail页面，直接返回document.body
  if (route.path === '/model-validation/task-detail') {
    return document.body;
  }
  // 如果trigger存在，返回其父节点，否则返回document.body
  return trigger ? trigger.parentNode : document.body;
};
</script>