# 国际化（i18n）使用指南

本项目使用 vue-i18n 实现国际化支持，目前支持中文（zh-CN）和英文（en-US）两种语言。

## 目录结构

```
src/i18n/
├── index.ts          # i18n 配置文件
├── locales/          # 语言包目录
│   ├── zh-CN.ts      # 中文语言包
│   └── en-US.ts      # 英文语言包
└── README.md         # 使用指南
```

## 如何使用

### 在模板中使用

```vue
<template>
  <!-- 使用 $t 方法 -->
  <div>{{ $t('common.welcome') }}</div>
</template>
```

### 在 JavaScript/TypeScript 中使用

```typescript
import { useI18n } from 'vue-i18n'

export default {
  setup() {
    const { t, locale } = useI18n()
    
    // 使用 t 方法获取翻译
    console.log(t('common.welcome'))
    
    // 切换语言
    function changeLanguage(lang) {
      locale.value = lang // 'zh-CN' 或 'en-US'
    }
    
    return { t, changeLanguage }
  }
}
```

## 添加新的翻译

1. 在 `src/i18n/locales/zh-CN.ts` 和 `src/i18n/locales/en-US.ts` 中添加新的翻译键值对
2. 建议按模块组织翻译内容，例如：

```typescript
export default {
  module: {
    key: '翻译内容'
  }
}
```

## 添加新的语言

1. 在 `src/i18n/locales/` 目录下创建新的语言文件，例如 `fr-FR.ts`
2. 在 `src/i18n/index.ts` 中导入并注册新语言
3. 在语言切换组件中添加新语言选项

## 语言切换

项目中已集成语言切换组件 `LanguageSwitcher.vue`，用户可以通过界面切换语言。语言偏好会保存在浏览器的 localStorage 中。