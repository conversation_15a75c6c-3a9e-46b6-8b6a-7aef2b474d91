export default {
  common: {
    welcome: "欢迎来到智能作业平台",
    search: "请输入关键词",
    advancedFilter: "高级筛选",
    new: "新增",
    add: "添加",
    query: "查询",
    reset: "重置",
    confirm: "确定",
    save: "保存",
    cancel: "取消",
    edit: "编辑",
    delete: "删除",
    action: '操作',
    confirmDelete: "确认删除?",
    pleaseSelect: "请选择",
    pleaseEnter: "请填写",
    view: "详情",
    back: "返回",
    saveSuccess: "保存成功",
    saveFailed: "保存失败",
    publishSuccess: "发布成功",
    publishFailed: "发布失败",
    remove: "移除",
    expand: "展开",
    notification: "温馨提示",
    yes: "是",
    no: "否",
    start: "开始",
    all: "全部",
    selected: "已选择",
    import: "导入",
    uploadSuccess: "上传成功",
    uploadFailed: "上传失败",
    delSuccess: "删除成功",
    delFailed: "删除失败",
    basicInfo: "基本信息",
    look: "查看",
    publish: "发布",
    del: "删除",
    querySuccess: "查询成功",
    queryFailed: "查询失败",
    enabled: '启用',
    disabled: '禁用',
    requestFailed: '请求失败',
    noMoreData: '没有更多数据',
    confirmPublishTask: '是否发布QA任务？',
    imageLoading: '图片加载中...',
    loading: '加载中...',
    refreshing: '刷新中...',
    processing: '处理中...',
    submitting: '提交中...',
    saving: '保存中...',
    matchData: '匹配数据',
  },
  matchData: {
    uploadText: '点击或拖拽文件到此区域上传',
    uploadHint: '支持单个文件上传。数据将进行严格的格式验证。请在上传前自行检查。',
    next: '下一步',
    previewTitle: '预览上传数据',
    uploadMatchData: '匹配数据上传',
    syncMessage: '请等待SKU数据同步完成后更新。',
    projectId: '项目ID',
    name: '名称',
    projectName: '项目名称',
    status: '状态',
    new: '新增',
    exist: '存在',
    error: '错误',
    update: '更新',
    syncWaitMessage: '请等待SKU数据同步完成后更新。',
    errorMessage: '异常数据无法更新，请更新后重新上传。',
    fileTypeError: '只能上传Excel文件！',
    fileSizeError: '文件大小不能超过10MB！',
    pleaseSelectFile: '请选择要上传的文件',
    parseSuccess: '文件解析成功',
    parseError: '文件解析失败',
    updateSuccess: '数据更新成功',
    updateError: '数据更新失败'
  },
  nav: {
    home: "首页",
    workbench: "工作台",
    annotation: "标注台",
    dashboard: "仪表板",
    projectList: "项目列表",
    systemManagement: "系统管理",
    modelValidation: "我的任务",
    mdValidation: "MD验证",
    addQATask: "添加QA任务",
  },
  modelValidation: {
    title: "模型验证",
    validation: "验证列表",
    taskList: "任务列表",
    qaList: "QA列表",
    statistics: "统计分析",
    qaListContent: "QA列表内容将在这里显示",
    statisticsContent: "统计分析内容将在这里显示",
    task1: "模型验证任务1",
    task2: "模型验证任务2",
    task3: "模型验证任务3",
    project1: "智能问答项目",
    project2: "智能推荐系统",
    project3: "智能客服系统",
    addQATask: "添加QA任务",
    taskName: "QA任务名称",
    enterTaskName: "请输入QA任务名称",
    expectedDeadline: "预期截止日期",
    selectExpectedDeadline: "请选择预期截止日期",
    sceneSkuList: "场景SKU列表",
    selectPreset: "选择预设",
    applyPreset: "应用预设",
    addSku: "添加",
    skuId: "SKU ID",
    skuName: "SKU名称",
    scene: "场景",
    accountList: "账户列表",
    accountId: "账户ID",
    accountName: "账户名称",
    phone: "手机号",
    department: "部门",
    addAccount: "添加",
    selectSku: "选择SKU",
    selectAccount: "选择账户",
    image: "图片",
    product: "产品",
    webAppVersion: "WebApp版本",
    taskSerial: "任务序列号",
    errorTitle: "错误分析报告",
    skuError: "SKU错误",
    relatedSceneSku: "相关场景SKU",
    selectedError: "选中错误",
    project: "项目ID",
    filter: {
      taskId: "任务ID",
      enterTaskId: "请输入任务ID",
      country: "国家",
      selectCountry: "请选择国家",
      client: "客户",
      selectClient: "请选择客户",
      modelType: "模型类型",
      selectModelType: "请选择模型类型",
      taskType: "任务类型",
      selectTaskType: "请选择任务类型",
      status: "状态",
      selectStatus: "请选择状态",
      createdOn: "创建时间",
      expectedDeadline: "预期截止日期",
      qaId: "QA ID",
      enterQaId: "请输入QA ID",
      dateRange: "日期范围",
      region: "区域",
      selectRegion: "请选择区域",
      taskSerial: "任务序列号",
      enterTaskSerial: "请输入任务序列号",
      qaName: "QA名称",
      enterQaName: "请输入QA名称",
      issueType: "问题类型",
      selectIssueType: "请选择问题类型",
      notStarted: "未开始"
    },
    countries: {
      indonesia: "印度尼西亚",
      malaysia: "马来西亚",
      singapore: "新加坡",
      thailand: "泰国"
    },
    clients: {
      indiaBev: "印度饮料",
      nestle: "雀巢",
      cocaCola: "可口可乐"
    },
    modelTypes: {
      mbu: "MBU",
      pbu: "PBU",
      cbu: "CBU"
    },
    taskTypes: {
      regular: "常规",
      uat: "UAT"
    },
    regions: {
      emea: "欧洲中东非洲",
      apac: "亚太地区",
      americas: "美洲"
    },
    issueTypes: {
      beverageOther: "饮料其他...",
      productMisclassification: "产品分类错误",
      dataError: "价格和数据错误"
    },
    noMoreErrors: "没有待执行的错误结果。",
    selectImageQuality: "选择图片质量",
    highQuality: "高质量",
    mediumQuality: "中等质量",
    lowQuality: "低质量",
    productImage: "产品图片",
    showAll: "显示全部",
    undetected: "未检测",
    groupId: "组ID",
    blockId: "块ID",
    shelf: "货架",
    column: "列",
    stack: "层",
    irResult: "IR结果",
    gtResult: "GT结果",
    groundTruth: "真实值",
    completion: "完成度",
    accuracy: "准确率",
    totalFacing: "总面数",
    orientation: "方向",
    newFaces: "新面数",
    errors: "错误",
    save: "保存",
    saveAndFinish: "保存并结束",
    saveAndNext: "保存并下一个",
    zoomIn: "放大",
    zoomOut: "缩小",
    columnSettings: "表格字段设置",
    columnSettingsUpdated: "表格字段设置已更新",
    deleteMode: "删除模式已启用",
    exitDeleteMode: "已退出删除模式",
    numberOfLine: "行数",
    qaTaskDetail: "QA任务详情",
    basicInfo: "基本信息",
    region: "区域",
    country: "国家",
    client: "客户",
    sceneId: "场景ID",
    validator: "验证员",
    lastModifiedOn: "最后修改时间",
    skuInfo: "SKU信息",
    irReskuDescription: "IR Resku描述",
    irProductName: "IR产品名称",
    gtReskuDescription: "GT Resku描述",
    gtProductName: "GT产品名称",
    error: "错误",
    dailyQaTotal: "每日QA总数",
    qaPass: "通过",
    qaFail: "失败",
    pleaseSelectErrorFirst: "请先选择错误状态",
    saveSuccess: "保存成功",
    saveFailed: "保存失败",
    lastItem: "这是最后一项",
    orientationGT: "朝向GT",
    sceneType: "场景类型",
    facing_count: "面数",
    error_facing_count: "面数SKU错误",
    created_at: "创建时间",
    requeue_user_id: "IR SKU重新排队",
    projectId: "项目ID",
    taskId: "任务ID",
    report: "报告",
    // 图片质量选项
    imageBlur: "图片模糊",
    pictureOfPicture: "图中图",
    capturedFromBadAngle: "拍摄角度不佳",
    duplicateImages: "重复图片",
    incompleteSceneType: "场景类型不完整",
    imageNotAvailable: "图片不可用",
    incorrectUseOfNextDoor: "下一门使用不正确",
    blockingObstacles: "障碍物阻挡",
    noUseOfNextDoor: "未使用下一门",
    bodyPart: "身体部位",
    notEnoughOverlap: "重叠不足",
    junkImage: "垃圾图片",
    wrongImageSequence: "图片顺序错误",
    tooDark: "太暗",
    productsCapturedOutOfScene: "场景外拍摄的产品",
    tooBright: "太亮",
    mixedImagesInSingleScene: "单一场景中的混合图片",
    mvStitchingIssue: "MV拼接问题",
    mvWrongOrientation: "MV方向错误",
    closedDoor: "关闭的门",
    multipleDisconnectedPos: "多个断开的POS",
    emptyCoolersShelves: "空冷柜/货架",
    priceCheckForm: "Price Check Form",
    missedTagDetection: '丢失标签检测',
    missedPriceValue: '丢失价格',
    priceAndPosition: '价格和位置',
    noPrice: '缺失价格',
    noPosition: '缺失位置',
    deleteSuccess: "删除成功",
    deleteFailed: "删除失败",
    canOnlyDeleteNewItems: "只能删除新添加的数据",
    pleaseSelectItems: "请选择要删除的项目",
    confirmDeleteSelected: "确认删除选中的 {count} 个项目？",
    confirmDeleteSingle: "确认删除这条数据？",
    commentRequired: "选择错误时，备注为必填项",
    pleaseSelectError: "请选择错误",
    pleaseSelectImage: "请选择图片",
    pleaseSelectAccount: "请选择账户",
    pleaseSelectScene: "请选择场景",
    pleaseSelectSku: "请选择SKU",
    cannotDeleteInitialData: "无法删除初始数据",
    myErrorResults: "我的错误结果",
    issueType: "问题类型",
    qaTaskName: "QA名称",
    comments: "备注",
    irResku: "IR RESKU",
    irProduct: "IR产品",
    gtResku: "GT RESKU",
    gtProduct: "GT产品",
    scenneId: "场景ID",
    tempTaskFound: "发现临时任务",
    tempDataFound: "发现临时数据",
    restoreTempData: "恢复临时数据",
    dataRestored: "数据恢复成功",
    dataSavedSuccess: "数据保存成功",
    taskTemporarilySaved: "任务临时保存",
    pleaseEnterLineNumber: "请输入行号",
    price: "价格",
    priceType: "价格类型",
    priceValue: "价格值",
    priceTypeGt: "价格类型GT",
    priceTagTypeGt: "价格标签类型GT",
    priceTagClassGt: "价格标签类别GT",
    priceLocationGt: "价格位置GT",
    priceBgColorGt: "价格背景颜色GT",
    priceFontSizeGt: "价格字体大小GT",
    crossedPriceGt: "划线价格GT",
    priceBgGt: "价格背景GT",
    priceFontColorGt: "价格字体颜色GT",
    priceFontSize: "价格字体大小",
    priceLocation: "价格位置",
    priceIR: "价格IR",
    priceGT: "价格GT",
    priceTag: "价格标签",
    tagMaping: "标签映射",
    priceTagClass: "价格标签类别",
    priceTagMapingIR: "价格标签映射IR",
    priceTagMapingGT: "价格标签映射GT",
    taskCompleted: "任务已完成。",
    increaseDotSize: "增大点尺寸",
    decreaseDotSize: "减小点尺寸",
    dotSize: "点尺寸",
    dotSizeTip: "点尺寸提示",
    pleaseFillInAllRequiredFieldsForNewItems: "请填写新增项目的所有必填字段",
    selectContent: "请选择内容",
    enterContent: "请输入内容",
    invalidErrorId: "错误ID无效",
  },
  user: {
    name: "名字",
    role: "角色",
    team: "所属团队",
    editInfo: "编辑账户信息",
    logout: "退出登录",
    modifyPassword: "修改密码",
    username: "用户名",
    ori_password: "旧密码",
    new_password: "新密码",
    updateSuccess: "用户信息更新成功",
    passwordUpdateSuccess: "密码修改成功",
    logoutSuccess: "退出登录成功",
    ori_passwordRequired: "请输入旧密码",
    new_passwordRequired: "请输入新密码",
    passwordLengthTip: "密码长度不能少于6个字符",
    passwordNotMatch: "两次输入的密码不一致",
    loginTitle: "验证管理",
    login: "登录",
    usernamePlaceholder: "请输入账号",
    passwordPlaceholder: "请输入密码",
    autoLogin: "自动登录",
    register: "注册账号",
    loginSuccess: "登录成功",
    loginFailed: "登录失败",
    unauthorized: "未授权，请登录",
    accessDenied: "拒绝访问",
    usernameRequired: "请输入用户名",
    passwordRequired: "请输入密码",
    account: "账号",
    password: "密码",
    validateName: "请输入姓名",
    validateTeam: "请输入团队",
    validateRole: "请输入角色"
  },
  system: {
    title: "系统管理",
    menu: "菜单管理",
    role: "角色管理",
    department: "部门管理",
    user: "用户管理",
    registration: "注册审核",
  },
  roleManagement: {
    title: "角色管理",
    roleName: "角色名称",
    roleStatus: "角色状态",
    roleDescription: "角色描述",
    roleExpirationDate: "角色截止日期",
    creator: "创建人",
    creationTime: "创建时间",
    userCount: "用户数量",
    edit: "编辑",
    authorize: "授权",
    delete: "删除",
    advancedFilterTitle: "高级筛选",
    keyword: "关键词",
    enterKeyword: "请输入关键词",
    enterRoleName: "请输入角色名称",
    enterRoleDescription: "请输入角色描述",
    addRole: "新增角色",
    editRole: "编辑角色",
    parentRole: "上级角色",
    selectParentRole: "请选择上级角色",
    roleDesc: "角色描述",
    enterRoleDesc: "请填写角色描述",
    enable: "启用",
    disable: "禁用",
    ddlType: "截止类型",
    ddlTypeNone: "无",
    ddlTypeBefore: "在截止日期前",
    ddl: "截止日期",
    selectDDL: "请选择截止日期",
    menuPermission: "菜单权限",
    expandCollapse: "展开/折叠",
    checkAll: "全选/全不选",
  },
  menuManagement: {
    title: "菜单管理",
    editMenu: "编辑菜单",
    addMenu: "新增菜单",
    parentMenu: "上级菜单",
    menuType: "菜单类型",
    menuName: "菜单名称",
    interfaceRule: "API接口",
    pageHierarchy: "页面层级",
    routePath: "路由路径",
    permissionRole: "权限角色",
    isExternalLink: "是否外链",
    externalLinkAddress: "外链地址",
    belongSkill: "所属技能",
    directory: "目录",
    menu: "菜单",
    button: "按钮",
    page: "页面",
    yes: "是",
    no: "否",
    selectParentMenu: "请选择上级菜单",
    selectMenuType: "请选择菜单类型",
  },
  departmentManagement: {
    parent_id: "上级部门",
    title: "部门管理",
    editDepartment: "编辑部门",
    addDepartment: "新增部门",
    departmentName: "部门名称",
    departmentHead: "部门负责人",
    headAccount: "负责人账号",
    mail: "邮箱",
    headPhone: "负责人电话",
    status: "部门状态",
    creator: '创建人',
    creationTime: '创建时间',
    updater: '更新人',
    updateTime: '更新时间',
    enterParentName: "请选择上级部门",
    enterDepartmentName: "请输入部门",
    enterDepartmentHead: "请选择部门负责人",
    enterHeadAccount: "请选择负责人账号",
    enterHeadPhone: "请输入负责人电话",
    selectDepartmentStatus: "请选择部门状态"
  },
  registrationAudit: {
    title: "注册审核",
    audit: "审核",
    name: "姓名",
    account: "账号",
    phone: "手机号",
    registrationTime: "注册时间",
    registrationAddress: "注册地址",
    registrationIp: "注册ip",
    auditor: "审核人",
    auditTime: "审核时间",
    auditStatus: "审核状态",
    pending: "待审核",
    rejected: "未通过",
    approved: "通过"
  },
  advancedFilter: {
    title: "高级筛选",
    keyword: "关键词",
    enterKeyword: "请输入关键词",
    menuName: "菜单名称",
    enterMenuName: "请输入菜单名称",
    taskName: "任务名称",
    enterTaskName: "请输入任务名称",
    relatedProject: "关联项目",
    enterRelatedProject: "请输入关联项目",
    createDate: "创建日期",
    startDate: "开始日期",
    endDate: "结束日期",
    taskTeam: "任务团队",
    selectTaskTeam: "请选择任务团队",
    taskStatus: "任务状态",
    selectTaskStatus: "请选择任务状态",
    team1: "团队 1",
    team2: "团队 2",
    active: "进行中",
    completed: "已完成"
  },
  userManagement: {
    title: "用户管理",
    departmentList: "部门列表",
    userList: "用户列表",
    name: "姓名",
    account: "账号",
    phone: "手机号",
    department: "所属部门",
    rolePermissions: "角色权限",
    skillNum: "技能数",
    officeLocation: "办公地点",
    userStatus: "用户状态",
    userSource: "用户来源",
    edit: "编辑",
    delete: "删除",
    addUser: "新增用户",
    editUser: "编辑用户",
    mail: "用户邮箱",
    enterMail: "请输入用户邮箱",
    enterName: "请输入姓名",
    enterPhone: "请输入手机号",
    selectDepartment: "请选择所属部门",
    enterOffice: "请输入办公地点",
    selectRole: "请选择用户角色",
    selectSkill: "请选择技能",
    selectStatus: "请选择用户状态",
    addSuccess: "添加成功",
    editSuccess: "修改成功",
    requestFailed: "请求失败"
  },
  projectList: {
    region: "区域",
    country: "国家",
    client: "客户",
    modelType: "模型类型",
    lastModifiedOn: "最后修改时间",
    accountCount: "账户数量",
    targetType: "目标类型",
    target: "目标",
    subClient: "子客户",
    enterRegion: "请输入区域",
    enterCountry: "请输入国家",
    enterClient: "请输入客户",
    enterSubClient: "请输入子客户",
    enterModelType: "请输入模型类型",
    enterTargetType: "请输入目标类型",
    enterClientReferenceId: "请输入客户引用ID",
    client_reference_id: "客户引用ID",

    title: "客户列表",
    clientName: "客户名称",
    contactPerson: "联系人",
    contactPhone: "联系电话",
    email: "电子邮箱",
    industry: "所属行业",
    status: "状态",
    address: "地址",
    description: "描述",
    createDate: "创建日期",
    addClient: "新增客户",
    editClient: "编辑客户",
    enterClientName: "请输入客户名称",
    enterContactPerson: "请输入联系人",
    enterContactPhone: "请输入联系电话",
    enterEmail: "请输入电子邮箱",
    enterAddress: "请输入地址",
    enterDescription: "请输入描述",
    nameRequired: "客户名称不能为空",
    nameMaxLength: "客户名称不能超过50个字符",
    contactPersonRequired: "联系人不能为空",
    contactPhoneRequired: "联系电话不能为空",
    contactPhoneInvalid: "联系电话格式不正确",
    emailInvalid: "电子邮箱格式不正确",
    industryRequired: "所属行业不能为空",
    statusRequired: "状态不能为空",
    industries: {
      finance: "金融",
      technology: "科技",
      education: "教育",
      healthcare: "医疗"
    },
    statuses: {
      active: "活跃",
      inactive: "非活跃"
    }
  },
  projectDetail: {
    title: "添加客户信息",
    detail: "详情",
    import: "导入",
    basicInfo: "信息",
    validationTask: "验证任务",
    validatorList: "验证列表",
    enterIsGenDaily: '请选择是否每日生成任务',
    enterReleaseType: '请选择发布类型',
    enterTgtType: '请选择目标类型',
    enterImageRetrieval: '请选择图片抽取方式',
    enterImgTgt: '请输入目标值',
    enterIsOriVal: '请选择是否进行朝向验证',
    target: "目标",
    yes: "是",
    no: "否",
    auto: "自动",
    manual: "手动",
    scene: "场景",
    image: "图片",
    number: "数量",
    percentage: "百分比",
    generateTaskDaily: "每日生成任务",
    taskReleaseType: "任务发布类型",
    tgtType: "目标类型",
    imageRetrieval: "图片检索",
    orientationValidation: "朝向验证",
    SceneList: "场景列表",
    AccountList: "账户列表",
    name: "姓名",
    department: "部门",
    selectDepartment: "请选择部门",
    selectGenerateTaskDaily: "请选择是否每日生成任务",
    selectTaskReleaseType: "请选择任务发布类型",
    selectTgtType: "请选择目标类型",
    selectImageRetrieval: "请选择图片检索方法",
    selectOrientationValidation: "请选择朝向验证方法",
    enterTarget: "请输入目标值",
    targetMinValue: "目标值不能小于1",
    pleaseSelectUsers: "请选择用户",
  },
  validitionTask: {
    unpublish: "取消发布",
    unpublished: "未发布",
    publishedNotStarted: "已发布未开始",
    inProgress: "进行中",
    notStarted: "未开始",
    completed: "已完成",
    type: "任务类型",
    enterType: "请输入任务类型",
    name: "任务名称",
    enterName: "请输入任务名称",
    region: "区域",
    enterRegion: "请输入区域",
    country: "国家",
    enterCountry: "请输入国家",
    client: "客户",
    enterClient: "请输入客户",
    model_type: "模型类型",
    no_scenes: "待验证场景数",
    enterModelType: "请输入模型类型",
    expect_deadline: "预期截止日期",
    selectDeadline: "请选择预期截止日期",
    sub_task_name: "子任务名称",
    enterSubTaskName: "请输入子任务名称",
    start_time: "开始时间",
    selectStartTime: "请选择开始时间",
    image_received_on: "收图日期",
    selectImageReceivedDate: "请选择收图日期",
    scene_total: "场景总数",
    enterSceneTotal: "请输入场景总数",
    requeued_and_validated: "重排并验证",
    enterRequeuedAndValidated: "请输入重排并验证",
    specific_scene_ids: "指定场景ID",
    enterSpecificSceneIds: "请输入指定场景ID",
    mds_available: "MDS可用",
    enterMdsAvailable: "请输入MDS可用",
    additional_document: "附加文档",
    enterAdditionalDocument: "请输入附加文档",
    priority_level: "优先级",
    enterPriorityLevel: "请输入优先级",
    attachments: "附件",
    enterAttachments: "请输入附件",
    remark: "备注",
    enterRemark: "请输入备注",
    publishSuccess: "发布成功",
    publishFailed: "发布失败",
  },
  scenesList: {
    title: "场景列表",
    name: "场景名称",
    type: "场景类型",
    sub_scene_type: "子场景类型",
    img_received_on: "图像接收日期",
    validation_status: "验证状态",
    id: "场景id",
    total_imgs: "总图片数",
    total_facings: "总面数",
    out_scene_ids: "场景ID",
    validator_ids: "被谁校验",
    created_at: "验证时间",
  },
  route: {
    "首页": "首页",
    "系统管理": "系统管理",
    "模型验证": "模型验证",
    "项目列表": "项目列表",
    "项目详情": "项目详情",
    "新增项目": "新增项目",
    "任务详情": "任务详情",
    "QA 任务详情": "QA任务详情",
    "新增任务": "新增任务",
    "添加QA任务": "添加QA任务",
    "错误结果": "错误结果",
    "MD 验证": "MD验证",
    "MD 详情": "MD详情"
  },
  error: {
    badRequest: "请求错误",
    notFound: "请求地址不存在",
    timeout: "请求超时",
    serverError: "服务器内部错误",
    notImplemented: "服务未实现",
    badGateway: "网关错误",
    serviceUnavailable: "服务不可用",
    gatewayTimeout: "网关超时",
    httpVersionNotSupported: "HTTP版本不受支持",
    connectionError: "连接出错",
    networkError: "网络错误，请检查您的网络连接"
  },
  mdValidation: {
    title: 'MD验证',
    image: '图片',
    brand: '品牌',
    flavor: '口味',
    category: '类别',
    shape: '形状',
    taskId: '任务ID',
    sceneId: '场景ID',
    validatedOn: '验证时间',
    validatedBy: '验证人',
    taskType: '任务类型',
    status: '状态',
    isDuplicated: '是否重复',
    isWrongMapping: '映射错误',
    activeStatus: '激活状态',
    outMdSkuId: '外部MD SKU ID',
    projectId: '项目ID',
    capacity: '容量',
    productName: '产品名称',
    id: '编号',
    hasImg: '是否有图片',
    first_out_sku_id: '首个外部SKU ID',
    first_img_urls: '首个图片URL',
    first_product_name: '首个产品名称',
    first_facing: '首个朝向',
    first_brand: '首个品牌',
    first_flavor: '首个口味',
    first_shape: '首个形状',
    first_capacity: '首个容量'
  }
};