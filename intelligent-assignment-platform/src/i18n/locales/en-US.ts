export default {
  common: {
    welcome: 'Welcome to Intelligent Assignment Platform',
    search: 'Please enter keywords',
    advancedFilter: 'Advanced Filter',
    new: 'New',
    add: "Add",
    query: 'Search',
    reset: 'Reset',
    confirm: 'Confirm',
    cancel: 'Cancel',
    edit: 'Edit',
    save: 'Save',
    delete: 'Delete',
    action: 'Action',
    actions: 'action',
    confirmDelete: 'Confirm delete?',
    pleaseSelect: 'Please select',
    pleaseEnter: 'Please enter',
    view: "Detail",
    back: "Back",
    saveSuccess: "Save successful",
    saveFailed: "Save failed",
    publishSuccess: "Publish successul",
    publishFailed: "Publish failed",
    remove: "Remove",
    expand: "Expand",
    notification: "Notification",
    yes: "Yes",
    no: "No",
    start: "Start",
    all: "All",
    selected: "Selected",
    import: "Import",
    uploadSuccess: 'Upload successful',
    uploadFailed: 'Upload failed',
    delSuccess: 'Delete successful',
    delFailed: 'Delete failed',
    basicInfo: 'Basic Info',
    look: "View",
    publish: "Publish",
    del: "Del",
    confirmPublishTask: 'Whether to publish QA tasks?',
    querySuccess: 'Query successful',
    queryFailed: 'Query failed',
    enabled: 'on',
    disabled: 'off',
    requestFailed: 'Request failed',
    noMoreData: 'No more data',
    imageLoading: 'Loading image...',
    loading: 'Loading...',
    refreshing: 'Refreshing...',
    processing: 'Processing...',
    submitting: 'Submitting...',
    saving: 'Saving...',
    title: 'Title',
    selectContent: 'Select content',
    enterContent: 'Enter content',
    addAccount: 'Add Account',
    selectSku: 'Please select SKU',
    selectAccount: 'Please select account',
    project: 'Project',
    exitDeleteMode: 'Exit Delete Mode',
    numberOfLine: 'Number of Line',
    qaTaskDetail: 'QA Task Detail',
    priceCheckForm: 'Price Check Form',
    priceFontSize: 'Price Font Size',
    priceLocation: 'Price Location',
    modelType: 'Model Type',
    account: 'Account',
    password: 'Password',
    matchData: 'Match Data',
  },
  matchData: {
    uploadText: 'Click or drag file to this area to upload',
    uploadHint: 'Support for a single upload. The data will undergo strict format validation. Please check it yourself before uploading.',
    next: 'Next',
    previewTitle: 'Preview Upload Data',
    uploadMatchData: 'Upload Match Data',
    syncMessage: 'After the update, the sku data will be synchronized in the background',
    projectId: 'Project Id',
    name: 'name',
    projectName: 'Project Name',
    status: 'Status',
    new: 'New',
    exist: 'Exist',
    error: 'Error',
    update: 'Update',
    syncWaitMessage: 'Please wait for SKU data synchronization to complete after update.',
    errorMessage: 'Abnormal data cannot be updated,Please update and re-upload.',
    fileTypeError: 'You can only upload Excel files!',
    fileSizeError: 'File size cannot exceed 10MB!',
    pleaseSelectFile: 'Please select a file to upload',
    parseSuccess: 'File parsed successfully',
    parseError: 'File parsing failed',
    updateSuccess: 'Data updated successfully',
    updateError: 'Data update failed'
  },
  nav: {
    home: 'Home',
    workbench: 'Workbench',
    annotation: 'Annotation',
    dashboard: 'Dashboard',
    projectList: 'Project List',
    systemManagement: 'Setting',
    modelValidation: 'My Task',
    mdValidation: 'MD Validation',
    addQATask: 'Add QA Task',
  },
  modelValidation: {
    validation: 'Validation',
    taskList: 'Task List',
    qaList: 'QA List',
    statistics: 'Statistics',
    qaListContent: 'QA list content will be displayed here',
    statisticsContent: 'Statistics content will be displayed here',
    task1: 'Model Validation Task 1',
    task2: 'Model Validation Task 2',
    task3: 'Model Validation Task 3',
    project: 'Project Id',
    project1: 'Intelligent Q&A Project',
    project2: 'Intelligent Recommendation System',
    project3: 'Intelligent Customer Service System',
    addQATask: 'Add QA Task',
    taskName: 'QA Task Name',
    enterTaskName: 'Please enter QA task name',
    expectedDeadline: 'Expected Deadline',
    selectExpectedDeadline: 'Please select expected deadline',
    sceneSkuList: 'Scene SKU List',
    selectPreset: 'Select Preset',
    applyPreset: 'Apply Preset',
    addSku: 'Add',
    skuId: 'SKU ID',
    skuName: 'SKU Name',
    scene: 'Scene',
    sku: 'SKU',
    accountList: 'Account List',
    accountId: 'Account ID',
    accountName: 'Account Name',
    phone: 'Phone',
    department: 'Department',
    addAccount: 'Add',
    selectSku: 'Select SKU',
    selectAccount: 'Select Account',
    image: 'Image',
    product: 'Product',
    webAppVersion: "Web App Version",
    taskSerial: 'Task Serial',
    errorTitle: 'Error Analysis Report',
    skuError: 'SKU Error',
    relatedSceneSku: 'Related Scene SKU',
    selectedError: 'Selected Error',
    filter: {
      taskId: 'Task ID',
      enterTaskId: 'Please enter task ID',
      country: 'Country',
      selectCountry: 'Please select country',
      client: 'Client',
      selectClient: 'Please select client',
      modelType: 'Model Type',
      selectModelType: 'Please select model type',
      taskType: 'Task Type',
      selectTaskType: 'Please select task type',
      status: 'Status',
      selectStatus: 'Please select status',
      createdOn: 'Created On',
      expectedDeadline: 'Expected Deadline',
      qaId: 'QA ID',
      enterQaId: 'Please enter QA ID',
      dateRange: 'Date Range',
      region: 'Region',
      selectRegion: 'Please select region',
      taskSerial: 'Task Serial',
      enterTaskSerial: 'Please enter task serial',
      qaName: 'QA Name',
      enterQaName: 'Please enter QA name',
      issueType: 'Issue Type',
      selectIssueType: 'Please select issue type',
      notStarted: 'Not Started',
    },
    countries: {
      indonesia: 'Indonesia',
      malaysia: 'Malaysia',
      singapore: 'Singapore',
      thailand: 'Thailand'
    },
    clients: {
      indiaBev: 'IndiaBev',
      nestle: 'Nestle',
      cocaCola: 'Coca-Cola',
    },
    modelTypes: {
      mbu: 'MBU',
      pbu: 'PBU',
      cbu: 'CBU'
    },
    taskTypes: {
      regular: 'Regular',
      uat: 'UAT',
      adhoc: 'Adhoc',
    },
    regions: {
      emea: 'EMEA',
      apac: 'APAC',
      americas: 'Americas',
    },
    issueTypes: {
      beverageOther: 'Beverage Other to...',
      productMisclassification: 'Product Misclassification',
      dataError: 'Price&Data Error',
    },
    noMoreErrors: 'There are no error results to be executed.',
    selectImageQuality: 'Select Image Quality',
    highQuality: 'High Quality',
    mediumQuality: 'Medium Quality',
    lowQuality: 'Low Quality',
    productImage: 'Product Image',
    showAll: 'Show All',
    undetected: 'Missed detection',
    groupId: 'Group ID',
    blockId: 'Block ID',
    shelf: 'Shelf',
    column: 'Column',
    stack: 'Stack',
    irResult: 'IR Result',
    gtResult: 'GT Result',
    groundTruth: 'Ground Truth',
    completion: 'Completion',
    accuracy: 'Accuracy',
    totalFacing: 'Total Facing',
    orientation:'Orientation',
    newFaces: 'New Facing',
    errors: 'Errors',
    save: 'Save',
    saveAndFinish: 'Save and Finish',
    saveAndNext: 'Save and Next',
    zoomIn: 'Zoom In',
    zoomOut: 'Zoom Out',
    columnSettings: 'Column Settings',
    columnSettingsUpdated: 'Column settings updated',
    deleteMode: 'Delete mode enabled',
    exitDeleteMode: 'Exit delete mode',
    numberOfLine: 'Number of line',
    qaTaskDetail: 'QA Task Detail',
    basicInfo: 'Basic Info',
    region: 'Region',
    country: 'Country',
    client: 'Client',
    sceneId: 'Scene ID',
    validator: 'Validator',
    facing_count: 'Facings',
    error_facing_count: 'Facing SKU Errors',
    error: 'Error',
    lastModifiedOn: 'Last Modified On',
    created_at: 'Created On',
    requeue_user_id: 'Requeued by IR SKU',
    projectId: 'Project Id',
    taskId: 'Task Id',
    skuInfo: 'SKU Info',
    irReskuDescription: 'IR Result',
    report: 'Report',
    irProductName: 'IR Product Name',
    gtReskuDescription: 'GT SKU Description',
    gtProductName: 'GT Product Name',
    dailyQaTotal: 'QA Progress',
    qaPass: 'QA Pass',
    qaFail: 'QA Fail',
    sceneType: 'Scene Type',
    pleaseSelectErrorFirst: 'Please select error status first',
    saveSuccess: 'Save successful',
    saveFailed: 'Save failed',
    lastItem: 'This is the last item',
    orientationGT: 'Orientation GT',
    // Image quality options
    imageBlur: 'Image Blur',
    pictureOfPicture: 'Picture of Picture',
    capturedFromBadAngle: 'Captured From Bad Angle',
    duplicateImages: 'Duplicate Images',
    incompleteSceneType: 'Incomplete Scene Type',
    imageNotAvailable: 'Image Not Available',
    incorrectUseOfNextDoor: 'Incorrect Use of Next Door/Next Side',
    blockingObstacles: 'Blocking Obstacles',
    noUseOfNextDoor: 'No Use of Next Door/Next Side',
    bodyPart: 'Body Part',
    notEnoughOverlap: 'Not Enough Overlap',
    junkImage: 'Junk Image',
    wrongImageSequence: 'Wrong Image Sequence',
    tooDark: 'Too Dark',
    productsCapturedOutOfScene: 'Products Captured Out of Scene',
    tooBright: 'Too Bright',
    mixedImagesInSingleScene: 'Mixed Images in Single Scene',
    mvStitchingIssue: 'MV Stitching Issue',
    mvWrongOrientation: 'MV Wrong Orientation',
    closedDoor: 'Closed Door',
    multipleDisconnectedPos: 'Multiple Disconnected POS in one scene',
    emptyCoolersShelves: 'Empty Coolers/Shelves',
    missedTagDetection: 'Missed Tag Detection',
    missedPriceValue: 'Missed Price Value',
    priceAndPosition: 'Price&Position',
    noPrice: 'No Price',
    noPosition: 'No Position',
    deleteSuccess: 'Delete successful',
    deleteFailed: 'Delete failed',
    canOnlyDeleteNewItems: 'Can only delete newly added items',
    pleaseSelectItems: 'Please select items to delete',
    confirmDeleteSelected: 'Confirm to delete {count} selected items?',
    confirmDeleteSingle: 'Confirm to delete this item?',
    commentRequired: 'Comment is required when selecting error',
    pleaseSelectError: 'Please select error',
    pleaseSelectImage: 'Please select image',
    pleaseSelectAccount: 'Please select account',
    pleaseSelectScene: 'Please select scene',
    pleaseSelectSku: 'Please select SKU',
    cannotDeleteInitialData: 'Cannot delete initial data',
    myErrorResults: 'My Error Results',
    issueType: 'Issue Type',
    qaTaskName: 'QA Task Name',
    comments: 'Comments',
    irResku: 'IR RESKU',
    irProduct: 'IR Product',
    gtResku: 'GT RESKU',
    gtProduct: 'GT Product',
    scenneId: 'SceneID',
    tempTaskFound: 'Temp task found',
    tempDataFound: 'Temp data found',
    restoreTempData: 'Restore temp data',
    dataRestored: 'Data restored successfully',
    dataSavedSuccess: 'Data saved successfully',
    taskTemporarilySaved: 'Task temporarily saved',
    pleaseEnterLineNumber: 'Please enter line number',
    price: 'Price',
    priceType: 'Price Type',
    priceValue: 'Price Value',
    priceTypeGt: 'Price Type GT',
    priceTagTypeGt: 'Price Tag Type GT',
    priceTagClassGt: 'Price Tag Class GT',
    priceLocationGt: 'Price Location GT',
    priceBgColorGt: 'Price BG Color GT',
    priceFontSizeGt: 'Price Font Size GT',
    crossedPriceGt: 'Crossed Price GT',
    priceBgGt: 'Price BG GT',
    priceFontColorGt: 'Price Font Color GT',
    priceFontSize: 'Price Font Size',
    priceLocation: 'Price Location',
    priceIR: 'Price IR',
    priceGT: 'Price GT',
    priceTag: 'Price Tag',
    tagMaping: 'Tag Mapping',
    priceTagClass: 'Price Tag Class',
    priceTagMapingIR: 'Price Tag Maping IR',
    priceTagMapingGT: 'Price Tag Maping GT',
    taskCompleted:'The task has been completed.',
    increaseDotSize: 'Increase Dot Size',
    decreaseDotSize: 'Decrease Dot Size',
    dotSize: 'Dot Size',
    dotSizeTip: 'Dot Size Tip',
    modelType: 'Model Type',
    invalidErrorId: 'Invalid Error ID',
    pleaseFillInAllRequiredFieldsForNewItems: 'Please fill in all required fields for new items',

  },
  user: {
    name: 'Name',
    role: 'Role',
    team: 'Team',
    editInfo: 'Personal Information',
    logout: 'Log Out',
    modifyPassword: 'Modify Password',
    username: 'Username',
    ori_password: 'Change Password',
    new_password: 'Confirm Password',
    updateSuccess: 'User information updated successfully',
    passwordUpdateSuccess: 'Password updated successfully',
    logoutSuccess: 'Logged out successfully',
    // usernameRequired: 'Please confirm username',
    ori_passwordRequired: 'Please enter old password',
    new_passwordRequired: 'Please enter new password',
    passwordLengthTip: 'Password must be at least 6 characters',
    passwordNotMatch: 'The two passwords do not match',
    loginTitle: "Validation System",
    login: "Login",
    usernamePlaceholder: "Please enter your username",
    passwordPlaceholder: "Please enter your password",
    autoLogin: "Auto Login",
    register: "Register Account",
    loginSuccess: "Login successful",
    loginFailed: "Login failed",
    unauthorized: "Unauthorized, please login",
    accessDenied: "Access denied",
    account: "Account",
    password: "Password",
    passwordRequired: "Please enter password",
    usernameRequired: "Please enter username",
    validateName: "Please enter name",
    validateTeam: "Please enter team",
    validateRole: "Please enter role"
  },
  system: {
    title: 'System Management',
    menu: 'Menu',
    role: 'Role',
    department: 'Department',
    user: 'User',
    registration: 'Registration Audit'
  },
  roleManagement: {
    title: 'Role Management',
    roleName: 'Name',
    roleStatus: 'Status',
    roleDescription: 'Description',
    roleExpirationDate: 'Role Expiration Date',
    creator: 'Creator',
    creationTime: 'Creation Time',
    userCount: 'User Count',
    edit: 'Edit',
    authorize: 'Authorize',
    delete: 'Delete',
    advancedFilterTitle: "Advanced Filter",
    keyword: "Keyword",
    enterKeyword: "Please enter keyword",
    enterRoleName: "Please enter role name",
    enterRoleDescription: "Please enter role description",
    addRole: "Add Role",
    editRole: "Edit Role",
    parentRole: "Parent Role",
    selectParentRole: "Please select parent role",
    roleDesc: "Description",
    enterRoleDesc: "Please enter role description",
    enable: "Enable",
    disable: "Disable",
    ddlType: "Deadline Type",
    ddlTypeNone: "None",
    ddlTypeBefore: "By the deadline",
    ddl: "Deadline",
    selectDDL: "Please select deadline",
    menuPermission: "Menu",
    expandCollapse: "Expand/Collapse",
    checkAll: "Select All/Deselect All",
  },
  menuManagement: {
    title: 'Menu Management',
    editMenu: 'Edit Menu',
    addMenu: 'Add Menu',
    parentMenu: 'Parent Menu',
    menuType: 'Menu Type',
    menuName: 'Menu Name',
    interfaceRule: 'API',
    pageHierarchy: 'View Level',
    routePath: 'Routing Path',
    permissionRole: 'Permission Role',
    isExternalLink: 'External Link',
    externalLinkAddress: 'External Link Address',
    belongSkill: 'Belong Skill',
    directory: 'Contents',
    menu: 'Menu',
    button: 'Button',
    page: 'Page',
    yes: 'Yes',
    no: 'No',
    selectParentMenu: 'Please select parent menu',
    selectMenuType: 'Please select menu type',
  },
  departmentManagement: {
    parent_id: 'Parent Department',
    title: 'Department Management',
    editDepartment: 'Edit Department',
    addDepartment: 'Add Department',
    departmentName: 'Name',
    departmentHead: 'Team Leader',
    headAccount: 'Leader Account',
    mail: 'Leader Email',
    headPhone: 'Leader Phone',
    creator: 'Creator',
    creationTime: 'Creation Time',
    updater: 'Updater',
    updateTime: 'Update Time',
    status: 'Status',
    enterParentName: 'Please enter parent name',
    enterDepartmentName: 'Please enter department',
    enterDepartmentHead: 'Please select department head',
    enterHeadAccount: 'Please select head account',
    enterHeadPhone: 'Please enter head phone',
    selectDepartmentStatus: 'Please select department status'
  },
  registrationAudit: {
    title: 'Registration Audit',
    audit: 'Audit',
    name: 'Name',
    account: 'Account',
    phone: 'Phone',
    registrationTime: 'Registration Time',
    registrationAddress: 'Registration Address',
    registrationIp: 'Registration IP',
    auditor: 'Auditor',
    auditTime: 'Audit Time',
    auditStatus: 'Audit Status',
    pending: 'Pending',
    rejected: 'Rejected',
    approved: 'Approved'
  },
  advancedFilter: {
    title: 'Advanced Filter',
    keyword: 'Keyword',
    enterKeyword: 'Please enter keyword',
    menuName: 'Menu Name',
    enterMenuName: 'Please enter menu name',
    taskName: 'Task Name',
    enterTaskName: 'Please enter task name',
    relatedProject: 'Related Project',
    enterRelatedProject: 'Please enter related project',
    createDate: 'Creation Date',
    startDate: 'Start Date',
    endDate: 'End Date',
    taskTeam: 'Task Team',
    selectTaskTeam: 'Please select task team',
    taskStatus: 'Task Status',
    selectTaskStatus: 'Please select task status',
    team1: 'Team 1',
    team2: 'Team 2',
    active: 'Active',
    completed: 'Completed'
  },
  userManagement: {
    title: 'User Management',
    departmentList: 'Department List',
    userList: 'User List',
    name: 'Name',
    account: 'Account',
    phone: 'Phone',
    department: 'Department',
    rolePermissions: 'Role',
    skillNum: 'Skill Number',
    officeLocation: 'Location',
    userStatus: 'Status',
    userSource: 'User Source',
    edit: 'Edit',
    delete: 'Delete',
    editUser: 'Edit Account',
    addUser: 'Add Account',
    mail: 'Email',
    enterMail: 'Please enter email',
    enterName: 'Please enter name',
    enterPhone: 'Please enter phone',
    selectDepartment: 'Please select department',
    enterOffice: 'Please enter office location',
    selectRole: 'Please select role',
    selectSkill: 'Please select skill number',
    selectStatus: 'Please select user status',
    addSuccess: 'User added successfully',
    editSuccess: 'User information updated successfully',
    requestFailed: 'Request failed',
  },
  projectList: {
    region: "Region",
    country: "Country",
    client: "Client",
    modelType: "Model Type",
    lastModifiedOn: "Last Modified on",
    client_reference_id: "Client reference ID",
    accountCount: "Account Count",
    targetType: "Target Type",
    target: "Target",
    subClient: "Sub Client",
    enterRegion: "Please enter region",
    enterCountry: "Please enter country",
    enterClient: "Please enter client",
    enterSubClient: "Please enter sub client",
    enterModelType: "Please enter model type",
    enterTargetType: "Please enter target type",
    enterClientReferenceId: "Please enter client reference ID",
    title: 'Project List',
    clientName: 'Client Name',
    contactPerson: 'Contact Person',
    contactPhone: 'Contact Phone',
    email: 'Email',
    industry: 'Industry',
    status: 'Status',
    address: 'Address',
    description: 'Description',
    createDate: 'Creation Date',
    addClient: 'Add Client',
    editClient: 'Edit Client',
    enterClientName: 'Please enter client name',
    enterContactPerson: 'Please enter contact person',
    enterContactPhone: 'Please enter contact phone',
    enterEmail: 'Please enter email',
    enterAddress: 'Please enter address',
    enterDescription: 'Please enter description',
    nameRequired: 'Client name is required',
    nameMaxLength: 'Client name cannot exceed 50 characters',
    contactPersonRequired: 'Contact person is required',
    contactPhoneRequired: 'Contact phone is required',
    contactPhoneInvalid: 'Invalid phone number format',
    emailInvalid: 'Invalid email format',
    industryRequired: 'Industry is required',
    statusRequired: 'Status is required',
    industries: {
      finance: 'Finance',
      technology: 'Technology',
      education: 'Education',
      healthcare: 'Healthcare'
    },
    statuses: {
      active: 'Active',
      inactive: 'Inactive'
    }
  },
  projectDetail: {
    title: "Add Client",
    detail: "Detail",
    import: "Import",
    basicInfo: "Info",
    validationTask: "Validation Task",
    validatorList: "Validator List",
    generateTaskDaily: "Generate Task Daily",
    taskReleaseType: "Task Release Type",
    tgtType: "Tgt Type",
    imageRetrieval: "Image Retrieval",
    orientationValidation: "Orientation Validation",
    target: "Target",
    yes: "Yes",
    no: "No",
    auto: "Auto",
    manual: "Manual",
    scene: "Scene",
    image: "Image",
    number: "Number",
    percentage: "Percentage",
    SceneList: "Scene List",
    AccountList: "Account List",
    name: "Name",
    department: "Department",
    selectDepartment: "Please select department",
    selectGenerateTaskDaily: "Please select whether to generate tasks daily",
    selectTaskReleaseType: "Please select task release type",
    selectTgtType: "Please select target type",
    selectImageRetrieval: "Please select image retrieval method",
    selectOrientationValidation: "Please select orientation validation method",
    enterTarget: "Please enter the target value",
    targetMinValue: "Target value cannot be less than 1",
    enterIsGenDaily: 'Please select whether to generate daily tasks',
    enterReleaseType: 'Please select the release type',
    enterTgtType: 'Please select the target type',
    enterImageRetrieval: 'Please select the image retrieval method',
    enterImgTgt: 'Please enter the target value',
    enterIsOriVal: 'Please select whether to validate orientation',
    pleaseSelectUsers: 'Please select users',
  },
  validitionTask: {
    unpublish: "Unpublish",
    unpublished: "Unpublished",
    publishedNotStarted: "Not Started",
    inProgress: "In Progress",
    notStarted: "Not Started",
    completed: "Completed",
    type: "Task Type",
    enterType: "Please enter task type",
    name: "Task Name",
    enterName: "Please enter task name",
    region: "Region",
    enterRegion: "Please enter region",
    country: "Country",
    enterCountry: "Please enter country",
    client: "Client",
    enterClient: "Please enter client",
    model_type: "Model Type",
    no_scenes: "No of Scenes to be Validated",
    enterModelType: "Please enter model type",
    expect_deadline: "Expected Deadline",
    selectDeadline: "Please select expected deadline",
    sub_task_name: "Sub Task Name",
    enterSubTaskName: "Please enter sub task name",
    start_time: "Start Time",
    selectStartTime: "Please select start time",
    image_received_on: "Image Received On",
    selectImageReceivedDate: "Please select image received date",
    scene_total: "Scene Total",
    enterSceneTotal: "Please enter scene total",
    requeued_and_validated: "Requeued and Validated",
    enterRequeuedAndValidated: "Please enter requeued and validated",
    specific_scene_ids: "Specific Scene IDs",
    enterSpecificSceneIds: "Please enter specific scene IDs",
    mds_available: "MDS Available",
    enterMdsAvailable: "Please enter MDS available",
    additional_document: "Additional Document",
    enterAdditionalDocument: "Please enter additional document",
    priority_level: "Priority Level",
    enterPriorityLevel: "Please enter priority level",
    attachments: "Attachments",
    enterAttachments: "Please enter attachments",
    remark: "Remark",
    enterRemark: "Please enter remark",
    publishSuccess: "Publish successful",
    publishFailed: "Publish failed",
  },
  scenesList: {
    title: "Scene List",
    name: "Scene",
    type: "Scene Type",
    sub_scene_type: "Subscene Type",
    img_received_on: "Date",
    validation_status: "Validation Status",
    id: "Sceneid",
    total_imgs: "Total Images",
    total_facings: "Total Facings",
    out_scene_ids: "SceneID",
    validator_ids: "Validator By",
    created_at: "Validation Time",
  },
  route: {
    "首页": "Dashboard",
    "系统管理": "System",
    "模型验证": "My Task",
    "项目列表": "Project List",
    "项目详情": "Project Detail",
    "新增项目": "Add Project",
    "任务详情": "Task Detail",
    "QA 任务详情": "QA Task Detail",
    "新增任务": "Add Task",
    "添加QA任务": "Add QA",
    "错误结果": "My Error Results",
    "MD 验证": "MD Validation",
    "MD 详情": "MD Detail",
  },
  error: {
    badRequest: "Bad request",
    notFound: "Request address does not exist",
    timeout: "Request timeout",
    serverError: "Internal server error",
    notImplemented: "Service not implemented",
    badGateway: "Bad gateway",
    serviceUnavailable: "Service unavailable",
    gatewayTimeout: "Gateway timeout",
    httpVersionNotSupported: "HTTP version not supported",
    connectionError: "Connection error",
    networkError: "Network error, please check your network connection"
  },
  mdValidation: {
    title: 'MD Validation',
    image: 'Image',
    brand: 'Brand',
    flavor: 'Flavor', 
    category: 'Category',
    shape: 'Shape',
    taskId: 'Task ID',
    sceneId: 'Scene ID',
    validatedOn: 'Validated On',
    validatedBy: 'Validated By',
    taskType: 'Task Type',
    status: 'Status',
    isDuplicated: 'Is Duplicated',
    isWrongMapping: 'Wrong Mapping',
    activeStatus: 'Active Status',
    outMdSkuId: 'SKU ID',
    projectId: 'Project ID',
    capacity: 'Capacity',
    productName: 'Product Name',
    id: 'ID',
    hasImg: 'Has Image',
    first_out_sku_id: 'First Out SKU ID',
    first_img_urls: 'First Image URLs',
    first_product_name: 'First Product Name',
    first_facing: 'First Facing',
    first_brand: 'First Brand',
    first_flavor: 'First Flavor', 
    first_shape: 'First Shape',
    first_capacity: 'First Capacity'
  }
}