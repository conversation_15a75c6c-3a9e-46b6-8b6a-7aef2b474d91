/**
 * 全局配置文件
 */
export default {
  method: "get",
  baseUrl: '/',
  // 请求头信息
  headers: {
    "Content-Type": "application/json;charset=UTF-8",
    // "Access-Control-Allow-Origin": "*",
    // "Access-Control-Allow-Credentials": true,
    Accept: "application/json",
    // "Authorization": "Bearer "
  },
  // 设置超时时间
  timeout: 5000,
  // 携带凭证
  withCredentials: true,
  // 返回数据类型
  responseType: "json",
};