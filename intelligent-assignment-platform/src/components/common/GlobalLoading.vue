<template>
  <div v-if="isVisible" class="global-loading-overlay">
    <div class="global-loading-container">
      <div class="loading-spinner"></div>
      <div class="loading-text">{{ loadingText }}</div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useGlobalLoadingStore } from '@/store/useGlobalLoadingStore'

const { t } = useI18n()
const globalLoadingStore = useGlobalLoadingStore()

// 计算属性：是否显示加载状态
const isVisible = computed(() => globalLoadingStore.isLoading)

// 计算属性：加载文本
const loadingText = computed(() => {
  return globalLoadingStore.loadingText || t('common.loading')
})
</script>

<style scoped>
.global-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity 0.3s ease;
}

.global-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-width: 200px;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-text {
  color: #666;
  font-size: 14px;
  text-align: center;
  line-height: 1.5;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .global-loading-overlay {
    background-color: rgba(0, 0, 0, 0.8);
  }
  
  .global-loading-container {
    background: #1f1f1f;
    color: #fff;
  }
  
  .loading-text {
    color: #ccc;
  }
  
  .loading-spinner {
    border-color: #333;
    border-top-color: #1890ff;
  }
}
</style>