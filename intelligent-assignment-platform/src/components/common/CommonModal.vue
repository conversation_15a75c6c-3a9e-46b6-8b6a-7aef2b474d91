<template>
    <a-modal v-model:open="modelValueLocal" :title="title" :width="width" :maskClosable="maskClosable" :closable="closable"
        :centered="centered" :footer="customFooter ? null : undefined" @cancel="handleCancel">
        <!-- 默认内容区域 -->
        <div class="my-4">
            <!-- 表单模式 -->
            <template v-if="formMode">
                <div class="form-container">
                    <!-- 表单项插槽 -->
                    <slot name="form-items">
                        <!-- 默认表单项 -->
                        <template v-for="(item, index) in formItems" :key="index">
                            <div class="mb-4" v-if="item.type === 'input' || item.type === 'number'">
                                <div class="flex items-center mb-2">
                                    <span class="text-[14px] font-medium">{{ item.label }}</span>
                                    <span v-if="item.required" class="text-red-500 ml-1">*</span>
                                </div>
                                <a-input v-if="item.type === 'input'" v-model:value="item.value"
                                    :placeholder="item.placeholder" class="w-full" />
                                <a-input-number v-if="item.type === 'number'" v-model:value="item.value" :min="item.min"
                                    :max="item.max" class="w-full" />
                            </div>

                            <div class="mb-4" v-else-if="item.type === 'checkbox-group'">
                                <div class="flex items-center mb-2">
                                    <span class="text-[14px] font-medium">{{ item.label }}</span>
                                    <span v-if="item.required" class="text-red-500 ml-1">*</span>
                                </div>
                                <div class="flex flex-wrap gap-4"
                                    :class="item.layout === 'grid' ? 'grid grid-cols-2' : ''">
                                    <div v-for="(option, optIndex) in item.options" :key="optIndex">
                                        <a-checkbox v-model:checked="option.checked">{{ option.label }}</a-checkbox>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </slot>
                </div>
            </template>

            <!-- 使用默认插槽允许自定义内容 -->
            <slot v-else></slot>
        </div>

        <!-- 自定义底部 -->
        <template v-if="customFooter" #footer>
            <slot name="footer">
                <div class="flex justify-end gap-2">
                    <a-button v-if="showCancel" @click="handleCancel">{{ t('common.cancel') }}</a-button>
                    <a-button v-if="showSave" :type="saveType" @click="handleSave">Save</a-button>
                </div>
            </slot>
        </template>
    </a-modal>
</template>

<script setup>
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
// 定义组件属性
const props = defineProps({
    // 控制弹窗显示/隐藏
    modelValue: {
        type: Boolean,
        required: true
    },
    // 弹窗标题
    title: {
        type: String,
        default: ''
    },
    // 弹窗宽度
    width: {
        type: [Number, String],
        default: 520
    },
    // 是否允许点击蒙层关闭
    maskClosable: {
        type: Boolean,
        default: true
    },
    // 是否显示右上角关闭按钮
    closable: {
        type: Boolean,
        default: true
    },
    // 是否居中显示
    centered: {
        type: Boolean,
        default: false
    },
    // 是否使用自定义底部
    customFooter: {
        type: Boolean,
        default: true
    },
    // 是否显示取消按钮
    showCancel: {
        type: Boolean,
        default: true
    },
    // 是否显示确认按钮
    showSave: {
        type: Boolean,
        default: true
    },
    // 取消按钮文本
    cancelText: {
        type: String,
        default: '取消'
    },
    // 确认按钮文本
    saveText: {
        type: String,
        default: '确定'
    },
    // 确认按钮类型
    saveType: {
        type: String,
        default: 'primary'
    },
    // 是否启用表单模式
    formMode: {
        type: Boolean,
        default: false
    },
    // 表单项配置
    formItems: {
        type: Array,
        default: () => []
    },
    // 表单布局
    formLayout: {
        type: String,
        default: 'vertical',
        validator: (value) => ['vertical', 'horizontal', 'inline'].includes(value)
    }
});

// 定义事件
const emit = defineEmits(['update:modelValue', 'cancel', 'save']);

// 处理取消事件
const handleCancel = () => {
    emit('update:modelValue', false);
    emit('cancel');
};

// 处理确认事件
const handleSave = () => {
    emit('save');
};

// 创建本地计算属性处理v-model
const modelValueLocal = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
});
</script>