# 全局 Loading 组件使用指南

## 概述

全局 Loading 组件提供了一个统一的加载状态显示方案，支持多语言、多并发请求计数和自定义加载文本。

## 组件结构

- `GlobalLoading.vue` - 全局加载组件
- `useGlobalLoadingStore.ts` - Pinia 状态管理
- `useGlobalLoading.ts` - Composable 函数

## 基本使用

### 1. 在组件中使用

```vue
<script setup>
import { useGlobalLoading } from '@/composable/useGlobalLoading'

const { showLoading, hideLoading, showRefreshing, showSaving } = useGlobalLoading()

// 基本用法
const handleSubmit = async () => {
  showLoading('提交中...')
  try {
    await submitData()
  } finally {
    hideLoading()
  }
}

// 使用预设的加载文本
const handleRefresh = async () => {
  showRefreshing() // 显示 "刷新中..."
  try {
    await refreshData()
  } finally {
    hideLoading()
  }
}

// 使用 withLoading 包装器（推荐）
const handleSave = useGlobalLoading().withLoading(async () => {
  await saveData()
}, '保存中...')
</script>
```

### 2. 在 API 请求中使用

```typescript
// api/example.ts
import { useGlobalLoading } from '@/composable/useGlobalLoading'

export const fetchUserData = async () => {
  const { withLoading } = useGlobalLoading()
  
  return withLoading(async () => {
    const response = await fetch('/api/users')
    return response.json()
  }, '获取用户数据中...')
}
```

## API 参考

### useGlobalLoading()

返回以下方法和状态：

#### 状态
- `isLoading` - 当前是否正在加载
- `loadingText` - 当前加载文本
- `loadingCount` - 当前加载计数

#### 基础方法
- `showLoading(text?: string)` - 显示加载状态
- `hideLoading()` - 隐藏加载状态
- `forceHideLoading()` - 强制隐藏所有加载状态
- `setLoadingText(text: string)` - 设置加载文本

#### 便捷方法
- `showRefreshing()` - 显示刷新状态
- `showProcessing()` - 显示处理状态
- `showSubmitting()` - 显示提交状态
- `showSaving()` - 显示保存状态

#### 高级方法
- `withLoading(asyncFn, loadingText?)` - 包装异步函数，自动管理加载状态

## 特性

### 1. 多并发支持
组件内部使用计数器机制，支持多个并发请求同时显示加载状态：

```javascript
// 同时发起多个请求
showLoading('请求1') // count: 1
showLoading('请求2') // count: 2
hideLoading()        // count: 1，仍然显示加载
hideLoading()        // count: 0，隐藏加载
```

### 2. 国际化支持
加载文本支持中英文切换，在语言包中定义：

```typescript
// zh-CN.ts
common: {
  loading: '加载中...',
  refreshing: '刷新中...',
  processing: '处理中...',
  submitting: '提交中...',
  saving: '保存中...'
}

// en-US.ts
common: {
  loading: 'Loading...',
  refreshing: 'Refreshing...',
  processing: 'Processing...',
  submitting: 'Submitting...',
  saving: 'Saving...'
}
```

### 3. 深色模式支持
组件自动适配系统的深色模式设置。

## 最佳实践

1. **使用 withLoading 包装器**：推荐使用 `withLoading` 来包装异步函数，它会自动管理加载状态。

2. **合理使用预设文本**：对于常见操作（刷新、保存、提交等），使用预设的便捷方法。

3. **避免嵌套调用**：在同一个操作流程中，避免重复调用 `showLoading`。

4. **错误处理**：确保在 `try-finally` 块中正确调用 `hideLoading`。

5. **自定义文本**：为特定业务场景提供有意义的加载文本。

## 注意事项

- 全局 Loading 组件已在 `App.vue` 中注册，无需在其他地方重复引入
- 组件使用固定的 z-index (9999)，确保在所有内容之上显示
- 加载状态会阻止用户交互，请谨慎使用
- 长时间的操作建议提供进度指示或允许用户取消