<template>
    <a-modal :open="open" width="500px" :title="t('user.modifyPassword')" @cancel="handleCancel">
        <a-form :model="formState" :rules="rules" ref="formRef" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" class="mt-4">
            <!-- <a-form-item :label="t('user.username')" name="username">
                <a-input v-model:value="formState.username" :placeholder="t('common.pleaseEnter')" />
            </a-form-item> -->
            <a-form-item :label="t('user.ori_password')" name="ori_password">
                <a-input-password v-model:value="formState.ori_password" :placeholder="t('common.pleaseEnter')" />
            </a-form-item>
            <a-form-item :label="t('user.new_password')" name="new_password">
                <a-input-password v-model:value="formState.new_password" :placeholder="t('common.pleaseEnter')" />
            </a-form-item>
        </a-form>
        <template #footer>
            <a-button @click="handleCancel">{{ t('common.cancel') }}</a-button>
            <a-button type="primary" @click="handleOk">{{ t('common.save') }}</a-button>
        </template>
    </a-modal>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useI18n } from 'vue-i18n';

const { t, locale } = useI18n();
locale.value = 'en-US';
const props = defineProps({
    open: {
        type: Boolean,
        default: false
    }
});
const emit = defineEmits(['update:open', 'submit']);
const formRef = ref();
const formState = reactive({
    // username: '',
    ori_password: '',
    new_password: '',
});

const rules = {
    username: [{ required: true, message: t('user.usernameRequired'), trigger: 'blur' }],
    ori_password: [{ required: true, message: t('user.ori_passwordRequired'), trigger: 'blur' }],
    new_password: [
        { required: true, message: t('user.new_passwordRequired'), trigger: 'blur' },
        { min: 6, message: t('user.passwordLengthTip') }
    ],
};

const handleOk = () => {
    formRef.value.validate().then(async() => {
        const submitData = {
            // username: formState.username,
            ori_password: formState.ori_password,
            new_password: formState.new_password
        };
        emit('submit', submitData);
        handleCancel();
    }).catch(error => {
        console.error('表单验证失败', error);
    });
};

const handleCancel = () => {
    emit('update:open', false);
    formRef.value?.resetFields();
};
</script>