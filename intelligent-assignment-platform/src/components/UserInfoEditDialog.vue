<template>
  <a-modal
    :open="open"
    width="500px"
    :title="$t('user.editInfo')"
    @cancel="handleCancel"
  >
    <a-form
      :model="formState"
      :rules="rules"
      ref="formRef"
      class="mt-4"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 20 }"
    >
      <a-form-item :label="$t('user.name')" name="name">
        <a-input v-model:value="formState.name" :placeholder="$t('common.pleaseEnter')" />
      </a-form-item>
      <a-form-item :label="$t('user.team')" name="team">
        <a-input v-model:value="formState.team" :placeholder="$t('common.pleaseEnter')" disabled/>
      </a-form-item>
      <a-form-item :label="$t('user.role')" name="role">
        <a-input v-model:value="formState.role" :placeholder="$t('common.pleaseEnter')" disabled/>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="handleCancel">{{ $t('common.cancel') }}</a-button>
      <a-button type="primary" @click="handleOk">{{ $t('common.save') }}</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  userData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:open', 'submit']);

const formRef = ref();
const formState = reactive({
  name: '',
  team: '',
  role: '',
});

// 表单验证规则
const rules = {
  name: [{ required: true, message: t('user.validateName') }],
};

// 监听对话框打开状态，当打开时初始化表单数据
watch(
  () => props.open,
  (newVal) => {
    if (newVal && props.userData) {
      formState.name = props.userData.name || '';
      formState.team = props.userData.team;
      formState.role = props.userData.role;
    }
  }
);

// 提交表单
const handleOk = () => {
  formRef.value.validate().then(() => {
    // 构建提交的数据对象
    const submitData = {
      name: formState.name,
      team: formState.team,
      role: formState.role
    };

    // 提交数据
    emit('submit', submitData);
    // message.success('用户信息更新成功');
    handleCancel();
  }).catch(error => {
    console.error('表单验证失败', error);
  });
};

// 取消操作
const handleCancel = () => {
  emit('update:open', false);
  // 重置表单
  formRef.value?.resetFields();
};
</script>