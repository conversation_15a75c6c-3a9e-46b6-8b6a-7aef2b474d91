<template>
    <div class="hot-figure-container" ref="container" @mousedown="startDrag" @mousemove="handleDrag" @mouseup="stopDrag"
        @mouseleave="stopDrag" @wheel="handleWheel">
        <!-- 加载状态显示 -->
        <div v-if="isLoading" class="loading-container">
            <div class="loading-spinner"></div>
            <div class="loading-text">Loading image...</div>
        </div>
        
        <div v-else class="image-wrapper"
            :style="{ transform: `scale(${zoomScale}) translate(${dragOffset.x}px, ${dragOffset.y}px)` }">
            <img v-if="imageUrl && isImageLoaded" :src="imageUrl" @load="onImageLoad" @error="onImageError" class="product-image" alt="Product Image">
            <!-- 绘制圆形小红点 - 只有在图片加载完成后才显示 -->
            <template v-if="imageUrl && isImageLoaded">
                <div v-for="(box, index) in boundingBoxes" :key="index" class="dot-marker" :style="getDotStyle(box)"
                        @click="handleBoxClick(box)">
                    <div class="dot-label">{{ getShortSkuName(box.sku_name) }}</div>
                </div>
            </template>
        </div>
    </div>
</template>

<script>
import { ref, reactive, onMounted, watch } from 'vue'
// 导入mock数据，实际项目中应该通过API获取
import { long_shot_data } from '@/mock/mockData'

export default {
    name: 'HotFigure',
    props: {
        canvasW: {
            type: Number,
            default: 480
        },
        canvasH: {
            type: Number,
            default: 400
        },
        // 可以接收初始数据
        initialData: {
            type: Object,
            default: () => ({})
        },
        // 是否显示所有红点
        showAllDots: {
            type: Boolean,
            default: false
        }
    },
    emits: ['imageFinshed', 'boxClick'],
    setup(props, { emit }) {
        // 图片URL
        const imageUrl = ref('')
        // 边界框数据
        const boundingBoxes = ref([])
        // 容器引用
        const container = ref(null)
        // 图片尺寸
        const imageSize = reactive({
            width: 0,
            height: 0
        })
        // 缩放比例
        const zoomScale = ref(1)
        // 拖拽偏移
        const dragOffset = reactive({ x: 0, y: 0 })
        // 拖拽状态
        const isDragging = ref(false)
        // 拖拽起始位置
        const dragStart = reactive({ x: 0, y: 0 })
        // 选中的边界框
        const selectedBox = ref(null)
        // 图片加载状态
        const isLoading = ref(false)
        const isImageLoaded = ref(false)
        // 图片预加载缓存
        const imageCache = new Map()

        // 加载模拟数据
        const loadMockData = async () => {
            try {
                // 使用导入的mock数据
                if (long_shot_data && long_shot_data.length > 0) {
                    const data = long_shot_data[0] // 获取第一个场景数据
                    await setData(data)
                }
            } catch (error) {
                console.error('加载数据失败:', error)
                isLoading.value = false
            }
        }

        // 图片预加载函数
        const preloadImage = (url) => {
            return new Promise((resolve, reject) => {
                // 检查缓存
                if (imageCache.has(url)) {
                    resolve(imageCache.get(url))
                    return
                }
                
                const img = new Image()
                img.onload = () => {
                    const imageData = {
                        url,
                        width: img.naturalWidth,
                        height: img.naturalHeight,
                        element: img
                    }
                    imageCache.set(url, imageData)
                    resolve(imageData)
                }
                img.onerror = () => {
                    reject(new Error(`Failed to load image: ${url}`))
                }
                img.src = url
            })
        }

        // 图片加载完成事件
        const onImageLoad = (event) => {
            imageSize.width = event.target.naturalWidth
            imageSize.height = event.target.naturalHeight
            isImageLoaded.value = true
            isLoading.value = false

            // 通知父组件图片加载完成
            emit('imageFinshed', {
                imageUrl: imageUrl.value,
                boundingBoxes: boundingBoxes.value,
                imageSize: {
                    width: imageSize.width,
                    height: imageSize.height
                }
            })
        }

        // 图片加载错误事件
        const onImageError = () => {
            console.error('图片加载失败:', imageUrl.value)
            isImageLoaded.value = false
            isLoading.value = false
        }

        // 红点大小状态变量
        const dotSize = ref(8);
        
        // 获取圆形小红点样式
        const getDotStyle = (box) => {
            // 计算圆点位置（使用边界框的中心点）
            const centerX = (box.tl[0] + box.tr[0]) / 2 * 100 + '%'
            const centerY = (box.tl[1] + box.bl[1]) / 2 * 100 + '%'

            
            // 根据showAllDots属性决定是否显示所有红点
            // 如果showAllDots为true，显示所有红点；否则只显示特殊SKU或选中的SKU
            const shouldShow = props.showAllDots || (selectedBox.value && selectedBox.value.sku_name === box.sku_name);

            // 仅当图片已加载、有点位数据且满足显示条件时才显示
            const displayValue = isImageLoaded.value && boundingBoxes.value.length > 0 && shouldShow ? 'block' : 'none';

            return {
                left: centerX,
                top: centerY,
                width: `${dotSize.value}px`,
                height: `${dotSize.value}px`,
                backgroundColor: '#ff0000',
                display: displayValue
            };
        }

        // 获取简短的SKU名称
        const getShortSkuName = (skuName) => {
            // 如果SKU名称太长，可以截取显示
            if (skuName === '百威啤酒金尊玻璃瓶装500毫升') {
                return '百威啤酒'
            }
            return skuName.length > 10 ? skuName.substring(0, 10) + '...' : skuName
        }

        // 处理边界框点击事件
        const handleBoxClick = (box) => {
            selectedBox.value = box
            emit('boxClick', box)
        }

        // 开始拖拽
        const startDrag = (event) => {
            isDragging.value = true
            dragStart.x = event.clientX - dragOffset.x
            dragStart.y = event.clientY - dragOffset.y
        }

        // 处理拖拽
        const handleDrag = (event) => {
            if (isDragging.value) {
                dragOffset.x = event.clientX - dragStart.x
                dragOffset.y = event.clientY - dragStart.y
            }
        }

        // 停止拖拽
        const stopDrag = () => {
            isDragging.value = false
        }

        // 处理滚轮缩放
        const handleWheel = (event) => {
            event.preventDefault()
            const delta = event.deltaY > 0 ? -0.1 : 0.1
            const newScale = Math.max(0.5, Math.min(3, zoomScale.value + delta))
            zoomScale.value = newScale
        }

        // 设置数据
        const setData = async (data) => {
            if (data && data.stitched_image) {
                isLoading.value = true
                isImageLoaded.value = false
                try {
                    // 预加载图片
                    await preloadImage(data.stitched_image)
                    imageUrl.value = data.stitched_image
                    isLoading.value = false
                    isImageLoaded.value = true
                } catch (error) {
                    console.error('图片预加载失败:', error)
                    isLoading.value = false
                }
            }
            if (data && data.bounding_box_list) {
                boundingBoxes.value = data.bounding_box_list
            }
        }

        // 监听props变化
        watch(() => props.initialData, (newVal) => {
            if (newVal && Object.keys(newVal).length > 0) {
                setData(newVal)
            }
        }, { immediate: true, deep: true })

        onMounted(() => {
            // 初始加载数据
            loadMockData()
        })

        // 图片缩放控制方法供父组件调用
        const increaseZoom = () => {
            const maxZoom = 3; // 最大缩放比例
            const zoomStep = 0.2; // 每次增加的缩放步长
            if (zoomScale.value < maxZoom) {
                zoomScale.value = Math.min(maxZoom, zoomScale.value + zoomStep);
            }
        };

        const decreaseZoom = () => {
            const minZoom = 0.5; // 最小缩放比例
            const zoomStep = 0.2; // 每次减少的缩放步长
            if (zoomScale.value > minZoom) {
                zoomScale.value = Math.max(minZoom, zoomScale.value - zoomStep);
            }
        };
        
        // 红点大小控制方法
        const increaseDotSize = () => {
            const maxDotSize = 24; // 最大红点大小
            const sizeStep = 2; // 每次增加的大小
            if (dotSize.value < maxDotSize) {
                dotSize.value = Math.min(maxDotSize, dotSize.value + sizeStep);
            }
        };

        const decreaseDotSize = () => {
            const minDotSize = 6; // 最小红点大小
            const sizeStep = 2; // 每次减少的大小
            if (dotSize.value > minDotSize) {
                dotSize.value = Math.max(minDotSize, dotSize.value - sizeStep);
            }
        };

        return {
            container,
            imageUrl,
            boundingBoxes,
            zoomScale,
            dragOffset,
            isLoading,
            isImageLoaded,
            getDotStyle,
            getShortSkuName,
            handleBoxClick,
            startDrag,
            handleDrag,
            stopDrag,
            handleWheel,
            onImageLoad,
            onImageError,
            setData,
            preloadImage,
            increaseZoom,
            decreaseZoom,
            increaseDotSize,
            decreaseDotSize,
            dotSize
        }
    }
}
</script>

<style scoped>
.hot-figure-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    cursor: grab;
    border-radius: 4px;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hot-figure-container:active {
    cursor: grabbing;
}

.image-wrapper {
    position: relative;
    transform-origin: center;
    transition: transform 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-image {
    display: block;
    max-width: 100%;
    max-height: 520px;
    height: auto;
    object-fit: contain;
}

.dot-marker {
    position: absolute;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
}

.dot-marker:hover {
    transform: translate(-50%, -50%) scale(1.2);
    background-color: #00ff00 !important;
}

.dot-label {
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 4px;
    font-size: 10px;
    border-radius: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.dot-marker:hover .dot-label {
    opacity: 1;
}

/* 加载状态样式 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 300px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

.loading-text {
    color: #666;
    font-size: 14px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>