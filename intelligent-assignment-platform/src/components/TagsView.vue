<template>
    <div class="navbar">
        <a-tabs v-model:activeKey="activeKey" type="editable-card" 
            @edit="(targetKey: string | number | MouseEvent | KeyboardEvent, action: 'add' | 'remove') => onEdit(targetKey as string | MouseEvent, action)"
            @change="(key: string | number) => onChange(String(key))">
                <a-tab-pane v-for="pane in panes" :key="pane.key" :tab="pane.title" :closable="pane.closable"></a-tab-pane>
        </a-tabs>
        <a-button v-if="panes.length === 0" type="link" @click="addTab('/user')">
            {{ $t('common.add') }}
        </a-button>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { Tabs as ATabs } from 'ant-design-vue';
import { useRouter, useRoute } from 'vue-router';

// 使用i18n
const { t } = useI18n();
const router = useRouter();
const route = useRoute();

// 定义标签页数据结构
interface TabPane {
    key: string;
    title: string;
    closable: boolean;
}

// 标签页数据
const panes = reactive<TabPane[]>([]);
const activeKey = ref<string>('');

// 路由路径与标题的映射
const routeTitleMap: Record<string, string> = {
    '/user': t('system.user'),
    '/depart': t('system.department'),
    '/role': t('system.role'),
    '/menu': t('system.menu'),
    // '/registration': t('system.registration')
};

// 添加标签页
const addTab = (path: string) => {
    const title = routeTitleMap[path] || path;
    const exists = panes.some(pane => pane.key === path);

    if (!exists) {
        panes.push({
            key: path,
            title,
            closable: true
        });
    }

    activeKey.value = path;
    router.push(path);
};

// 处理标签页编辑（添加/删除）
const onEdit = (targetKey: string | MouseEvent, action: 'add' | 'remove') => {
    if (action === 'remove') {
        removeTab(targetKey as string);
    }
};

// 删除标签页
const removeTab = (targetKey: string) => {
    const targetIndex = panes.findIndex(pane => pane.key === targetKey);

    if (targetIndex === -1) return;

    panes.splice(targetIndex, 1);

    // 如果关闭的是当前活动标签页，则需要激活其他标签页
    if (activeKey.value === targetKey) {
        if (panes.length) {
            // 优先选择右侧标签页，如果没有则选择左侧标签页
            const newActiveKey = panes[targetIndex] ? panes[targetIndex].key : panes[targetIndex - 1].key;
            activeKey.value = newActiveKey;
            router.push(newActiveKey);
        } else {
            // 如果没有标签页了，可以跳转到首页或其他默认页面
            router.push('/');
        }
    }
};

// 标签页切换事件
const onChange = (key: string) => {
    router.push(key);
};

// 监听路由变化，自动添加标签页
watch(
    () => route.path,
    (newPath) => {
        if (routeTitleMap[newPath]) {
            addTab(newPath);
        }
    }
);

// 组件挂载时，根据当前路由添加初始标签页
onMounted(() => {
    const currentPath = route.path;
    if (routeTitleMap[currentPath]) {
        addTab(currentPath);
    } else if (Object.keys(routeTitleMap).length > 0) {
        // 如果当前路径不在映射中，默认添加第一个标签页
        addTab(Object.keys(routeTitleMap)[0]);
    }
});
</script>

<style scoped>
.navbar {
    padding: 10px;
    background: #f0f0f0;
}

.navbar :deep(.ant-tabs-nav) {
    margin-bottom: 0;
}

.navbar :deep(.ant-tabs-tab) {
    padding: 8px 16px;
    border: 0;
}
</style>