<template>
    <div class="language-switcher">
        <a-dropdown>
            <a class="ant-dropdown-link">
                <!-- @click.prevent -->
                <GlobalOutlined /> {{ currentLang }}
                <!-- <DownOutlined /> -->
            </a>
            <!-- <template #overlay>
                <a-menu @click="changeLanguage">
                    <a-menu-item key="zh-CN">中文</a-menu-item>
                    <a-menu-item key="en-US">English</a-menu-item>
                </a-menu>
            </template> -->
        </a-dropdown>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';
import { GlobalOutlined, DownOutlined } from '@ant-design/icons-vue';

const { locale } = useI18n();

// 当前语言显示文本
const currentLang = computed(() => {
    return locale.value === 'zh-CN' ? '中文' : 'English';
});

// 切换语言
const changeLanguage = async (e: { key: string }) => {
    locale.value = e.key as 'zh-CN' | 'en-US';
    // 保存语言偏好到本地存储
    localStorage.setItem('language', e.key);

    // 等待DOM更新
    await nextTick();

    // 发出语言变更事件，通知其他组件
    window.dispatchEvent(new CustomEvent('language-changed', { detail: e.key }));
};

// 初始化时从本地存储加载语言偏好
const initLanguage = () => {
    const savedLanguage = localStorage.getItem('language');
    if (savedLanguage && (savedLanguage === 'zh-CN' || savedLanguage === 'en-US')) {
        locale.value = savedLanguage;
    }
    locale.value = 'en-US';
};

// 组件挂载时初始化语言
initLanguage();
</script>

<style scoped>
.language-switcher {
    cursor: pointer;
    color: white;
}
</style>