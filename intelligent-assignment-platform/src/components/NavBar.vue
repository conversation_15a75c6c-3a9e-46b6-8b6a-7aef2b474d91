<template>
  <div class="navbar">
    <router-link to="/user">{{ $t('system.user') }}</router-link>
    <router-link to="/depart">{{ $t('system.department') }}</router-link>
    <router-link to="/role">{{ $t('system.role') }}</router-link>
    <router-link to="/menu">{{ $t('system.menu') }}</router-link>
    <!-- <router-link to="/registration">{{ $t('system.registration') }}</router-link> -->
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

// 使用i18n
const { t } = useI18n();
</script>

<style scoped>
.navbar {
  padding: 10px;
  background: #f0f0f0;
}

.navbar a {
  margin-right: 10px;
}
</style>