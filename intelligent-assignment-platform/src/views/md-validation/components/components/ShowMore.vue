<template>
  <a-modal v-model:open="open" title="More SKU" width="80%" @cancel="handleCancel" @ok="handleOk" centered>
    <div class="flex w-full">
      <!-- 左侧图片区域 -->
      <div  class="w-200px max-h-650px overflow-y-auto">
        <div v-for="(img, idx) in images" :key="idx" class="bg-[#f5f5f5] flex flex-col items-center p-4 mb-4">
          <img
            :src="img"
            alt="sku-img"
            class="w-20 h-auto"
          />
          
        </div>
      </div>

      <!-- 分隔线 -->
      <div class="w-[1px] bg-[#F0F0F0] ml-4"></div>

      <!-- 右侧表格区域 -->
      <div class="flex-1 p-4 overflow-auto">
        <span class="text-[#000000] font-500 text-14px leading-20px">SKU List &nbsp;({{ reList.length }})</span>
        <a-table :columns="columns" :data-source="reList" class="mt-2" :pagination="pagination.paginationConfig" />
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref } from 'vue';
import { useTablePagination } from '@/composable/useTablePagination';

const pagination = useTablePagination({
    onChange: (page, size) => {
        handlePageChange(page, size);
    },
});
const open = ref(false);
const reList = ref([])
// 模拟图片地址，根据实际情况替换
const images = Array.from({ length: 5 }, () => 
  'https://re-sku-1255412942.cos.ap-shanghai.myqcloud.com/RE-SKU-LOGO-ZZ16478265668433.jpg'
);

// 表格列配置
const columns = [
  { title: 'SKU Code', dataIndex: 'out_sku_id', key: 'out_sku_id' },
  { title: 'Product Name', dataIndex: 'product_name', key: 'product_name' },
  { title: 'Brand', dataIndex: 'brand', key: 'brand' },
  { title: 'Flavor', dataIndex: 'flavor', key: 'flavor' },
  { title: 'Shape', dataIndex: 'shape', key: 'shape' },
  { title: 'Capacity', dataIndex: 'capacity', key: 'capacity' },
  { title: 'Facing', dataIndex: 'facing', key: 'facing' },
  { title: 'Status', dataIndex: 'status', key: 'status' }
];

const handlePageChange = async (page, size) => {
    pagination.page_no.value = page;
    pagination.page_size.value = size;
}

const handleOk = () => {
  open.value = false;
};

const handleCancel = () => {
  open.value = false;
};

const showMore = (data) => {
  open.value = true;
  reList.value = data
};

defineExpose({
  showMore
});
</script>