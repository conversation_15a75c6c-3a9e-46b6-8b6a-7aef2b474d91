<template>
  <div class="w-full p-4">
    <div class="flex gap-4 h-[85vh]">
      <!-- 左侧区域 -->
      <a-card title="Info" class="w-[30%] h-full">
        <template #extra>
            <div v-if="!isEdit" class="flex gap-4">
              <a class="text-[#8C8C8C]" @click="handleCancel">{{ t('common.cancel') }}</a>
              <a @click="handleSave" :loading="saving">
                {{ t('common.save') }}
              </a>
            </div>
            <a @click="handleEdit" v-else>Edit</a>
        </template>

        <div class="overflow-y-auto">
          <a-form :model="mdSkuInfo" layout="horizontal" label-align="right" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" class="mr-2">
            <a-form-item label="images">
                <div class="w-10 h-10 bg-gray-100 rounded flex items-center justify-center">
                  <img :src="mdSkuInfo.img_urls" alt="" class="w-[30px] h-[50px]"/>
                </div>
            </a-form-item>

            <a-form-item label="Model Type">
              <a-input :value="mdSkuInfo.is_duplicated === 0 ? 'null' : 'duplicated'" placeholder="Model Type" disabled/>
            </a-form-item>

            <a-form-item label="Client">
              <a-input v-model:value="mdSkuInfo.is_wrong_mapping" placeholder="Client" disabled/>
            </a-form-item>

            <a-form-item label="Shape">
              <a-input v-model:value="mdSkuInfo.out_md_sku_id" placeholder="Shape" :disabled="isEdit"/>
            </a-form-item>

            <a-form-item label="Country">
              <a-input v-model:value="mdSkuInfo.project_id" placeholder="Country" :disabled="isEdit"/>
            </a-form-item>

            <a-form-item label="Model Type">
              <a-input v-model:value="mdSkuInfo.brand" placeholder="Model Type" :disabled="isEdit"/>
            </a-form-item>

            <a-form-item label="Client">
              <a-input v-model:value="mdSkuInfo.flavor" placeholder="Client" :disabled="isEdit"/>
            </a-form-item>

            <a-form-item label="Brand">
              <a-input v-model:value="mdSkuInfo.category" placeholder="Brand" :disabled="isEdit"/>
            </a-form-item>

            <a-form-item label="Shape">
              <a-input v-model:value="mdSkuInfo.shape" placeholder="Shape" :disabled="isEdit"/>
            </a-form-item>

            <a-form-item label="Flavor">
              <a-input v-model:value="mdSkuInfo.capacity" placeholder="Flavor" :disabled="isEdit"/>
            </a-form-item>

            <a-form-item label="Category">
              <a-input v-model:value="mdSkuInfo.product_name" placeholder="Category" :disabled="isEdit"/>
            </a-form-item>

            <a-form-item label="Is Active">
              <a-switch v-model:checked="mdSkuInfo.active_status" :disabled="isEdit"/>
            </a-form-item>
          </a-form>
        </div>
      </a-card>
      
      <!-- 右侧区域 -->
      <div class="flex flex-col gap-4 w-[70%] h-full">
          <!-- RE List 区域 -->
          <a-card title="RE List" class="flex-1 flex flex-col">
              <template #extra>
                  <a-button type="link" @click="handleShowMore">Show More</a-button>
              </template>
              <div class="flex w-full">
                <!-- 左侧缩略图列表 -->
                <div class="w-[10%]">
                  <div class="max-h-[calc(85vh/3-120px)] overflow-y-auto">
                    <div v-for="(img, index) in reList[selectedIndex]?.img_urls" :key="index" 
                        @click="activeIndex = index"
                        class="bg-btn-primary m-2 mt-1 w-18 h-18 flex items-center justify-center rounded-md transition-all duration-200 hover:bg-[#e6e6e6] cursor-pointer"
                        :class="{ 'ring-2 ring-[#FF4D4F] shadow-md': activeIndex === index }">
                      <img :src="img" alt="" class="w-[30px] h-[50px]" />
                    </div>
                  </div>
                </div>

                <!-- 中间大图 -->
                <div class="w-[30%] flex items-center justify-center bg-btn-primary ml-4">
                  <img 
                    :src="reList[selectedIndex]?.img_urls?.[activeIndex]" 
                    alt=""  
                    class="w-[56px] h-[160px]"
                  >
                </div>

                <!-- 分隔线 -->
                <div class="w-[1px] bg-[#F0F0F0] h-[calc(85vh/3-120px)] ml-[25px] mr-[25px]"></div>

                <!-- 右侧列表 -->
                <div class="w-[60%]">
                  <div class="overflow-y-auto max-h-[calc(85vh/3-120px)]">
                    <a-list item-layout="horizontal" :data-source="reList">
                      <template #renderItem="{ item, index }">
                        <a-list-item @click="handleItemClick(index)" 
                          :class="['border border-solid mb-2 rounded-[4px] relative px-2 py-3',
                                    selectedIndex === index ? 'bg-[#1677FF1A] border-primary1' : 'border-[#D9D9D9]'
                                  ]"
                          :style="{ borderBlockEnd: selectedIndex === index ? '1px solid #1677FF' : '1px solid #D9D9D9'}">
                          <div v-if="selectedIndex === index" class="absolute left-0 top-0 bottom-0 w-[6px] bg-primary1" />
                          <a-list-item-meta>
                            <template #description>
                              <div class="flex items-center">
                                <div>{{ item.out_sku_id }}</div>
                                <div class="flex-1"></div>
                                <div>Facing: {{ item.facing }}</div>
                              </div>
                            </template>
                            <template #title>
                              <div style="font-weight: bold;">{{ item.product_name }}</div>
                            </template>
                            <template #avatar>
                              <img :src="item.img_urls[index]" alt="" class="w-[30px] h-[50px]">
                            </template>
                          </a-list-item-meta>
                        </a-list-item>
                      </template>
                    </a-list>
                  </div>
                </div>
              </div>
          </a-card>
          
          <!-- Similar List 区域 -->
          <a-card title="Similar List" class="flex-1 flex flex-col">
            <template #extra>
                <a-button type="primary" @click="handleAction()">Action</a-button>
            </template>
            <a-table
              :columns="similarColumns"
              :data-source="similarList"
              :pagination="false"
              :row-selection="{ selectedRowKeys: selectedSkuKeys, onChange: onSelectSkuChange }"
              row-key="id"
              :scroll="{ y: 'calc(85vh/3 - 165px)' }"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'img_urls'">
                  <div class="w-10 h-10 bg-gray-100 rounded flex items-center justify-center">
                    <img v-if="record.img_urls" :src="record.img_urls" alt="" class="w-[30px] h-[50px]"/>
                    <span v-else class="text-gray-400 text-xs">No Image</span>
                  </div>
                </template>
                
                <template v-if="column.dataIndex === 'active_status'">
                  <a-dropdown>
                    <a class="ant-dropdown-link" @click="handleChange(record)">
                      {{ record.active_status ? 'Yes' : 'No' }}
                      <DownOutlined />
                    </a>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item>
                          <a>{{ record.isActive ? 'No' : 'Yes' }}</a>
                        </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </template>
              </template>
            </a-table>
          </a-card>
          
          <!-- Project List 区域 -->
          <a-card title="Project List" class="flex-1 flex flex-col">
            <template #extra>
                <a-button type="primary" @click="handleSync">Sync</a-button>
            </template>
            <a-table
              :columns="projectColumns"
              :data-source="projectList"
              :pagination="false"
              :row-selection="rowSelection"
              row-key="id"
              :scroll="{ y: 'calc(85vh/3 - 165px)' }"
              class="custom-table"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'is_duplicated'">
                  {{ record.is_duplicated === 0 ? 'null' : 'duplicated' }}
                </template>
                <template v-if="column.key === 'active_status'">
                  {{ record.active_status === 0 ? 'not active' : 'active' }}
                </template>
                <template v-if="column.key === 'img_urls'">
                  <div class="w-10 h-10 bg-gray-100 rounded flex items-center justify-center">
                    <img v-if="record.img_urls" :src="record.img_urls" alt="" class="w-[30px] h-[50px]"/>
                    <span v-else class="text-gray-400 text-xs">No Image</span>
                  </div>
                </template>
              </template>
            </a-table>
          </a-card>
      </div>
    </div>

    <a-card class="mt-4">
      <div class="flex justify-between items-center">
        <a-alert message="Submitting this action is irreversible. Proceed with caution." type="info" show-icon :style="{ backgroundColor: 'transparent', border: 'none' }" />
        <div class="flex gap-2">
          <a-button type="primary" class="bg-[#52C41A]" @click="handleSubmit" :disabled="!isEdit">{{ !isEdit ? 'Submit' : 'Remove Anomaly'}}</a-button>
          <a-button type="primary" @click="handleClose">Close</a-button>
        </div>
      </div>
    </a-card>

    <show-more ref="showMoreRef" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n';
import { DownOutlined } from '@ant-design/icons-vue';
import ShowMore from './components/ShowMore.vue'
import { showConfirm } from '@/utils/confirm';
import { message } from 'ant-design-vue';
import { reskusApi, duplicatesApi, reprojectInfoApi, reprojectInfoSyncApi, mdSkuDetailApi, putMdSkuApi  } from '../../../api/md-validation/index'

const router = useRouter()
const route = useRoute()
const { t } = useI18n();
const selectedKeys = ref([]);
const disabledKeys = ref([]);
const showMoreRef = ref(null);
const selectedSkuKeys = ref([]);
const props = defineProps({
  mdSkuId: {
    type: Number,
    default: null
  }
});
const isEdit = ref(true)
// 默认选中第一条
const selectedIndex = ref(0); 

// 点击列表项时触发，更新选中索引
const handleItemClick = (index) => {
  selectedIndex.value = index;
};

const reList = ref(Array.from({ length: 20 }, (_, index) => ({
  out_sku_id: `SKU${123456 + index}`,
  img_urls: [
    'https://re-sku-1255412942.cos.ap-shanghai.myqcloud.com/RE-SKU-LOGO-ZZ16478265668433.jpg',
    'https://re-sku-1255412942.cos.ap-shanghai.myqcloud.com/RE-SKU-LOGO-ZZ16478265668433.jpg',
    'https://re-sku-1255412942.cos.ap-shanghai.myqcloud.com/RE-SKU-LOGO-ZZ16478265668433.jpg',
    'https://re-sku-1255412942.cos.ap-shanghai.myqcloud.com/RE-SKU-LOGO-ZZ16478265668433.jpg',
    'https://re-sku-1255412942.cos.ap-shanghai.myqcloud.com/RE-SKU-LOGO-ZZ16478265668433.jpg',
    'https://re-sku-1255412942.cos.ap-shanghai.myqcloud.com/RE-SKU-LOGO-ZZ16478265668434.jpg',
  ],
  product_name: 'Glaceau Vitaminwater Energy Tropical Citrus',
  facing: 2,
  brand: 'Vitaminwater',
  flavor: 'Tropical Citrus', 
  shape: 'Plastic Bottle',
  capacity: '500ml'
})))

const getReSku = async() => {
  const res = await reskusApi(props.mdSkuId)
  if(res.code === '0000') {
    //  reList.value = res.data?.data_list
  }
}

// 当前选中的缩略图索引
const activeIndex = ref(0);

// 详情数据
const mdSkuInfo = ref({
  is_duplicated: Math.round(Math.random()), // 随机生成0或1
  is_wrong_mapping: Math.round(Math.random()), // 随机生成0或1
  active_status: Math.round(Math.random()), // 随机生成0或1
  out_md_sku_id: `SKU${Math.floor(10000 + Math.random() * 90000)}`, // 随机生成SKU ID
  project_id: Math.floor(1000 + Math.random() * 9000), // 随机生成项目ID
  img_urls: 'https://re-sku-1255412942.cos.ap-shanghai.myqcloud.com/RE-SKU-LOGO-ZZ16478265668433.jpg',
  brand: ['Coca-Cola', 'Pepsi', 'Sprite', 'Fanta'][Math.floor(Math.random() * 4)],
  flavor: ['Original', 'Cherry', 'Vanilla', 'Lemon'][Math.floor(Math.random() * 4)],
  category: ['Carbonated', 'Energy', 'Sports', 'Water'][Math.floor(Math.random() * 4)],
  shape: ['Can', 'Bottle', 'Glass', 'PET'][Math.floor(Math.random() * 4)],
  capacity: ['330ml', '500ml', '1L', '2L'][Math.floor(Math.random() * 4)],
  product_name: `Product ${Math.floor(1000 + Math.random() * 9000)}`,
  id: Math.floor(1000 + Math.random() * 9000),
})

const getMdSkuInfo = async()=> {
  const res = await mdSkuDetailApi(props.mdSkuId)
  if(res.code === '0000') {
    // mdSkuInfo.value = res.data
  } 
}

// Similar List 表格列定义
const similarColumns = [
  { title: 'SKU ID', dataIndex: 'out_md_sku_id', key: 'out_md_sku_id', width: 120 },
  { title: 'Project ID', dataIndex: 'project_id', key: 'project_id', width: 100 },
  { title: 'Image', dataIndex: 'img_urls', key: 'img_urls', width: 120 },
  { title: 'Brand', dataIndex: 'brand', key: 'brand', width: 120 },
  { title: 'Flavor', dataIndex: 'flavor', key: 'flavor', width: 120 },
  { title: 'Category', dataIndex: 'category', key: 'category', width: 120, ellipsis: true },
  { title: 'Shape', dataIndex: 'shape', key: 'shape', width: 120 },
  { title: 'Capacity', dataIndex: 'capacity', key: 'capacity', width: 100, ellipsis: true },
  { title: 'Product Name', dataIndex: 'product_name', key: 'product_name', width: 150 },
  { title: 'Is Duplicated', dataIndex: 'is_duplicated', key: 'is_duplicated', width: 150},
  { title: 'Wrong Mapping', dataIndex: 'is_wrong_mapping', key: 'is_wrong_mapping', width: 120, ellipsis: true },
  { title: 'Is Active?', dataIndex: 'active_status', key: 'active_status', width: 120, fixed: 'right' }
]

const similarList = ref(Array.from({ length: 10 }, () => ({
    is_duplicated: Math.round(Math.random()), // 随机生成0或1
    is_wrong_mapping: Math.round(Math.random()), // 随机生成0或1
    active_status: Math.round(Math.random()), // 随机生成0或1
    out_md_sku_id: `SKU${Math.floor(10000 + Math.random() * 90000)}`, // 随机生成SKU ID
    project_id: Math.floor(1000 + Math.random() * 9000), // 随机生成项目ID
    img_urls: 'https://re-sku-1255412942.cos.ap-shanghai.myqcloud.com/RE-SKU-LOGO-ZZ16478265668433.jpg',
    brand: ['Coca-Cola', 'Pepsi', 'Sprite', 'Fanta', 'Dr Pepper'][Math.floor(Math.random() * 5)],
    flavor: ['Original', 'Cherry', 'Vanilla', 'Lemon', 'Orange'][Math.floor(Math.random() * 5)],
    category: ['Carbonated Drinks', 'Energy Drinks', 'Sports Drinks', 'Water', 'Tea'][Math.floor(Math.random() * 5)],
    shape: ['Can', 'Bottle', 'Glass Bottle', 'PET Bottle', 'Tetra Pack'][Math.floor(Math.random() * 5)],
    capacity: ['330ml', '500ml', '1L', '1.5L', '2L'][Math.floor(Math.random() * 5)],
    product_name: `Product ${Math.floor(1000 + Math.random() * 9000)}`,
    id: Math.floor(1000 + Math.random() * 9000)
  })))

const getDuplicates = async() => {
  const res = await duplicatesApi(props.mdSkuId);
  if(res.code === '0000') {
    // similarList.value = res.data?.data_list
  }
}

// Project List 表格列定义
const projectColumns = [
  { title: 'Country', dataIndex: 'country', key: 'country', width: 100 },
  { title: 'Client', dataIndex: 'client', key: 'client', width: 100 },
  { title: 'Model Type', dataIndex: 'model_type', key: 'model_type', width: 120 },
  { title: 'SKU ID', dataIndex: 'out_md_sku_id', key: 'out_md_sku_id', width: 120 },
  { title: 'Project ID', dataIndex: 'project_id', key: 'project_id', width: 100 },
  { title: 'Brand', dataIndex: 'brand', key: 'brand', width: 100 },
  { title: 'Flavor', dataIndex: 'flavor', key: 'flavor', width: 100 },
  { title: 'Category', dataIndex: 'category', key: 'category', width: 120 },
  { title: 'Shape', dataIndex: 'shape', key: 'shape', width: 100 },
  { title: 'Capacity', dataIndex: 'capacity', key: 'capacity', width: 100 },
  { title: 'Product Name', dataIndex: 'product_name', key: 'product_name', width: 150 },
  { title: 'Is Duplicated', dataIndex: 'is_duplicated', key: 'is_duplicated', width: 120 },
  { title: 'Wrong Mapping', dataIndex: 'is_wrong_mapping', key: 'is_wrong_mapping', width: 120 },
  { title: 'Is Active', dataIndex: 'active_status', key: 'active_status', width: 100 },
]

const projectList = ref(Array.from({ length: 10 }, (_, index) => ({
    is_duplicated: Math.round(Math.random()), // 随机生成0或1
    is_wrong_mapping: Math.round(Math.random()), // 随机生成0或1
    active_status: Math.round(Math.random()), // 随机生成0或1
    out_md_sku_id: `SKU${Math.floor(10000 + Math.random() * 90000)}`, // 随机生成SKU ID
    project_id: Math.floor(1000 + Math.random() * 9000), // 随机生成项目ID
    img_urls: 'https://re-sku-1255412942.cos.ap-shanghai.myqcloud.com/RE-SKU-LOGO-ZZ16478265668433.jpg', // 随机图片
    brand: ['Coca-Cola', 'Pepsi', 'Sprite', 'Fanta'][Math.floor(Math.random() * 4)],
    flavor: ['Original', 'Cherry', 'Vanilla', 'Lemon'][Math.floor(Math.random() * 4)],
    category: ['Carbonated', 'Energy', 'Sports', 'Water'][Math.floor(Math.random() * 4)],
    shape: ['Can', 'Bottle', 'Glass', 'PET'][Math.floor(Math.random() * 4)],
    capacity: ['330ml', '500ml', '1L', '2L'][Math.floor(Math.random() * 4)],
    product_name: `Product ${Math.floor(1000 + Math.random() * 9000)}`,
    id: Math.floor(1000 + Math.random() * 9000),
    model_type: ['Type A', 'Type B', 'Type C'][Math.floor(Math.random() * 3)],
    country: ['USA', 'China', 'UK', 'Japan'][Math.floor(Math.random() * 4)],
    client: ['Client A', 'Client B', 'Client C'][Math.floor(Math.random() * 3)],
  })))

const getProject = async() => {
  const res = await reprojectInfoApi(props.mdSkuId)
  if(res.code === '0000') {
    // projectList.value = res.data.data_list
  }
}

const handleCancel = () => {
  isEdit.value = true
}

const handleSave = async() => {
  const res = await putMdSkuApi(props.mdSkuId, mdSkuInfo.value)
  if(res.code === '0000') {
     mdSkuInfo.value = res.data
     message.success('Saved successfully')
  }
  isEdit.value = true
}

const handleEdit = () => {
  isEdit.value = false
}

const rowSelection = ref({
  onChange: (selectedRowKeys) => {
    selectedKeys.value = selectedRowKeys;
  },
  getCheckboxProps: (record) => ({
    // 当isEdit为false时禁用所有checkbox选择
    disabled: isEdit.value === false || disabledKeys.value.includes(record.id),
  }),
  selectedRowKeys: selectedKeys
});

const handleAction = async () => {
    try {
        const res = await showConfirm({
            title: 'Are you sure you want to execute batch operation?',
            content: `action is final and cannot be undone.`,
            cancelText: 'Unable',
            okText: 'Able',
            cancelButtonProps: { type: 'primary', danger: true},
            async onOk() {
              await new Promise((resolve) => setTimeout(resolve, 500));
              return "0000"
            },
        });
        if(res === '0000') {
          message.success('操作成功');
        }
    } catch (e) {
    }
}

const handleSubmit = async () => {
  try {
    const res = await showConfirm({
      title: 'Are you sure you want to submit the record?',
      content: `action is final and cannot be undone.`,
      cancelText: 'Cancel',
      okText: 'Confirm',
      async onOk() {
        await new Promise((resolve) => setTimeout(resolve, 500));
        return "0000"
      },
    });
    if(res === '0000') {
      message.success('操作成功');
    }
  } catch (e) {
  }
}

const handleChange = async (record) => {
  try {
    const res = await showConfirm({
      title: 'Are you sure you want to Disable SKU?',
      content: `action is final and cannot be undone.`,
      cancelText: 'No',
      okText: 'Yes',
      async onOk() {
        await new Promise((resolve) => setTimeout(resolve, 500));
        // 更新状态
        record.active_status = !record.active_status;
        return "0000"
      },
    });
    if(res === '0000') {
      message.success(`SKU has been ${record.active_status ? 'enabled' : 'disabled'} successfully`);
    }
  } catch (e) {
  }
}

const handleSync = async()=> {
  const res = await reprojectInfoSyncApi(props.mdSkuId)
  if(res.code === '0000') {
    message.success(res.message || 'Sync successful')
  }
}

const onSelectSkuChange = (selectedRowKeys) => {
  selectedSkuKeys.value = selectedRowKeys;
};

const handleClose = () => {
  if(route.query.isTab === 'true') {
    router.push({
      path: `/project-detail`,
      query: { 
        id: props.mdSkuId,
        tab: 'mdList'
      }
    });
  } else {
    router.back()
  }
}

const handleShowMore = () => {
  showMoreRef.value.showMore(reList.value);
};
onMounted(() => {
  Promise.all([getReSku(), getDuplicates(), getProject(), getMdSkuInfo()])
})
</script>


<style scoped>
.selected-item {
  border: 2px solid blue !important; 
  margin: 12px;
}
</style>