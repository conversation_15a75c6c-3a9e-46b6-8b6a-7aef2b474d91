<template>
  <div class="w-full">
    <a-tabs v-model:activeKey="activeTab" :tab-bar-style="{ borderRadius: 0 }">
      <a-tab-pane key="basic-info" :tab="$t('common.basicInfo')">
        <basic-info :mdSkuId="mdSkuId"/>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import BasicInfo from './components/BasicInfo.vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const mdSkuId = ref(route.query.id ? Number(route.query.id) : null);
const activeTab = ref('basic-info');
</script>

<style scoped>
:deep(.ant-tabs-nav) {
    padding-left: 16px;
    height: 56px;
    background-color: #fff;
}
.ant-card {
  height: 67vh;
}
</style>