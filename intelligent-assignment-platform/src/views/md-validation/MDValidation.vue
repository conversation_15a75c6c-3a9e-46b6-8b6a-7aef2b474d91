<template>
    <div class="p-4">
      <a-card>
        <a-spin :spinning="loading">
          <div class="flex">
            <a-form :model="formState" layout="horizontal" class="flex-1" ref="basicFormRef" label-align="right" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <a-row :gutter="24">
                <a-col :span="8">
                    <a-form-item :label="t('validitionTask.name')" name="active_status">
                      <a-select
                        v-model:value="formState.active_status"
                        :placeholder="t('validitionTask.enterName')"
                        allowClear
                        :default-value="1"
                      >
                        <a-select-option :value="0">未禁用</a-select-option>
                        <a-select-option :value="1">禁用</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                <a-col :span="8">
                    <a-form-item :label="t('validitionTask.name')" name="name">
                      <a-select v-model:value="formState.flavor" :placeholder="t('common.pleaseSelect')" style="width: 100%" class="rounded-md">
                        <a-select-option v-for="type in flavorOptions" :key="type" :value="type">{{ type }}</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                <a-col :span="8">
                    <a-form-item :label="t('validitionTask.name')" name="name">
                      <a-select v-model:value="formState.flavor" :placeholder="t('common.pleaseSelect')" style="width: 100%" class="rounded-md">
                        <a-select-option v-for="type in brandOptions" :key="type" :value="type">{{ type }}</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
              </a-row>
              
              <div v-show="isExpanded">
                <a-row :gutter="24">
                  <a-col :span="8">
                    <a-form-item :label="t('validitionTask.name')" name="name">
                      <a-select v-model:value="formState.flavor" :placeholder="t('common.pleaseSelect')" style="width: 100%" class="rounded-md">
                        <a-select-option v-for="type in categoryOptions" :key="type" :value="type">{{ type }}</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item :label="t('validitionTask.sub_task_name')" name="sub_task_name">
                      <a-select v-model:value="formState.flavor" :placeholder="t('common.pleaseSelect')" style="width: 100%" class="rounded-md">
                        <a-select-option v-for="type in shapeOptions" :key="type" :value="type">{{ type }}</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item :label="t('validitionTask.sub_task_name')" name="sub_task_name">
                      <a-input v-model:value="formState.country" :placeholder="t('validitionTask.enterSubTaskName')" allowClear/> 
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-row :gutter="24">
                  <a-col :span="8">
                    <a-form-item :label="t('validitionTask.name')" name="name">
                      <a-input v-model:value="formState.client" :placeholder="t('validitionTask.enterName')" allowClear/>
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item :label="t('validitionTask.sub_task_name')" name="sub_task_name">
                      <a-input v-model:value="formState.model_type" :placeholder="t('validitionTask.enterSubTaskName')" allowClear/> 
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item :label="t('validitionTask.sub_task_name')" name="sub_task_name">
                      <a-input v-model:value="formState.client" :placeholder="t('validitionTask.enterSubTaskName')" allowClear/> 
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-row :gutter="24">
                  <a-col :span="8">
                    <a-form-item :label="t('validitionTask.name')" name="name">
                      <a-input v-model:value="formState.client" :placeholder="t('validitionTask.enterName')" allowClear/>
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item :label="t('validitionTask.sub_task_name')" name="sub_task_name">
                      <a-input v-model:value="formState.client" :placeholder="t('validitionTask.enterSubTaskName')" allowClear/> 
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item :label="t('validitionTask.sub_task_name')" name="sub_task_name">
                      <a-input v-model:value="formState.client" :placeholder="t('validitionTask.enterSubTaskName')" allowClear/> 
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-row :gutter="24">
                  <a-col :span="8">
                    <a-form-item :label="t('validitionTask.name')" name="name">
                      <a-input v-model:value="formState.client" :placeholder="t('validitionTask.enterName')" allowClear/>
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item :label="t('validitionTask.sub_task_name')" name="sub_task_name">
                      <a-input v-model:value="formState.client" :placeholder="t('validitionTask.enterSubTaskName')" allowClear/> 
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item :label="t('validitionTask.sub_task_name')" name="sub_task_name">
                      <a-input v-model:value="formState.client" :placeholder="t('validitionTask.enterSubTaskName')" allowClear/> 
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-row :gutter="24">
                  <a-col :span="8">
                    <a-form-item :label="t('validitionTask.name')" name="name">
                      <a-input v-model:value="formState.client" :placeholder="t('validitionTask.enterName')" allowClear/>
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item :label="t('validitionTask.sub_task_name')" name="sub_task_name">
                      <a-input v-model:value="formState.client" :placeholder="t('validitionTask.enterSubTaskName')" allowClear/> 
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item :label="t('validitionTask.sub_task_name')" name="sub_task_name">
                      <a-input v-model:value="formState.client" :placeholder="t('validitionTask.enterSubTaskName')" allowClear/> 
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>
            </a-form>

            <a-button type="link" @click="handleExpand" class="flex ml-1">
              {{ t('common.expand') }}
              <span class="ml-1"> <DownOutlined v-if="!isExpanded" /><UpOutlined v-else /></span>
            </a-button>
            <a-button @click="handleExpand" class="flex ml-1">
              Reset
            </a-button>
            <a-button type="primary" @click="handleExpand" class="flex ml-1">
              Search
            </a-button>
          </div>
        </a-spin>
      </a-card>

      <a-card title="SKU List" class="mt-[14px] h-[80vh]">
        <a-spin :spinning="loading">
            <div class="w-full">
                <a-table
                  :columns="columns"
                  :data-source="skuData"
                  :loading="loading"
                  size="small"
                  :scroll="{ x: 'max-content' }"
                  :pagination="pagination.paginationConfig">
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'img_urls'">
                      <img :src="record.img_urls" alt="" class="w-[30px] h-[50px]" />
                    </template>
                    <template v-if="column.key === 'first_re_sku_img_urls'">
                      <img 
                        :src="record.first_re_sku.img_urls[0]" 
                        alt="" 
                        class="w-[30px] h-[50px]" 
                        @mouseenter="() => {
                          show = true;
                          images = record.first_re_sku.img_urls;
                        }" 
                        @mouseleave="show = false"
                      />
                    </template>
                    <template v-if="column.key === 'active_status'">
                      <span :style="{ color: record.active_status === 0 ? '#52c41a' : record.active_status === 1 ? '#ff4d4f' : 'inherit' }">
                        {{ record.active_status === 0 ? 'Not Disabled' : record.active_status === 1 ? 'Disabled' : record.active_status }}
                      </span>
                    </template>
                    <template v-if="column.key === 'action'">
                     <a-button type="link" size="small" @click="handleDetail(record.id)">Detail</a-button>
                    </template>
                  </template>
                </a-table>
            </div>
        </a-spin>
      </a-card>

      <div v-if="show"
        class="absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] bg-white border border-solid border-[#ccc] shadow-[0_2px_8px_rgba(0,0,0,0.15)] p-16px z-100 min-w-400px"
        @mouseenter="show = true"
        @mouseleave="show = false">
        <span class="mb-2 font-bold">RE SKU Images</span>
        <div class="flex gap-5 flex-wrap mt-4">
          <img
            v-for="(img, idx) in images"
            :key="idx"
            :src="img"
            style="width:60px; height:100px; object-fit:cover; background:#eee;"
          />
        </div>
      </div>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter } from 'vue-router';
import { UpOutlined, DownOutlined } from "@ant-design/icons-vue";
import { useI18n } from 'vue-i18n';
import { useTablePagination } from '@/composable/useTablePagination';
import { dataSourceApi, mdListApi } from '../../api/md-validation'

const pagination = useTablePagination({
    onChange: (page, size) => {
        handlePageChange(page, size);
    },
});
const props = defineProps({
  clientId: {
    type: Number,
    default: null
  }
});
const { t } = useI18n();
const router = useRouter();
const loading = ref(false);
const isExpanded = ref(false);
const skuData = ref([]);
const selectedKeys = ref([]);
const disabledKeys = ref([]);
const show = ref(false)
const flavorOptions = ref([])
const brandOptions = ref([])
const categoryOptions = ref([])
const shapeOptions = ref([])

const formState = reactive({
  project_id: props.clientId,
  active_status: null,
  flavor: '',
  brand: '',
  category: '',
  shape: '',
  country: '',
  client: '',
  model_type: '',
});

// 筛选条件
const filters = reactive({
    brand: undefined,
    flavor: undefined,
    category: undefined,
});
const rowSelection = ref({
  onChange: (selectedRowKeys) => {
    selectedKeys.value = selectedRowKeys;
  },
  getCheckboxProps: (record) => ({
    disabled: disabledKeys.value.includes(record.id),
  }),
  selectedRowKeys: selectedKeys
});
// 表格列定义
const columns = [
    { title: t('mdValidation.id'), dataIndex: 'id', key: 'id' },
    { title: t('mdValidation.outMdSkuId'), dataIndex: 'out_md_sku_id', key: 'out_md_sku_id' },
    { title: t('mdValidation.image'), dataIndex: 'img_urls', key: 'img_urls' },
    { title: t('mdValidation.isDuplicated'), dataIndex: 'is_duplicated', key: 'is_duplicated' },
    { title: t('mdValidation.isWrongMapping'), dataIndex: 'is_wrong_mapping', key: 'is_wrong_mapping' },
    { title: t('mdValidation.activeStatus'), dataIndex: 'active_status', key: 'active_status' },
    { title: t('mdValidation.projectId'), dataIndex: 'project_id', key: 'project_id' },
    { title: t('mdValidation.brand'), dataIndex: 'brand', key: 'brand',    
      sorter: {
        compare: (a, b) => a.brand - b.brand,
        multiple: 1,
      }, 
    },
    { title: t('mdValidation.flavor'), dataIndex: 'flavor', key: 'flavor',
      sorter: {
        compare: (a, b) => a.flavor - b.flavor,
        multiple: 2,
      }, 
     },
    { title: t('mdValidation.category'), dataIndex: 'category', key: 'category',
      sorter: {
        compare: (a, b) => a.category - b.category,
        multiple: 3,
      }, 
     },
    { title: t('mdValidation.shape'), dataIndex: 'shape', key: 'shape',
      sorter: {
        compare: (a, b) => a.shape - b.shape,
        multiple: 4,
      },
     },
    { title: t('mdValidation.capacity'), dataIndex: 'capacity', key: 'capacity' },
    { title: t('mdValidation.productName'), dataIndex: 'product_name', key: 'product_name' },
    { title: t('mdValidation.hasImg'), dataIndex: 'has_img', key: 'has_img' },
    { title: t('mdValidation.first_out_sku_id'), dataIndex: ['first_re_sku', 'out_sku_id'], key: 'first_re_sku_out_sku_id', fixed: 'right' },
    { title:  t('mdValidation.first_img_urls'), dataIndex: ['first_re_sku', 'img_urls'], key: 'first_re_sku_img_urls', fixed: 'right' },
    { title:  t('mdValidation.first_product_name'), dataIndex: ['first_re_sku', 'product_name'], key: 'first_re_sku_product_name', fixed: 'right' },
    // { title:  t('mdValidation.first_facing'), dataIndex: ['first_re_sku', 'facing'], key: 'first_re_sku_facing', fixed: 'right' },
    // { title:  t('mdValidation.first_brand'), dataIndex: ['first_re_sku', 'brand'], key: 'first_re_sku_brand', fixed: 'right' },
    // { title:  t('mdValidation.first_flavor'), dataIndex: ['first_re_sku', 'flavor'], key: 'first_re_sku_flavor', fixed: 'right' },
    // { title: t('mdValidation.first_shape'), dataIndex: ['first_re_sku', 'shape'], key: 'first_re_sku_shape', fixed: 'right' },
    // { title:  t('mdValidation.first_capacity'), dataIndex: ['first_re_sku', 'capacity'], key: 'first_re_sku_capacity', fixed: 'right' },
    { title: t('common.action'), dataIndex: 'action', key: 'action', align: 'center', fixed: 'right' }
];

// 模拟数据
const res1 = {
  data: {
    data_list: Array(40).fill(0).map((_, index) => {
      return {
        is_duplicated: Math.round(Math.random()), // 随机生成0或1
        is_wrong_mapping: Math.round(Math.random()), // 随机生成0或1
        active_status: Math.round(Math.random()), // 随机生成0或1
        out_md_sku_id: `SKU-${index + 1000}`,
        project_id: index,
        img_urls: 'https://re-sku-1255412942.cos.ap-shanghai.myqcloud.com/RE-SKU-LOGO-ZZ16478265668433.jpg',
        brand: `Brand-${index}`,
        flavor: `Flavor-${index}`,
        category: `Category-${index}`,
        shape: `Shape-${index}`,
        capacity: `${Math.floor(Math.random() * 1000)}ml`,
        product_name: `Product-${index}`,
        id: index + 1,
        first_re_sku: {
            out_sku_id: `OUT-SKU-${index}`,
            img_urls: ['https://re-sku-1255412942.cos.ap-shanghai.myqcloud.com/RE-SKU-LOGO-ZZ16478265668433.jpg',
              'https://re-sku-1255412942.cos.ap-shanghai.myqcloud.com/RE-SKU-LOGO-ZZ16478265668433.jpg',
              'https://re-sku-1255412942.cos.ap-shanghai.myqcloud.com/RE-SKU-LOGO-ZZ16478265668433.jpg',
              'https://re-sku-1255412942.cos.ap-shanghai.myqcloud.com/RE-SKU-LOGO-ZZ16478265668433.jpg',
              'https://re-sku-1255412942.cos.ap-shanghai.myqcloud.com/RE-SKU-LOGO-ZZ16478265668433.jpg',
            ],
            product_name: `First-Product-${index}`,
            facing: Math.floor(Math.random() * 10),
            brand: `First-Brand-${index}`,
            flavor: `First-Flavor-${index}`,
            shape: `First-Shape-${index}`,
            capacity: `${Math.floor(Math.random() * 1000)}ml`
        },
        re_sku_ids: [`SKU-ID-${index}-1`, `SKU-ID-${index}-2`],
        has_img: Math.random() > 0.5
      }
    })
  },
  code: "0000",
  message: "success"
};

const fetchSkuData = async () => {
    loading.value = true;
    try {
        // 模拟API调用
        // const params = {
        //   ...formState,
        //     page_no: pagination.page_no.value - 1,
        //     page_size: pagination.page_size.value,
        // }
        // const res = await mdListApi(formState.project_id, params)
        if(res1.code === '0000') {
          skuData.value = res1.data.data_list;
          pagination.total = res1.data.data_list.length;
        }
    } catch (error) {
        console.error("Failed to fetch SKU data:", error);
    } finally {
        loading.value = false;
    }
};

const handleImageError = (event) => {
    event.target.src = "/api/placeholder/40/40";
};

const handleExpand = () => {
  isExpanded.value = !isExpanded.value;
}

const dataSource = async () => {
  try {
    const res = await dataSourceApi();
    if (res.code === '0000') {
      const { flavor, brand, category, shape } = res.data;
      console.log("res",res)
      flavorOptions.value = flavor
      brandOptions.value = brand
      categoryOptions.value = category
      shapeOptions.value = shape
      console.log("flavorOptions.value",flavorOptions.value)
      console.log("brandOptions.value",brandOptions.value)
      console.log("categoryOptions.value",categoryOptions.value)
      console.log("shapeOptions.value",shapeOptions.value)
    }
  } catch (error) {
    console.error(error);
  }
};

const handlePageChange = async (page, size) => {
    pagination.page_no.value = page;
    pagination.page_size.value = size;
    await fetchSkuData();
}

const handleDetail = (id) => {
  if(id){
    router.push({ name: 'MDDetail', query: { id }});
  }
}

onMounted(() => {
    Promise.all([dataSource(), fetchSkuData()]);
});
</script>