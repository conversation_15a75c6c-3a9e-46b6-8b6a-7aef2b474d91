<template>
  <a-modal :open="open" width="890px" title="审核注册用户" @cancel="handleCancel">
    <a-form :model="formState" :rules="rules" ref="formRef" class="mt-8 mr-6 mb-8 ml-2">
      <a-row>
        <a-col :span="12">
          <a-form-item label="用户名称" name="name" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
            <a-input v-model:value="formState.name" placeholder="请填写" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="登陆账号" name="account" :labelCol="{ span: 8 }" :wrapperCol="{ span: 16 }">
            <a-input v-model:value="formState.account" placeholder="请填写" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12">
          <a-form-item label="手机号" name="phone" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
            <a-input v-model:value="formState.phone" placeholder="请填写" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="注册ip" name="registrationIp" :labelCol="{ span: 8 }" :wrapperCol="{ span: 16 }">
            <a-select v-model:value="formState.registrationIp" placeholder="请选择" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12">
          <a-form-item label="注册时间" name="registrationTime" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
            <a-input v-model:value="formState.phone" placeholder="请填写" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-form-item label="注册说明" name="parentMenu" :labelCol="{ span: 3 }">
        <a-input v-model:value="formState.parentMenu" placeholder="请填写注册说明" />
      </a-form-item>
      <a-form-item label="审核说明" name="parentMenu" :labelCol="{ span: 3 }">
        <a-input v-model:value="formState.parentMenu" placeholder="请填写审核说明" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button type="primary" @click="handleOk">通过</a-button>
      <a-button type="primary" class="bg-[#52C41A] text-[#FFFFFF]" @click="handleEditUser">通过并编辑用户</a-button>
      <a-button danger class="bg-[#FF4D4F33] text-[#FFFFFF]" @click="handleCancel">未通过</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';

const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  currentData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:open', 'submit', 'cancel', 'edit-user']);

const formRef = ref();

const formState = reactive({
  parentMenu: '',
  registrationTime: 'directory',
  name: '',
  account: '',
  phone: '',
  registrationIp: '',
});

const rules = {
  parentMenu: [{ required: true, message: '请选择上级菜单' }],
  registrationTime: [{ required: true, message: '请选择菜单类型' }],
  name: [{ required: true, message: '请输入用户名称' }],
  account: [{ required: true }],
  phone: [{ required: true }],
  registrationIp: [{ required: true }],
};

watch(() => props.currentData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.assign(formState, newVal);
  }
}, { deep: true, immediate: true });

const handleOk = async () => {
  try {
    await formRef.value.validate();
    emit('submit', { ...formState });
    handleCancel();
  } catch (error) {
    console.error('Validation failed:', error);
  }
};

const handleEditUser = async () => {
  try {
    emit('edit-user', { ...formState });
  } catch (error) {
    console.error('Validation failed:', error);
  }
};

const handleCancel = () => {
  emit('update:open', false);
  emit('cancel');
};
</script>