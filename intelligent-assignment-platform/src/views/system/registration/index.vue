<template>
	<div class="mt-6">
        <div class="flex justify-between items-center mb-[10px]">
            <div>
                <a-input-search v-model:value="searchKeyword" :placeholder="$t('common.search')" class="w-75" />
                <a-button type="text ml-2" @click="handleAdvancedFilter">
                    <img class="mt-[-4px]" src="@/assets/images/filter.svg" width="18" height="18" />
                    <span class="ml-1">{{ $t('common.advancedFilter') }}</span>
                </a-button>
            </div>
            <a-button type="primary" @click="handleOpenModal()">+ {{ $t('common.add') }}</a-button>
        </div>
        <a-table :columns="columns" :data-source="filteredRegistration" row-key="id" :scroll="{ x: 'max-content' }">
            <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'auditStatus'">
                    <a-tag :color="getStatusColor(record.auditStatus)">{{ record.auditStatus }}</a-tag>
                </template>
                <template v-else-if="column.dataIndex === 'action'">
                    <a @click="editRegistration(record)">{{ $t('registrationAudit.audit') }}</a>
                </template>
            </template>
        </a-table>
    </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

const columns = [{
    title: '',
    dataIndex: 'id',
    key: 'id'
},
{
    title: t('registrationAudit.name'),
    dataIndex: 'name',
    key: 'name'
},
{
    title: t('registrationAudit.account'),
    dataIndex: 'account',
    key: 'account'
},
{
    title: t('registrationAudit.phone'),
    dataIndex: 'phone',
    key: 'phone'
},
{
    title: t('registrationAudit.registrationTime'),
    dataIndex: 'registrationTime',
    key: 'registrationTime'
},
{
    title: t('registrationAudit.registrationAddress'),
    dataIndex: 'registrationAddress',
    key: 'registrationAddress'
},
{
    title: t('registrationAudit.registrationIp'),
    dataIndex: 'registrationIp',
    key: 'registrationIp'
},
{
    title: t('registrationAudit.auditStatus'),
    dataIndex: 'auditor',
    key: 'auditor'
},
{
    title: t('registrationAudit.auditTime'),
    dataIndex: 'auditTime',
    key: 'auditTime'
},
{
    title: t('registrationAudit.auditStatus'),
    dataIndex: 'auditStatus',
    key: 'auditStatus',
    align: 'center'
},
{
    title: t('common.action'),
    dataIndex: 'action',
    key: 'action'
}
];

const getStatusColor = (status) => {
    switch (status) {
        case '待审核':
            return 'blue';
        case '未通过':
            return 'red';
        case '通过':
            return 'green';
        default:
            return '';
    }
};
</script>