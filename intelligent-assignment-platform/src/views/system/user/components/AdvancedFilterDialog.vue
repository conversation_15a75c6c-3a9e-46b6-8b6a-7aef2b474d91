<template>
  <a-modal :title="$t('advancedFilter.title')" :open="open" @cancel="handleCancel" :mask="false" centered>
    <a-form :model="form" ref="formRef" class="mt-8 mr-6 mb-8 ml-1" label-align="right" :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }">
      <a-form-item name="keyword" label="关键词">
        <a-input placeholder="请输入关键词" v-model:value="form.keyword" />
      </a-form-item>
      <a-form-item name="name" label="角色名称">
        <a-input placeholder="请输入角色名称" v-model:value="form.name" />
      </a-form-item>
      <a-form-item name="username" label="账号">
        <a-input placeholder="请输入账号" v-model:value="form.username" />
      </a-form-item>
      <a-form-item name="phone" label="手机号">
        <a-input placeholder="请输入手机号" v-model:value="form.phone" />
      </a-form-item>
      <a-form-item name="department" label="所属部门">
        <a-input placeholder="请输入所属部门" v-model:value="form.department" />
      </a-form-item>
      <a-form-item name="roleName" label="角色名">
        <a-input placeholder="请输入角色名" v-model:value="form.roleName" />
      </a-form-item>
    </a-form>
    <template #footer>
      <div class="flex justify-center">
        <a-button @click="handleReset">{{ $t('common.reset') }}</a-button>
        <a-button type="primary" @click="handleSearch">{{ $t('common.query') }}</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useI18n } from 'vue-i18n';
import { userFilterApi } from '@/api/system/index';
import { debounceAsync } from '@/utils/debounce';

const { t } = useI18n();
const emit = defineEmits(['update', 'refresh']);
const formRef = ref();
const form = reactive({
  keyword: '',
  name: '',
  username: '',
  phone: '',
  department: '',
  roleName: '',
})

const open = ref(false);

const handleSearch = debounceAsync(async () => {
  try {
    const res = await userFilterApi(form);
    emit('update', res.data);
    handleCancel();
	} catch (error) {
    console.error(t('common.queryFailed'), error);
	}
}, 500);

const handleCancel = () => {
  open.value = false;
  formRef.value.resetFields();
};

const handleReset = () => {
  handleCancel();
  emit('refresh');
};

const showAdvancedFilter = () => {
  open.value = true;
};

defineExpose({
    showAdvancedFilter
});
</script>