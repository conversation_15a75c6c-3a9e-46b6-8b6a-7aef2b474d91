<template>
  <a-modal :open="open" width="890px" :title="isEdit ? t('userManagement.editUser') : t('userManagement.addUser')" @ok="handleOk" @cancel="handleCancel" centered>
    <a-form layout="inline" :model="formState" :rules="rules" ref="formRef" label-align="right" :label-col="{ span: 6 }"
    :wrapper-col="{ span: 18 }" class="grid gap-6 grid-cols-2 mt-8 mr-1 mb-8 ml-1">
      <a-form-item :label="t('userManagement.name')" name="name">
        <a-input v-model:value="formState.name" :placeholder="t('userManagement.enterName')" />
      </a-form-item>
      <a-form-item :label="t('userManagement.mail')" name="mail">
        <a-input v-model:value="formState.mail" :placeholder="t('userManagement.enterMail')" />
      </a-form-item>
      <a-form-item :label="t('userManagement.phone')" name="phone">
        <a-input v-model:value="formState.phone" :placeholder="t('userManagement.enterPhone')" />
      </a-form-item>
      <a-form-item :label="t('userManagement.department')" name="department_id">
        <a-cascader 
          v-model:value="formState.department_id" 
          :options="departmentOptions" 
          :placeholder="t('userManagement.selectDepartment')"
          change-on-select 
        />
      </a-form-item>
      <a-form-item :label="t('userManagement.officeLocation')" name="office">
        <a-input v-model:value="formState.office" :placeholder="t('userManagement.enterOffice')" />
      </a-form-item>
      <a-form-item :label="t('userManagement.rolePermissions')" name="role_ids">
        <a-cascader 
          v-model:value="formState.role_ids" 
          :options="roleOptions"
          :placeholder="t('userManagement.selectRole')"
          change-on-select 
        />
      </a-form-item>
      <a-form-item :label="t('userManagement.userStatus')" name="status">
        <a-switch v-model:checked="computedStatus" :checked-children="t('common.enabled')" :un-checked-children="t('common.disabled')" />
      </a-form-item>
      </a-form>
      <template #footer>
        <a-button @click="handleCancel">{{ t('common.cancel') }}</a-button>
        <a-button type="primary" @click="handleOk">{{ t('common.save') }}</a-button>
      </template>
    </a-modal>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { userAddApi, userUpdateApi, rolesListApi, departmentsApi } from '@/api/system/index';
import { debounceAsync } from '@/utils/debounce';
import { useTreeStore } from '@/store/useTreeStore.ts';
import { message } from 'ant-design-vue';
import { useStatusSwitch } from '@/composable/useStatusSwitch'
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const props = defineProps({
  users: {
    type: Array,
    default: () => ([])
  },
});
const emit = defineEmits(['refresh']);
const open = ref(false);
const formRef = ref();
const isEdit = ref(false);
const treeStore = useTreeStore()
const roleData = ref([]);

const formState = reactive({
  name: '',
  mail: '',
  phone: '',
  office: '',
  department_id: null,
  role_ids: [],
  skill_ids: [],
  status: '',
});
const computedStatus = useStatusSwitch(formState);

const rules = {
  name: [{ required: true, message: t('userManagement.enterName') }],
  mail: [{ required: true, message: t('userManagement.enterMail') }],
  phone: [{ required: true, message: t('userManagement.enterPhone') }],
  department_id: [{ required: true, message: t('userManagement.selectDepartment') }],
  role_ids: [{ required: true, message: t('userManagement.selectRole') }],
  skill_ids: [{ required: true, message: t('userManagement.selectSkill') }],
};

const handleCancel = () => {
  open.value = false;
  formRef.value.resetFields();
};

const handleOk = debounceAsync(async () => {
  try {
    const { ...rest } = formState;
    const requestData = {
      ...rest,
      department_id: Array.isArray(formState.department_id) ? formState.department_id.at(-1) : formState.department_id,
      role_ids: Array.isArray(formState.role_ids) ? [Number(formState.role_ids.at(-1))] : [Number(formState.role_ids)],
      status: computedStatus.value === true ? "启用" : "禁用",
    };
    await formRef.value.validate();
    if (isEdit.value === false) { 
      const res = await userAddApi(requestData);
      if (res.code === 0) {
        message.success(t('common.saveSuccess'));
      }
    } else {
      const res = await userUpdateApi(requestData);
      if (res.code === 0) {
        message.success(t('common.saveSuccess'));
      }
    }
    emit('refresh');
    handleCancel();
  } catch (error) {
    console.error(t('common.saveFailed'), error);
  }
}, 300);

const findValueByLabel = (options, label) => {
  const targetLabel = label?.includes('/') ? label.split('/').pop() : label;
  for (const item of options) {
    if (item.label === targetLabel) return item.value;
    if (item.children) {
      const found = findValueByLabel(item.children, targetLabel);
      if (found !== undefined) return found;
    }
  }
  return undefined;
}

function findPathByValue(options, targetValue, path = []) {
  for (const item of options) {
    const newPath = path.concat(item.value);
    if (item.value === targetValue) return newPath;
    if (item.children) {
      const childPath = findPathByValue(item.children, targetValue, newPath);
      if (childPath) return childPath;
    }
  }
  return null;
}

const departmentOptions = computed(() => treeStore.getTreeOptions('department'));

const roleOptions = computed(() => {
   const toCascaderOptions = (roles) => {
        return roles.map(dep => ({
          value: dep.id,
          label: dep.name,
          children: dep.children && dep.children.length ? toCascaderOptions(dep.children) : undefined
        }));
      };
    return toCascaderOptions(roleData.value); 
});

const showUserDialog = async(item) => {
  treeStore.fetchTreeData('department', departmentsApi)
  try {
    const [departmentRes, roleRes] = await Promise.all([ departmentsApi(), rolesListApi()]);
    if (departmentRes.code === 0) {
      treeStore.$patch((state) => {
        state.treeDataMap.department = departmentRes.data;
      });
    }
    if (roleRes.code === 0) {
      roleData.value = roleRes.data;
    }
  } catch (error) {
    console.error(error);
  }
  open.value = true;
  if (item && Object.keys(item).length > 0) {
    isEdit.value = true;
    const fieldMappings = {
      userid: 'id',
      name: 'name',
      mail: 'mail',
      phone: 'phone',
      office: 'office',
      department_id: 'department',
      role_ids: 'roles',
      status: 'status',
    };
    Object.entries(fieldMappings).reduce((_, [formKey, dataKey]) => {
      switch(formKey) {
        case 'role_ids':
          formState[formKey] = findPathByValue(roleOptions.value, Number(Object.keys(item[dataKey])[0]));
          break;
        case 'department_id':
          formState[formKey] = item[dataKey] ? findPathByValue(departmentOptions.value, findValueByLabel(departmentOptions.value, item[dataKey])) : null;
          break;
        default: 
          formState[formKey] = item[dataKey] ?? null;
      }
    }, {});
  } else {  
    isEdit.value = false;
    Object.keys(formState).forEach(key => {
      if (Array.isArray(formState[key])) {
        formState[key] = [];
      } else {
        formState[key] = '';
      }
    });
  }
};

defineExpose({
    showUserDialog
});
</script>