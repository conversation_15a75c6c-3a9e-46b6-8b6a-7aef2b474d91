<template>
	<div>
		<div class="flex w-full">
			<div class="w-[200px] mr-6">
				<h4 class="bg-[#FAFAFA] p-[15px] text-[14px] ">{{ $t('userManagement.departmentList') }}</h4>
				<a-tree :tree-data="departments" @select="handleTreeSelect">
					<template #switcherIcon="{ switcherCls }"><down-outlined :class="switcherCls" /></template>
				</a-tree>
			</div>
			<div class="flex-1 p-[15px] overflow-x-auto">
				<h4 class="pb-4">
					<span class="w-[18px] h-full bg-[#000000] mr-[10px] rounded-[2px]">|</span>
					{{ $t('userManagement.userList') }}
				</h4>
				<div class="flex justify-between items-center mb-[10px]">
					<div>
						<a-input v-model:value="searchKeyword" :placeholder="$t('common.search')"  class="w-75" @pressEnter="onSearch">
							<template #suffix>
								<a-tooltip title="Extra information">
									<SearchOutlined style="color: rgba(0, 0, 0, 0.45)"/>
								</a-tooltip>
							</template>
						</a-input>
						<!-- <a-button type="text ml-2" @click="handleAdvancedFilter">
							<img class="mt-[-4px]" src="@/assets/images/filter.svg" width="18" height="18" />
							<span class="ml-1">{{ $t('common.advancedFilter') }}</span>
						</a-button> -->
					</div>
					<a-button type="primary" @click="handleOpenModal()">+ {{ $t('common.add') }}</a-button>
				</div>
				<a-table :columns="columns" :data-source="users" row-key="id" class="w-full" size="middle">
					<template #bodyCell="{ column, record, index }">
						<template v-if="column.dataIndex === 'index'">
							{{ index + 1 }}
						</template>
						<template v-else-if="column.dataIndex === 'roles'">
							{{ Object.values(record.roles)[0] }}
						</template>
						<template v-else-if="column.dataIndex === 'status'">
							<a-tag :color="record.status === '启用' ? 'green' : ''">
								{{ record.status }}</a-tag>
						</template>
						<template v-else-if="column.dataIndex === 'skills'">
							{{ Object.keys(record.skills).length }}
						</template>
						<template v-else-if="column.dataIndex === 'action'">
							<a @click="handleOpenModal(record)">{{ $t('userManagement.edit') }}</a>
							<!-- <a-popconfirm :title="$t('common.confirmDelete')" @confirm="deleteUser(record.id)">
								<a-button type="link" danger>{{ $t('userManagement.delete') }}</a-button>
							</a-popconfirm> -->
						</template>
					</template>
				</a-table>
			</div>
		</div>
		<UserFormDialog ref="showRoleRef" :user-data="users" @refresh="userList" />
		<AdvancedFilterDialog ref="showFilterRef" @update="updateUserData"  @refresh="userList" />
	</div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import UserFormDialog from '@/views/system/user/components/UserFormDialog.vue';
import AdvancedFilterDialog from './components/AdvancedFilterDialog.vue';
import { userListApi, departmentsApi, userDelApi, userFilterApi, departmentUsersApi } from '@/api/system/index';
import { debounceAsync } from '@/utils/debounce';
import { useTreeStore } from '@/store/useTreeStore.ts';
import { useI18n } from 'vue-i18n';
import { message } from 'ant-design-vue';
import { SearchOutlined } from '@ant-design/icons-vue';
import { DownOutlined } from '@ant-design/icons-vue';

const { t } = useI18n();
const searchKeyword = ref('');
const showRoleRef = ref();
const users = ref([]);
const treeStore = useTreeStore()
const showFilterRef = ref()

const handleAdvancedFilter = () => {
	showFilterRef.value.showAdvancedFilter();
};

const handleOpenModal = (record) => {
	showRoleRef.value.showUserDialog(record);
};

const columns = [
	{ title: '', dataIndex: 'index', key: 'index', width: 80 },
	{ title: t('userManagement.name'), dataIndex: 'name', key: 'name'},
	{ title: t('userManagement.mail'), dataIndex: 'mail', key: 'mail', ellipsis: true},
	{ title: t('userManagement.phone'), dataIndex: 'phone', key: 'phone', ellipsis: true },
	{ title: t('userManagement.department'), dataIndex: 'department', key: 'department', ellipsis: true },
	{
		title: t('userManagement.rolePermissions'), dataIndex: 'roles', key: 'roles'
	},
	// {
	// 	title: t('userManagement.skillNum'), dataIndex: 'skills', key: 'skills', align: 'center',
	// 	customCell: () => {
	// 		return {
	// 			style: {
	// 				color: '#00ABEB'
	// 			}
	// 		};
	// 	}
	// },
	// { title: t('userManagement.officeLocation'), dataIndex: 'office', key: 'office' },
	{ title: t('userManagement.userStatus'), dataIndex: 'status', key: 'status' },
	// { title: t('userManagement.userSource'), dataIndex: 'source', key: 'source' },
	{ title: t('common.action'), dataIndex: 'action', key: 'action', width: 100 }
];

const userList = async () => {
	try {
		const res = await userListApi();
		users.value = res.data;
	} catch (error) {
		console.error("请求失败:", error);
	}
}

const updateUserData = (data) => {
	users.value = data;
}

const convertToTreeData = (data) => {
	if (!Array.isArray(data)) return [];
	return data.map(item => ({
		title: item.label,
		key: item.value,
		children: item.children ? convertToTreeData(item.children) : []
	}));
}
const departmentOptions = computed(() => treeStore.getTreeOptions('department'))
const departments = computed(() => {
	return convertToTreeData(departmentOptions.value);
});

const deleteUser = (async (id) => {
	try {
		const res = await userDelApi({ id: id });
		if (res.code === 0) {
			message.success('删除成功');
		}
		users.value = users.value.filter(user => user.id !== id);
	} catch (error) {
		console.error("删除失败:", error);
	}
});

const onSearch = debounceAsync(async () => {
	const res = await userFilterApi({keyword: searchKeyword.value});
    users.value = res.data;
}, 500);

const handleTreeSelect = async (selectedKeys) => {
  if (!selectedKeys.length) return;
  const id = selectedKeys[0];
  const res = await departmentUsersApi({ id });
  if( res.code === 0 ) {
    users.value = res.data;
  }
};


onMounted(async () => {
	await Promise.all([userList(), treeStore.fetchTreeData('department', departmentsApi)]);
});
</script>

<style scoped>
:deep(.ant-tree-treenode) {
  width: 100%;
  padding: 15px;
  border-bottom: 1px solid #e8e8e8;
}
:deep(.ant-tree-treenode-selected) {
  background-color: #1677FF1A !important;
}
</style>