<template>
  <div class="flex flex-col h-full">
    <!-- 权限拒绝页面 -->
    <div v-if="isUnauthorized" class="flex-1 flex items-center justify-center">
      <div class="text-center">
        <img src="@/assets/images/noPermisson.svg" alt="Permission Denied" class="mx-auto mb-4" style="width: 323px; height: 270px;" />
        <p class="text-gray-500 text-lg">Permission Denied</p>
      </div>
    </div>
    
    <!-- 正常系统管理页面 -->
    <div v-else class="border-b tab-customer flex-1">
      <a-tabs v-model:activeKey="activeSystemTab" :tab-bar-style="{ borderRadius: 0 }">
        <a-tab-pane key="menu" :tab="$t('system.menu')" class="h-full">
          <div class="h-[89vh] overflow-y-auto bg-white">
            <menu-management />
          </div>
        </a-tab-pane>
        <a-tab-pane key="role" :tab="$t('system.role')">
          <div class="h-[89vh] overflow-y-auto bg-white">
            <role-management />
          </div>
        </a-tab-pane>
        <a-tab-pane key="depart" :tab="$t('system.department')">
          <div class="h-[89vh] overflow-y-auto bg-white">
            <department-management />
          </div>
        </a-tab-pane>
        <a-tab-pane key="user" :tab="$t('system.user')">
          <div class="h-[89vh] overflow-y-auto bg-white">
            <user-management />
          </div>
        </a-tab-pane>
        <!-- <a-tab-pane key="registration" :tab="$t('system.registration')">
          <div class="p-4 rounded-lg bg-white m-4">
            <registration-audit />
          </div>
        </a-tab-pane> -->
      </a-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, inject, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import MenuManagement from '@/views/system/menu/index.vue';
import RoleManagement from '@/views/system/role/index.vue';
import DepartmentManagement from '@/views/system/department/index.vue';
import UserManagement from '@/views/system/user/index.vue';
import RegistrationAudit from '@/views/system/registration/index.vue';

const router = useRouter();
const route = useRoute();

// 从SideNav组件注入的activeSystemTab
const activeSystemTab = inject('activeSystemTab', ref('menu'));

// 检查是否为未授权访问
const isUnauthorized = computed(() => {
  return route.query.authorized === '0';
});

// 组件映射
const componentMap = {
  menu: MenuManagement,
  role: RoleManagement,
  depart: DepartmentManagement,
  user: UserManagement,
  registration: RegistrationAudit
};

// 当前显示的组件
const currentComponent = computed(() => {
  return componentMap[activeSystemTab.value];
});

// 处理标签页切换
const handleTabChange = (key) => {
  // 根据选中的标签页更新路由
  const pathMap = {
    menu: '/menu',
    role: '/role',
    depart: '/depart',
    user: '/user',
    registration: '/registration'
  };

  router.push(pathMap[key]);
};

// 监听路由变化，更新activeSystemTab
onMounted(() => {
  // 根据当前路由设置初始activeSystemTab
  const pathToKey = {
    '/menu': 'menu',
    '/role': 'role',
    '/depart': 'depart',
    '/user': 'user',
    '/registration': 'registration'
  };
  if (pathToKey[route.path]) {
    activeSystemTab.value = pathToKey[route.path];
  }
});

// 监听路由变化
watch(
  () => route.path,
  (newPath) => {
    const pathToKey = {
      '/menu': 'menu',
      '/role': 'role',
      '/depart': 'depart',
      '/user': 'user',
      '/registration': 'registration'
    };

    if (pathToKey[newPath]) {
      activeSystemTab.value = pathToKey[newPath];
    }
  }
);

// 处理卡片标签的编辑事件
const handleTagEdit = (item) => {
  message.info(`编辑菜单项: ${item.title}`);
  // 这里可以打开编辑对话框或跳转到编辑页面
};

// 处理卡片标签的删除事件
const handleTagDelete = (item) => {
  message.info(`删除菜单项: ${item.title}`);
  // 这里可以弹出确认对话框
};

// 处理卡片标签的新增事件
const handleTagAdd = () => {
  message.info('新增菜单项');
  // 这里可以打开新增对话框
};

// 处理卡片标签的选择事件
const handleTagSelect = (item) => {
  message.info(`选择菜单项: ${item.title}`);
  // 这里可以根据选择的菜单项更新当前视图
};
// 返回上一页
const goBack = () => {
    router.back();
};
</script>

<style scoped>
:deep(.ant-tabs-nav) {
  height: 56px;
  padding-left: 16px;
  background: #fff;
}

:deep(.ant-tabs-content-holder) {
    margin: 16px;
    border-radius: 8px;
    padding: 16px;
    background-color: #fff;
}
.tab-customer .ant-tabs-tab {
  margin-left: 16px;
  height: 56px;
}
.ant-tabs-top >.ant-tabs-nav {
  background: #fff;
}
</style>