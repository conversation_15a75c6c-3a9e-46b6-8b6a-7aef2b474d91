<template>
  <a-modal :title="$t('advancedFilter.title')" :open="open" @cancel="handleCancel" :mask="false" centered>
    <a-form :model="form" ref="formRef" class="mt-8 mr-6 mb-8 ml-1" label-align="right" :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }">
      <a-form-item name="keyword" :label="$t('advancedFilter.keyword')">
        <a-input :placeholder="$t('advancedFilter.enterKeyword')" v-model:value="form.keyword" />
      </a-form-item>
      <a-form-item name="name" :label="$t('advancedFilter.menuName')">
        <a-input :placeholder="$t('advancedFilter.enterMenuName')" v-model:value="form.name" />
      </a-form-item>
    </a-form>
    <template #footer>
      <div class="flex justify-center">
        <a-button @click="handleReset">{{ $t('common.reset') }}</a-button>
        <a-button type="primary" @click="handleSearch">{{ $t('common.query') }}</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useI18n } from 'vue-i18n';
import { menufilterApi } from '@/api/system/index';
import { debounceAsync } from '@/utils/debounce';

const { t } = useI18n();
const emit = defineEmits(['update', 'refresh']);
const formRef = ref();
const form = reactive({
  keyword: '',
  name: '',
})

const open = ref(false);

const handleSearch = debounceAsync(async() => {
  try {
    const res = await menufilterApi(form);
    emit('update', res.data);
    handleCancel();
	} catch (error) {
		console.error(t('common.requestFailed'));
	}
}, 500);

const handleCancel = () => {
  open.value = false;
  formRef.value.resetFields();
};

const handleReset = () => {
  handleCancel();
  emit('refresh');
};

const showAdvancedFilter = () => {
  open.value = true;
};

defineExpose({
    showAdvancedFilter
});
</script>