<template>
  <a-modal :open="open" width="570px" :title="isEdit ? $t('menuManagement.editMenu') : $t('menuManagement.addMenu')" @ok="handleOk" @cancel="handleCancel">
    <a-form layout="horizontal" :model="formState" :rules="rules" ref="formRef" label-align="right"
      :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" class="mt-8 mr-1 mb-8 ml-1">
      <a-form-item :label="$t('menuManagement.parentMenu')" name="parent_id">
         <a-cascader v-model:value="formState.parent_id" :options="menuOptions" :placeholder="$t('menuManagement.selectParentMenu')" change-on-select/>
      </a-form-item>
      <a-form-item :label="$t('menuManagement.menuType')" name="type">
        <a-radio-group v-model:value="formState.type">
          <a-checkbox value="目录" :checked="formState.type === '目录'" @change="() => formState.type = '目录'">{{ $t('menuManagement.directory') }}</a-checkbox>
          <a-checkbox value="菜单" :checked="formState.type === '菜单'" @change="() => formState.type = '菜单'">{{ $t('menuManagement.menu') }}</a-checkbox>
          <a-checkbox value="按钮" :checked="formState.type === '按钮'" @change="() => formState.type = '按钮'">{{ $t('menuManagement.button') }}</a-checkbox>
          <a-checkbox value="页面" :checked="formState.type === '页面'" @change="() => formState.type = '页面'">{{ $t('menuManagement.page') }}</a-checkbox>
        </a-radio-group>
      </a-form-item>
      <a-form-item :label="$t('menuManagement.menuName')" name="name" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
        <a-input v-model:value="formState.name" :placeholder="$t('common.pleaseEnter')" />
      </a-form-item>
      <a-form-item :label="$t('menuManagement.interfaceRule')" name="api" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
        <a-input v-model:value="formState.api" :placeholder="$t('common.pleaseEnter')" />
      </a-form-item>
      <a-form-item :label="$t('menuManagement.routePath')" name="path" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
        <a-input v-model:value="formState.path" :placeholder="$t('common.pleaseEnter')" />
      </a-form-item>
      <a-form-item :label="$t('menuManagement.pageHierarchy')" name="level" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
        <a-input v-model:value="formState.level" :placeholder="$t('common.pleaseEnter')" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="handleCancel">{{ $t('common.cancel') }}</a-button>
      <a-button type="primary" @click="handleOk">{{ $t('common.save') }}</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { menuAddApi, menuUpdateApi } from '@/api/system/index';
import { message } from 'ant-design-vue'
import { debounceAsync } from '@/utils/debounce';
import { findParentPath } from '../../../../utils/findParentPath';

const { t } = useI18n();
const open = ref(false);
const formRef = ref();
const isEdit = ref(false);
const props = defineProps({
  menuData: {
    type: Array,
    default: () => ([])
  },
});
const emit = defineEmits(['refresh']);

const menuOptions = computed(() => {
  const loop = (list) => list.map(item => ({
    label: item.name,
    value: item.id,
    children: item.children ? loop(item.children) : undefined
  }));
  return loop(props.menuData || []);
});

const formState = reactive({
  id: null,
  parent_id: null,
  type: '',
  name: '',
  path: '',
  api: '',
  e_link: '',
  level: null,
});

const rules = {
  type: [{ required: true, message: t('menuManagement.selectMenuType') }],
  name: [{ required: true, message: t('menuManagement.enterMenuName') }],
};

const handleOk = debounceAsync(async () => {
  try {
    await formRef.value.validate();
    const {...rest } = formState;
    const requestData = {
      ...rest,
      parent_id: Array.isArray(formState.parent_id) ? Number(formState.parent_id.at(-1)) : Number(formState.parent_id),
      level: Number(formState.level),
      type: Array.isArray(formState.type) ? formState.type[0] : formState.type,
    };
    let res;
    if (isEdit.value === false) { 
      const {id, ...submit } = requestData;
      res = await menuAddApi(submit);
    } else {
      res = await menuUpdateApi(requestData);
    }
    if (res.code === 0) {
      message.success(t('common.saveSuccess'));
    }
    emit('refresh');
    handleCancel();
  } catch (error) {
    console.error('Validation failed:', error);
  }
}, 300);

const handleCancel = () => {
  open.value = false;
  formRef.value.resetFields();
};

const showMenuDialog = (item) => {
  open.value = true;
  if (item && Object.keys(item).length > 0) {
    isEdit.value = true;
    const parentPath = findParentPath(menuOptions.value, item.id) || [];
    Object.keys(formState).forEach(key => {
      formState[key] = item[key] !== undefined ? item[key] : null;
    });
    formState.parent_id = parentPath;
  } else {
    isEdit.value = false;
    Object.keys(formState).forEach(key => {
      if (Array.isArray(formState[key])) {
        formState[key] = [];
      } else {
        formState[key] = '';
      }
    });
  }
};

defineExpose({
    showMenuDialog
});
</script>