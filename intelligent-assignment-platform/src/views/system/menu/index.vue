<template>
	<div>
		<div class="flex justify-between items-center mb-[10px]">
			<div>
				<a-input v-model:value="searchKeyword" :placeholder="$t('common.search')"  class="w-75" @pressEnter="onSearch">
					<template #suffix>
						<a-tooltip title="Extra information">
							<SearchOutlined style="color: rgba(0, 0, 0, 0.45)"/>
						</a-tooltip>
					</template>
				</a-input>

				<!-- <a-button type="text ml-2" @click="handleAdvancedFilter">
					<img class="mt-[-4px]" src="@/assets/images/filter.svg" width="18" height="18" />
					<span class="ml-1">{{ $t('common.advancedFilter') }}</span>
				</a-button> -->
			</div>
			<a-button type="primary" @click="handleOpenModal()">+ {{ $t('common.add') }}</a-button>
		</div>
		<a-table :columns="columns" :data-source="menuData" :expandable="{ expandRowByClick: false, expandIcon: customExpandIcon, childrenColumnName: 'children' }" size="middle" row-key="id" :scroll="{ x: 'max-content' }">
			<template #expandIcon="props">
				<RightOutlined 
					v-if="props.record.children && props.record.children.length > 0"
					:rotate="props.expanded ? 90 : 0" 
					@click="e => props.onExpand(props.record, e)" 
					class="mr-2 text-[10px]"
				/>
				<span v-else class="mr-[18px]"></span>
			</template>
			<template #bodyCell="{ column, record }">
				<template v-if="column.dataIndex === 'type'">
					<a-tag :color="getTagColor(record.type)">{{ record.type }}</a-tag>
				</template>
				<template v-if="column.dataIndex === 'action'">
					<a @click="handleOpenModal(record)">{{ t('common.edit') }}</a>
					<!-- <a-button type="link" danger @click="deleteMenu(record.id)">{{ $t('common.delete') }}</a-button> -->
				</template>
			</template>
		</a-table>
		<MenuManageDialog ref="showMenuRef" :menu-data="menuData" @refresh="fetchMenuData" />
		<AdvancedFilterDialog ref="showFilterRef" @update="updateMenuData" @refresh="fetchMenuData"/>

		<a-modal v-model:open="open" title="Notification" :confirm-loading="confirmLoading" @ok="handleOk" centered>
			<p>{{ modalText }}</p>
			<template #footer>
				<a-button key="back" @click="open = false">Cancel</a-button>
				<a-button key="submit" type="primary" :loading="loading" @click="handleOk">Confirm</a-button>
			</template>
		</a-modal>
	</div>
</template>

<script setup>
import { ref, onMounted, h } from 'vue';
import { useI18n } from 'vue-i18n';
import MenuManageDialog from '@/views/system/menu/components/MenuManageDialog.vue';
import AdvancedFilterDialog from '@/views/system/menu/components/AdvancedFilterDialog.vue';
import { menusApi, menuDelApi, menufilterApi } from '@/api/system/index';
import { debounceAsync } from '@/utils/debounce';
import { message } from 'ant-design-vue';
import { SearchOutlined, RightOutlined } from '@ant-design/icons-vue';

const { t } = useI18n();
const showMenuRef = ref()
const showFilterRef = ref()
const menuData = ref([]);
const searchKeyword = ref('');
const open = ref(false);
const modalText = ref('Are you sure you want to delete this record?');
const recordId = ref();

const handleAdvancedFilter = () => {
	showFilterRef.value.showAdvancedFilter();
};
const handleOpenModal = (record) => {
	showMenuRef.value.showMenuDialog(record);
};

const columns = [
	{ title: t('menuManagement.menuName'), dataIndex: 'name', key: 'name' },
	{ title: t('menuManagement.routePath'), dataIndex: 'path', key: 'path' },
	{ title: t('menuManagement.interfaceRule'), dataIndex: 'api', key: 'api' },
	{ title: t('menuManagement.pageHierarchy'), dataIndex: 'level', key: 'level' },
	{ title: t('menuManagement.menuType'), dataIndex: 'type', key: 'type' },
	{ title: t('menuManagement.isExternalLink'), dataIndex: 'e_link', key: 'e_link', align: 'center' },
	{ title: t('common.action'), dataIndex: 'action', key: 'action',width: 150, fixed: 'right' },
];

const customExpandIcon = ({ expanded, onExpand, record }) => {
	if (!record.children || record.children.length === 0) {
		return h('span', { style: { width: '16px', height: '16px', marginRight: '8px' } });
	}
	return h('span', {
		onClick: (e) => {
			e.stopPropagation();
			onExpand(record, e);
		},
		style: {
			cursor: 'pointer',
			display: 'inline-block',
			width: '0',
			height: '0',
			border: 'solid #666',
			borderWidth: expanded ? '6px 4px 0 4px' : '4px 0 4px 6px',
			transition: 'all 0.2s ease',
			marginRight: '8px',
			verticalAlign: 'middle'
		}
	});
}

const getTagColor = (type) => {
	switch (type) {
		case '目录':
			return 'blue';
		case '菜单':
			return 'green';
		case '页面':
			return 'red';
		case '按钮':
			return 'orange';
		default:
			return 'gray';
	}
};

const fetchMenuData = async () => {
	try {
		const res = await menusApi();
		menuData.value = res.data;
		localStorage.setItem('userMenus', JSON.stringify(res.data));
	} catch (error) {
		console.error("请求失败:", error);
	}
}

const updateMenuData = async (data) => {
	menuData.value = data;
}


const deleteMenu = (id) => {
	recordId.value = id;
	open.value = true;
};

const handleOk = debounceAsync(async () => {
	try {
		const res = await menuDelApi({ id: recordId.value });
		if (res.code === 0) {
			message.success(t('common.delSuccess'));
			await fetchMenuData();
		}
	} catch (error) {
		console.error(error);
	} finally {
		open.value = false;
	}
}, 300);


const onSearch = debounceAsync(async () => {
	const res = await menufilterApi({keyword: searchKeyword.value});
    menuData.value = res.data;
}, 500);

onMounted(async () => {
	await fetchMenuData();
})
</script>

<style scoped>
.box {
	padding: 20px;
}
</style>