<template>
  <a-modal :open="open" width="570px" :title="isEdit ? $t('departmentManagement.editDepartment') : $t('departmentManagement.addDepartment')" @ok="handleOk" @cancel="handleCancel" centered>
    <a-form layout="horizontal" :model="formState" :rules="rules" ref="formRef" label-align="right"
      :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" class="mt-8 mr-1 mb-8 ml-1">
      <a-form-item :label="$t('departmentManagement.parent_id')" name="parent_id">
        <a-cascader v-model:value="formState.parent_id" :options="departmentOptions"  :placeholder="$t('departmentManagement.enterParentName')"  change-on-select />
      </a-form-item>
      <a-form-item :label="$t('departmentManagement.departmentName')" name="name">
        <a-input v-model:value="formState.name" :placeholder="$t('departmentManagement.enterDepartmentName')" />
      </a-form-item>
      <a-form-item :label="$t('departmentManagement.departmentHead')" name="leader_name">
        <a-select
          v-model:value="formState.leader_name"
          show-search
          :placeholder="$t('departmentManagement.enterDepartmentHead')"
          :default-active-first-option="false"
          :show-arrow="false"
          :filter-option="false"
          :not-found-content="null"
          :options="searchOptions"
          @search="handleSearch"
          @change="handleSelectChange"
        >
        </a-select>
      </a-form-item>
      <a-form-item :label="$t('departmentManagement.mail')" name="mail">
          <a-input v-model:value="formState.mail" disabled/>
      </a-form-item>
      <a-form-item :label="$t('departmentManagement.headPhone')" name="leader_phone">
        <a-input v-model:value="formState.leader_phone"  disabled/>
      </a-form-item>
      <a-form-item :label="$t('departmentManagement.status')" name="status">
        <a-switch v-model:checked="computedStatus" :checked-children="$t('common.enabled')" :un-checked-children="$t('common.disabled')" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="handleCancel">{{ $t('common.cancel') }}</a-button>
      <a-button type="primary" @click="handleOk">{{ $t('common.save') }}</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { departmentsApi, departmentAddApi, departmentUpdateApi } from '@/api/system/index';
import { debounceAsync } from '@/utils/debounce';
import { useTreeStore } from '@/store/useTreeStore.ts';
import { useStatusSwitch } from '@/composable/useStatusSwitch'
import { useI18n } from 'vue-i18n';
import { message } from 'ant-design-vue'
import { findParentPath } from '../../../../utils/findParentPath';
import { useUserDataStore } from '@/store/userDataStore';
import { storeToRefs } from 'pinia';

const store = useUserDataStore();
const { userData } = storeToRefs(store);

const props = defineProps({
  departments: {
    type: Array,
    default: () => ([])
  },
});
const emit = defineEmits(['update']);
const open = ref(false);
const { t } = useI18n();
const formRef = ref();
const isEdit = ref(false);
const treeStore = useTreeStore()
const names = ref([]);
const searchOptions = ref([]);
let timeout;
let currentValue = '';
const formState = reactive({
  id: null,
  name: '',
  parent_id: null,
  status: '',
  leader_userid: null,
  leader_name: null,
  leader_phone: '',
  mail: '',
});

const computedStatus = useStatusSwitch(formState);

const rules = {
  name: [{ required: true, message: t('departmentManagement.enterDepartmentName') }],
};

const userList = async () => {
  try {
    names.value = userData.value.map(item => ({ name: item.name, id: item.id, phone: item.phone, mail: item.mail }));
  } catch (error) {
    console.error("请求失败:", error);
  }
};
// 防抖和本地过滤
const handleSearch = (val) => {
  if (timeout) {
    clearTimeout(timeout);
    timeout = null;
  }
  currentValue = val;
  function performSearch() {
    if (currentValue === val) {
      if (val) {
        searchOptions.value = names.value
          .filter(user => user.name.toLowerCase().includes(val.toLowerCase()))
          .map(user => ({ value: user.id, label: user.name + ' - ' + user.phone}));
      } else {
        searchOptions.value = [];
      }
    }
  }
  timeout = setTimeout(performSearch, 300);
};

const handleSelectChange = (userId) => {
  // 选择后需 清除防抖计时器
  if (timeout) {
    clearTimeout(timeout);
    timeout = null;
  }
  const selectedUser = names.value.find(res => res.id === userId);
  if (selectedUser) {
    formState.leader_userid = selectedUser.id;
    formState.leader_phone = selectedUser.phone;
    formState.mail = selectedUser.mail;
    // 更新选项为当前选中的，以便显示正确
    searchOptions.value = [{ value: selectedUser.id, label: selectedUser.name + '-' + selectedUser.phone }];
  }
};

const departmentOptions = computed(() => treeStore.getTreeOptions('department'));

const handleCancel = () => {
  open.value = false;
  formRef.value.resetFields();
};

const handleOk = debounceAsync(async () => {
  try {
    await formRef.value.validate();
    const { leader_name, mail, leader_phone, ...rest } = formState;
    const requestData = {
      ...rest,
      leader_userid: Number(formState.leader_userid),
      parent_id: Array.isArray(formState.parent_id) 
      ? (formState.parent_id.length > 0 ? Number(formState.parent_id.at(-1)) : 0)
      : (formState.parent_id ? Number(formState.parent_id) : 0),
      status: computedStatus.value === true ? "启用" : "禁用",
    };
    let res;
    if (isEdit.value === false) { 
      const {id, ...submit } = requestData;
      res = await departmentAddApi(submit);
    } else {
      res = await departmentUpdateApi(requestData);
    }
    if (res.code === 0) {
      message.success(t('common.saveSuccess'));
    }
    emit('update');
    handleCancel();
  } catch (error) {
    console.error('请求出错:', error);
  }
}, 300);

onMounted(async () => {
  if (!store.hasData) { // 仅在数据未加载时调用API
        await store.fetchUserData();
  }
  await userList()
});

const showMenuDialog = async (item) => {
  treeStore.fetchTreeData('department', departmentsApi)

  const res = await departmentsApi();
  if (res.code === 0) {
    treeStore.$patch((state) => {
      state.treeDataMap.department = res.data;
    });
  }
  open.value = true;

  if (item && Object.keys(item).length > 0) {
    isEdit.value = true;
    formState.parent_id = findParentPath(departmentOptions.value, item.id) || [];
    const fieldMappings = {
      id: 'id',
      name: 'name', 
      status: 'status',
      leader_name: 'leader_name',
      leader_userid: 'leader_userid',
      leader_phone: 'leader_phone',
      mail: 'mail',
    };
    Object.entries(fieldMappings).forEach(([formKey, dataKey]) => {
      formState[formKey] = item[dataKey] ?? null;
    });
  } else {
    isEdit.value = false;
    Object.keys(formState).forEach(key => {
      if (Array.isArray(formState[key])) {
        formState[key] = [];
      } else {
        formState[key] = '';
      }
    });
  }
};

defineExpose({
    showMenuDialog
});
</script>