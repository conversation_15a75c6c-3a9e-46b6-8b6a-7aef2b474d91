<template>
	<div>
		<div class="flex justify-between items-center mb-[10px]">
        <div>
			<a-input v-model:value="searchKeyword" :placeholder="$t('common.search')"  class="w-75" @pressEnter="onSearch">
				<template #suffix>
					<a-tooltip title="Extra information">
						<SearchOutlined style="color: rgba(0, 0, 0, 0.45)"/>
					</a-tooltip>
				</template>
			</a-input>
			<!-- <a-button type="text ml-2" @click="handleAdvancedFilter">
				<img class="mt-[-4px]" src="@/assets/images/filter.svg" width="18" height="18" />
				<span class="ml-1">{{ $t('common.advancedFilter') }}</span>
			</a-button> -->
        </div>
        <a-button type="primary" @click="handleOpenModal()">+ {{ $t('common.add') }}</a-button>
    </div>
		<a-table :columns="columns" :data-source="departments" row-key="id" :scroll="{ x: 'max-content' }" :pagination="false">
			<template #expandIcon="props">
				<RightOutlined 
					v-if="props.record.children && props.record.children.length > 0"
					:rotate="props.expanded ? 90 : 0" 
					@click="e => props.onExpand(props.record, e)" 
					class="mr-2 text-[10px]"
				/>
				<span v-else class="mr-[18px]"></span>
			</template>
			<template #bodyCell="{ column, record }">
				<template v-if="column.dataIndex === 'status'">
					<a-tag :color="record.status === '启用' ? 'green' : ''">{{ record.status }}</a-tag>
				</template>
				<template v-else-if="column.dataIndex === 'action'">
					<a @click="handleOpenModal(record)">{{ $t('common.edit') }}</a>
					<a-button type="link" danger @click="deleteDepartment(record.id)">{{ $t('common.delete') }}</a-button>
				</template>
			</template>
		</a-table>
		<DepartmentDialog ref="showDepartmentRef" :department-data="departments" @update="fetchDepartmentData"/>
		<AdvancedFilterDialog ref="showFilterRef" @update="updateDepartmentData" @refresh="fetchDepartmentData"/>

		<a-modal v-model:open="open" title="Notification" :confirm-loading="confirmLoading" @ok="handleOk" centered>
			<p>{{ modalText }}</p>
			<template #footer>
				<a-button key="back" @click="open = false">Cancel</a-button>
				<a-button key="submit" type="primary" :loading="loading" @click="handleOk">Confirm</a-button>
			</template>
		</a-modal>
	</div>
</template>

<script setup>
import { ref } from 'vue';
import DepartmentDialog from '@/views/system/department/components/DepartmentDialog.vue';
import AdvancedFilterDialog from './components/AdvancedFilterDialog.vue';
import { departmentDelApi, departmentsApi, departmentFilterApi } from '@/api/system/index';
import { debounceAsync } from '@/utils/debounce';
import { useI18n } from 'vue-i18n'
import { message } from 'ant-design-vue';
import { SearchOutlined, RightOutlined } from '@ant-design/icons-vue';

const open = ref(false);
const modalText = ref('Are you sure you want to delete this record?');
const recordId = ref();
const { t } = useI18n()
const showDepartmentRef = ref()
const departments = ref([]);
const showFilterRef = ref()
const searchKeyword = ref('');

const handleAdvancedFilter = () => {
	showFilterRef.value.showAdvancedFilter();
};

const handleOpenModal = (record) => {
	showDepartmentRef.value.showMenuDialog(record);
};

const deleteDepartment = (id) => {
	recordId.value = id;
	open.value = true;
};

const handleOk = debounceAsync(async() => {
	try {
		const res = await departmentDelApi({id: recordId.value});
		if (res.code === 0) {
			message.success(t('common.delSuccess'));
			departments.value = departments.value.filter(department => department.id !== recordId.value);
			await fetchDepartmentData();
		}
	} catch (error) {
		console.error(error);
	} finally {
		open.value = false;
	}
}, 300);

const columns = [
	{ title: t('departmentManagement.departmentName'), dataIndex: 'name', key: 'name' },
	{ title: t('departmentManagement.status'), dataIndex: 'status', key: 'status' },
	{ title: t('departmentManagement.departmentHead'), dataIndex: 'leader_name', key: 'leader_name' },
	{ title: t('departmentManagement.headPhone'), dataIndex: 'leader_phone', key: 'leader_phone' },
	{ title: t('departmentManagement.creator'), dataIndex: 'creator', key: 'creator' },
	{ title: t('departmentManagement.creationTime'), dataIndex: 'created_at', key: 'created_at' },
	{ title: t('departmentManagement.updater'), dataIndex: 'updator', key: 'updator' },
	{ title: t('departmentManagement.updateTime'), dataIndex: 'updated_at', key: 'updated_at' },
	{ title: t('common.action'), dataIndex: 'action', key: 'action',width: 150, fixed: 'right' }
];

const fetchDepartmentData = async () => {
    const res = await departmentsApi();
	departments.value = res.data;
};

const updateDepartmentData = (data) => {
	departments.value = data;
};
const onSearch = debounceAsync(async () => {
	const res = await departmentFilterApi({keyword: searchKeyword.value});
    departments.value = res.data;
}, 500);

fetchDepartmentData();
</script>
