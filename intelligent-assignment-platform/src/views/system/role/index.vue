<template>
	<div>
		<div class="flex justify-between items-center mb-[10px]">
        <div>
			<a-input v-model:value="searchKeyword" :placeholder="$t('common.search')"  class="w-75" @pressEnter="onSearch">
				<template #suffix>
					<a-tooltip title="Extra information">
						<SearchOutlined style="color: rgba(0, 0, 0, 0.45)"/>
					</a-tooltip>
				</template>
			</a-input>
            <!-- <a-button type="text ml-2" @click="handleAdvancedFilter">
                <img class="mt-[-4px]" src="@/assets/images/filter.svg" width="18" height="18" />
                <span class="ml-1">{{ $t('common.advancedFilter') }}</span>
            </a-button> -->
        </div>
        <a-button type="primary" @click="handleOpenModal()">+ {{ $t('common.add') }}</a-button>
    </div>
		<AuthorizeDialog ref="settingAuthorizeRef" />
		<a-table 
			:columns="columns" 
			:data-source="roleData" 
			row-key="id" 
			:scroll="{ x: 'max-content' }" 
			:pagination="false"
		>
			<template #expandIcon="props">
				<RightOutlined 
					v-if="props.record.children && props.record.children.length > 0"
					:rotate="props.expanded ? 90 : 0" 
					@click="e => props.onExpand(props.record, e)" 
					class="mr-2 text-[10px]"
				/>
				<span v-else class="mr-[18px]"></span>
			</template>
			<template #bodyCell="{ column, record }">
				<template v-if="column.dataIndex === 'status'">
					<a-tag :color="record.status === '启用' ? 'green' : ''">{{ record.status }}</a-tag>
				</template>
				<template v-else-if="column.dataIndex === 'ddl'">
					{{ record.ddl ? record.ddl.split(' ')[0] : '无' }}
				</template>
				<template v-else-if="column.dataIndex === 'action'">
					<a @click="handleOpenModal(record)">{{ $t('roleManagement.edit') }}</a>
					<a-button type="link" danger @click="deleteRole(record.id)">{{ $t('common.delete') }}</a-button>
				</template>
			</template>
		</a-table>
		<RoleManageDialog ref="showRoleRef" :role-data="roleData" @refresh="fetchRoleData"/>
		<AdvancedFilterDialog ref="showFilterRef" @update="updateRoleData" @refresh="fetchRoleData"/>

		<a-modal v-model:open="open" title="Notification" :confirm-loading="confirmLoading" @ok="handleOk" centered>
			<p>{{ modalText }}</p>
			<template #footer>
				<a-button key="back" @click="open = false">Cancel</a-button>
				<a-button key="submit" type="primary" :loading="loading" @click="handleOk">Confirm</a-button>
			</template>
		</a-modal>
	</div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import RoleManageDialog from '@/views/system/role/components/RoleManageDialog.vue';
import AuthorizeDialog from './components/AuthorizeDialog.vue'
import AdvancedFilterDialog from './components/AdvancedFilterDialog.vue';
import { rolesListApi, roleDelApi, roleFilterApi } from '@/api/system/index';
import { debounceAsync } from '@/utils/debounce';
import { message } from 'ant-design-vue';
import { SearchOutlined, RightOutlined } from '@ant-design/icons-vue';


const { t } = useI18n();
const settingAuthorizeRef = ref();
const showRoleRef = ref()
const roleData = ref([]);
const showFilterRef = ref()
const searchKeyword = ref('');
const open = ref(false);
const modalText = ref('Are you sure you want to delete this record?');
const recordId = ref();
const columns = [
	{ title: t('roleManagement.roleName'), dataIndex: 'name', key: 'name' },
	{ title: t('roleManagement.roleStatus'), dataIndex: 'status', key: 'status' },
	{ title: t('roleManagement.roleDescription'), dataIndex: 'desc', key: 'desc' },
	{ title: t('roleManagement.roleExpirationDate'), dataIndex: 'ddl', key: 'ddl' },
	{ title: t('roleManagement.creator'), dataIndex: 'creator', key: 'creator' },
	{ title: t('roleManagement.creationTime'), dataIndex: 'created_at', key: 'created_at' },
	{ title: t('roleManagement.userCount'), dataIndex: 'user_num', key: 'user_num', align: 'center' },
	{ title: t('common.action'), dataIndex: 'action', key: 'action',width: 150, fixed: 'right' }
];

const handleAdvancedFilter = () => {
	showFilterRef.value.showAdvancedFilter();
};

const fetchRoleData = async () => {
	try {
		const res = await rolesListApi();
		roleData.value = res.data;
	} catch (error) {
		console.error('获取角色数据失败:', error);
		roleData.value = [];
	}
};

const updateRoleData = (data) => {
	roleData.value = data;
}

const handleOpenModal = (record) => {
	showRoleRef.value.showRoleDialog(record);
};

const deleteRole = async(id) => {
	recordId.value = id;
	open.value = true;
};

const handleOk = debounceAsync(async () => {
	try {
		const res = await roleDelApi({ id: recordId.value });
		if (res.code === 0) {
			message.success(t('common.delSuccess'));
			roleData.value = roleData.value.filter(role => role.id !== recordId.value);
			await fetchRoleData();
		}
	} catch (error) {
		console.error(error);
	} finally {
		open.value = false;
	}
}, 300);

const onSearch = debounceAsync(async () => {
	const res = await roleFilterApi({keyword: searchKeyword.value});
    roleData.value = res.data;
}, 500);

onMounted(async() => {
	await fetchRoleData();
});
</script>
