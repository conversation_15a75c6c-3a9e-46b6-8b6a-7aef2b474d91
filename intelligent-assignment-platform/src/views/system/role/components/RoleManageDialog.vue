<template>
  <a-modal :open="open" width="890px" :title="isEdit ? t('roleManagement.editRole') : t('roleManagement.addRole')" @cancel="handleCancel" centered>
    <a-form :model="formState" :rules="rules" ref="formRef" class="mt-8 mr-6 mb-8 ml-2">
      <a-row>
        <a-col :span="12">
          <a-form-item :label="t('roleManagement.parentRole')" name="parent_id" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
            <a-cascader v-model:value="formState.parent_id" :placeholder="t('roleManagement.selectParentRole')" :options="roleOptions" change-on-select />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="t('roleManagement.roleName')" name="name" :labelCol="{ span: 8 }" :wrapperCol="{ span: 16 }">
            <a-input v-model:value="formState.name" :placeholder="t('roleManagement.enterRoleName')" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-form-item :label="t('roleManagement.roleDesc')" name="desc" :labelCol="{ span: 3 }">
        <a-input v-model:value="formState.desc" :placeholder="t('roleManagement.enterRoleDesc')" />
      </a-form-item>
      <a-row>
        <a-col :span="12">
          <a-form-item :label="t('roleManagement.roleStatus')" name="status" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
            <a-switch v-model:checked="computedStatus" :checked-children="$t('common.enabled')" :un-checked-children="$t('common.disabled')" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12">
          <a-form-item :label="t('roleManagement.ddlType')" name="ddl_type" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
            <a-radio-group v-model:value="formState.ddl_type">
              <a-radio value="0">{{ t('roleManagement.ddlTypeNone') }}</a-radio>
              <a-radio value="1">{{ t('roleManagement.ddlTypeBefore') }}</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12">
          <a-form-item :label="t('roleManagement.ddl')" name="ddl" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
            <a-date-picker
              v-model:value="formState.ddl"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              :disabled="formState.ddl_type !== '1'"
              style="width: 100%;"
              :placeholder="t('roleManagement.selectDDL')"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-form-item :label="t('roleManagement.menuPermission')" class="ml-13">
        <a-checkbox v-model:checked="expandAll">{{ t('roleManagement.expandCollapse') }}</a-checkbox>
        <a-checkbox v-model:checked="checkAll">{{ t('roleManagement.checkAll') }}</a-checkbox>
      </a-form-item>
      <a-form-item class="border border-solid border-[#ccc] ml-[100px] p-[15px] rounded" name="menu_ids">
        <a-tree
          checkable
          :tree-data="menuData"
          v-model:checkedKeys="formState.menu_ids"
          :expandedKeys="expandedKeys"
          @expand="onExpand"
        >
          <template #title="{ title }">
            <span>{{ title }}</span>
          </template>
        </a-tree>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="handleCancel">{{ $t('common.cancel') }}</a-button>
      <a-button type="primary" @click="handleOk">{{ $t('common.save') }}</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, onMounted, computed  } from 'vue';
import { menusApi, roleAddApi, roleUpdateApi, roleElementsApi } from '@/api/system/index';
import { useTreeStore } from '@/store/useTreeStore.ts';
import { useStatusSwitch } from '@/composable/useStatusSwitch'
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { useI18n } from 'vue-i18n';
import { debounceAsync } from '@/utils/debounce';
import { findParentPath } from '../../../../utils/findParentPath';

const props = defineProps({
  roleData: {
    type: Array,
    default: () => ([])
  },
});
const { t } = useI18n();
const emit = defineEmits(['refresh']);
const open = ref(false);
const formRef = ref();
const isEdit = ref(false);
const treeStore = useTreeStore();
const expandAll = ref(false);
const checkAll = ref(false);
const expandedKeys = ref([]);
const formState = reactive({
  id: null,
  parent_id: null,
  name: '',
  status: '',
  ddl: null,
  menu_ids: [],
  desc: '',
  ddl_type: '0'
});

const getAllKeys = (tree) => {
  let keys = [];
  tree.forEach(item => {
    keys.push(item.key);
    if (item.children && item.children.length) {
      keys = keys.concat(getAllKeys(item.children));
    }
  });
  return keys;
};

const computedStatus = useStatusSwitch(formState);
const rules = {
  name: [{ required: true, message: t('roleManagement.selectParentRole') }],
};

const roleOptions = computed(() => {
  const toCascaderOptions = (list) => list.map(item => ({
    label: item.name,
    value: item.id,
    children: item.children ? toCascaderOptions(item.children) : undefined
  }));
  return toCascaderOptions(props.roleData || []);
});

const menuOptions = computed(() => treeStore.getTreeOptions('menu')) 

const convertToTreeData = (data) => {
  if (!Array.isArray(data)) return [];
	return data.map(item => ({
		title: item.label,
		key: item.value,
		children: item.children ? convertToTreeData(item.children) : []
	}));
}

const menuData = computed(() => {
  return convertToTreeData(menuOptions.value);
});

watch(expandAll, (val) => {
  if (val) {
    expandedKeys.value = getAllKeys(menuData.value);
  } else {
    expandedKeys.value = [];
  }
});

watch(checkAll, (val) => {
  if (val) {
    formState.menu_ids = getAllKeys(menuData.value);
  } else {
    formState.menu_ids = [];
  }
});

const onExpand = (keys) => {
  expandedKeys.value = keys;
};

const handleCancel = () => {
  open.value = false;
  formRef.value.resetFields();
  checkAll.value = false;
  expandAll.value = false;
};

const handleOk = debounceAsync(async () => {
  try {
    await formRef.value.validate();
    const { ddl_type, ...rest } = formState;
    const requestData = {
      ...rest,
      ddl: formState.ddl === null ? 0 : (formState.ddl.$isDayjsObject ? formState.ddl.unix() : parseInt(dayjs(formState.ddl).unix())),
      parent_id: Array.isArray(formState.parent_id) 
      ? (formState.parent_id.length > 0 ? Number(formState.parent_id.at(-1)) : 0)
      : (formState.parent_id ? Number(formState.parent_id) : 0),
      status: computedStatus.value === true ? "启用" : "禁用",
      menu_ids: formState.menu_ids === null ? [] : formState.menu_ids
    };
    let res;
    if (isEdit.value === false) { 
      const {id, ...submit } = requestData;
      res = await roleAddApi(submit);
    } else {
      res = await roleUpdateApi(requestData);
    }
    if (res.code === 0) {
      message.success(t('common.saveSuccess'));
    }
    emit('refresh');
    handleCancel();
  } catch (error) {
    console.error('请求出错:', error);
  }
}, 300);

const handleElements = async () => {
  const res = await roleElementsApi({role_id: formState.id});
  if (res.code === 0) {
    formState.menu_ids = res.data;
  }
};

onMounted(async () => {
  await Promise.all[treeStore.fetchTreeData('menu', menusApi)];
});

const showRoleDialog = (item) => {
  open.value = true;
  if (item && Object.keys(item).length > 0) {
    isEdit.value = true;
    const parentPath = findParentPath(roleOptions.value, item.id) || [];
    Object.keys(formState).forEach(key => {
      if(key === 'ddl' && item[key]) {
        formState[key] = dayjs(item[key]);
      } else {
        formState[key] = item[key] !== undefined ? item[key] : null;
      }
    });
    formState.parent_id = parentPath;
    handleElements();
  } else {
    isEdit.value = false;
    Object.keys(formState).forEach(key => {
      if (Array.isArray(formState[key])) {
        formState[key] = [];
      } else {
        formState[key] = '';
      }
    });
  }
};

defineExpose({
    showRoleDialog
});
</script>