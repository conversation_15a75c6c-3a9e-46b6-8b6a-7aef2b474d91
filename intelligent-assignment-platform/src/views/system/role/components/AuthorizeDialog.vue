<template>
    <a-modal :open="open" width="100%" title="数据权限设置" @cancel="handleCancel" centered>
        <a-alert :message="message" type="info" show-icon class="mt-5 mb-5" />
        <a-table :columns="columns" :data-source="filteredMenus" row-key="id" :row-selection="rowSelection">
            <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'type'">
                    <span>{{ getTagText(record.type) }}</span>
                </template>
                <template v-if="column.dataIndex === 'action'">
                    <a-radio-group v-model:value="column.type">
                        <a-radio value="1">全部</a-radio>
                        <a-radio value="2">本部门及以下</a-radio>
                        <a-radio value="3">本部门</a-radio>
                        <a-radio value="4">仅本人</a-radio>
                        <a-radio value="5" @click="handleCustomize(column.key)">自定义</a-radio>
                    </a-radio-group>
                </template>
            </template>
        </a-table>
        <template #footer>
            <a-button type="primary" @click="handleOk">确定</a-button>
            <a-button @click="open = false">取消</a-button>
        </template>
    </a-modal>
    <CustomizeDialog ref="settingCustomizeRef" />
</template>
<script setup>
import { ref, computed } from 'vue';
import CustomizeDialog from './CustomizeDialog.vue'
import { menusApi } from '@/api/system/index';
const searchKeyword = ref('');
const settingCustomizeRef = ref();
const formRef = ref();
const open = ref(false);
const message = ref('并非所有接口都需要配置数据权限，只适用于页面与菜单，多用于业务模块')
const menuData = ref([]);
const columns = [
    {
        title: '菜单名称',
        dataIndex: 'menuName',
        key: 'menuName'
    },
    {
        title: '路由路径',
        dataIndex: 'routePath',
        key: 'routePath'
    },
    {
        title: 'API接口',
        dataIndex: 'apiUrl',
        key: 'apiUrl'
    },
    {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
    },
    {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
    }
];

const handleOk = async () => {
    try {
        await formRef.value.validate();
        emit('submit', { ...formState });
        handleCancel();
    } catch (error) {
        console.error('Validation failed:', error);
    }
};

const handleCancel = () => {
    open.value = false;
};

const menuList = async() => {
    try {
        // const res = await menusApi();
        // menuData.value = res.data;
    } catch (error) {
        console.error("请求失败:", error);
    }
}

const filteredMenus= computed(() => {
    return menuData.value.filter(menu =>
        menu.menuName.includes(searchKeyword.value)
    );
});

const showAuthorizeDialog = () => {
    open.value = true;
};

const handleCustomize = () => {
    settingCustomizeRef.value.showCustomizeDialog();
};

const getTagText = (type) => {
    switch (type) {
        case '1':
            return '目录';
        case '2':
            return '菜单';
        case '3':
            return '页面';
        case '4':
            return '按钮';
        default:
            return '';
    }
};
const rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
    },
    getCheckboxProps: (record) => ({
        disabled: record.name === 'Disabled User',
        name: record.name,
    }),
};

defineExpose({
    showAuthorizeDialog
});
menuList();
</script>