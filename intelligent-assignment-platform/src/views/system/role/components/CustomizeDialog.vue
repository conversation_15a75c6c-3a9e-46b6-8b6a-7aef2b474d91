<template>
    <a-modal :open="open" width="100%" @cancel="handleCancel" centered>
        <a-tabs v-model:activeKey="activeKey" type="card" centered>
            <a-tab-pane key="1" tab="选择授权用户" class="p-[15px] pl-0">
                <div class="flex w-full">
                    <div class="w-80 mr-6">
                        <h4 class="text-[14px]">
                            <span class="w-[18px] h-full bg-[#000000] mr-[10px] rounded-[2px]">|</span>部门列表
                        </h4>
                        <a-table :columns="departmentColumns" :data-source="departments" row-key="id" :pagination="false">
                            <template #bodyCell="{ column, record }">
                                <template v-if="column.dataIndex === 'status'">
                                    <a-tag :color="record.status === '1' ? 'green' : 'red'">
                                        {{ record.status === '1' ? '启用' : '禁用' }}
                                    </a-tag>
                                </template>
                            </template>
                        </a-table>
                    </div>
                    <div class="flex-1">
                        <h4 class="pb-4">
                            <span class="w-[18px] h-full bg-[#000000] mr-[10px] rounded-[2px]">|</span>待选用户
                            <a-input-search v-model:value="searchKeyword" placeholder="请输入搜索关键词"
                                class="float-right w-75" />
                        </h4>
                        <a-table :columns="getColumns" :data-source="filteredUsers" row-key="id" :row-selection="rowSelection">
                            <template #bodyCell="{ column, record }">
                                <template v-if="column.dataIndex === 'status'">
                                    <a-tag :color="record.userStatus === true ? 'green' : ''">
                                        {{ record.userStatus === true ? '启用' : '禁用' }}
                                    </a-tag>
                                </template>
                            </template>
                        </a-table>
                    </div>
                </div>
            </a-tab-pane>
            <a-tab-pane key="2" tab="已选授权用户" class="pb-[15px]">
                <h4 class="pb-4">
                    <span class="w-[18px] h-full bg-[#000000] mr-[10px] rounded-[2px]">|</span>已选用户
                    <a-button danger class="bg-[#FF4D4F33] text-[#FFFFFF] float-right" @click="handleCancel"
                        :icon="h(DeleteOutlined)">移除已选</a-button>
                </h4>
                <a-table :columns="getColumns" :data-source="filteredUsers" row-key="id" :row-selection="rowSelection">
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.dataIndex === 'status'">
                            <a-tag :color="record.userStatus === true ? 'green' : ''">
                                {{ record.userStatus === true ? '启用' : '禁用' }}
                            </a-tag>
                        </template>
                        <template v-if="column.dataIndex === 'action'">
                            <a class="text-[#FF4D4F]" @click="handleDelete">移除</a>
                        </template>
                    </template>
                </a-table>
            </a-tab-pane>
            <template #leftExtra>
                <span class="font-semibold text-4 text-[#1F1F1F]">数据权限设置</span>
            </template>
        </a-tabs>
        <template #footer>
            <a-button type="primary" @click="handleOk">确定</a-button>
            <a-button v-if="activeKey === '2'" type="primary" class="bg-[#52C41A] text-[#FFFFFF]"
                @click="handleEditUser">通过并编辑用户</a-button>
            <a-button @click="open = false">取消</a-button>
        </template>
    </a-modal>
</template>

<script setup>
    import { ref, computed, h,reactive } from 'vue';
    import { DeleteOutlined } from '@ant-design/icons-vue';
    import { userListApi,departmentsApi } from '@/api/system/index';

    const activeKey = ref('1');
    const open = ref(false);
    const searchKeyword = ref('');
    const users = ref([]);
    const departments = ref([]);

const departmentColumns = [
    {
        title: '部门名称',
        dataIndex: 'title',
        key: 'title',
    },
    {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
    }
];

const baseColumns = [
    { title: '用户名称', dataIndex: 'name', key: 'name' },
    { title: '账号', dataIndex: 'account', key: 'account' },
    { title: '手机号', dataIndex: 'phone', key: 'phone' },
    { title: '所属部门', dataIndex: 'department', key: 'department' },
    { title: '办公地点', dataIndex: 'officeLocation', key: 'officeLocation' },
    { title: '用户来源', dataIndex: 'userSource', key: 'userSource' },
    { title: '启用状态', dataIndex: 'status', key: 'status', align: 'center' }
];

const getColumns = computed(() => {
    if (activeKey.value === '2') {
        return [...baseColumns, { title: '操作', dataIndex: 'action', key: 'action' }];
    }
    return baseColumns;
});

    const data = reactive({
			current: 1,
			pageSize: 10,
		})
    const userList = async() => {
        try {
            // const res = await userListApi(data);
            // users.value = res.data;
        } catch (error) {
            console.error("请求失败:", error);
        }
    }
    const filteredUsers = computed(() => {
        return users.value.filter(user => 
            user.name.includes(searchKeyword.value)
        )
    });

const rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
    },
    getCheckboxProps: (record) => ({
        disabled: record.name === 'Disabled User',
        name: record.name,
    }),
};

const handleOk = async () => {
    try {
        await formRef.value.validate();
        emit('submit', { ...formState });
        handleCancel();
    } catch (error) {
        console.error('Validation failed:', error);
    }
};

const handleCancel = () => {
    open.value = false;
};
const showCustomizeDialog = () => {
    open.value = true;
};

    defineExpose({
        showCustomizeDialog
    });
    const departmentData = async () => {
        try {
        // const res = await departmentsApi();
        // departments.value = res.data;
        } catch (error) {
            console.error("请求失败:", error);
        }
    }

    userList();
    departmentData();
</script>