<template>
    <div class="m-4 mr-0">
        <!-- 权限拒绝页面 -->
        <div v-if="isUnauthorized" class="flex-1 flex items-center justify-center h-[90vh]">
            <div class="text-center">
                <img src="@/assets/images/noPermisson.svg" alt="Permission Denied" class="mx-auto mb-4" style="width: 323px; height: 270px;" />
                <p class="text-gray-500 text-lg">Permission Denied</p>
            </div>
        </div>
        
        <!-- 正常仪表板页面 -->
        <div v-else>
            <a-row :gutter="16" class="w-full">
                <a-col v-for="(item, index) in cards" :key="index" :span="24 / cards.length" :flex="1">
                    <a-card class="mb-4" :style="cardStyles[index]">
                        <div class="card-content">
                            <div class="value text-2xl font-medium leading-9 text-black">{{item.value}}</div>
                            <div class="precision text-xs leading-5 text-[#1f1f1f]">{{item.precision}}</div>
                            <div class="stats flex items-center text-xs leading-5 text-black mt-7">
                                <img :src="item.icon === 'CaretUpOutlined' ? upImg : downImg" width="16" height="16" />
                                <span class="ml-1">{{item.total}}%</span>
                            </div>
                        </div>
                    </a-card>
                </a-col>
            </a-row>

            <TimePeriodStatus @time-data="handleTimeData"/>
            <AccountStatus />
            <PersonalStatus />
            <a-card>
                <TaskStatus />
                <TaskChart />
            </a-card>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, computed, provide } from 'vue';
import { useRoute } from 'vue-router';
import TimePeriodStatus from './components/TimePeriodStatus.vue';
import TaskStatus from './components/TaskStatus.vue';
import TaskChart from './components/TaskChart.vue';
import PersonalStatus from './components/PersonalStatus.vue';
import AccountStatus from './components/AccountStatus.vue';
import upImg from '@/assets/images/up-green.svg';
import downImg from '@/assets/images/down-red.svg';
import { baseApi } from '../../api/dashboard/index'
import { dateToTimestamp } from '@/utils/date';
import { debounceAsync } from '@/utils/debounce';
import { tableLsitFileterApi } from '@/api/task/index';
import dayjs from 'dayjs';
import { useUserDataStore } from '@/store/userDataStore';
import { storeToRefs } from 'pinia';

const route = useRoute();

// 权限判断
const isUnauthorized = computed(() => {
    return route.query.authorized === '0';
})

const store = useUserDataStore();
const { userData } = storeToRefs(store);
const userOptions = ref([]);

provide('dashboardData', {
  userOptions,
});

const cardStyles = [
  { backgroundColor: '#FFF1B8' },
  { backgroundColor: '#FFD6E7' },
  { backgroundColor: '#FFCCC7' },
  { backgroundColor: '#D9F7BE' },
  { backgroundColor: '#B5F5EC' }
];
const cardData = ref([])
const prevcardData = ref([])

const rangeStart = ref(dayjs().startOf('day').hour(9));
const rangeEnd = computed(() => rangeStart.value.add(1, 'day').startOf('day').hour(4));

const prevRangeStart = computed(() => rangeStart.value.subtract(1, 'day'));
const prevRangeEnd = computed(() => rangeEnd.value.subtract(1, 'day'));

// 同比增长率
const calculateGrowthRate = (current, previous) => {
  if (!current || !previous) return '0';
  const growthRate = ((current - previous) / previous) * 100;
  return growthRate <= 0 ? '0' : growthRate.toFixed(1);
};

// 增长趋势图标
const getGrowthIcon = (current, previous) => {
  if (!current || !previous) return 'CaretDownOutlined';
  return current >= previous ? 'CaretUpOutlined' : 'CaretDownOutlined';
};

function getCompletion(data) {
  return data ? `${data.completed_count || 0}/${data.count || 0}` : '0/0';
}

function getValue(data, key, defaultValue = 0) {
  return data && data[key] !== undefined ? data[key] : defaultValue;
}
const getBaseData = debounceAsync(async () => {
  const timeData = {
    time_range_start: dateToTimestamp(rangeStart.value),
    time_range_end: dateToTimestamp(rangeEnd.value),
  }
  const res = await baseApi(timeData);
  if(res.code === "0000"){
    cardData.value = res.data;
  }
})

const getPrevBaseData = async () => {
  const timeData = {
    time_range_start: dateToTimestamp(prevRangeStart.value),
    time_range_end: dateToTimestamp(prevRangeEnd.value),
  }
  const res = await baseApi(timeData);
  prevcardData.value = res.data;
}

const handleTimeData = (start) => {
  rangeStart.value = start;
  getBaseData();
}

const cards = computed(() => {
  const c = cardData.value;
  const p = prevcardData.value;
  return [
    {
      value: getCompletion(c.facing),
      precision: 'Completion / Total (facings)', 
      total: calculateGrowthRate(parseFloat(getValue(c.facing, 'completed_count')), parseFloat(getValue(p.facing, 'completed_count'))),
      icon: getGrowthIcon(getValue(c.facing, 'completed_count'), getValue(p.facing, 'completed_count')),
    },
    {
      value: getCompletion(c.scene),
      precision: 'Completion / Total (Scene)', 
      total: calculateGrowthRate(parseFloat(getValue(c.scene, 'completed_count')), parseFloat(getValue(p.scene, 'completed_count'))),
      icon: getGrowthIcon(getValue(c.scene, 'completed_count'), getValue(p.scene, 'completed_count')),
    },
    {
      value: getValue(c.work, 'average_working_hours'),
      precision: 'Average Working Hours',
      total: calculateGrowthRate(parseFloat(getValue(c.work, 'average_working_hours')), parseFloat(getValue(p.work, 'average_working_hours'))),
      icon: getGrowthIcon(getValue(c.work, 'average_working_hours'), getValue(p.work, 'average_working_hours')),
    },
    {
      value: getValue(c, 'scene_completed_count_avg'),
      precision: 'Per Capita Capacity/Scene',
      total: calculateGrowthRate(parseFloat(getValue(c, 'scene_completed_count_avg')), parseFloat(getValue(p, 'scene_completed_count_avg'))),
      icon: getGrowthIcon(getValue(c, 'scene_completed_count_avg'), getValue(p, 'scene_completed_count_avg')),
    },
    {
      value: getValue(c, 'facing_completed_count_avg'),
      precision: 'Per Capita Capacity/facing',
      total: calculateGrowthRate(parseFloat(getValue(c, 'facing_completed_count_avg')), parseFloat(getValue(p, 'facing_completed_count_avg'))),
      icon: getGrowthIcon(getValue(c, 'facing_completed_count_avg'), getValue(p, 'facing_completed_count_avg')),
    },
  ]
})

onMounted(async() => {
  if (!store.hasData) { // 仅在数据未加载时调用API
      await store.fetchUserData();
  }
  userOptions.value = userData.value.map(user => ({ key: user.id, label: `${user.name}`}));
  await getPrevBaseData()
});
</script>

<style scoped>
:deep(.ant-card-body) {
  padding: 16px;
}
:deep(.ant-btn-primary) {
  color: #8c8c8c !important;
  border: 1px solid #8c8c8c !important;
}
</style>