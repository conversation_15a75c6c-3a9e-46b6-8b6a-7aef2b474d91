<template>
    <div class="mr-4 mb-4 h-[360px]">
      <div class="flex items-center justify-between">
          <div class="flex items-center">
            <span class="mr-2 font-[SourceHanSansCN] font-medium text-[14px] leading-[20px]">Task Chart</span>
            <a-range-picker v-model:value="rangeData" class="w-60" picker="week" :presets="rangePresets" @change="handleDateChange"/>
          </div>
      </div>
      <div class="mt-4" ref="target">
        <div ref="container" class="h-[320px]">
          <template v-if="!initialData?.length">
            <div class="h-[320px] flex items-center justify-center">
              <a-empty :image="simpleImage" />
            </div>
          </template>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, onBeforeUnmount, computed, watch } from 'vue';
  import { corelib, extend, Runtime } from '@antv/g2';
  import dayjs from 'dayjs';
  import { Empty } from 'ant-design-vue';
  import { taskDateApi } from '../../../api/dashboard/index'
  import { dateToTimestamp } from '@/utils/date';
  import { useLazyData } from '@/composable/useLazyData';

  const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
  let chart;
  const rangeData = ref([dayjs().startOf('week'), dayjs().endOf('week')]);
  const container = ref(null);
  const initialData = ref([]);
  const rangePresets = ref([
    { label: 'This week', value: [dayjs().startOf('week'), dayjs().endOf('week')]},
    { label: 'Last week', value: [dayjs().subtract(1, 'week').startOf('week'), dayjs().subtract(1, 'week').endOf('week')]},
    { label: 'Last two weeks', value: [dayjs().subtract(2, 'week').startOf('week'), dayjs().subtract(1, 'week').endOf('week')]},
    { label: 'Last three weeks', value: [dayjs().subtract(3, 'week').startOf('week'), dayjs().subtract(1, 'week').endOf('week')]},
  ]);

  const mockData = computed(() => ({
    data: {
      data_list: Array(20).fill(0).map((_, index) => {
        return {
          scene_count: Math.floor(Math.random() * 100),
          facing_count: Math.floor(Math.random() * 100),
          worker_count: Math.floor(Math.random() * 100),
          // 动态依赖当前的 rangeData.value[0]
          date: dayjs(rangeData.value[0]).add(
            index * ((rangeData.value[1].diff(rangeData.value[0], 'day') + 1) / 20), 
            'day'
          ).format('YYYY-MM-DD')
        };
      })
    },
    code: "0000",
    message: "success"
  }));
  
  const fetchData = async () => {
    const commitData = {
      time_range_start: dateToTimestamp(rangeData.value[0]),
      time_range_end: dateToTimestamp(rangeData.value[1]),
    }
    return await taskDateApi(commitData);
  };

  const { result, target } = useLazyData(() => fetchData());

  watch(result, (newResult) => {
    if (!newResult || !newResult.data) {
      initialData.value = [];
      return;
    }
    // initialData.value = mockData.value?.data.data_list;

    initialData.value = newResult.data?.data_list;
    if(!chart) {
      initChart();
    } else {
      updateChart();
    }
  }, { immediate: true });
  
  // 转为图表所需格式
const transformedData = computed(() => {
  return initialData.value.reduce((acc, item) => {
    const metrics = [
      { name: 'Total Scene', value: item.scene_count },
      { name: 'Per Person Output', value: item.worker_count },
      { name: 'Total Facing', value: item.facing_count }
    ];
    metrics.forEach(metric => {
      acc.push({
        date: dayjs(item.date).format('MM/DD'),
        name: metric.name,
        value: metric.value
      });
    });
    return acc;
  }, []);
});

const initChart = () => {
  const Chart = extend(Runtime, corelib());
  chart = new Chart({
    container: container.value,
    autoFit: true,
    height: 320,
  });

  if(transformedData.value && transformedData.value.length > 0) {
    chart.data(transformedData.value);
  }

  chart.legend({
    color: {
      position: 'top',
      layout: {
        justifyContent: 'center',
      },
    },
  })
  .encode('color', 'name')
  .scale('color', { range: ['#5B8FF9', '#5AD8A6', '#5D7092'] })

  chart
    .interval()
    .encode('x', 'date')
    .encode('y', 'value')
    .transform({ type: 'dodgeX' })
    .scale('y', { independent: true })
    .interaction('elementHighlight', { background: true })
    .axis({ 
      'x': {
        title: '',
        labelFill: '#1677FF',
      },
      'y': {
        title: '',
        labelFill: '#1677FF',
        labelFormatter: (d) => d >= 1000 ? `${d/1000}.0k` : `${d}`,
      }
    });

    chart.render();
  }

  // 更新图表数据，不需要重新render
  const updateChart = () => {
    if (!chart) return;
    chart.changeData(transformedData);
  };

const handleDateChange = async(date) => {
  if (date) {
    const start = dayjs(date[0]).startOf('week');
    const end = dayjs(date[0]).endOf('week');
    rangeData.value = [start, end];

    const res = await fetchData();
    if (res.code === '0000') {
      // initialData.value = mockData.value?.data.data_list;
      initialData.value = res.data?.data_list;
    }
    updateChart();
  }
};

  onBeforeUnmount(() => {
    if (chart) {
      chart.destroy();
    }
  });
  </script>