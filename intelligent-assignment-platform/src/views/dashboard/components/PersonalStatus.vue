<template>
    <a-card class="mr-4 mb-4 h-[650px]">
      <div class="flex items-center justify-between">
          <div class="flex items-center">
            <span class="mr-2 font-[SourceHanSansCN] font-medium text-[14px] leading-[20px]">Personal Status</span>
              <a-form :model="formState" layout="inline" ref="FormRef" class="flex flex-wrap gap-y-2">
                <a-form-item name="user_ids" class="w-30">
                    <a-select 
                        show-search
                        v-model:value="formState.user_ids" 
                        placeholder="User Name" allowClear 
                        class="rounded-md" 
                        :show-arrow="false"
                        :filter-option="false"
                        :options="searchOptions"
                        :not-found-content="null"
                        @search="handleSearch"
                        @change="fetchData">
                        </a-select>
                    </a-form-item>
                    <a-form-item name="project_ids">
                        <a-input-number v-model:value="formState.project_ids" placeholder="Project ID" allowClear class="rounded-md" @change="fetchData"></a-input-number>
                    </a-form-item>
                    <a-form-item name="task_type_list" class="w-30">
                        <a-select v-model:value="formState.task_type_list" placeholder="Task Type" allowClear class="rounded-md" @change="fetchData">
                            <a-select-option v-for="type in taskTypeOptions" :key="type.value" :value="type.value">{{ type.label }}</a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item name="rangeData" class="w-60">
                        <a-range-picker
                        v-model:value="rangeData"
                        picker="date"
                        format="YYYY-MM-DD"
                        :placeholder="['Start Time', 'End Time']"
                        @change="fetchData"
                        />
                    </a-form-item>
                </a-form>
          </div>
          <div class="flex items-center">
            <a-button class="mr-2 border-[#1677FF] text-[#1677FF]" @click="handleExport" :disabled="!tableData?.length"> 
                <img class="mt-[-4px]" :src="tableData?.length ? '/src/assets/images/export-hover.svg' : '/src/assets/images/export.svg'"
                    width="16" height="16" loading="lazy"/>
                <span class="ml-1">Export</span>
            </a-button>
          </div>
      </div>
      <div class="mt-4" ref="target">
          <a-table :columns="columns" :data-source="tableData" :scroll="{ x: 'max-content'}" :loading="loading" :pagination="pagination.paginationConfig">
            <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'started_at'">
                {{ formatTimestamp(record.started_at)  }}
                </template>
                <template v-if="column.dataIndex === 'finished_at'">
                  {{ formatTimestamp(record.finished_at) }}
                </template>
                <template v-if="column.dataIndex === 'actual_work_secs'">
                  {{ (record.actual_work_secs / 3600).toFixed(1) }}
                </template>
            </template>
          </a-table>
      </div>
    </a-card>
</template>
  
<script setup>
import {  reactive, ref, watch, inject } from 'vue';
import { sceneApi, sceneExportApi } from '../../../api/dashboard/index'
import { dateToTimestamp, formatTimestamp } from '@/utils/date';
import dayjs from 'dayjs';
import { exportFile } from '@/utils/exportFile';
import { useTablePagination } from '@/composable/useTablePagination';
import { useLazyData } from '@/composable/useLazyData';
import { taskTypeOptions } from '@/utils/dropdownOptions';

const pagination = useTablePagination({
    onChange: (page, size) => {
        handlePageChange(page, size);
    },
});
const { userOptions } = inject('dashboardData');
const loading = ref(false);
const rangeData = ref([]);
const tableData = ref([]);
const searchOptions = ref([]);
let timeout;
let currentValue = '';
const formState = reactive({
    user_ids: [],
    project_ids: null,
    task_type_list: [],
})

const getTimeRangeData = () => {
    const processedData = {
        user_ids: (Array.isArray(formState.user_ids)? formState.user_ids : [formState.user_ids]).map(Number).filter(Boolean),
        project_ids: formState.project_ids,
        task_type_list: (Array.isArray(formState.task_type_list) ? formState.task_type_list : [formState.task_type_list]).filter(String),
        time_range_start: rangeData.value[0] ? dateToTimestamp(dayjs(rangeData.value[0]).format('YYYY-MM-DD')) : null,
        time_range_end: rangeData.value[1] ? dateToTimestamp(dayjs(rangeData.value[1]).format('YYYY-MM-DD')) : null,
        page_no: pagination.page_no.value - 1,
        page_size: pagination.page_size.value,
    };
    return processedData;
};

const fetchData = async () => {
  return await sceneApi(getTimeRangeData());
};

const { result, target } = useLazyData(() => fetchData());

const mockData = Array(10000).fill(0).map((_, index) => {
  return {
      user_id: Math.floor(Math.random() * 100), // 随机生成用户ID
      task_id: Math.floor(Math.random() * 1000), // 随机生成任务ID
      country: ['China', 'USA', 'Japan', 'UK', 'Germany'][Math.floor(Math.random() * 5)], // 随机选择国家
      client: ['ClientA', 'ClientB', 'ClientC', 'ClientD'][Math.floor(Math.random() * 4)], // 随机选择客户
      model_type: ['TypeA', 'TypeB', 'TypeC'][Math.floor(Math.random() * 3)], // 随机选择模型类型
      task_type: ['Regular', 'Special', 'Urgent'][Math.floor(Math.random() * 3)], // 随机选择任务类型
      out_scene_id: Math.random().toString(36).substring(2, 15), // 生成随机字符串作为场景ID
      total_facings: Math.floor(Math.random() * 5000) + 1000, // 随机生成1000-6000之间的数
      finished_at: Math.floor(Date.now() / 1000) + Math.floor(Math.random() * 86400), // 随机生成结束时间戳
      started_at: Math.floor(Date.now() / 1000) - Math.floor(Math.random() * 86400), // 随机生成开始时间戳
      actual_work_secs: Math.floor(Math.random() * 3600 * 8) // 随机生成工作秒数（最多8小时）
  }
})
const handlePageChange = async (page, size) => {
    loading.value = true;
    pagination.page_no.value = page;
    pagination.page_size.value = size;
    const res = await fetchData();
    if (res.code === '0000') {
      // tableData.value = mockData;
      tableData.value = res.data?.data_list;
      pagination.total.value = res.data?.count;
    }
    loading.value = false;
}

const columns = [
  { title: 'Name', dataIndex: 'user_id', key: 'user_id' },
  { title: 'Data', dataIndex: 'finished_at', key: 'finished_at' },
  { title: 'Task ID', dataIndex: 'task_id', key: 'task_id' },
  { title: 'Task Key', dataIndex: 'task_type', key: 'task_type' },
  { title: 'Scene ID', dataIndex: 'out_scene_id', key: 'out_scene_id' },
  { title: 'Total Facings', dataIndex: 'total_facings', key: 'total_facings' },
  { title: 'Start Time', dataIndex: 'started_at', key: 'started_at' },
  { title: 'End Time', dataIndex: 'finished_at', key: 'finished_at' },
  { title: 'Scene Working Hours', dataIndex: 'actual_work_secs', key: 'actual_work_secs' }
];

const handleSearch = (val) => {
  if (timeout) {
    clearTimeout(timeout);
    timeout = null;
  }
  currentValue = val;
  function performSearch() {
    if (currentValue === val) {
      if (val) {
        searchOptions.value = userOptions.value
          .filter(user => user.label.toLowerCase().includes(val.toLowerCase()))
          .map(user => ({ value: user.key, label: user.label }));
      } else {
        searchOptions.value = [];
      }
    }
  }
  timeout = setTimeout(performSearch, 300);
};

const handleExport = async() => {
    exportFile({ export: await sceneExportApi(getTimeRangeData())})
};

watch(result, (newVal) => {
  if (!newVal || !newVal.data) {
    tableData.value = [];
    return;
  }
  // tableData.value = mockData;
  tableData.value = newVal.data?.data_list;
}, { immediate: true });
</script>