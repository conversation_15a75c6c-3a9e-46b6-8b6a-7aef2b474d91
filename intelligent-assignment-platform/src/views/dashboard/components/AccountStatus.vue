<template>
    <a-card class="mr-4 mb-4 h-[650px]">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <span class="mr-2 font-[SourceHanSansCN] font-medium text-[14px] leading-[20px]">Account Status</span>
                <a-form :model="formState" layout="inline" ref="FormRef" class="flex flex-wrap gap-y-2">
                    <a-form-item name="name" class="w-30">
                        <a-select show-search v-model:value="formState.name" placeholder="User Name" allowClear
                            class="rounded-md" :show-arrow="false" :filter-option="false" :options="searchOptions"
                            :not-found-content="null" @search="handleSearch" @change="fetchData">
                        </a-select>
                    </a-form-item>
                    <a-form-item name="department_id" class="w-30" ref="departmentRef">
                        <a-cascader v-model:value="formState.department_id" :options="departmentOptions" allowClear
                            placeholder="Department" change-on-select class="rounded-md" @change="fetchData" />
                    </a-form-item>
                    <a-form-item name="login_status" class="w-40">
                        <a-select v-model:value="formState.login_status" allowClear placeholder="Login Status"
                            class="rounded-md" @change="fetchData">
                            <a-select-option v-for="type in loginStatusOptions" :key="type.value" :value="type.value">{{
                                type.label }}</a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item name="work_status" class="w-40">
                        <a-select v-model:value="formState.work_status" allowClear placeholder="Work Status"
                            class="rounded-md" @change="fetchData">
                            <a-select-option v-for="type in workStatusOptions" :key="type.value" :value="type.value">{{
                                type.label }}</a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item name="work_page" class="w-30">
                        <a-select v-model:value="formState.work_page" allowClear placeholder="Work Page"
                            class="rounded-md" @change="fetchData">
                            <a-select-option v-for="type in workPageOptions" :key="type.value" :value="type.value">{{
                                type.label }}</a-select-option>
                        </a-select>
                    </a-form-item>
                </a-form>
            </div>
            <div class="flex items-center">
                <!-- 禁用状态下设置为 text-[#8C8C8C] 颜色 -->
            <a-button class="mr-2 border-[#1677FF] text-[#1677FF]" @click="handleExport" :disabled="!accountStatusData?.length"> 
                <img class="mt-[-4px]" :src="accountStatusData?.length ? '/src/assets/images/export-hover.svg' : '/src/assets/images/export.svg'"
                    width="16" height="16" loading="lazy"/>
                <span class="ml-1">Export</span>
            </a-button>
            </div>
        </div>
        <div class="flex mt-4">
            <div class="w-[70%] pr-4" ref="target">
                <a-table :columns="columns" :data-source="accountStatusData" :scroll="{ x: 'max-content' }"
                    :loading="loading" :pagination="pagination.paginationConfig">
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.dataIndex === 'login_timestamp'">
                            {{ formatTimestamp(record.login_timestamp) }}
                        </template>
                        <template v-if="column.dataIndex === 'last_headrtbeat'">
                            {{ formatTimestamp(record.last_headrtbeat) }}
                        </template>
                        <template v-if="column.dataIndex === 'login_status'">
                            <span :class="record.login_status === 'Online' ? 'text-[#1677FF]' : 'text-[#FF4D4F]'">
                                {{ record.login_status }}
                            </span>
                        </template>
                        <template v-if="column.dataIndex === 'work_status'">
                            <a-tag :class="[
                                'min-w-[60px] text-center',
                                record.work_status === 'Idle' ? 'bg-[#FF4D4F]/10 text-[#ff4d4f] border border-solid border-[#FF4D4F]' :
                                    record.work_status === 'Active' ? 'bg-[#52c41a]/10 text-[#52c41a] border border-solid border-[#52c41a]' :
                                        'bg-[#faad14]/10 text-[#faad14] border border-solid border-[#faad14]'
                            ]">
                                {{ record.work_status }}
                            </a-tag>
                        </template>
                        <template v-if="column.dataIndex === 'work_hour'">
                            {{rankData.find(item => item.name === record.name)?.work_hour || '-'}}h
                        </template>
                    </template>
                </a-table>
            </div>
            <div class="w-[30%]" ref="target1">
                <div class="h-[520px] overflow-y-auto">
                    <a-list item-layout="horizontal" :data-source="rankData">
                        <template #renderItem="{ item }">
                            <a-list-item class="h-13">
                                <a-list-item-meta>
                                    <template #title>
                                        <div class="flex justify-between items-center">
                                            <span>{{ item.name }}</span>
                                            <span>{{ item.work_hour }}h</span>
                                        </div>
                                    </template>
                                    <template #avatar>
                                        <div v-if="item.key <= 3" class="w-8 h-8 flex items-center justify-center">
                                            <img v-if="item.key === 1" src="@/assets/images/top_1.svg" alt="No.1"
                                                class="w-9 h-9" loading="lazy"/>
                                            <img v-else-if="item.key === 2" src="@/assets/images/top_2.svg" alt="No.2"
                                                class="w-9 h-9" loading="lazy"/>
                                            <img v-else-if="item.key === 3" src="@/assets/images/top_3.svg" alt="No.3"
                                                class="w-9 h-9" loading="lazy"/>
                                        </div>
                                        <a-avatar v-else :src="item.avatar" class="h-8 bg-[#00ABEB]">
                                            <span>{{ item.key }}</span>
                                        </a-avatar>
                                    </template>
                                    <template #description>
                                        <a-progress :percent="item.work_hour" class="h-2" :showInfo="false" />
                                    </template>
                                </a-list-item-meta>
                            </a-list-item>
                        </template>
                    </a-list>
                </div>
            </div>
        </div>
    </a-card>
</template>

<script setup>
import { ref, reactive, inject, computed, watch } from 'vue';
import { departmentsApi } from '@/api/system/index';
import { useTreeStore } from '@/store/useTreeStore.ts';
import { formatTimestamp } from '@/utils/date';
import { accountStatusApi, accountExportApi, rankApi } from '../../../api/dashboard/index'
import { exportFile } from '@/utils/exportFile';
import { loginStatusOptions, workStatusOptions, workPageOptions } from '@/utils/dropdownOptions';
import { useTablePagination } from '@/composable/useTablePagination';
import { useLazyData } from '@/composable/useLazyData';

const pagination = useTablePagination({
    onChange: (page, size) => {
        handlePageChange(page, size);
    },
});
const { userOptions } = inject('dashboardData');
const loading = ref(false);
let timeout;
let currentValue = '';
const searchOptions = ref([]);
const treeStore = useTreeStore()
const departmentOptions = computed(() => treeStore.getTreeOptions('department'));
const accountStatusData = ref([]);
const rankData = ref([]);

const formState = reactive({
    name: null,
    department_id: 0,
    login_status: null,
    work_status: null,
    work_page: null,
})

const columns = [
    { title: 'id', dataIndex: 'userid', key: 'userid' },
    { title: 'Account Name', dataIndex: 'name', key: 'name' },
    { title: 'Department', dataIndex: 'department', key: 'department' },
    { title: 'Login Time', dataIndex: 'login_timestamp', key: 'login_timestamp' },
    { title: 'Login Status', dataIndex: 'login_status', key: 'login_status' },
    { title: 'Work Status', dataIndex: 'work_status', key: 'work_status' },
    { title: 'Work Page', dataIndex: 'work_page', key: 'work_page' },
    { title: 'Last Heartbeat', dataIndex: 'last_headrtbeat', key: 'last_headrtbeat' },
    { title: 'Total working hours', dataIndex: 'work_hour', key: 'work_hour' }
];

const commitData = () => {
    return {
        ...formState,
        department_id: formState.department_id ? Number(formState.department_id) : 0,
        page_no: pagination.page_no.value - 1,
        page_size: pagination.page_size.value,
    };
};

const mockData = Array.from({ length: 10000 }, (_, index) => ({
    userid: index + 1,
    name: `User ${index + 1}`,
    department: ['开发部门', '质量检测', '脑波'][Math.floor(Math.random() * 3)],
    login_timestamp: Date.now() - Math.floor(Math.random() * 86400000), // 随机24小时内的时间戳
    login_status: ['Online', 'Offline'][Math.floor(Math.random() * 2)],
    work_status: ['Active', 'Idle', 'Loafing'][Math.floor(Math.random() * 3)],
    work_page: ['Validation Task', 'My Error', 'QA Task'][Math.floor(Math.random() * 3)],
    last_headrtbeat: Date.now() - Math.floor(Math.random() * 3600000) // 随机1小时内的时间戳
}))

const handleSearch = (val) => {
    if (timeout) {
        clearTimeout(timeout);
        timeout = null;
    }
    currentValue = val;
    function performSearch() {
        if (currentValue === val) {
            if (val) {
                searchOptions.value = userOptions.value
                    .filter(user => user.label.toLowerCase().includes(val.toLowerCase()))
                    .map(user => ({ value: user.label, label: user.label }));
            } else {
                searchOptions.value = [];
            }
        }
    }
    timeout = setTimeout(performSearch, 300);
};

const handleExport = async () => {
    exportFile({ export: await accountExportApi(commitData()) })
};

const mockDataaaa = Array.from({ length: 20 }, (_, index) => ({
    key: index + 1,
    name: `User ${index + 1}`,
    work_hour: Number((100 - (index * (100 / 20))).toFixed(1)),
}));

const fetchData = async () => {
    return await accountStatusApi(commitData());
};

const { result, target } = useLazyData(() => fetchData());

const fetchRank = async () => {
    return await rankApi();
};

const { result: result1, target: target1 } = useLazyData(() => fetchRank());

const handlePageChange = async (page, size) => {
    pagination.page_no.value = page;
    pagination.page_size.value = size;
    const res = await fetchData();
    if (res.code === 0) {
        accountStatusData.value = res.data;
    }
}

const fetchDepartment = async () => {
    return await treeStore.fetchTreeData('department', departmentsApi)
};

const { result: departmentResult, target: departmentRef } = useLazyData(() => fetchDepartment());

watch([result, result1], async ([newResult, newRank]) => {
    // accountStatusData.value = mockData;
    // rankData.value = mockDataaaa;
      accountStatusData.value = newResult?.data;
      rankData.value = newRank?.data;
}, { immediate: true });
</script>


<style scoped>
.export-btn:hover {
    color: #1677FF !important;
    border-color: #1677FF !important;
}

.export-btn:hover .export-icon {
    content: url('@/assets/images/export-hover.svg');
}
</style>
