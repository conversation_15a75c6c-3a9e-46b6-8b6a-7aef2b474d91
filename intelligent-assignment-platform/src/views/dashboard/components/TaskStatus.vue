<template>
    <div class="mr-4 mb-4 h-[650px]">
      <div class="flex items-center justify-between">
          <div class="flex items-center">
            <span class="mr-2 font-[SourceHanSansCN] font-medium text-[14px] leading-[20px]">Task Status</span>
              <a-form :model="formState" layout="inline" ref="FormRef" class="flex flex-wrap gap-y-2">
                    <a-form-item name="project_ids">
                        <a-input-number v-model:value="formState.project_ids" placeholder="Project ID" allowClear class="rounded-md"  @change="fetchData"></a-input-number>
                    </a-form-item>
                    <a-form-item name="task_type_list" class="w-30">
                        <a-select v-model:value="formState.task_type_list" placeholder="Task Type" allowClear class="rounded-md"  @change="fetchData">
                            <a-select-option v-for="type in taskTypeOptions" :key="type.value" :value="type.value">{{ type.label }}</a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item name="rangeData" class="w-60">
                        <a-range-picker
                        v-model:value="rangeData"
                        picker="date"
                        format="YYYY-MM-DD"
                        :placeholder="['Start Time', 'End Time']"
                         @change="fetchData"
                        />
                    </a-form-item>
                </a-form>
          </div>
          <div class="flex items-center">
            <a-button class="mr-2 border-[#1677FF] text-[#1677FF]" @click="handleExport" :disabled="!tableData?.length"> 
                <img class="mt-[-4px]" :src="tableData?.length ? '/src/assets/images/export-hover.svg' : '/src/assets/images/export.svg'"
                    width="16" height="16" loading="lazy"/>
                <span class="ml-1">Export</span>
            </a-button>
          </div>
      </div>
      <div class="mt-4" ref="target">
          <a-table 
            :columns="columns" 
            :data-source="tableData" 
            :scroll="{ x: 'max-content'}" 
            :loading="loading"
            :pagination="pagination.paginationConfig"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'finished_at'">
                {{ formatTimestamp(record.finished_at, 'YYYY/MM/DD') }}
              </template>
              <template v-if="column.dataIndex === 'per_person_output_min'">
                {{ record.per_person_output_hour * 60 }}
              </template>
            </template>
          </a-table>
      </div>
    </div>
</template>
  
<script setup>
import { ref, watch, reactive, inject } from 'vue';
import { taskApi, taskExportApi } from '../../../api/dashboard/index'
import { dateToTimestamp, formatTimestamp } from '@/utils/date';
import dayjs from 'dayjs';
import { exportFile } from '@/utils/exportFile';
import { useTablePagination } from '@/composable/useTablePagination';
import { useLazyData } from '@/composable/useLazyData';
import { taskTypeOptions } from '@/utils/dropdownOptions';

const pagination = useTablePagination({
    onChange: (page, size) => {
        handlePageChange(page, size);
    },
});
const loading = ref(false);
const rangeData = ref([]);
const tableData = ref([]);
const formState = reactive({
    project_ids: null,
    task_type_list: [],
})

const columns = [
  { title: 'Task ID', dataIndex: 'task_id', key: 'task_id' },
  { title: 'Task Key', dataIndex: 'task_type', key: 'task_type' },
  { title: 'Completion Data', dataIndex: 'finished_at', key: 'finished_at' },
  { title: 'Total Scenes', dataIndex: 'total_scenes', key: 'total_scenes' },
  { title: 'Total Facings', dataIndex: 'total_facings', key: 'total_facings' },
  { title: 'Total Work Hours', dataIndex: 'actual_work_secs', key: 'actual_work_secs' },
  { title: 'Actual Head Count', dataIndex: 'actual_head_count', key: 'actual_head_count' },
  { title: 'Avg Task Hours', dataIndex: 'avg_task_hour', key: 'avg_task_hour' },
  { title: 'Per Person Output/H', dataIndex: 'per_person_output_hour', key: 'per_person_output_hour' },
  { title: 'Per Person Output/M', dataIndex: 'per_person_output_min', key: 'per_person_output_min' },
];

const getTimeRangeData = () => {
    const processedData = {
        project_ids: formState.project_ids,
        task_type_list: (Array.isArray(formState.task_type_list) ? formState.task_type_list : [formState.task_type_list]).filter(String),
        time_range_start: rangeData.value[0] ? dateToTimestamp(dayjs(rangeData.value[0]).format('YYYY-MM-DD')) : undefined,
        time_range_end: rangeData.value[1] ? dateToTimestamp(dayjs(rangeData.value[1]).format('YYYY-MM-DD')) : undefined,
        page_no: pagination.page_no.value - 1,
        page_size: pagination.page_size.value,
    };
    return processedData;
};

const handleExport = async() => {
    exportFile({export: await taskExportApi(getTimeRangeData())})
};

const mockData = Array(10000).fill(0).map((_, index) => {
   return {
       task_id: `TASK${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
       country: ['China', 'USA', 'Japan', 'UK', 'Germany'][Math.floor(Math.random() * 5)],
       client: ['Walmart', 'Carrefour', 'Tesco', 'Aldi', 'Costco'][Math.floor(Math.random() * 5)],
       model_type: ['Detection', 'Classification', 'Segmentation', 'OCR'][Math.floor(Math.random() * 4)],
       task_type: ['Validation', 'Training', 'Testing', 'Deployment'][Math.floor(Math.random() * 4)],
       finished_at: Math.floor(Date.now() / 1000) - Math.floor(Math.random() * 30 * 24 * 60 * 60),
       total_scenes: Math.floor(Math.random() * 1000) + 100,
       total_facings: Math.floor(Math.random() * 500) + 50,
       actual_work_secs: Math.floor(Math.random() * 3600) + 1800,
       actual_head_count: Math.floor(Math.random() * 20) + 5,
       avg_task_hour: Math.floor(Math.random() * 10) + 2,
       per_person_output_hour: Math.floor(Math.random() * 10) + 2,
   }
})

const fetchData = async () => {
  return await taskApi(getTimeRangeData());
};

const { result, target } = useLazyData(() => fetchData());

const handlePageChange = async (page, size) => {
    loading.value = true;
    pagination.page_no.value = page;
    pagination.page_size.value = size;
    const res = await fetchData();
    if (res.code === '0000') {
      // tableData.value = mockData;
      tableData.value = res.data?.data_list;
      pagination.total.value = res.data?.count
    }
    loading.value = false;
}

watch(result, (newVal) => {
  if (!newVal || !newVal.data) {
    tableData.value = [];
    return;
  }
  // tableData.value = mockData;
  tableData.value = newVal.data?.data_list;
}, { immediate: true });
</script>