<template>
  <a-card class="mr-4 mb-4 h-[650px]">
    <div class="flex items-center justify-between">
        <div class="flex items-center">
            <span class="mr-2 font-[SourceHanSansCN] font-medium text-[14px] leading-[20px]">Time Period Status</span>
            <a-date-picker v-model:value="rangeStart" class="w-60" placeholder="Select Time" :format="'YYYY/MM/DD'" @change="handleDateChange"/>
        </div>
        <div class="flex items-center">
            <a-button class="mr-2 border-[#1677FF] text-[#1677FF]" v-if="activeKey === '2'" @click="handleExport" :disabled="!tableData?.length"> 
                <img class="mt-[-4px]" :src="tableData?.length ? '/src/assets/images/export-hover.svg' : '/src/assets/images/export.svg'"
                    width="16" height="16" loading="lazy"/>
                <span class="ml-1">Export</span>
            </a-button>
            <a-tabs v-model:activeKey="activeKey" type="card" class="flex-none bg-[#E5E5E5] [&_.ant-tabs-tab]:w-8 [&_.ant-tabs-tab]:h-8">
                <a-tab-pane key="1">
                    <template #tab>
                        <img  src="@/assets/images/chart.svg" width="16" height="16" loading="lazy"/>
                    </template>
                </a-tab-pane>
                <a-tab-pane key="2">
                    <template #tab>
                        <img  src="@/assets/images/list.svg" width="16" height="16" loading="lazy"/>
                    </template>
                </a-tab-pane>
            </a-tabs>
        </div>
    </div>
    <div class="mt-4">
      <div v-show="activeKey === '1'">
        <div ref="container" class="h-[320px]">
          <template v-if="!tableData?.length">
            <div class="h-[320px] flex items-center justify-center">
              <a-empty :image="simpleImage" />
            </div>
          </template>
        </div>
      </div>
      <div v-show="activeKey === '2'" class="h-[540px] overflow-y-auto">
        <a-table :columns="columns" :data-source="tableData" :loading="loading" :pagination="false">
            <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'hour_start'">
                    {{ record.hour_start.toString().padStart(2, '0') }}:00-{{ ((record.hour_start + 1) % 24).toString().padStart(2, '0') }}:00
                </template>
            </template>
        </a-table>
      </div>
    </div>
  </a-card>
</template>
<script setup>
import { ref, onMounted, onBeforeUnmount, computed } from 'vue';
import { corelib, extend, Runtime } from '@antv/g2';
import dayjs from 'dayjs';
import { Empty } from 'ant-design-vue';
import { hourApi, hourExportApi } from '../../../api/dashboard/index'
import { dateToTimestamp } from '@/utils/date';
import { debounceAsync } from '@/utils/debounce';
import { exportFile } from '@/utils/exportFile';

const loading = ref(false);
const activeKey = ref('1');
const rangeStart = ref(dayjs().startOf('day').hour(9));
const rangeEnd = computed(() => rangeStart.value.add(1, 'day').startOf('day').hour(4));
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
const emit = defineEmits(['time-data']);
const tableData = ref([]);
const container = ref(null);
let chart;

const columns = [
    { title: 'Interval', dataIndex: 'hour_start', key: 'hour_start' },
    { title: 'Completed this hour', dataIndex: 'completed_facing', key: 'completed_facing' },
    { title: 'Actual Account', dataIndex: 'actual_worker_count', key: 'actual_worker_count' },
    { title: 'Actual Work hours', dataIndex: 'actual_work_hour', key: 'actual_work_hour' },
    { title: 'Per capita capacity/H', dataIndex: 'per_capita_hour', key: 'per_capita_hour' },
    { title: 'Per capita capacity/M', dataIndex: 'per_capita_min', key: 'per_capita_min' },
];

const getTimeRangeData = () => ({
    time_range_start: dateToTimestamp(rangeStart.value),
    time_range_end: dateToTimestamp(rangeEnd.value),
});

const generateData = debounceAsync(async() => {
    loading.value = true;
    try {
        // todo 调用后端接口 获取到数据
        // 表单校验 rangeStart不为空
        const res = await hourApi(getTimeRangeData())
        if(res.code === "0000"){
            const mockData =  Array(19).fill(0).map((_, index) => {
                // 从早上10点开始
                return {
                    hour_start: index + 10 > 23 ? index - 14 : index + 10,
                    completed_facing: Math.floor(Math.random() * 3000) + 1000, // 随机生成1000-4000之间的数
                    actual_worker_count: Math.floor(Math.random() * 3000) + 1000, // 随机生成0-1之间的浮点数
                    actual_work_hour: Math.random().toFixed(1), // 随机生成1000-4000之间的数
                    date: `2025-06-${String(index + 1).padStart(2, '0')}`, // 从6月1日开始的连续日期
                    per_capita_hour: Math.random().toFixed(1), // 随机生成0-1之间的浮点数
                    per_capita_min: Math.random().toFixed(1) // 随机生成0-60之间的数
                }
            })
            // tableData.value = mockData;
          tableData.value = res.data?.data_list;
        }
    } catch (error) {
      console.error(error);
    } finally {
      loading.value = false;
  }
});


const handleExport = async() => {
    exportFile({ export: await hourExportApi(getTimeRangeData())})
};

const handleDateChange = (date) => {
    if (date) {
      rangeStart.value = date;
      emit('time-data', rangeStart.value, rangeEnd.value);
      generateData();
      updateChart();
    }
};

const initChart = () => {
    if (!container.value) return;
    const Chart = extend(Runtime, corelib());
    chart = new Chart({
        container: container.value,
        autoFit: true,
        height: 550,
    });

    chart.data(tableData.value);

    chart.legend({
        color: {
            position: 'top',
            layout: {
                justifyContent: 'center',
            },
          },
    });

    chart.interaction('tooltip', {
        crosshairs: {
            type: 'xy',
            line: {
                style: {
                    stroke: '#565656',
                    lineDash: [4]
                }
            }
        }
    });

    const tooltipConfig = { title: 'date' };

    chart
        .interval()
        .encode('x', 'hour_start')
        .encode('y', 'completed_facing')
        .encode('color', '#5470C6')
        .encode('color', () => 'completed')
        .scale('y', { independent: true, domainMax: 5000 })
        .style('fillOpacity', 0.8)
        .axis({
            'x': {
                title: '',
                labelFill: '#1677FF',
            },
            'y': {
                title: '',
                labelFill: '#1677FF',
                position: 'left',
                labelFormatter: (d) => d >= 1000 ? `${d/1000}.0k` : `${d}`,
            }
        })
        .tooltip(tooltipConfig);

    chart
        .line()
        .encode('x', 'hour_start')
        .encode('y', 'actual_work_hour')
        .encode('color', '#EE6666')
        .encode('color', () => 'Actual Work Hours')
        .scale('series', { independent: true })
        .axis({
            'x': {
                title: '',
                labelFill: '#1677FF',
            },
            'y': {
                title: '',
                labelFill: '#1677FF',
                position: 'right',
                grid: null,
            }
        })
        .tooltip(tooltipConfig);

    chart
        .line()
        .encode('x', 'hour_start')
        .encode('y', 'per_capita_hour')
        .encode('color', '#91CC75')
        .encode('color', () => 'Per Capita Capacity/H')
        .scale('series', { independent: true })
        .tooltip(tooltipConfig);

    chart.render();
};

const updateChart = () => {
    if (chart) {
        chart.changeData(tableData.value);
    }
};

onMounted(async () => {
    await generateData();
    initChart();
});

onBeforeUnmount(() => {
    if (chart) { chart.destroy();}
});
</script>

<style scoped>
:deep(.ant-tabs){
  border-radius: 4px;
}
:deep(.ant-tabs-nav .ant-tabs-tab){
  margin: 2px;
  padding: 2px 8px;
  border-radius: 2px !important;
  border: none !important;
}
:deep(.ant-tabs-nav::before){
  border-bottom: none !important;
}
</style>