<template>
  <div class="w-full">
    <a-tabs v-model:activeKey="activeTab" :tab-bar-style="{ borderRadius: 0 }">
      <a-tab-pane key="basic" :tab="$t('common.basicInfo')">
        <basic-info :client-id="clientId" :projectDetailList="projectDetailList" @update="getProjectDetail"/>
      </a-tab-pane>
      <a-tab-pane v-if="clientId" key="projects" :tab="$t('projectDetail.validationTask')">
        <validation-task :client-id="clientId" :projectValidation="projectValidation"/>
      </a-tab-pane>
      <a-tab-pane v-if="clientId" key="contacts" :tab="$t('projectDetail.scene')">
        <Scene :client-id="clientId"/>
      </a-tab-pane>
      <!-- <a-tab-pane key="mdList" tab="MD List">
        <MdList :client-id="clientId"/>
      </a-tab-pane>
      <a-tab-pane key="newSku" tab="New SKU">
        <new-sku :client-id="clientId"/>
      </a-tab-pane> -->
    </a-tabs>
  </div>
</template>

<script setup>
import { onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import BasicInfo from './components/BasicInfo.vue';
import ValidationTask from './components/ValidationTask.vue';
import Scene from './components/Scene.vue';
import MdList from './components/MdList.vue';
import { projectListApi } from '@/api/project-list/index';
import NewSku from './components/NewSku.vue';

const route = useRoute();
const clientId = ref(route.query.id ? Number(route.query.id) : null);
const projectDetailList = ref({});
const activeTab = ref('basic');
const projectValidation = ref({});

const getProjectDetail = async () => {
  if (!clientId.value) return;
  
  try {
    const res = await projectListApi(clientId.value);
    if (res.code === "0000") {
      projectDetailList.value = res.data;
      const { country, client, model_type } = res.data;
      projectValidation.value = {
        country,
        client,
        model_type
      };
    }
  } catch (error) {
    console.error('获取项目详情失败:', error);
  }
};

watch(
  () => route.query.id,
  async (newId) => {
    clientId.value = newId ? Number(newId) : null;
    if (clientId.value) {
      await getProjectDetail();
    }
  }
);

watch(
  () => route.query.tab,
  (tab) => {
    if (tab) activeTab.value = tab;
  },
  { immediate: true }
);

onMounted(async () => {
  if (clientId.value) {
    await getProjectDetail();
  }
});
</script>

<style scoped>
:deep(.ant-tabs-nav) {
    padding-left: 16px;
    height: 56px;
    background-color: #fff;
    position: sticky;
    top: 0;
    z-index: 100;
    border-bottom: 1px solid #f0f0f0;
}
.ant-card {
  height: 67vh;
}
</style>