<template>
    <div class="p-4 h-full flex flex-col">
        <!-- 权限拒绝页面 -->
        <div v-if="isUnauthorized" class="flex-1 flex items-center justify-center">
            <div class="text-center">
                <img src="@/assets/images/noPermisson.svg" alt="Permission Denied" class="mx-auto mb-4" style="width: 323px; height: 270px;" />
                <p class="text-gray-500 text-lg">Permission Denied</p>
            </div>
        </div>
        
        <!-- 正常列表页面 -->
        <a-card v-else class="flex-1 bg-white">
            <div class="flex justify-between items-center mb-[10px]">
                <div class="relative">
                    <a-input v-model:value="searchKeyword" :placeholder="$t('common.search')"  class="w-75" @pressEnter="onSearch">
                        <template #suffix>
                            <a-tooltip title="Extra information">
                                <SearchOutlined style="color: rgba(0, 0, 0, 0.45)"/>
                            </a-tooltip>
                        </template>
                    </a-input>

                    <a-button type="text" ref="filterButtonRef" :class="['ml-2', isFilterActive ? 'text-blue-500 hover:text-blue-600' : 'text-gray-400 hover:text-gray-500']" @click="handleAdvancedFilter">
                        <img class="mt-[-4px]" src="@/assets/images/filter.svg" width="18" height="18" 
                        :style="{ filter: isFilterActive ? 
                            'invert(40%) sepia(90%) saturate(2000%) hue-rotate(190deg)' :
                            'invert(60%) sepia(0%) saturate(0%) brightness(80%)' }"/>
                        <span class="ml-1">{{ t('common.advancedFilter') }}</span>
                    </a-button>
                    
                    <advanced-filter-dialog ref="showFilterRef" :style="filterDialogStyle" :pagination="pagination" @update="fetchProjectList" @refresh="onAdvancedFilterResult" @getParams="handleFilterParams" />
                    <match-data-upload-modal ref="matchDataModalRef" @success="handleUploadSuccess" @cancel="handleUploadCancel" />
                </div>
                <div class="flex justify-end">
                    <a-button type="default" @click="exportProjectList" class="border-[#1677FF] text-[#1677FF]">
                        <img src="@/assets/images/import1.svg" width="16" height="16" class="mr-2 mt-[-4px]" style="vertical-align: middle;"/>
                        &nbsp;{{ t('common.matchData') }}
                    </a-button>
                    <!-- <a-button type="primary" @click="viewProjectDetail()">
                        <plus-outlined />{{ t('common.new') }}
                    </a-button> -->
                </div>
            </div>

            <a-table :columns="columns" :data-source="currentClient" :loading="loading" :scroll="{ x: 'max-content' }" size="middle" :pagination="pagination.paginationConfig">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'updated_at'">
                        {{ formatTimestamp(record.updated_at) }}
                    </template>
                    <template v-if="column.key === 'action'">
                        <a @click="viewProjectDetail(record.id)">{{ t('common.view') }}</a>
                    </template>
                </template>
            </a-table>
        </a-card>
    </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter, useRoute } from 'vue-router';
import AdvancedFilterDialog from './components/InfoAdvancedFilterDialog.vue';
import MatchDataUploadModal from './components/MatchDataUploadModal.vue';
import { queryProjectApi, projectEqueryApi } from '@/api/project-list/index';
import { debounceAsync } from '@/utils/debounce';
import { message } from 'ant-design-vue';
import { formatTimestamp } from '@/utils/date';
import { SearchOutlined } from '@ant-design/icons-vue';
import { useTablePagination } from '@/composable/useTablePagination';

const pagination = useTablePagination({
    onChange: (page, size) => {
        fetchProjectList(page, size);
    },
});
const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const loading = ref(false);
const searchKeyword = ref('');
const currentClient = ref([]);
const filterParamsStatus = ref({});
const showFilterRef = ref();
const filterButtonRef = ref();
const matchDataModalRef = ref();

function getPaginationParams() {
    return {
        page_no: pagination.page_no.value - 1,
        page_size: pagination.page_size.value,
    }
}

// 筛选对话框样式
const filterDialogStyle = computed(() => {
    if (!filterButtonRef.value) return {};
    const buttonRect = filterButtonRef.value.$el.getBoundingClientRect();
    return {
        position: 'absolute',
        top: `${buttonRect.height + 100}px`,
        left: `${buttonRect.left}px`,
        zIndex: 1000
    };
});

// 检查是否为未授权访问
const isUnauthorized = computed(() => {
    return route.query.authorized === '0';
})

const handleAdvancedFilter = () => {
	showFilterRef.value.showAdvancedFilter();
};


// 计算属性判断是否有激活的筛选条件
const isFilterActive = computed(() => {
  return Object.entries(filterParamsStatus.value).some(([_, value]) => 
    Array.isArray(value) ? value.length > 0 : (value !== undefined && value !== null && value !== '')
  );
});

const onSearch = debounceAsync(async () => {
    const res = await projectEqueryApi({ key: searchKeyword.value, ...getPaginationParams()});
    const dataList = (res.data?.data_list || []).map(item => ({
        ...item,
        accountCount: item.project_config_users?.length,
        targetType: item.project_config?.tgt_type === 0 ? t('projectDetail.scene') : t('projectDetail.image'),
        target: item.project_config?.img_tgt,
    }));
    currentClient.value = dataList;
    pagination.total.value = res.data?.count
}, 500);

const columns = [
    { title: 'ID', dataIndex: 'id', key: 'id' },
    { title: t('projectList.country'), dataIndex: 'country', key: 'country' },
    { title: t('projectList.client'), dataIndex: 'client', key: 'client' },
    { title: t('projectList.modelType'), dataIndex: 'model_type', key: 'model_type' },
    { title: t('projectList.lastModifiedOn'), dataIndex: 'updated_at', key: 'updated_at' },
    { title: t('projectList.accountCount'), dataIndex: 'accountCount', key: 'accountCount' },
    { title: t('projectList.targetType'), dataIndex: 'targetType', key: 'targetType' },
    { title: t('projectList.target'), dataIndex: 'target', key: 'target' },
    { title: t('common.action'), dataIndex: 'action', key: 'action', align: 'left', fixed: 'right' }
];

const fetchProjectList = async () => {
    if (loading.value) return;
    loading.value = true;
    try {
        const res = await queryProjectApi(getPaginationParams());
        currentClient.value = (res.data?.data_list || []).map(item => ({
            ...item,
            accountCount: item.project_config_users?.length,
            targetType: item.project_config?.tgt_type === 0 ? t('projectDetail.scene') : t('projectDetail.image'),
            target: item.project_config?.img_tgt,
        }));
        pagination.total.value = res.data?.count
    } catch (error) {
        message.error(error?.message);
    } finally {
        loading.value = false;
    }
};

const onAdvancedFilterResult = (data) => {
  currentClient.value = data;
};

const handleFilterParams = (params) => {
    filterParamsStatus.value = params;
};

const viewProjectDetail = (id) => {
    if(id) {
        router.push({ path: '/project-detail', query: { id } });
    } else {
        router.push(`/project-detail`);
    }
};

const exportProjectList = () => {
    matchDataModalRef.value?.showModal();
};

const handleUploadSuccess = () => {
    message.success(t('matchData.updateSuccess'));
    fetchProjectList();
};

const handleUploadCancel = () => {
    // 取消上传时的处理逻辑
};

onMounted(async() => {
    // 只有在授权状态下才获取项目列表数据
    if (!isUnauthorized.value) {
        await fetchProjectList();
    }
});
</script>