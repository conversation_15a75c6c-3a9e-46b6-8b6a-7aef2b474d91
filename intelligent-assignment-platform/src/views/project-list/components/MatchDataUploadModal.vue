<template>
  <a-modal
    :title="currentStep === 1 ? t('matchData.uploadMatchData') : t('matchData.previewTitle')"
    :open="open"
    :width="688"
    :footer="null"
    @cancel="handleCancel"
    :maskClosable="false"
  >
    <!-- 第一步：文件上传 -->
    <div v-if="currentStep === 1" class="upload-step">
      <div class="upload-area">
        <a-upload-dragger
          v-model:file-list="fileList"
          name="file"
          :multiple="false"
          :before-upload="beforeUpload"
          :customRequest="handleUpload"
          accept=".xlsx,.xls,.csv"
          @change="handleChange"
          @drop="handleDrop"
        >
          <div class="upload-content">
            <div class="upload-icon">
              <img src="@/assets/images/upload-inbox1.svg" alt="upload-icon" width="48" height="48">
            </div>
            <p class="upload-text">{{ t('matchData.uploadText') }}</p>
            <p class="upload-hint">{{ t('matchData.uploadHint') }}</p>
          </div>
        </a-upload-dragger>
      </div>
      
      <div class="modal-footer">
        <a-button @click="handleCancel">{{ t('common.cancel') }}</a-button>
        <a-button type="primary" @click="handleNext" :loading="uploading">
          {{ t('matchData.next') }}
        </a-button>
      </div>
    </div>

    <!-- 第二步：数据预览 -->
    <div v-if="currentStep === 2" class="preview-step">
      <a-alert
          v-if="hasErrorData"
          :message="t('matchData.errorMessage')"
          type="error"
          show-icon
          class="my-4"
      />
      <a-alert
        v-else  
        :message="t('matchData.syncMessage')"
        type="info"
        show-icon
        class="my-4"
      />
      
      <!-- 数据表格 -->
      <div class="table-container">
        <a-table
          :columns="previewColumns"
          :dataSource="previewData"
          :pagination="false"
          :scroll="{ x: 'max-content' }"
          :sticky="true"
          size="small"
          :customRow="() => ({ style: { height: '48px' } })"
        >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag v-if="record.status === 'New'" color="green" class="w-15 text-center">{{ t('matchData.new') }}</a-tag>
            <a-tag v-else-if="record.status === 'Exist'" color="blue" class="w-15 text-center">{{ t('matchData.exist') }}</a-tag>
            <a-tag v-else-if="record.status === 'Error'" color="red" class="w-15 text-center">{{ t('matchData.error') }}</a-tag>
          </template>
        </template>
        </a-table>
      </div>
      
      <div class="modal-footer">
        <a-button type="primary" @click="handleUpdate" :disabled="hasErrorData" :loading="updating">
          {{ t('matchData.update') }}
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed, h } from 'vue'
import { useI18n } from 'vue-i18n'
import { message } from 'ant-design-vue'
import { uploadMatchDataApi, updateMatchDataApi } from '@/api/project-list/index'

const { t } = useI18n()
const emit = defineEmits(['success', 'cancel'])

const open = ref(false)
const currentStep = ref(1)
const fileList = ref([])
const selectedFile = ref(null)
const uploading = ref(false)
const updating = ref(false)
const previewData = ref([])

// 预览表格列定义
const previewColumns = computed(() => [
  { title: '#', dataIndex: 'index', key: 'index', width: 60 },
  { title: t('matchData.projectId'), dataIndex: 'projectId', key: 'projectId', width: 100 },
  { title: t('matchData.name'), dataIndex: 'name', key: 'name', width: 100 },
  { title: t('matchData.projectName'), dataIndex: 'projectName', key: 'projectName', width: 120 },
  { title: t('matchData.status'), dataIndex: 'status', key: 'status', width: 100 }
])

// 计算是否有错误数据
const hasErrorData = computed(() => {
  return previewData.value.some(item => item.isNameErr === true)
})

// 文件上传前验证
const beforeUpload = (file) => {
  const isValidFile = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                      file.type === 'application/vnd.ms-excel' ||
                      file.type === 'text/csv' ||
                      file.name.toLowerCase().endsWith('.csv')
  if (!isValidFile) {
    message.error(t('matchData.fileTypeError'))
    return false
  }
  
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error(t('matchData.fileSizeError'))
    return false
  }
  
  selectedFile.value = file
  return false // 阻止自动上传
}
function handleDrop(e) { console.log(e);}

// 自定义上传处理
const handleUpload = ({ file, onSuccess }) => {
  selectedFile.value = file
  // 模拟上传成功
  setTimeout(() => {
    onSuccess()
  }, 0)
}

// 文件变化处理
const handleChange = (info) => {
  const { status } = info.file
  if (status === 'done') {
    selectedFile.value = info.file.originFileObj || info.file
  } else if (status === 'removed') {
    selectedFile.value = null
  }
}

// 下一步
const handleNext = async () => {
  if (!selectedFile.value || fileList.value.length === 0) {
    message.warning(t('matchData.pleaseSelectFile'))
    return
  }
  
  uploading.value = true
  try {
    // 创建FormData对象
    const formData = new FormData()
    formData.append('file', selectedFile.value)
    
    // 调用上传API
    // 暂时使用模拟数据进行交互，后续接入真实API
    const response = await uploadMatchDataApi(formData)
    
    // 模拟API响应
    const mockResponse = {
      code: '0000',
      data: {
        data_list: [
          { out_project_id: 91, out_name: 'snapshot-belgium', out_project_name: 'Belgium', is_new: true, is_name_err: false },
          { out_project_id: 108, out_name: 'snapshot-bulgaria', out_project_name: 'Bulgaria', is_new: true, is_name_err: false },
          { out_project_id: 108, out_name: 'snapshot-bulgaria', out_project_name: 'Bulgaria', is_new: false, is_name_err: false },
          { out_project_id: 108, out_name: 'snapshot-bulgaria', out_project_name: 'Bulgaria', is_new: false, is_name_err: false },
          { out_project_id: 108, out_name: 'snapshot-bulgaria', out_project_name: 'Bulgaria', is_new: false, is_name_err: false }
        ]
      }
    }
    
    // 使用模拟数据
    // const response = mockResponse
    
    if (response.code === '0000' && response.data) {
      // 处理返回的数据，添加索引
      previewData.value = response.data?.data_list?.map((item, index) => ({
        index: index + 1,
        projectId: item.out_project_id,
        name: item.out_name,
        projectName: item.out_project_name,
        status: item.is_name_err ? 'Error':item.is_new ? 'New':'Exist',
        isNameErr: item.is_name_err
      }))
      
      currentStep.value = 2
    }
  } catch (error) {
    console.error('Upload error:', error)
  } finally {
    uploading.value = false
  }
}

// 更新数据
const handleUpdate = async () => {
  updating.value = true
  try {
    // 准备更新数据
    const updateData = {
      data_list: previewData.value.map(item => ({
        project_id: item.projectId,
        name: item.name,
        project_name: item.projectName,
        status: item.status,
        isNameErr: item.isNameErr,
        is_new: item.isNew,
      }))
    }
    
    // 暂时使用模拟数据进行交互，后续接入真实API
    const response = await updateMatchDataApi(updateData)
    
    // 模拟API响应
    const mockResponse = {
      code: '0000',
      message: '更新成功'
    }
    
    // 使用模拟数据
    // const response = mockResponse
    
    if (response.code === '0000') {
      emit('success')
      handleCancel()
    } else {
      message.error(response.message || t('matchData.updateError'))
    }
  } catch (error) {
    console.error('Update error:', error)
  } finally {
    updating.value = false
  }
}

// 取消/关闭
const handleCancel = () => {
  open.value = false
  currentStep.value = 1
  fileList.value = []
  selectedFile.value = null
  previewData.value = []
  uploading.value = false
  updating.value = false
  emit('cancel')
}

// 显示弹窗
const showModal = () => {
  open.value = true
}

defineExpose({
  showModal
})
</script>

<style scoped>
.upload-step {
  padding: 20px 0;
}
:deep(.ant-modal-header) {
  margin-bottom: 16px;
}
.upload-area {
  margin-bottom: 24px;
}

.upload-content {
  padding: 40px 20px;
  text-align: center;
}

.upload-icon {
  margin-bottom: 16px;
}

.upload-text {
  font-size: 16px;
  color: #262626;
  margin-bottom: 8px;
}

.upload-hint {
  width:78%;
  font-size: 14px;
  color: #8c8c8c;
  margin: 0 auto;
}

.status-messages {
  max-height: 200px;
  overflow-y: auto;
}

.table-container {
  max-height: 600px;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 24px;
}
:deep(.ant-upload-list-item-name) {
  color: #1677ff;
}
</style>
