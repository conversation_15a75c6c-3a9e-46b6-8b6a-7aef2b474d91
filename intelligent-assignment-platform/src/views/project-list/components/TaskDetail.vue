<template>
  <div class="p-4">
    <a-card :title="t('projectDetail.basicInfo')">
      <template #extra>
            <a-button type="link" class="text-[#8C8C8C]" @click="cancelEdit">{{ t('common.cancel') }}</a-button>
            <a-button type="link" @click="saveChanges" :loading="saving">
              {{ t('common.save') }}
            </a-button>
      </template>
      <a-spin :spinning="loading">
        <div class="flex">
          <a-form :model="formState" layout="horizontal" class="flex-1" ref="basicFormRef" label-align="right" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item :label="t('validitionTask.type')" name="type">
                  <a-select v-model:value="formState.type" :placeholder="t('common.pleaseSelect')" style="width: 100%" class="rounded-md" disabled>
                    <a-select-option v-for="type in taskTypeOptions" :key="type.value" :value="type.value">{{ type.label }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item :label="t('validitionTask.expect_deadline')"  name="expect_deadline">
                  <a-date-picker v-model:value="formState.expect_deadline" class="w-full" :placeholder="t('validitionTask.selectDeadline')" 
                    :show-time="{ format: 'MM/DD/YYYY HH:mm:ss' }" :format="'MM/DD/YYYY HH:mm:ss'">
                    <!-- <template #suffixIcon>
                      <SmileOutlined />
                    </template> -->
                  </a-date-picker>
                </a-form-item>
              </a-col>
            </a-row>
            <div v-show="isExpanded">
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-form-item :label="t('validitionTask.name')" name="name">
                    <a-input v-model:value="formState.name" :placeholder="t('validitionTask.enterName')" allowClear/>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item :label="t('validitionTask.sub_task_name')" name="sub_task_name">
                    <a-input v-model:value="formState.sub_task_name" :placeholder="t('validitionTask.enterSubTaskName')" allowClear/> 
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-form-item :label="t('validitionTask.client')" name="client">
                    <a-input v-model:value="formState.client" :placeholder="t('validitionTask.enterClient')" disabled />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item :label="t('validitionTask.country')" name="country">
                    <a-input v-model:value="formState.country" :placeholder="t('validitionTask.enterCountry')" disabled />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-form-item :label="t('validitionTask.model_type')" name="model_type">
                    <a-input v-model:value="formState.model_type" :placeholder="t('validitionTask.enterModelType')" disabled />
                  </a-form-item>
                </a-col>
                <a-col :span="12"><!-- excel列 R -->
                  <a-form-item :label="excelLabelMap['R'] || t('validitionTask.priority_level')" name="priority_level">
                    <a-input v-model:value="extraInfo.priority_level" :placeholder="t('validitionTask.enterPriorityLevel')" allowClear/>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :span="12"><!-- excel列 M -->
                  <a-form-item :label="excelLabelMap['M'] || t('validitionTask.no_scenes')" name="no_scenes">
                    <a-input-number v-model:value="extraInfo.no_scenes" :placeholder="t('validitionTask.enterSceneTotal')" style="width: 100%" />
                  </a-form-item>
                </a-col>
                <a-col :span="12"><!-- excel列 K -->
                  <a-form-item :label="t('validitionTask.image_received_on')" name="image_received_on">
                    <a-date-picker v-model:value="extraInfo.image_received_on" class="w-full" :placeholder="t('validitionTask.selectImageReceivedDate')" :format="'MM/DD/YYYY'">
                      <!-- <template #suffixIcon>
                        <SmileOutlined />
                      </template> -->
                    </a-date-picker>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :span="12"><!-- excel列 N -->
                  <a-form-item :label="excelLabelMap['N'] || t('validitionTask.requeued_and_validated')" name="requeued_and_validated">
                    <a-select v-model:value="extraInfo.requeued_and_validated" :placeholder="t('common.pleaseSelect')" style="width: 100%" class="rounded-md">
                      <a-select-option v-for="type in truthValueOptions" :key="type.value" :value="type.value">{{ type.label }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12"><!-- excel列 O -->
                  <a-form-item :label="t('validitionTask.specific_scene_ids')" name="specific_scene_ids">
                    <a-select v-model:value="extraInfo.specific_scene_ids" :placeholder="t('common.pleaseSelect')" style="width: 100%" class="rounded-md">
                      <a-select-option v-for="type in truthValueOptions" :key="type.value" :value="type.value">{{ type.label }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :span="12"><!-- excel列 P -->
                  <a-form-item :label="excelLabelMap['P'] || t('validitionTask.mds_available')" name="mds_available">
                    <a-select v-model:value="extraInfo.mds_available" :placeholder="t('common.pleaseSelect')" style="width: 100%" class="rounded-md">
                      <a-select-option v-for="type in truthValueOptions" :key="type.value" :value="type.value">{{ type.label }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12"><!-- excel列 Q -->
                  <a-form-item :label="excelLabelMap['Q'] || t('validitionTask.additional_document')" name="additional_document">
                    <a-select v-model:value="extraInfo.additional_document" :placeholder="t('common.pleaseSelect')" style="width: 100%" class="rounded-md">
                      <a-select-option v-for="type in truthValueOptions" :key="type.value" :value="type.value">{{ type.label }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :span="24" class="ml-[-5px]"><!-- excel列 U -->
                  <a-form-item :label="excelLabelMap['U'] || t('validitionTask.attachments')" name="attachments" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
                    <a-textarea v-model:value="extraInfo.attachments" :placeholder="t('validitionTask.enterAttachments')" allow-clear />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="24">
                <a-col :span="24" class="ml-[-5px]"><!-- excel列 T -->
                  <a-form-item :label="excelLabelMap['T'] || t('validitionTask.remark')" name="remark" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
                    <a-textarea v-model:value="extraInfo.remark" :placeholder="t('validitionTask.enterRemark')" allow-clear />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </a-form>
          <a-button type="link" @click="handleExpand" class="flex ml-1">
            {{ t('common.expand') }}
            <span class="ml-1"> <DownOutlined v-if="!isExpanded" /><UpOutlined v-else /></span>
          </a-button>
        </div>
      </a-spin>
    </a-card>

    <a-card :title="t('projectDetail.detail')" class="mt-[14px] h-[80vh]">
      <a-spin :spinning="loading">
          <div class="flex gap-6 relative">
            <div class="w-3/5">
                <div class="flex justify-between items-center mb-4">
                  <div class="text-sm font-medium">{{ t('projectDetail.SceneList') }}</div>
                  <div class="flex">
                    <a-button type="link" class="text-[#8C8C8C]" @click="open = true" v-if="taskDetailStatus < 1">
                      <img class="mt-[-4px]" src="@/assets/images/import.svg" width="18" height="18" />
                      <span class="ml-1">{{ t('projectDetail.import') }}</span>
                    </a-button>
                    <a-button type="link" :class="selectedValidators.length ? '!text-[#ff4d4f]' : '!text-[#8C8C8C]'" @click="handleRemove()">
                      {{ t('common.remove') }}
                    </a-button>
                    <a-button type="link" @click="handleScene">
                      {{ t('common.add') }}
                    </a-button>
                  </div>
                </div>
                <a-table
                  :columns="validatorColumns"
                  :data-source="sceneLists"
                  :row-selection="{ 
                    selectedRowKeys: selectedValidators, 
                    onChange: onValidatorSelect,
                    getCheckboxProps: (record) => ({
                      disabled: taskDetailStatus >= 1 && initialSceneIds.has(record.id)
                    })
                  }"
                  :pagination="false"
                  :scroll="{ x: 'max-content' }"
                  class="max-h-[60vh] overflow-auto">
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'action'">
                      <a 
                        @click="() => removeValidator(record.id)" 
                        :class="(taskDetailStatus >= 1 && initialSceneIds.has(record.id)) ? 'text-[#8C8C8C] cursor-not-allowed' : 'text-[#FF4D4F] hover:text-[#FF4D4F] cursor-pointer'"
                          :style="{ pointerEvents: (taskDetailStatus >= 1 && initialSceneIds.has(record.id)) ? 'none' : 'auto' }">
                        {{ t('common.remove') }}
                      </a>
                    </template>
                    <template v-if="column.dataIndex === 'img_received_on'">
                      {{ formatTimestamp(record.img_received_on, 'MM/DD/YYYY') }}
                    </template>
                  </template>
                </a-table>
            </div>

            <div class="absolute left-[60%] top-0 bottom-0 w-[1px] bg-[#f0f0f0]"></div>
            
            <div class="w-2/5">
              <AccountList :validators="validators" @save="handleSave" @user-data="handleUserData" :task-status="taskDetailStatus"/>
            </div> 
          </div>
      </a-spin>
    </a-card>
    
    <a-modal :title="t('common.import')" :open="open" @cancel="handleCancel" centered>       
        <a-upload-dragger
            v-model:fileList="fileList"
            name="file"
            :multiple="true"
            :customRequest="customRequest"
            @change="handleChange">
            <p class="ant-upload-drag-icon">
            <inbox-outlined></inbox-outlined>
            </p>
            <p class="ant-upload-text">Click or drag file to this area to upload</p>
            <p class="ant-upload-hint">
            Support for a single or bulk upload. Strictly prohibit from uploading company data or other
            band files
            </p>
        </a-upload-dragger>

        <template #footer>
            <a-button @click="handleCancel">{{ t('common.cancel') }}</a-button>
            <a-button type="primary" @click="handleAdd">{{ t('common.add') }}</a-button>
        </template>
    </a-modal>
    <select-scene-dialog ref="showSelectSceneRef" @add-scenes="handleAddScenes"/>
  </div>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { DownOutlined, UpOutlined, InboxOutlined } from '@ant-design/icons-vue';
import SelectSceneDialog from './components/SelectSceneDialog.vue';
import { taskDetailApi, uploadScenesApi, putDetailApi } from '@/api/project-list/index';
import { debounceAsync } from '@/utils/debounce';
import { useI18n } from 'vue-i18n';
import AccountList from './AccountList.vue';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
dayjs.extend(customParseFormat);
import { dateToTimestamp, formatTimestamp } from '@/utils/date';
import { taskTypeOptions, truthValueOptions } from '@/utils/dropdownOptions';

const { t } = useI18n();
const emit = defineEmits(['update']);
const loading = ref(false);
const saving = ref(false);
const basicFormRef = ref();
const sceneLists = ref([]);
const validators = ref([]);
const showSelectSceneRef = ref();
const selectedValidators = ref([]);
const open = ref(false);
const fileList = ref([]);
const isExpanded = ref(false);
const initialSceneIds = ref(new Set()); // 存储初始化时的scene IDs
const route = useRoute();
const router = useRouter();
const valTaskId = ref(route.query.id ?? null);
const userData = ref([]);
const excelLabelMap = ref({}); 
const projectValidation = ref({});
const uploadFile = ref(null);

if (history.state.projectValidation) {
  try {
    projectValidation.value = JSON.parse(history.state.projectValidation);
  } catch (e) {
    projectValidation.value = {};
  }
}

const extraInfo = reactive({
  image_received_on: null,
  no_scenes: '',
  requeued_and_validated: null,
  specific_scene_ids: null,
  mds_available: null,
  additional_document: null,
  priority_level: '',
  attachments: '',
  remark: '',
  name: '',
  sub_task_name: '',
  expect_deadline: null
});

const formState = reactive({
  name: '',
  type: '',
  project_id: Number(history.state.clientId) ?? null,
  expect_deadline: null,
  sub_task_name: '',
  region: '',
  country: '',
  client: '',
  model_type: '',
  extra_info: extraInfo,
  scene_ids: [],
  val_user_ids: [],
});

const getUserInfo = (userId) => {
  const user = userData.value.find(u => u.id === userId) || {};
  return {
    key: user.id,
    user_id: user.id,
    department: user.department,
    name: user.name
  };
};
const taskDetailStatus = ref(0);

const parseDateWithMultipleFormats = (dateStr) => {
  if (!dateStr) return null;
  const parsed = dayjs(dateStr, ['YYYY/MM/DD', 'MM/DD/YYYY', 'MM/DD/YY'], true);
  return parsed.isValid() ? parsed : null;
};

const fetchTaskDetail = async () => {
  if (valTaskId.value) {
    loading.value = true;
    try {
        const res = await taskDetailApi(valTaskId.value);
        if (!res.data) return;
        const { type, extra_info = {}, scene_list = [], user_list = [], status } = res.data;
        taskDetailStatus.value = status;
        const found = taskTypeOptions.find(item => item.value === type);
        Object.assign(extraInfo, extra_info);

        Object.assign(formState, {
          type: found ? found.label : '',
          name: extraInfo.name,
          expect_deadline: parseDateWithMultipleFormats(extraInfo.expect_deadline),
          sub_task_name: extraInfo.sub_task_name,
        });
        validators.value = res.data.user_list;

        extraInfo.image_received_on = parseDateWithMultipleFormats(extra_info.image_received_on);

        sceneLists.value = scene_list.map(item => ({
              ...item,
              key: item.id
        }));
        // 记录初始化时的scene IDs
        initialSceneIds.value = new Set(scene_list.map(item => item.id));
        validators.value = user_list.map(item => getUserInfo(item.user_id));
    } catch (error) {
        console.error(error);
    } finally {
      loading.value = false;
    }
  }
};

const handleSave = ({ allValidators, targetKeys }) => {
  validators.value = allValidators;
  formState.val_user_ids = targetKeys;
};

const handleUserData = (data) => {
  userData.value = data;
  fetchTaskDetail();
}

const handleScene = () => {
  const existedIds = sceneLists.value.map(item => item.id);
  showSelectSceneRef.value.showSelectSceneDialog(existedIds);
}

const handleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

const handleCancel = () => {
  open.value = false;
  fileList.value = [];
};

const customRequest = ({ onSuccess }) => {
  window.setTimeout(() => {
    onSuccess();
  }, 0);
};

const handleChange = (info) => {
  const status = info.file.status;
  if (status !== "uploading") {
    const file = info.file.originFileObj;
    if (file) {
      uploadFile.value = file;
    }
  }
  if (status === "done") {
    info.file.url = window.URL.createObjectURL(info.file.originFileObj);
    message.success(`${info.file.name} file uploaded successfully.`);
  } else if (status === "") {
    message.error(`${info.file.name} file upload failed.`);
  }
};

const handleAdd = debounceAsync(async () => {
  try {
    const formData = new FormData();
    formData.append('file', uploadFile.value); // 上传原始文件
    const res = await uploadScenesApi(valTaskId.value, formData);
    if (res.code === "0000") {
      message.success(t('common.uploadSuccess'));
    }
  } catch (error) {
    console.error(error);
  }
  handleCancel();
}, 300);

const validatorColumns = [
  { title: t('scenesList.type'), dataIndex: 'type', key: 'type' },
  { title: t('scenesList.sub_scene_type'), dataIndex: 'sub_scene_type', key: 'sub_scene_type' },
  { title: t('scenesList.img_received_on'), dataIndex: 'img_received_on', key: 'img_received_on' },
  { title: t('scenesList.id'), dataIndex: 'id', key: 'id' },
  { title: t('scenesList.total_imgs'), dataIndex: 'total_imgs', key: 'total_imgs' },
  { title: t('scenesList.total_facings'), dataIndex: 'total_facings', key: 'total_facings' },
  { title: t('common.action'), key: 'action', width: 100 },
];

const onValidatorSelect = (selectedRowKeys) => {
  // 当taskDetailStatus <= 1时，所有数据都可以选择；否则只允许选择新添加的数据
  if (taskDetailStatus.value <= 1) {
    selectedValidators.value = selectedRowKeys;
  } else {
    selectedValidators.value = selectedRowKeys.filter(id => !initialSceneIds.value.has(id));
  }
};

const removeValidator = (id) => {
  // 当taskDetailStatus <= 1时，所有数据都可以删除；否则只允许删除新添加的数据
  if (taskDetailStatus.value <= 1 || !initialSceneIds.value.has(id)) {
    sceneLists.value = sceneLists.value.filter(item => item.id !== id);
  }
};

const handleRemove = () => {
  // 当taskDetailStatus <= 1时，删除所有选中的数据；否则只删除选中的新添加数据
  let removableIds;
  if (taskDetailStatus.value <= 1) {
    removableIds = selectedValidators.value;
  } else {
    removableIds = selectedValidators.value.filter(id => !initialSceneIds.value.has(id));
  }
  sceneLists.value = sceneLists.value.filter(item => !removableIds.includes(item.id));
  selectedValidators.value = [];
};

const goToProjectDetail = () => {
  router.push({
    path: `/project-detail`,
    query: { 
      id: history.state.clientId,
      tab: 'projects' 
    }
  });
};

const cancelEdit = () => {
  if (valTaskId.value) {
    fetchTaskDetail();
  } else {
    sceneLists.value = [];
    validators.value = [];
  }
  
  // 关闭当前tab
  const currentPath = route.path;
  const event = new CustomEvent('removeTab', { detail: currentPath });
  window.dispatchEvent(event);
  
  goToProjectDetail();
}

const saveChanges = async () => {
  saving.value = true;
  try {
    formState.scene_ids = sceneLists.value.map(item => item.id);
    formState.val_user_ids = validators.value.map(validator => validator.user_id);
    const { client, country, region, model_type, type, ...submitData } = formState;
    const typeId = taskTypeOptions.find(item => item.label === type)?.value ?? null;

    const requestData = {
      ...submitData,
      type: typeId,
      expect_deadline: formState.expect_deadline ? dateToTimestamp(formState.expect_deadline) : undefined,
      extra_info: {
        ...extraInfo,
        image_received_on: extraInfo.image_received_on ? extraInfo.image_received_on.format('MM/DD/YYYY') : null,
      }
    }
    if (valTaskId.value === null) {
      valTaskId.value = history.state.taskId;
    }
    const res = await putDetailApi(valTaskId.value, requestData);
    if (res.code === "0000") {
      message.success(t('common.saveSuccess'));
      emit('update');
    }
    // 关闭当前tab
    const currentPath = route.path;
    const event = new CustomEvent('removeTab', { detail: currentPath });
    window.dispatchEvent(event);
    goToProjectDetail();
  } catch (error) {
    console.error(error);
  } finally {
    saving.value = false;
  }
};

const handleAddScenes = (scenes) => {
  const existingKeys = new Set(sceneLists.value.map(item => item.key || item.id));
  const newScenes = scenes.filter(item => !existingKeys.has(item.key || item.id));
  sceneLists.value = [...sceneLists.value, ...newScenes];
};

onMounted(() => {
  if (projectValidation.value !== null) { 
    Object.assign(formState, {
        country: projectValidation.value.country,
        client: projectValidation.value.client,
        model_type: projectValidation.value.model_type,
      });
  }
  if (valTaskId.value === null) {    
    const typeId = history.state.type;
    const found = taskTypeOptions.find(item => item.value === typeId);
    formState.type = found ? found.label : '';  

    const excelDataRaw = history.state.excelData ? JSON.parse(history.state.excelData) : null;
    if (excelDataRaw) {
      const { name, sub_task_name, expect_deadline, ...otherExcelData } = excelDataRaw;
      
      let parsedDeadline = null;
      if (expect_deadline) {
        const parsed = dayjs(expect_deadline, ['MM/DD/YYYY', 'YYYY/MM/DD', 'YYYY-MM-DD'], true);
        parsedDeadline = parsed.isValid() ? dayjs(parsed) : null;
      }
      Object.assign(formState, {
        name: name,
        sub_task_name: sub_task_name,
        expect_deadline: parsedDeadline
      });
      
      Object.assign(extraInfo, otherExcelData);
      if (extraInfo.image_received_on) {
        const parsed = dayjs(extraInfo.image_received_on, ['MM/DD/YYYY', 'YYYY/MM/DD', 'YYYY-MM-DD'], true);
        extraInfo.image_received_on = parsed.isValid() ? dayjs(parsed.format('MM/DD/YYYY')) : null;
      }
    }
  }
});
</script>