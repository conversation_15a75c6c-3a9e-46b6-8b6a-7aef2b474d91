<template>
    <a-modal :title="$t('common.add')" :open="open" @cancel="handleCancel" :width="688" centered>
        <div class="mb-6 mt-2">
            <div class="mb-4 mt-2 font-medium">{{ $t('projectDetail.department') }}</div>
            <a-cascader v-model:value="department_id" :options="departmentOptions"  :placeholder="$t('projectDetail.selectDepartment')"  change-on-select class="rounded-md w-full"/>
        </div>
        <div>
            <div class="mb-2 font-medium">{{ $t('projectDetail.validatorList') }}</div>
            <a-transfer
                v-model:target-keys="targetKeys"
                :data-source="searchOptions"
                show-search
                :filter-option="filterOption"
                :render="item => item.title"
                :list-style="{ width: '100%', height: '300px'}"
                />
        </div>

        <template #footer>
            <a-button @click="handleCancel">{{ $t('common.cancel') }}</a-button>
            <a-button type="primary" @click="handleSave">{{ $t('common.save') }}</a-button>
        </template>
    </a-modal>
</template>

<script setup>
import { onMounted, ref, computed, watch } from 'vue';
import { departmentsApi } from '@/api/system/index';
import { useTreeStore } from '@/store/useTreeStore.ts';

const emit = defineEmits(['refresh','save-personnel']);
const props = defineProps({
  userData: { type: Array, default: () => [] },
});
const open = ref(false);
const targetKeys = ref([]);
const departmentOptions = computed(() => treeStore.getTreeOptions('department'));
const treeStore = useTreeStore()
const usersName = ref([]);
const searchOptions = ref([]);
const department_id = ref(0);

const filterOption = (inputValue, option) => {
  return option.title.toLowerCase().indexOf(inputValue.toLowerCase()) > -1;
};

const handleCancel = () => {
  department_id.value = undefined;
  searchOptions.value = usersName.value.map(user => ({
    key: user.id,
    department: user.department,
    title: `${user.name} - ${user.phone}`,
  }));
  open.value = false;
};

const userList = () => {
  try {
    usersName.value = props.userData.map(item => ({ 
      name: item.name, 
      id: item.id, 
      department: item.department.split('/').pop(),
      phone: item.phone 
    }));

    searchOptions.value = usersName.value.map(user => ({ 
      key: user.id, 
      title: `${user.name} - ${user.phone}`
    }));
  } catch (error) {
    console.error("请求失败:", error);
  }
};

onMounted(async () => {
  await Promise.all([userList(), treeStore.fetchTreeData('department', departmentsApi)]);
});

const handleSave = () => {
    emit('save-personnel', { targetKeys: targetKeys.value });
    handleCancel();
};

watch(department_id, (val) => {
  if (val) {
    const departmentId = Array.isArray(val) ? val.at(-1) : val;
    const getDepartmentLabel = (id, options) => {
      for (const option of options) {
        if (option.value === id) {
          return option.label;
        }
        if (option.children) {
          const label = getDepartmentLabel(id, option.children);
          if (label) return label;
        }
      }
      return '';
    };
    const departmentLabel = getDepartmentLabel(departmentId, departmentOptions.value);
    searchOptions.value = usersName.value
    .filter(user => {
        return user.department === departmentLabel;
      })
      .map(user => ({
        key: user.id,
        title: `${user.name} - ${user.phone}`,
      }));
  } else {
    searchOptions.value = usersName.value.map(user => ({
      key: user.id,
      title: `${user.name} - ${user.phone}`,
    }));
  }
});

watch(
  () => props.userData,
  () => {
    userList();
  },
  { immediate: true, deep: true }
);

const showAddPersonelDialog = (keys) => {
    targetKeys.value = keys;
    open.value = true;
};

defineExpose({
    showAddPersonelDialog
});
</script>