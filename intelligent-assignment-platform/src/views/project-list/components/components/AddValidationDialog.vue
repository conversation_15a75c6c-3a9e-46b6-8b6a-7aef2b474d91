<template>
    <a-modal :title="$t('common.add')" :open="open" @cancel="handleCancel">
        <a-form :model="formState" layout="vertical" ref="formRef">
            <a-form-item :label="$t('validitionTask.type')" name="type" :rules="[{ required: true, message: $t('common.pleaseSelect') }]">
                <a-select v-model:value="formState.type" :placeholder="$t('projectDetail.selectDepartment')" style="width: 100%" class="rounded-md">
                    <a-select-option v-for="type in taskTypeOptions" :key="type.value" :value="type.value">{{ type.label }}</a-select-option>
                </a-select>
            </a-form-item>
        </a-form>
        <a-upload-dragger
            v-model:fileList="fileList"
            name="file"
            :multiple="true"
            :customRequest="customRequest"
            @change="handleChange"
            @drop="handleDrop">
            <p class="ant-upload-drag-icon">
            <inbox-outlined></inbox-outlined>
            </p>
            <p class="ant-upload-text">Click or drag file to this area to upload</p>
            <p class="ant-upload-hint">
            Support for a single or bulk upload. Strictly prohibit from uploading company data or other
            band files
            </p>
        </a-upload-dragger>

        <template #footer>
            <a-button @click="handleCancel">{{ $t('common.cancel') }}</a-button>
            <a-button type="primary" @click="handleAdd()">{{ $t('common.add') }}</a-button>
        </template>
    </a-modal>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { InboxOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { read, utils } from 'xlsx'
import { createTaskApi } from '@/api/project-list/index';
import { debounceAsync } from '@/utils/debounce';
import { useI18n } from 'vue-i18n';
import dayjs from 'dayjs';

const { t } = useI18n();
const props = defineProps({
  projectValidation: {
    type: Object,
    default: () => ({})
  },
  clientId: {
    type: Number,
    default: null
  }
});
const emit = defineEmits(['refresh']);
const open = ref(false);
const fileList = ref([]);
const router = useRouter();
const excelData = ref({});
const formRef = ref();
const extraInfo = reactive({
  image_received_on: null,
  no_scenes: '',
  requeued_and_validated: null,
  specific_scene_ids: null,
  mds_available: null,
  additional_document: null,
  priority_level: '',
  attachments: '',
  remark: '',
});
const formState = reactive({
    type: '',
    extra_info: extraInfo,
    project_id: props.clientId
});

const taskTypeOptions = [
    { value: 'Adhoc', label: 'Adhoc' },
    { value: 'UAT', label: 'UAT' },
]

const customRequest = ({ onSuccess }) => {
  window.setTimeout(() => {
    onSuccess();
  }, 0);
};

const handleChange = (info) => {
  const status = info.file.status;
  if (status !== "uploading") {
    // 解析 Excel 文件
    const file = info.file.originFileObj;
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const data = new Uint8Array(e.target.result);
        const workbook = read(data, { type: 'array', cellDates: true });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        
        const range = utils.decode_range(worksheet['!ref']);
        const columnInfo = {};
        
        // 遍历第一行数据，获取实际内容
        for(let C = range.s.c; C <= range.e.c; C++) {
          // 获取第一行(索引为0)单元格的内容作为头部信息
          const headerCellAddress = utils.encode_cell({r: 0, c: C});
          const headerCell = worksheet[headerCellAddress];
          
          // 获取第二行(索引为1)单元格的内容
          const contentCellAddress = utils.encode_cell({r: 1, c: C});
          const contentCell = worksheet[contentCellAddress];
          
          if(headerCell && contentCell) {
            // 生成列的key（A, B, C等）
            const columnKey = utils.encode_col(C);
            // 判断类型，优先取显示字符串
            let value = contentCell.v;
            if (contentCell.t === 'd' && contentCell.w) {
              value = contentCell.w;
            }
            const dateObj = dayjs(value);
            if (dateObj.isValid() && (typeof value === 'string')) {
              value = dateObj.format('MM/DD/YYYY');
            }
            columnInfo[columnKey] = {
              label: headerCell.v,
              content: value
            };
          }
        }
        excelData.value = columnInfo;
      };
      reader.readAsArrayBuffer(file);
    }
  }
  if (status === "done") { // 上传成功-添加下载链接
    info.file.url = window.URL.createObjectURL(info.file.originFileObj);
    message.success(`${info.file.name} file uploaded successfully.`);
  } else if (status === "") {
    message.error(`${info.file.name} file upload failed.`);
  }
};

function handleDrop(e) { console.log(e);}

const handleCancel = () => {
  formRef.value?.resetFields();
  fileList.value = [];
  open.value = false;
};

const handleAdd = debounceAsync(async() => {
  await formRef.value?.validate();
  if (excelData.value) {
      const excel = excelData.value
      extraInfo.expect_deadline = excel['S']?.content;
      extraInfo.name = excel['D']?.content;
      extraInfo.sub_task_name = excel['E']?.content;
      extraInfo.no_scenes = excel['M']?.content;
      extraInfo.image_received_on = excel['K']?.content;
      extraInfo.requeued_and_validated = excel['N']?.content;
      extraInfo.specific_scene_ids = excel['O']?.content;
      extraInfo.mds_available = excel['P']?.content;
      extraInfo.additional_document = excel['Q']?.content;
      extraInfo.priority_level = excel['R']?.content;
      extraInfo.attachments = excel['U']?.content;
      extraInfo.remark = excel['T']?.content;
  }
  const res = await createTaskApi(formState);
  if(res.code === "0000") {
    message.success(t('common.uploadSuccess'));
  }
  const { extra_info, type, id } = res.data;
  router.push({ path: '/task-detail', state: { excelData: JSON.stringify(extra_info), type: type, 
  projectValidation: JSON.stringify(props.projectValidation), clientId: props.clientId, taskId: id }});
  emit('refresh');
}, 300);

const showAddValidationDialog = () => {
    open.value = true;
};

defineExpose({
    showAddValidationDialog
});
</script>
