<template>
    <a-modal title="Select Scene" :open="open" width="80%" @cancel="handleCancel" centered>
        <div style="height: 500px; overflow: auto;" @scroll="handleScroll">
            <div class="flex items-center mb-4 mt-4">
              <div class="ml-2 h-8 leading-[32px]">
                <span class="mr-2 font-sourcehans text-14px font-500">{{ $t('scenesList.title') }}</span>
                <a-radio-group v-model:value="status" class="ml-2" name="radioGroup">
                    <a-radio value="0">{{ $t('common.all') }}</a-radio>
                    <a-radio value="1">{{ $t('common.selected') }}</a-radio>
                </a-radio-group>
              </div>
                <a-form 
                    v-if="status === '0'" 
                    :model="formState" 
                    layout="inline" 
                    ref="formRef"
                    class="ml-4"
                >
                    <a-form-item name="value4">
                        <a-range-picker v-model:value="value4" :format="dateFormat" @change="fetchSceneDetail" />
                    </a-form-item>
                    <a-form-item name="scene_type" class="w-40">
                        <a-select v-model:value="formState.scene_type" allowClear :placeholder="$t('scenesList.type')" class="rounded-md" @change="fetchSceneDetail">
                            <a-select-option v-for="type in sceneTypeOptions" :key="type" :value="type">{{ type }}</a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item name="sub_scene_type" class="w-40">
                        <a-select v-model:value="formState.sub_scene_type" allowClear :placeholder="$t('scenesList.sub_scene_type')" class="rounded-md" @change="fetchSceneDetail">
                            <a-select-option v-for="type in subSceneTypeOptions" :key="type" :value="type">{{ type }}</a-select-option>
                        </a-select>
                    </a-form-item>
                </a-form>
            </div>
          <div :class="{'status-selected': status === '1'}">
                <a-table 
                    ref="tableRef"
                    :columns="columns" 
                    :data-source="filteredTableData" 
                    :row-selection="status === '0' ? rowSelection : null"
                    :pagination="false"
                    :loading="loading"
                >
                    <template #bodyCell="{ column, record, index }">
                        <template v-if="column.dataIndex === 'index' && status === '1'" >
                            {{ index + 1 }} 
                        </template>
                        <template v-if="column.dataIndex === 'img_received_on'">
                            {{ formatTimestamp(record.img_received_on, 'MM/DD/YYYY') }}
                        </template>
                        <template v-if="column.dataIndex === 'validation_status'">
                            <a-badge :status="record.validation_status === 1 ? 'success' : 'processing'" :text="record.validation_status === 1 ? 'Verified' : 'Unverified'" />
                        </template>
                        <template v-if="column.dataIndex === 'action' && status === '1'">
                            <a @click="handleRemove(record)" class="text-[#FF4D4F] hover:text-[#FF4D4F]">
                                {{ $t('common.remove') }}
                            </a>
                        </template>
                    </template>
                </a-table>
            </div>
        </div>
        <template #footer>
            <div class="h-[32px] mt-8">
                <template v-if="status === '1'">
                    <a-button @click="handleCancel">{{ t('common.cancel') }}</a-button>
                    <a-button type="primary" @click="handleSave">{{ t('common.save') }}</a-button>
                </template>
            </div>
        </template>
    </a-modal>
</template>

<script setup>
import { ref, computed, reactive } from 'vue';
import { useI18n } from 'vue-i18n';
import { scenesListApi } from '@/api/project-list/index';
import dayjs from 'dayjs';
import { dateToTimestamp, formatTimestamp } from '@/utils/date';
import { tableLsitFileterApi } from '@/api/task/index';
import { message } from 'ant-design-vue';

const { t } = useI18n();
const emit = defineEmits(['refresh']);
const open = ref(false);
const tableData = ref([]);
const loading = ref(false);
const status = ref('0');
const dateFormat = 'MM/DD';
const selectedKeys = ref([]);
const disabledKeys = ref([]);
const formRef = ref();
const tableRef = ref();
const sceneTypeOptions = ref([]);
const subSceneTypeOptions = ref([]);
const value4 = ref([
  dayjs().subtract(7, 'day').startOf('day'),
  dayjs().endOf('day')
]);
const formState = reactive({
  created_at_start: null,
  created_at_end: null,
  scene_type: null,
  sub_scene_type: null,
  page_no: 0,
  page_size: 20
});

const columns = computed(() => {
  const baseColumns = [
    { title: "#", dataIndex: 'index' },
    { title: t('scenesList.type'), dataIndex: 'type' },
    { title: t('scenesList.sub_scene_type'), dataIndex: 'sub_scene_type' },
    { title: t('scenesList.img_received_on'), dataIndex: 'img_received_on' },
    { title: t('scenesList.id'), dataIndex: 'out_scene_id', ellipsis: true },
    { title: t('scenesList.total_facings'), dataIndex: 'total_facings' },
    { title: t('scenesList.total_imgs'), dataIndex: 'total_imgs' },
  ];

  if (status.value === '1') {
    baseColumns.push(
      { title: t('common.action'), dataIndex: 'action' },
    );
  } else {
    baseColumns.shift();
    baseColumns.splice(4, 0, { 
      title: t('scenesList.validation_status'), 
      dataIndex: 'validation_status' 
    });
  }
  return baseColumns;
});

function handleScroll(e) {
  if(status.value === '0') {
    const el = e.target;
    const scrollPosition = el.scrollTop;
    const viewportHeight = el.clientHeight;
    const totalHeight = el.scrollHeight;
    
    // 当滚动到距离底部90%的位置时就开始预加载下一页数据,提前加载 降低loading效果
    const threshold = totalHeight - viewportHeight * 1.9;
    
    if (scrollPosition > threshold) {
      if (handleScroll.timer) {
        clearTimeout(handleScroll.timer);
      }
    // requestAnimationFrame 会在下一帧之前执行回调函数,可以避免频繁触发
      handleScroll.timer = requestAnimationFrame(() => {
        // 检查是否正在加载中,避免重复请求
        if(!loading.value && !handleScroll.hasShownWarning) {
          // 预加载数据时不显示loading状态
          const originalLoading = loading.value;
          loading.value = false;
          
          formState.page_no++;
          fetchSceneDetail(true).catch(err => {
            console.error('预加载数据失败:', err);
            formState.page_no--;
          }).finally(() => {
            // 恢复原始loading状态
            loading.value = originalLoading;
          });
        }
      });
    }
  }
}

const fetchSceneDetail = async (append = false) => {
  try {
    loading.value = true;
    if (value4.value && value4.value.length === 2) {
      formState.created_at_start = dateToTimestamp(dayjs(value4.value[0]).startOf('day'));
      formState.created_at_end = dateToTimestamp(dayjs(value4.value[1]).endOf('day'));
    }
    const res = await scenesListApi(formState);
    const newData = (res.data?.data_list || []).map(item => ({
      ...item,
      key: item.id
    }));
    if (append === true) {
      if (newData.length === 0) {
        handleScroll.hasShownWarning = true;
        message.warning(t('common.noMoreData'));
      }
      tableData.value = [...tableData.value, ...newData];
    } else {
      handleScroll.hasShownWarning = false;
      formState.page_no = 0;
      tableData.value = newData;
    }
  } catch (error) {
  } finally {
      loading.value = false;
  }
};

const filteredTableData = computed(() => {
  if (status.value === '1') {
    return tableData.value.filter(item => selectedKeys.value.includes(item.key));
  }
  return tableData.value;
});

const rowSelection = ref({
  onChange: (selectedRowKeys) => {
    selectedKeys.value = selectedRowKeys;
  },
  getCheckboxProps: (record) => ({
    disabled: disabledKeys.value.includes(record.id),
  }),
  selectedRowKeys: selectedKeys
});

const handleCancel = () => {
  open.value = false;
  formRef.value?.resetFields();
  if(tableRef.value?.store) {
      tableRef.value.store.selectedRowKeys = [];
    } else {
      selectedKeys.value = [];
  }
  status.value = '0';
};

const handleRemove = (record) => {
  selectedKeys.value = selectedKeys.value.filter(key => key !== record.key);
};

const handleSave = () => {
  if (status.value === '1') {
    emit('add-scenes', filteredTableData.value);
  }
  open.value = false;
  status.value = '0';
};

const fetchFilterOptions = async () => {
  try {
    const params = {
      data_source_list: ['scene']
    };
    const res = await tableLsitFileterApi(params);
    if (res.data && res.data.scene) {
      sceneTypeOptions.value = res.data.scene.scene_type || [];
      subSceneTypeOptions.value = res.data.scene.sub_scene_type || [];
    }
  } catch (error) {
    console.error(error);
  }
};

const showSelectSceneDialog = (ids = []) => {
  open.value = true;
  disabledKeys.value = ids;
  selectedKeys.value = [];
  formState.page_no = 0;
  tableData.value = [];
  fetchFilterOptions();
  fetchSceneDetail();
};

defineExpose({
  showSelectSceneDialog
});
</script>

<style scoped>
.ant-table-row-selected > td {
  background: none !important;
}
:deep(.ant-table-thead > tr > th),
:deep(.ant-table-tbody > tr > td.ant-table-cell) {
  height: 32px !important;
}
</style>