<template>
    <a-modal title="Synchronization" :open="open" :closable="false">
        <a-result title="Synchronization is complete!">
            <template #icon>
                <a-progress
                    type="circle"
                    :stroke-color="{
                        '0%': '#108ee9',
                        '100%': '#87d068',
                    }"
                    :percent="100"
                />
            </template>
            <template #extra>
                <a-button type="primary">Go Back</a-button>
            </template>
        </a-result>

        <a-result title="Synchronizing data, please do not close the window!">
            <template #icon>
                <a-progress
                    type="circle"
                    :stroke-color="{
                        '0%': '#108ee9',
                        '100%': '#87d068',
                    }"
                    :percent="90"
                />
            </template>
            <template #extra>
                <a-button type="primary" disabled>Go Back</a-button>
            </template>
        </a-result>

        <a-result title="Synchronization error, please try again later!">
            <template #icon>
                <a-progress type="circle" :percent="100" status="exception" />
            </template>
            <template #extra>
                <a-button type="primary">Go Back</a-button>
            </template>
        </a-result>
    </a-modal>
</template>

<script setup>
import { ref } from 'vue';

const open = ref(false)

const showSyscStatusDialog = () => {
    open.value = true;
};

defineExpose({
    showSyscStatusDialog
});
</script>