<template>
  <div class="p-4">
    <a-card :title="t('projectDetail.basicInfo')">
      <template #extra v-if="props.clientId === null">
            <a-button type="link" class="text-[#8C8C8C]" @click="resetEdit">{{ t('common.cancel') }}</a-button>
            <a-button type="link" @click="handleCreate" :loading="saving">
              {{ t('common.save') }}
            </a-button>
      </template>
      <a-spin :spinning="loading">
          <a-form :model="formState" layout="horizontal" ref="basicFormRef" label-align="right" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }" :rules="basicFormRules">
            <a-row justify="space-evenly">
              <a-col :span="8">
                <a-form-item :label="t('projectList.region')" name="region">
                  <a-input v-model:value="formState.region" :placeholder="t('projectList.enterRegion')" :disabled="props.clientId !== null" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item :label="t('projectList.country')" name="country">
                  <a-input v-model:value="formState.country" :placeholder="t('projectList.enterCountry')" :disabled="props.clientId !== null" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row justify="space-evenly">
              <a-col :span="8">
                <a-form-item :label="t('projectList.client')" name="client">
                  <a-input v-model:value="formState.client" :placeholder="t('projectList.enterClient')" :disabled="props.clientId !== null" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item :label="t('projectList.subClient')" name="sub_client">
                  <a-input v-model:value="formState.sub_client" :placeholder="t('projectList.enterSubClient')" :disabled="props.clientId !== null" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row justify="space-evenly">
              <a-col :span="8">
                <a-form-item :label="t('projectList.modelType')" name="model_type">
                  <a-input v-model:value="formState.model_type" :placeholder="t('projectList.enterModelType')" :disabled="props.clientId !== null" />
                </a-form-item>
              </a-col>
              <a-col :span="8" v-if="props.clientId !== null">
                <a-form-item :label="t('projectList.lastModifiedOn')" name="updated_at">
                  <a-input v-model:value="formState.updated_at" :disabled="props.clientId !== null" />
                </a-form-item>
              </a-col>
              <a-col :span="8" v-if="props.clientId === null">
                <a-form-item :label="t('projectList.client_reference_id')" name="client_reference_id">
                  <a-input v-model:value="formState.client_reference_id" :disabled="props.clientId !== null" :placeholder="t('projectList.enterClientReferenceId')" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
      </a-spin>
    </a-card>

    <a-card :title="t('projectDetail.detail')" class="mt-[14px] h-[69vh]">
      <template #extra v-if="props.clientId !== null">
        <a-button type="link" @click="handleEdit" v-if="isSave">{{ t('common.edit') }}</a-button>
        <span v-else>
          <a-button type="link" class="text-[#8C8C8C]" @click="cancelEdit">{{ t('common.cancel') }}</a-button>
          <a-button type="link" @click="saveChanges" :loading="saving">
            {{ t('common.save') }}
          </a-button>
        </span>
      </template>
      <a-spin :spinning="loading">
          <div class="flex gap-6 relative">
            <div class="w-2/5">
              <a-form :model="formState.project_config" layout="horizontal" ref="formRef" label-align="right" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }" :rules="detailFormRules">
                <a-form-item :label="t('projectDetail.generateTaskDaily')" name="is_gen_daily">
                  <a-radio-group v-model:value="formState.project_config.is_gen_daily" :disabled="isSave">
                    <a-radio :value="1" class="w-20">{{ t('projectDetail.yes') }}</a-radio>
                    <a-radio :value="0">{{ t('projectDetail.no') }}</a-radio>
                  </a-radio-group>
                </a-form-item>
                
                <a-form-item label="Generate Task Type" name="generate_task_type">
                  <a-radio-group v-model:value="formState.project_config.generate_task_type" :disabled="isSave">
                    <a-radio value="None" class="w-20">None</a-radio>
                    <a-radio value="Repetition">Repetition</a-radio>
                  </a-radio-group>
                </a-form-item>
                
                <a-form-item label="Repetition Rate" name="repetition_rate">
                  <a-select 
                    v-model:value="formState.project_config.repetition_rate" 
                    :disabled="isSave || formState.project_config.generate_task_type !== 'Repetition'"
                    :placeholder="formState.project_config.generate_task_type === 'Repetition' ? 'Select content' : 'Select content'"
                    class="w-[180px]"
                  >
                    <a-select-option value="1 Day">1 Day</a-select-option>
                    <a-select-option value="7 Day">7 Day</a-select-option>
                  </a-select>
                </a-form-item>
                
                <a-form-item :label="t('projectDetail.taskReleaseType')" name="release_type">
                  <a-radio-group v-model:value="formState.project_config.release_type" :disabled="isSave">
                    <a-radio :value="0" class="w-20">{{ t('projectDetail.auto') }}</a-radio>
                    <a-radio :value="1">{{ t('projectDetail.manual') }}</a-radio>
                  </a-radio-group>
                </a-form-item>
                
                <a-form-item :label="t('projectDetail.tgtType')" name="tgt_type">
                  <a-radio-group v-model:value="formState.project_config.tgt_type" :disabled="isSave">
                    <a-radio :value="0" class="w-20">{{ t('projectDetail.scene') }}</a-radio>
                    <a-radio :value="1">{{ t('projectDetail.image') }}</a-radio>
                  </a-radio-group>
                </a-form-item>
                
                <a-form-item :label="t('projectDetail.imageRetrieval')" name="img_retireval">
                  <a-radio-group v-model:value="formState.project_config.img_retireval" :disabled="isSave">
                    <a-radio :value="0" class="w-20">{{ t('projectDetail.number') }}</a-radio>
                    <a-radio :value="1">{{ t('projectDetail.percentage') }}</a-radio>
                  </a-radio-group>
                </a-form-item>
                
                <a-form-item :label="t('projectDetail.orientationValidation')" name="is_ori_val">
                  <a-radio-group v-model:value="formState.project_config.is_ori_val" :disabled="isSave">
                    <a-radio :value="1" class="w-20">{{ t('projectDetail.yes') }}</a-radio>
                    <a-radio :value="0">{{ t('projectDetail.no') }}</a-radio>
                  </a-radio-group>
                </a-form-item>
                
                <a-form-item :label="t('projectDetail.target')" name="img_tgt">
                  <div class="relative inline-flex items-center">
                    <a-input-number v-model:value="formState.project_config.img_tgt" class="w-[180px]" :min="0" :disabled="isSave"/>
                    <span v-if="formState.project_config.img_retireval === 1" class="ml-1 leading-[32px]">%</span>
                  </div>
                </a-form-item>
              </a-form>
            </div>

            <div class="absolute left-[40%] top-0 bottom-0 w-[1px] bg-[#f0f0f0]"></div>
            <div class="w-3/5">
              <AccountList :validators="validators" @save="handleSave" @user-data="handleUserData" :isSave="isSave"/>
            </div>
          </div>
      </a-spin>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { message } from 'ant-design-vue';
import { projectApi, putProjectApi } from '@/api/project-list/index';
import AccountList from './AccountList.vue';
import { formatTimestamp } from '@/utils/date';

const { t } = useI18n();
const props = defineProps({
  projectDetailList: {
    type: Object,
    default: {}
  },
  clientId: {
    type: Number,
    default: null
  }
});
const emit = defineEmits(['update']);
const loading = ref(false);
const saving = ref(false);
const formRef = ref();
const basicFormRef = ref();
const validators = ref([]);
const selectedValidators = ref([]);
const userData = ref([]);
const isSave = ref(true); 
const router = useRouter();

const initialFormState = () => ({
  region: '',
  country: '',
  client: '',
  model_type: '',
  sub_client: '',
  client_reference_id: '',
  project_config: {
    project_id: 0,
    is_gen_daily: null,
    generate_task_type: 'None',
    repetition_rate: null,
    release_type: null,
    tgt_type: null,
    img_retireval: null,
    img_tgt: null,
    is_ori_val: null,
    user_ids: [],
  },
  updated_at: null,
});

const formState = reactive(initialFormState());

const basicFormRules = computed(() => {
  if (props.clientId !== null) return undefined;
  return {
    region: [{ required: true, message: t('projectList.enterRegion'), trigger: 'blur' }],
    country: [{ required: true, message: t('projectList.enterCountry'), trigger: 'blur' }],
    client: [{ required: true, message: t('projectList.enterClient'), trigger: 'blur' }],
    model_type: [{ required: true, message: t('projectList.enterModelType'), trigger: 'blur' }],
    sub_client: [{ required: true, message: t('projectList.enterSubClient'), trigger: 'blur' }],
    client_reference_id: [{ required: true, message: t('projectList.enterClientReferenceId'), trigger: 'blur' }]
  };
});

const detailFormRules = computed(() => {
  if (props.clientId !== null) return undefined;
  return {
    is_gen_daily: [
      { required: true, message: t('projectDetail.enterIsGenDaily'), trigger: 'change' }
    ],
    release_type: [
      { required: true, message: t('projectDetail.enterReleaseType'), trigger: 'change' }
    ],
    tgt_type: [
      { required: true, message: t('projectDetail.enterTgtType'), trigger: 'change' }
    ],
    img_retireval: [
      { required: true, message: t('projectDetail.enterImageRetrieval'), trigger: 'change' }
    ],
    img_tgt: [
      { required: true, message: t('projectDetail.enterImgTgt'), trigger: 'blur' }
    ],
    is_ori_val: [
      { required: true, message: t('projectDetail.enterIsOriVal'), trigger: 'change' }
    ]
  };
});

const resetEdit = () => {
  formRef.value?.resetFields();
  basicFormRef.value?.resetFields();
  validators.value = [];
  selectedValidators.value = [];
  router.back();
};

const getUserInfo = (userId) => {
  const user = userData.value.find(u => u.id === userId) || {};
  return {
    key: user.id,
    user_id: user.id,
    department: user.department,
    name: user.name
  };
};

const getProjectDetail = async () => {
    const {
      region = '',
      country = '',
      client = '',
      sub_client = '',
      model_type = '',
      updated_at = '',
      project_config = null,
      project_config_users = [],
    } = props.projectDetailList || {};
    
    validators.value = project_config_users.map(item => getUserInfo(item.user_id));
    
    const defaultConfig = {
      is_gen_daily: null,
      generate_task_type: 'None',
      repetition_rate: null,
      release_type: null,
      tgt_type: null,
      img_retireval: null,
      img_tgt: null,
      is_ori_val: null,
    };

    const config = project_config || defaultConfig;

    Object.assign(formState, {
      region,
      country,
      client, 
      model_type,
      sub_client,
      updated_at: formatTimestamp(updated_at),
      project_config: {
        is_gen_daily: config.is_gen_daily,
        generate_task_type: config.generate_task_type || 'None',
        repetition_rate: config.repetition_rate,
        release_type: config.release_type,
        tgt_type: config.tgt_type,
        img_retireval: config.img_retireval,
        img_tgt: config.img_tgt,
        is_ori_val: config.is_ori_val,
      }
    });
};

const cancelEdit = () => {
  isSave.value = true;
  getProjectDetail();
};

const handleSave = ({ allValidators, targetKeys }) => {
  validators.value = allValidators;
  formState.project_config.user_ids = targetKeys;
};

const handleUserData = (data) => {
  userData.value = data;
  if (props.clientId) {
    getProjectDetail();
  }
}

const handleCreate = async () => {
  try {
    saving.value = true;
    if (!formState.project_config.user_ids?.length) {
      message.warning(t('projectDetail.pleaseSelectUsers'));
      return;
    }
    await Promise.all([
      basicFormRef.value?.validate(),
      formRef.value?.validate()
    ]);
    const { updated_at, ...submitData } = formState;
    const response = await projectApi(submitData);
    if (response.code === "0000") {
      message.success(t('common.saveSuccess'));
      emit('update');
      router.back();
    } else {
      message.error(t('common.saveFailed'));
    }
  } catch (error) {
    console.log("error", error);
  } finally {
    saving.value = false;
  }
};

const saveChanges = async () => {
  try {
    saving.value = true;
    const { project_config } = formState;
    if (!validators.value?.length) {
      message.warning(t('projectDetail.pleaseSelectUsers'));
      return;
    }
    const id = Number(props.clientId);
    project_config.user_ids = validators.value.map(validator => validator.user_id);
    const processedConfig = {
      is_gen_daily: project_config.is_gen_daily === null ? 0 : project_config.is_gen_daily,
      generate_task_type: project_config.generate_task_type || 'None',
      repetition_rate: project_config.repetition_rate,
      release_type: project_config.release_type === null ? 0 : project_config.release_type,
      tgt_type: project_config.tgt_type === null ? 0 : project_config.tgt_type,
      img_retireval: project_config.img_retireval === null ? 0 : project_config.img_retireval,
      img_tgt: project_config.img_tgt === null ? 0 : project_config.img_tgt,
      is_ori_val: project_config.is_ori_val === null ? 0 : project_config.is_ori_val,
      user_ids: project_config.user_ids
    };
    const submitData = {
      ...processedConfig,
      project_id: id
    };
    const response = await putProjectApi(id, submitData);
    if (response.code === "0000") {
      message.success(t('common.saveSuccess'));
      isSave.value = true;
      emit('update');
    } else {
      message.error(t('common.saveFailed'));
    }
  } catch (error) {
    message.error(t('common.saveFailed'));
  } finally {
    saving.value = false;
  }
}; 

const handleEdit = () => {
  isSave.value = false;
};

watch(
  () => props.projectDetailList,
  () => {
    getProjectDetail();
  },
  { immediate: true }
);
</script>