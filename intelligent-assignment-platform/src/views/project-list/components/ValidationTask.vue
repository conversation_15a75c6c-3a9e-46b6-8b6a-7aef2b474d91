<template>
  <div class="p-4">
    <a-card class="h-[89vh]">
      <div class="flex justify-between items-center mb-[10px]">
        <div class="relative">
          <a-input v-model:value="searchKeyword" :placeholder="$t('common.search')"  class="w-75" @pressEnter="onSearch">
            <template #suffix>
              <a-tooltip title="Extra information">
                <SearchOutlined style="color: rgba(0, 0, 0, 0.45)"/>
              </a-tooltip>
            </template>
          </a-input>

          <a-button type="text" ref="filterButtonRef" :class="['ml-2', isFilterActive ? 'text-blue-500 hover:text-blue-600' : 'text-gray-400 hover:text-gray-500']"  @click="handleAdvancedFilter">
              <img class="mt-[-4px]" src="@/assets/images/filter.svg" width="18" height="18" 
              :style="{ filter: isFilterActive ? 
                      'invert(40%) sepia(90%) saturate(2000%) hue-rotate(190deg)' :
                      'invert(50%) sepia(0%) saturate(0%) brightness(90%)' }"/>
              <span class="ml-1">{{ t('common.advancedFilter') }}</span>
          </a-button>

          <advanced-filter-dialog ref="showFilterRef" :style="filterDialogStyle" @update="fetchValidationDetail" :pagination="pagination" @refresh="onAdvancedFilterResult"  @getParams="handleFilterParams" />
        </div>
        <a-button type="primary" @click="showAddClientDialog()"><plus-outlined />{{ t('common.new') }}</a-button>
      </div>
      <a-table 
        :columns="columns" 
        :data-source="tableData" 
        :scroll="{ y: 'calc(89vh - 180px)', x: 1300 }" 
        size="middle" 
        :loading="loading"
        :row-key="record => record.key"
        :pagination="pagination.paginationConfig"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'index'">
            {{ index + 1 }}
          </template>
          <template v-if="column.dataIndex === 'created_at'">
            {{ formatTimestamp(record.created_at) }}
          </template>
          <template v-if="column.dataIndex === 'expect_deadline'">
            {{ formatTimestamp(record.expect_deadline) }}
          </template>
          <template v-if="column.dataIndex === 'status'">
            <a :style="{ color: getStatusTextColor(record.status) }">
              {{ 
                record.status === 0 ? t('validitionTask.unpublished') : 
                record.status === 1 ? t('validitionTask.publishedNotStarted') :
                record.status === 2 ? t('validitionTask.inProgress') :
                record.status === 3 ? t('validitionTask.completed') : 
                record.status 
              }}
            </a>
          </template>
          <template v-if="column.dataIndex === 'scene_done'">
            <div class="flex items-center">
              <div class="w-24 flex-shrink-0">
                <a-progress 
                  :percent="getProgressPercent(record.scene_done, record.scene_total)" 
                  size="small"
                  :showInfo="false" 
                  strokeColor="#1890FF"/>
              </div>
              <span class="ml-2">{{ record.scene_done }}/{{ record.scene_total }}</span>
            </div>
          </template>
          <template v-if="column.dataIndex === 'action'">
            <a class="text-primary1 mr-2" @click="viewTaskDetail(record)">{{ record.status === 3 ? t('common.look') : t('common.edit')}}</a>
						<a-button type="link" size="small" class="text-primary1" v-if="record.status === 0" @click="handlePublish(record.id)">{{t('common.publish')}}</a-button>
						<a-button type="link" size="small" danger class="text-primary1" v-if="record.status === 0" @click="handleDel(record.id)">{{t('common.del')}}</a-button>
          </template>
        </template>
      </a-table>
      <add-validation-dialog ref="showAndPersonnelRef" :clientId="clientId" @refresh="fetchValidationDetail" :projectValidation="projectValidation"/>
    </a-card>
    <a-modal v-model:open="open" title="Notification" :confirm-loading="confirmLoading" @ok="handleOk">
      <p>{{ modalText }}</p>
      <template #footer>
        <a-button key="back" @click="open = false">Cancel</a-button>
        <a-button key="submit" type="primary" :loading="loading" @click="handleOk">Confirm</a-button>
      </template>
    </a-modal>

    <a-modal v-model:open="open1" title="Notification" :confirm-loading="confirmLoading" @ok="handleOk1">
      <p>{{ modalText1 }}</p>
      <template #footer>
        <a-button key="back" @click="open1 = false">Cancel</a-button>
        <a-button key="submit" type="primary" :loading="loading" @click="handleOk1">Confirm</a-button>
      </template>
    </a-modal>
  </div> 
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { PlusOutlined } from '@ant-design/icons-vue';
import AddValidationDialog from './components/AddValidationDialog.vue';
import AdvancedFilterDialog from './TaskAdvancedFilterDialog.vue';
import { taskApi, publishScenesApi, delDetailApi, DetailSqueryApi } from '@/api/project-list/index';
import { debounceAsync } from '@/utils/debounce';
import { message } from 'ant-design-vue';
import { useI18n } from 'vue-i18n';
import { formatTimestamp } from '@/utils/date';
import { SearchOutlined } from '@ant-design/icons-vue';
import { useTablePagination } from '@/composable/useTablePagination';

const pagination = useTablePagination({
    onChange: (page, size) => {
        fetchValidationDetail(page, size);
    },
});
const { t } = useI18n();
const open = ref(false);
const open1 = ref(false);
const confirmLoading = ref(false);
const modalText = ref('Are you sure you want to publish this record?');
const modalText1 = ref('Are you sure you want to delete this record?');
const loading = ref(false);
const searchKeyword = ref('');
const tableData = ref([]);
const router = useRouter();
const showAndPersonnelRef = ref();
const showFilterRef = ref();
const emit = defineEmits(['view-detail']);
const projectId = ref();
const recordId = ref();
const filterParamsStatus = ref({});
const filterButtonRef = ref();

function getPaginationParams() {
    return {
        page_no: pagination.page_no.value - 1,
        page_size: pagination.page_size.value,
    }
}

const filterDialogStyle = computed(() => {
    if (!filterButtonRef.value) return {};
    const buttonRect = filterButtonRef.value.$el.getBoundingClientRect();
    return {
        position: 'absolute',
        top: `${buttonRect.height + 150}px`,
        left: `${buttonRect.left}px`,
        zIndex: 1000
    };
});

// 计算属性判断是否有激活的筛选条件
const isFilterActive = computed(() => {
  return Object.entries(filterParamsStatus.value).some(([_, value]) => 
    Array.isArray(value) ? value.length > 0 : (value !== undefined && value !== null && value !== '')
  );
});

const handleAdvancedFilter = () => {
	showFilterRef.value.showAdvancedFilter();
};

const props = defineProps({
  projectValidation: {
    type: Object,
    default: () => ({})
  },
  clientId: {
    type: Number,
    default: null
  },
  clientId: {
    type: Number,
    default: null
  }
});

const onSearch = debounceAsync(async () => {
    const res = await DetailSqueryApi({key: searchKeyword.value, ...getPaginationParams()});
    tableData.value = res.data?.data_list || [];
    pagination.total.value = res.data?.count
}, 500);

const viewTaskDetail = (record) => {
  if(record.status === 3) {
    router.push({
        name: 'TaskDetail',
        query: { taskId: record.id, type: "view" }
    })
  } else {
    router.push({
      path: `/task-detail`, 
      query: { id: record.id }, 
      state: { projectValidation: JSON.stringify(props.projectValidation), clientId:  projectId.value }
    });
  }
};

const fetchValidationDetail = async () => {
  projectId.value = props.clientId;
  loading.value = true;
  try {
    const commitData = {
      project_id: projectId.value,
      ...getPaginationParams(),
    }
    const res = await taskApi(commitData);
    tableData.value = res.data?.data_list || [];
    pagination.total.value = res.data?.count
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};

const onAdvancedFilterResult = (data) => {
  tableData.value = data;
};

const handleFilterParams = (params) => {
    filterParamsStatus.value = params;
};

const handlePublish = async (id) => {
  recordId.value = id;
  open.value = true;
}

const handleOk = debounceAsync(async() => {
  try {
    const res = await publishScenesApi(Number(recordId.value));
    if (res.code === "0000") {
      message.success(t('common.publishSuccess'));
    } 
    fetchValidationDetail();
    open.value = false;
  } catch (error) {
    console.log(error);
  }
}, 300);

const handleDel = (id) => {
  recordId.value = id;
  open1.value = true;
}

const handleOk1 = debounceAsync(async(id) => {
  try {
    const res = await delDetailApi(Number(recordId.value));
    if (res.code === "0000") {
      message.success(t('common.delSuccess'));
      tableData.value = tableData.value.filter(item => item.id !== id);
    }
    fetchValidationDetail();
    open1.value = false;
  } catch (error) {
    console.log(error);
  }
}, 300);

const columns = [
  { title: 'Task Id', dataIndex: 'id', width: 80 },
  { title: 'Task Name', dataIndex: 'name'},
  { title: 'Task Type', dataIndex: 'type'},
  { title: 'Created On', dataIndex: 'created_at', width: 180 },
  { title: 'Expected deadline', dataIndex: 'expect_deadline', width: 180},
  { title: 'Scene', dataIndex: 'scene_total'},
  { title: 'Total Validator', dataIndex: 'validator_count'},
  { title: 'Total Progress', dataIndex: 'scene_done', width: 180},
  { title: 'Status', dataIndex: 'status'},
  { title: 'Action', dataIndex: 'action', fixed: 'right', width: 180 },
];

const getStatusTextColor = (status) => {
  const colorMap = {
    0: '#00ABEB',
    1: '#1677FF', 
    2: '#FAAD14', 
    3: '#52C41A',
  };
  return colorMap[status] || '#000000';
};

const getProgressPercent = (scene_done, scene_total) => {
  if (scene_total === 0) return 0;
  return Math.floor((scene_done / scene_total) * 100);
};

const showAddClientDialog = () => {
	showAndPersonnelRef.value.showAddValidationDialog();
};

onMounted(() => {
	fetchValidationDetail();
});
</script>