<template>
   <a-modal :title="$t('advancedFilter.title')" :open="open" @cancel="handleCancel" :mask="false">
        <a-form :model="formState" layout="horizontal" ref="formRef" class="m-6" label-align="right" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" >
            <div class="form-container">
                <a-form-item label="Task Id" name="task_id">
                    <a-input v-model:value="formState.task_id" :placeholder="$t('common.enterContent')" />
                </a-form-item>

                <a-form-item label="Task Name" name="task_name">
                    <a-input v-model:value="formState.task_name" :placeholder="$t('common.enterContent')" />
                </a-form-item>

                <a-form-item label="Task Type" name="task_type">
                    <a-select v-model:value="formState.task_type" :placeholder="$t('common.selectContent')" allowClear class="rounded-md" @change="sceneList">
                        <a-select-option v-for="type in taskOptions" :key="type" :value="type">{{ type }}</a-select-option>
                    </a-select>
                </a-form-item>

                <a-form-item label="Created On" name="dataRange">
                    <a-range-picker v-model:value="dataRange" :format="'YYYY/MM/DD'" class="w-full"/>
                </a-form-item>

                <a-form-item label="Status" name="status">
                    <a-select v-model:value="formState.status" allowClear :placeholder="$t('common.selectContent')" class="rounded-md">
                        <a-select-option v-for="type in statusOptions" :key="type.value" :value="type.vaule">{{ type.label }}</a-select-option>
                    </a-select>
                </a-form-item>
            </div>
        </a-form>

        <template #footer>
            <div class="mr-6 text-right">
            <a-button @click="handleReset">{{ $t('common.reset') }}</a-button>
            <a-button type="primary" @click="handleSearch">{{ $t('common.query') }}</a-button>
            </div>
        </template>
    </a-modal>
  </template>
  
  <script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { useI18n } from 'vue-i18n';
  import { message } from 'ant-design-vue';
  import { statusOptions } from '@/utils/dropdownOptions';
  import { tableLsitFileterApi } from '@/api/task/index';
  import { debounceAsync } from '@/utils/debounce';
  import { taskApi } from '@/api/project-list/index';
  import { dateToTimestamp } from '@/utils/date';
  
  const { t } = useI18n();
  const emit = defineEmits(['refresh', 'update', 'close', 'getParams']);
  const formRef = ref();
  const dataRange = ref([]);
  const taskOptions = ref([]);
  const open = ref(false);
  const formState = reactive({
        task_id: null,
        task_name: '',
        task_type: null,
        create_on_start: null,
        create_on_end: null,
        status: null,
  });
  const props = defineProps({
    pagination: {
            type: Object,
            default: false
        }
  });
  
  const fetchFilterOptions = async () => {
    try {
        const params = { data_source_list: ['val_task'] };
        const res = await tableLsitFileterApi(params);
        if (res.data?.val_task) {
            taskOptions.value = res.data.val_task.task_type || [];
        }
    } catch (error) {
        console.error(error);
    }
  };

  const handleCancel = () => {
    formRef.value?.resetFields();
    open.value = false;
  };

  const handleSearch = debounceAsync(async () => {
      try {
          if (dataRange.value && dataRange.value.length === 2) {
            formState.create_on_start = dateToTimestamp(dataRange.value[0]);
            formState.create_on_end = dateToTimestamp(dataRange.value[1]);
          }
          emit('getParams', formState);
          const page_no = props.pagination?.page_no?.value ?? 1;
          const page_size = props.pagination?.page_size?.value ?? 10;
          const res = await taskApi({
            ...formState,
            page_no: page_no - 1,
            page_size
          });
          if (res.code === "0000") {
              message.success(t('common.querySuccess'));
              emit('refresh', res.data?.data_list || []);
              if (props.pagination && res.data?.count !== undefined) {
                props.pagination.total.value = res.data.count;
              }
          }
          open.value = false;
      } catch (error) {
          console.error(t('common.queryFailed'), error);
      }
  }, 500);

  const handleReset = () => {
    emit('update');
    handleCancel();
  };
  
  onMounted(() => {
    fetchFilterOptions();
  });

  const showAdvancedFilter = () => {
    open.value = true;
  };
  
 defineExpose({ 
   showAdvancedFilter
});
</script>