<template>
    <div class="p-4 ">
      <a-card class="h-[89vh]">
        <a-form :model="formState" layout="inline" ref="FormRef" class="mb-2 flex flex-wrap gap-y-2">
          <a-form-item name="value4">
            <a-range-picker v-model:value="value4" :format="dateFormat" @change="fetchSceneDetail" />
          </a-form-item>
          <a-form-item name="scene_type" class="w-50">
            <a-select v-model:value="formState.scene_type" allowClear :placeholder="$t('scenesList.type')" class="rounded-md" @change="fetchSceneDetail">
              <a-select-option v-for="type in sceneTypeOptions" :key="type" :value="type">{{ type }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item name="sub_scene_type" class="w-50">
            <a-select v-model:value="formState.sub_scene_type" allowClear :placeholder="$t('scenesList.sub_scene_type')" class="rounded-md" @change="fetchSceneDetail">
              <a-select-option v-for="type in subSceneTypeOptions" :key="type" :value="type">{{ type }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item name="val_status" class="w-50">
            <a-select v-model:value="formState.val_status" allowClear :placeholder="$t('scenesList.validation_status')"  class="rounded-md" @change="fetchSceneDetail">
              <a-select-option v-for="type in types" :key="type.id" :value="type.id">{{ type.label }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item name="validator_id" class="w-50">
            <a-select v-model:value="formState.validator_id" allowClear :placeholder="$t('scenesList.validator_ids')" class="rounded-md" @change="fetchSceneDetail">
              <a-select-option v-for="type in validatorByOptions" :key="type.id" :value="type.id">{{ type.label }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item name="out_scene_id">
            <a-input v-model:value="formState.out_scene_id" allowClear :placeholder="$t('scenesList.out_scene_ids')" @change="fetchSceneDetail"/>
          </a-form-item>
        </a-form>

        <a-table 
          :columns="columns" 
          :data-source="tableData" 
          size="middle"
          :scroll="{ y: 'calc(89vh - 180px)', x: 'max-content' }" 
          :pagination="pagination.paginationConfig"
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'index'">
              {{ index + 1 }}
            </template>
            <template v-if="column.dataIndex === 'img_received_on'">
                {{ formatTimestamp(record.img_received_on, 'MM/DD/YYYY') }}
            </template>
            <template v-if="column.dataIndex === 'validation_status'">
                <a-badge :status="record.validation_status === 1 ? 'success' : 'processing'" :text="record.validation_status === 1 ? 'Verified' : 'Unverified'" />
            </template>
            <template v-if="column.dataIndex === 'finished_at'">
              {{ formatTimestamp(record.finished_at) }}
            </template>
            <template v-if="column.dataIndex === 'validator_id'">
              {{ record.validation_status === 0 ? '' : getUserName(record.validator_id) }}
            </template>
          </template>
        </a-table>
      </a-card>
    </div>
  </template>
  
  <script setup>
  import { ref, reactive, onMounted, computed } from 'vue';
  import { scenesListApi } from '@/api/project-list/index';
  import dayjs from 'dayjs';
  import { useI18n } from 'vue-i18n';
  import { dateToTimestamp, formatTimestamp } from '@/utils/date';
  import { tableLsitFileterApi } from '@/api/task/index';
  import { useUserDataStore } from '@/store/userDataStore';
  import { storeToRefs } from 'pinia';
  import { useTablePagination } from '@/composable/useTablePagination';

  const pagination = useTablePagination({
      onChange: (page, size) => {
          fetchSceneDetail(page, size);
      },
  });
  const store = useUserDataStore();
  const { userData } = storeToRefs(store);

  const { t } = useI18n();
  const loading = ref(false);
  const tableData = ref([]);
  const dateFormat = 'MM/DD';
  const sceneTypeOptions = ref([]);
  const subSceneTypeOptions = ref([]);
  const lastValUserIds = ref([]);
  const props = defineProps({
    clientId: {
      type: Number,
      default: null
    }
  });
  const value4 = ref([
    dayjs().subtract(7, 'day').startOf('day'),
    dayjs().endOf('day')
  ]);
  const types = [
    { id: 0, label: 'Unverified' },
    { id: 1, label: 'Verified' },
  ]

  const validatorByOptions = computed(() => {
    return userData.value.filter(user => lastValUserIds.value.includes(user.id))
      .map(user => ({ id: user.id, label: user.name }));
  });

  const formState = reactive({
    created_at_start: null,
    created_at_end: null,
    scene_type: null,
    sub_scene_type: null,
    val_status: null,
    validator_id: null,
    out_scene_id: null,
    project_id: props.clientId,
  });
  const columns = [
    // { title: "#", dataIndex: 'index' },
    { title: t('scenesList.type'), dataIndex: 'type', width: 120 },
    { title: t('scenesList.sub_scene_type'), dataIndex: 'sub_scene_type', width: 150 },
    { title: t('scenesList.img_received_on'), dataIndex: 'img_received_on', width: 120 },
    { title: t('scenesList.id'), dataIndex: 'out_scene_id', ellipsis: true, width: 200 },
    { title: t('scenesList.total_facings'), dataIndex: 'total_facings', width: 150 },
    { title: t('scenesList.total_imgs'), dataIndex: 'total_imgs', width: 120  },
    { title: t('scenesList.validator_ids'), dataIndex: 'validator_id', width: 120},
    { title: t('scenesList.created_at'), dataIndex: 'finished_at', width: 180 },
    { title: t('scenesList.validation_status'), dataIndex: 'validation_status', width: 150 },
  ]

  const getUserName = (id) => {
    const user = userData.value.find(user => user.id === id);
    return user ? user.name : id;
  };

  const fetchSceneDetail = async () => {
    loading.value = true;
    try {
      if (value4.value && value4.value.length === 2) {
        formState.created_at_start = dateToTimestamp(dayjs(value4.value[0]).startOf('day'));
        formState.created_at_end = dateToTimestamp(dayjs(value4.value[1]).endOf('day'));
      }
      const commitData = {
        ...formState,
        page_no: pagination.page_no.value - 1,
        page_size: pagination.page_size.value,
      }
      const res = await scenesListApi(commitData);
      tableData.value = (res.data?.data_list || []).map(item => ({
          ...item,
          key: item.id
      }));
      pagination.total.value = res.data?.count
    } catch (error) {
    } finally {
      loading.value = false;
    }
  };

  const fetchFilterOptions = async () => {
    try {
      const params = { data_source_list: ['scene']};
      const res = await tableLsitFileterApi(params);
      if (res.data && res.data.scene) {
        sceneTypeOptions.value = res.data.scene.scene_type || [];
        subSceneTypeOptions.value = res.data.scene.sub_scene_type || [];
        lastValUserIds.value = res.data.scene.last_val_user_ids || [];
      }
    } catch (error) {
      console.error(error);
    }
  };

  onMounted(async () => {
    if (!store.hasData) { // 仅在数据未加载时调用API
        await store.fetchUserData();
    }
    await Promise.all([ fetchFilterOptions(), fetchSceneDetail()]);
  })
  </script>
