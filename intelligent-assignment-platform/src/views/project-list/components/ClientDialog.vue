<template>
    <a-modal :title="editMode ? $t('projectList.editClient') : $t('projectList.addClient')" :open="visible"
        :width="700" @cancel="handleCancel">
        <a-form :model="formState" :rules="rules" layout="vertical" ref="formRef">
            <a-row :gutter="24">
                <a-col :span="12">
                    <a-form-item :label="$t('projectList.clientName')" name="name">
                        <a-input v-model:value="formState.name" :placeholder="$t('projectList.enterClientName')" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item :label="$t('projectList.contactPerson')" name="contactPerson">
                        <a-input v-model:value="formState.contactPerson"
                            :placeholder="$t('projectList.enterContactPerson')" />
                    </a-form-item>
                </a-col>
            </a-row>
            <a-row :gutter="24">
                <a-col :span="12">
                    <a-form-item :label="$t('projectList.contactPhone')" name="contactPhone">
                        <a-input v-model:value="formState.contactPhone"
                            :placeholder="$t('projectList.enterContactPhone')" />
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item :label="$t('projectList.email')" name="email">
                        <a-input v-model:value="formState.email" :placeholder="$t('projectList.enterEmail')" />
                    </a-form-item>
                </a-col>
            </a-row>
            <a-row :gutter="24">
                <a-col :span="12">
                    <a-form-item :label="$t('projectList.industry')" name="industry">
                        <a-select v-model:value="formState.industry" :placeholder="$t('common.pleaseSelect')">
                            <a-select-option value="finance">{{ $t('projectList.industries.finance') }}</a-select-option>
                            <a-select-option value="technology">{{ $t('projectList.industries.technology')
                                }}</a-select-option>
                            <a-select-option value="education">{{ $t('projectList.industries.education')
                                }}</a-select-option>
                            <a-select-option value="healthcare">{{ $t('projectList.industries.healthcare')
                                }}</a-select-option>
                        </a-select>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item :label="$t('projectList.status')" name="status">
                        <a-select v-model:value="formState.status" :placeholder="$t('common.pleaseSelect')">
                            <a-select-option value="active">{{ $t('projectList.statuses.active') }}</a-select-option>
                            <a-select-option value="inactive">{{ $t('projectList.statuses.inactive') }}</a-select-option>
                        </a-select>
                    </a-form-item>
                </a-col>
            </a-row>
            <a-row :gutter="24">
                <a-col :span="24">
                    <a-form-item :label="$t('projectList.address')" name="address">
                        <a-textarea v-model:value="formState.address" :placeholder="$t('projectList.enterAddress')"
                            :rows="2" />
                    </a-form-item>
                </a-col>
            </a-row>
            <a-row :gutter="24">
                <a-col :span="24">
                    <a-form-item :label="$t('projectList.description')" name="description">
                        <a-textarea v-model:value="formState.description"
                            :placeholder="$t('projectList.enterDescription')" :rows="3" />
                    </a-form-item>
                </a-col>
            </a-row>
        </a-form>
        <template #footer>
            <a-button @click="handleCancel">{{ $t('common.cancel') }}</a-button>
            <a-button type="primary" @click="handleSave">{{ $t('common.save') }}</a-button>
        </template>
    </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const formRef = ref();

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    editMode: {
        type: Boolean,
        default: false
    },
    clientData: {
        type: Object,
        default: null
    }
});

const emit = defineEmits(['update:visible', 'save']);

// 表单状态
const formState = reactive({
    id: null,
    name: '',
    contactPerson: '',
    contactPhone: '',
    email: '',
    industry: undefined,
    status: 'active',
    address: '',
    description: ''
});

// 表单验证规则
const rules = {
    name: [
        { required: true, message: t('projectList.nameRequired'), trigger: 'blur' },
        { max: 50, message: t('projectList.nameMaxLength'), trigger: 'blur' }
    ],
    contactPerson: [
        { required: true, message: t('projectList.contactPersonRequired'), trigger: 'blur' }
    ],
    contactPhone: [
        { required: true, message: t('projectList.contactPhoneRequired'), trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: t('projectList.contactPhoneInvalid'), trigger: 'blur' }
    ],
    email: [
        { type: 'email', message: t('projectList.emailInvalid'), trigger: 'blur' }
    ],
    industry: [
        { required: true, message: t('projectList.industryRequired'), trigger: 'change' }
    ],
    status: [
        { required: true, message: t('projectList.statusRequired'), trigger: 'change' }
    ]
};

// 监听visible和clientData变化
watch(
    () => [props.visible, props.clientData],
    ([newVisible, newClientData]) => {
        if (newVisible) {
            if (props.editMode && newClientData) {
                // 编辑模式，填充表单数据
                Object.keys(formState).forEach(key => {
                    if (newClientData[key] !== undefined) {
                        formState[key] = newClientData[key];
                    }
                });
            } else {
                // 添加模式，重置表单
                resetForm();
            }
        }
    },
    { immediate: true }
);

// 重置表单
const resetForm = () => {
    formState.id = null;
    formState.name = '';
    formState.contactPerson = '';
    formState.contactPhone = '';
    formState.email = '';
    formState.industry = undefined;
    formState.status = 'active';
    formState.address = '';
    formState.description = '';

    // 重置表单验证
    if (formRef.value) {
        formRef.value.resetFields();
    }
};

// 取消
const handleCancel = () => {
    emit('update:visible', false);
};

// 保存
const handleSave = () => {
    formRef.value.validate().then(() => {
        // 表单验证通过
        const clientData = { ...formState };
        emit('save', clientData);
    }).catch(error => {
        console.log(error);
    });
};
</script>