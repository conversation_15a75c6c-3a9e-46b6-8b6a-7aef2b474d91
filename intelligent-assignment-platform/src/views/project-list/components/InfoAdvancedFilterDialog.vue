<template>
    <a-modal :title="$t('advancedFilter.title')" :open="open" @cancel="handleCancel" :mask="false">
        <a-form :model="formState" layout="horizontal" ref="formRef" class="m-6" label-align="right" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" >
            <a-form-item :label="$t('projectList.country')" name="country">
                <a-select v-model:value="formState.country" allowClear :placeholder="$t('common.selectContent')" class="rounded-md" @change="fetchSceneDetail">
                    <a-select-option v-for="country in countryOptions" :key="country" :value="country">{{ country }}</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item :label="$t('projectList.client')" name="client">
                <a-select v-model:value="formState.client" allowClear :placeholder="$t('common.selectContent')" class="rounded-md">
                    <a-select-option v-for="client in clientOptions" :key="client" :value="client">{{ client }}</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item :label="$t('projectList.modelType')" name="model_type">
                <a-select v-model:value="formState.model_type" allowClear :placeholder="$t('common.selectContent')" class="rounded-md">
                    <a-select-option v-for="modelType in modelTypeOptions" :key="modelType" :value="modelType">{{ modelType }}</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item :label="$t('projectList.targetType')" name="config_tgt_type">
                <a-select v-model:value="formState.config_tgt_type" allowClear :placeholder="$t('common.selectContent')" class="rounded-md">
                    <a-select-option v-for="targetType in targetTypeOptions" :key="targetType.value" :value="targetType.value">{{ targetType.label }}</a-select-option>
                </a-select>
            </a-form-item>
        </a-form>

        <template #footer>
            <div class="mr-6 text-right">
                <a-button @click="handleReset">{{ $t('common.reset') }}</a-button>
                <a-button type="primary" @click="handleSearch">{{ $t('common.query') }}</a-button>
            </div>
        </template>
    </a-modal>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { queryProjectApi } from '@/api/project-list/index';
import { tableLsitFileterApi } from '@/api/task/index';
import { debounceAsync } from '@/utils/debounce';
import { targetTypeOptions } from '@/utils/dropdownOptions';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const emit = defineEmits(['refresh', 'update', 'close', 'getParams']);

const formRef = ref();
const countryOptions = ref([]);
const clientOptions = ref([]);
const modelTypeOptions = ref([]);
const open = ref(false);
const formState = reactive({
    country: null,
    client: null,
    model_type: null,
    config_tgt_type: null,
});
const props = defineProps({
  pagination: {
        type: Object,
        default: false
    }
});

const fetchFilterOptions = async () => {
    try {
        const params = { data_source_list: ['project'] };
        const res = await tableLsitFileterApi(params);
        if (res.data && res.data.project) {
            countryOptions.value = res.data.project.country || [];
            clientOptions.value = res.data.project.client || [];
            modelTypeOptions.value = res.data.project.model_type || [];
        }
    } catch (error) {
        console.error('Failed to get filter options:', error);
    }
};

const handleCancel = () => {
    formRef.value?.resetFields();
    open.value = false;
};

const handleSearch = debounceAsync(async () => {
    try {
        emit('getParams', formState);
        const page_no = props.pagination?.page_no?.value ?? 1;
        const page_size = props.pagination?.page_size?.value ?? 10;

        const res = await queryProjectApi({
            ...formState,
            page_no: page_no - 1,
            page_size
        });

        if (res.code === "0000") {
            message.success(t('common.querySuccess'));
            emit('refresh', res.data?.data_list || []);
            if (props.pagination && res.data?.count !== undefined) {
                props.pagination.total.value = res.data.count;
            }
        }
        open.value = false;
    } catch (error) {
        message.error(t('common.queryError'));
    }
}, 500);

const handleReset = () => {
    emit('update');
    handleCancel();
};

onMounted(() => {
    fetchFilterOptions();
});

const showAdvancedFilter = () => {
    open.value = true;
};
  
defineExpose({
    showAdvancedFilter
});

</script>
