<template>
    <div class="pl-1">
        <div class="flex justify-between items-center mb-4">
            <div class="text-sm font-medium">{{ t('projectDetail.validatorList') }}</div>
            <div class="flex" v-if="!isSave">
              <a-button 
                type="link" 
                :class="selectedValidators.length ? '!text-[#ff4d4f]' : '!text-[#8C8C8C]'" 
                @click="handleRemove()">
                  {{ t('common.remove') }}
              </a-button>
              <a-dropdown v-if="props.showDropdown">
                <template #overlay>
                  <a-menu @click="handleMenuClick">
                    <a-menu-item key="1">
                      <template #icon>
                          <img src="@/assets/images/presets.svg" :alt="t('modelValidation.zoomOut')" width="18" height="18" />
                      </template>
                      <span>Presets</span>
                    </a-menu-item>
                    <a-menu-item key="2">
                      <template #icon>
                          <img src="@/assets/images/custom.svg" :alt="t('modelValidation.zoomOut')" width="18" height="18" />
                      </template>
                      <span>Custom</span>
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link">
                  {{ t('common.add') }}
                </a-button>
              </a-dropdown>
              <a-button type="link" @click="addValidator" v-else>
                  {{ t('common.add') }}
              </a-button>
            </div>
        </div>
        <a-table
            :columns="validatorColumns"
            :data-source="props.validators"
            row-key="user_id"
            :row-selection="!isSave ? {
              selectedRowKeys: selectedValidators,
              onChange: onValidatorSelect,
              getCheckboxProps: (record) => ({
                disabled: props.taskStatus >= 1 && initialValidatorIds.has(record.user_id)
              })
            } : null"
            :pagination="false"
            :scroll="{ y: '45vh' }"
            >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex === 'index'" >
                {{ index + 1 }}
              </template>
              <template v-if="column.key === 'action'">
                <a-button 
                  type="link" 
                  danger 
                  @click="() => removeValidator(record.user_id)" 
                  :disabled="isSave || (props.taskStatus >= 1 && initialValidatorIds.has(record.user_id))"
                  :style="{ color: (props.taskStatus >= 1 && initialValidatorIds.has(record.user_id)) ? '#ccc' : '#ff4d4f' }"
                >
                  {{ t('common.remove') }}
                </a-button>
              </template>
            </template>
        </a-table>
        <AddPersonnelDialog ref="showAndPersonnelRef" @save-personnel="handlePersonnelSave" :user-data="userData"/>
    </div>
  </template>
  
<script setup>
import { ref, onMounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import AddPersonnelDialog from './components/AddPersonnelDialog.vue';
import { userListApi } from '@/api/system/index';
import { getQAConfigApi } from '@/api/task/index';

const emit = defineEmits(['save', 'user-data']);
const { t } = useI18n();
const userData = ref([]);
const showAndPersonnelRef = ref();
const selectedValidators = ref([]);
const userIds = ref([]);
const props = defineProps({
  validators: { type: Array, default: () => [] },
  isSave: { type: Boolean, default: false },
  showDropdown: { type: Boolean, default: false },
  taskStatus: { type: Number, default: 0 },
});

const handleMenuClick = (e) => {
  if (e.key === '1') {
    handleQAConfig();
  } else if (e.key === '2') {
    addValidator();
  }
}
const getUserInfo = (userId) => {
  const user = userData.value.find(u => u.id === userId) || {};
  return {
    key: user.id,
    user_id: user.id,
    department: user.department,
    name: user.name,
  };
};

const handleQAConfig = async () => {
  try {
    const res = await getQAConfigApi();
    if (res.code === '0000') {
      const ids = res.data.data_list.map(item => item.user_id);
      const mapped = ids.map(id => getUserInfo(id));
      emit('save', { allValidators: mapped, targetKeys: ids });
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
};

const userList = async () => {
  try {
    const res = await userListApi();
    userData.value = res.data;
    emit('user-data', userData.value);
  } catch (error) {
    console.error("请求失败:", error);
  }
};

const addValidator = () => {
  const ids = props.validators.map(item => item.user_id);
  showAndPersonnelRef.value.showAddPersonelDialog(ids);
};

const validatorColumns = computed(() => {
  const baseColumns = [
    // { title: "#", dataIndex: 'index' },
    { title: t('projectDetail.name'), dataIndex: 'name', key: 'name' },
    { title: t('projectDetail.department'), dataIndex: 'department', key: 'department' },
    { title: t('common.action'), dataIndex: 'action', key: 'action', width: 110, align: 'center' },
  ];
  // if (!props.isSave) {
  //   baseColumns.shift();
  // }
  return baseColumns;
});

const onValidatorSelect = (selectedRowKeys) => {
  // 当taskStatus < 1时，所有数据都可以选择；当taskStatus >= 1时，只允许选择新添加的数据
  if (props.taskStatus < 1) {
    selectedValidators.value = selectedRowKeys;
  } else {
    selectedValidators.value = selectedRowKeys.filter(id => !initialValidatorIds.value.has(id));
  }
};

const removeValidator = (id) => {
  // 当taskStatus < 1时，所有数据都可以删除；当taskStatus >= 1时，只允许删除新添加的数据
  if (props.taskStatus < 1 || !initialValidatorIds.value.has(id)) {
    const newValidators = props.validators.filter(item => item.user_id !== id);
    emit('save', { allValidators: newValidators, targetKeys: newValidators.map(item => item.user_id) });
  }
};

const handleRemove = () => {
  // 当taskStatus < 1时，删除所有选中的数据；当taskStatus >= 1时，只删除选中的新添加数据
  let removableIds;
  if (props.taskStatus < 1) {
    removableIds = selectedValidators.value;
  } else {
    removableIds = selectedValidators.value.filter(id => !initialValidatorIds.value.has(id));
  }
  const newValidators = props.validators.filter(item => !removableIds.includes(item.user_id));
  emit('save', { allValidators: newValidators, targetKeys: newValidators.map(item => item.user_id) });
  selectedValidators.value = [];
};

const handlePersonnelSave = ({ targetKeys }) => {
  const existedIds = props.validators.map(item => item.user_id);
  const newIds = targetKeys.filter(id => !existedIds.includes(id));
  const newValidators = newIds.map(id => getUserInfo(id));
  const existedValidators = existedIds.map(id => getUserInfo(id));
  const allValidators = [...existedValidators, ...newValidators];
  userIds.value = [...new Set([...existedIds, ...newIds])];
  emit('save', { allValidators: allValidators, targetKeys: userIds.value });
};
const initialValidatorIds = ref(new Set());
onMounted(async () => {
  await userList();
  // 确保在所有数据加载完成后再初始化 initialValidatorIds
  initialValidatorIds.value = new Set(props.validators.map(item => item.user_id));
});
</script>