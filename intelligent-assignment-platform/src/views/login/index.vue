<template>
    <div class="flex justify-center items-center w-full h-screen">
        <div class="bg-white rounded-lg shadow-lg w-[480px] h-[610px] px-12 py-16">
            <div v-if="!isRegister">
                <!-- Logo区域 -->
                <div class="flex justify-center items-center mb-12">
                    <img src="@/assets/images/login-logo.svg" alt="" class="h-20">
                </div>
                
                <!-- 表单区域 -->
                <a-form :model="formState" autocomplete="off" @finish="onFinish" class="w-full">
                    <a-form-item name="username" :rules="[{ required: true, message: t('user.usernameRequired') }]" 
                        class="mb-6 flex justify-center items-center">
                        <a-input 
                            v-model:value="formState.username" 
                            :placeholder="$t('user.account')"
                            class="login-input bg-[#f5f5f5] rounded-lg w-80 h-12 text-base" 
                            size="large"
                        />
                    </a-form-item>
                    
                    <a-form-item name="password" :rules="[{ required: true, message: t('user.passwordRequired') }]" 
                        class="mb-8 flex justify-center items-center">
                        <a-input-password 
                            v-model:value="formState.password" 
                            :placeholder="$t('user.password')"
                            class="login-input bg-[#f5f5f5] rounded-[4px] w-80 h-12 text-base" 
                            size="large"
                        />
                    </a-form-item>
                    
                    <a-form-item class="mb-6 w-80 ml-8">
                        <a-button 
                            type="primary" 
                            html-type="submit" 
                            class="login-button h-12 text-base font-medium" 
                            size="large"
                            :loading="loading"
                            block
                        >
                            Login
                        </a-button>
                    </a-form-item>
                    
                    <div class="flex items-center ml-9">
                        <a-checkbox v-model:checked="formState.remember" class="text-gray-400 text-sm">
                            Automatic Login
                        </a-checkbox>
                    </div>
                </a-form>
            </div>
            <div class="mb-2" v-else>
                <Register @backToLogin="isRegister = false" />
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import Register from '@/page/login/register.vue'
import { loginByUserApi } from '@/api/user';
import { authMenuListApi } from '@/api/system';
import { debounceAsync } from '@/utils/debounce';
import { message } from 'ant-design-vue';
import { useI18n } from 'vue-i18n';
import { useMenuStore } from '@/store/useMenuStore';
const { t, locale } = useI18n();
locale.value = 'en-US'

const router = useRouter();
const menuStore = useMenuStore();
const isRegister = ref(false);
const loading = ref(false);
const formState = ref({
    username: '',
    password: '',
    remember: false,
});

const onFinish = debounceAsync(async () => {
    loading.value = true;
    const data = {
        username: formState.value.username,
        password: formState.value.password,
    };
    try {
        const res = await loginByUserApi(data);
        if (res.code === 0) {
            message.success(t('user.loginSuccess'));
            localStorage.setItem('exp', res.data.exp);
            
            // 获取用户菜单权限
            let redirectPath = '/';
            try {
                const menuRes = await authMenuListApi();
                if (menuRes.code === 0) {
                    // 调试输出，查看接口返回的原始菜单数据
                    // console.log('authMenuListApi 原始菜单数据:', menuRes.data);
                    localStorage.setItem('userMenus', JSON.stringify(menuRes.data));
                    // 同步到store
                    menuStore.setUserMenus(menuRes.data);
                    
                    // 查找第一个authorized为true的菜单进行跳转
                    const authorizedMenu = menuRes.data.find(menu => 
                        menu.authorized === true && 
                        (menu.type === '目录' || menu.type === '页面') && menu.path
                    );
                    
                    if (authorizedMenu) {
                        redirectPath = authorizedMenu.path;
                    }
                }
            } catch (menuErr) {
                console.error('获取菜单权限失败:', menuErr);
            }
            
            if (formState.value.remember) {
                localStorage.setItem('autoLoginInfo', JSON.stringify({
                    username: formState.value.username,
                    password: formState.value.password,
                    remember: true
                }));
            } else {
                localStorage.removeItem('autoLoginInfo');
            }
            console.log(redirectPath)
            router.push({ path: redirectPath });
        }
    } catch (err) {
        message.error(err.message);
    } finally {
        loading.value = false;
    }
}, 500);

const handleRegister = () => {
    isRegister.value = true;
};

onMounted(() => {
    const autoLoginInfo = localStorage.getItem('autoLoginInfo');
    if (autoLoginInfo) {
        const { username, password, remember } = JSON.parse(autoLoginInfo);
        formState.value.username = username;
        formState.value.password = password;
        formState.value.remember = remember;
        // 自动登录
        if (remember && username && password) {
            onFinish();
        }
    }
});
</script>
<style scoped>
:deep(.login-input .ant-input) {
    background-color: #f5f5f5;
    border-radius: 8px;
    font-size: 16px;
}
:deep(.ant-btn.ant-btn-lg) {
    height: 48px !important;
}
</style>