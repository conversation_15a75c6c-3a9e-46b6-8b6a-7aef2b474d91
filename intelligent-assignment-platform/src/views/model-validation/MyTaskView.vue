<template>
    <div class="w-100% rounded-[8px]">
        <!-- 权限拒绝页面 -->
        <div v-if="isUnauthorized" class="rounded-[8px] h-[95vh] min-h-[784px] flex items-center justify-center">
            <div class="text-center">
                <img src="@/assets/images/noPermisson.svg" alt="Permission Denied" class="mx-auto mb-4" style="width: 323px; height: 270px;" />
                <p class="text-gray-500 text-lg">Permission Denied</p>
            </div>
        </div>
        
        <!-- 正常任务页面 -->
        <div v-else class="rounded-[8px] h-[95vh] min-h-[784px]">
            <!-- 标签页导航 -->
            <div class="tabs-container mb-4">
                <a-tabs v-model:activeKey="activeTab" class="custom-tabs" @change="handleTabChange" :tab-bar-style="{ borderRadius: 0 }">
                    <!-- 验证任务 -->
                    <a-tab-pane key="validation" tab="Validation">
                        <template #tab>
                            <span>Validation</span>
                            <a-badge v-if="unfinishedValidationCount > 0" :count="unfinishedValidationCount"
                                :number-style="{ backgroundColor: '#ff4d4f' }" />
                        </template>
                        <div class="flex justify-between items-center mb-[10px] relative">
                            <div>
                                <a-input v-model:value="searchKeyword" :placeholder="$t('common.search')" class="w-75" @pressEnter="onSearch('validation')">
                                    <template #suffix>
                                        <a-tooltip title="Extra information">
                                            <SearchOutlined style="color: rgba(0, 0, 0, 0.45)"/>
                                        </a-tooltip>
                                    </template>
                                </a-input>

                                <a-button :type="'text'" ref="filterButtonRef" :class="['ml-2', isFilterActive ? 'text-blue-500 hover:text-blue-600' : 'text-gray-400 hover:text-gray-500']" @click="handleAdvancedFilter('validation')">
                                    <img class="mt-[-4px]" src="@/assets/images/filter.svg" width="18" height="18" 
                                    :style="{ filter: isFilterActive ? 
                                        'invert(40%) sepia(90%) saturate(2000%) hue-rotate(190deg)' :
                                        'invert(60%) sepia(0%) saturate(0%) brightness(80%)' }"/>
                                    <span class="ml-1">{{ $t('common.advancedFilter') }}</span>
                                </a-button>

                                <AdvancedFilterDialog ref="showFilterRef" :style="filterDialogStyle" :activeTab="'validation'" @refresh="fetchFilterListData" @close="() => {}" />
                            </div>
                        </div>
                        <!-- 表格区域 -->
                        <a-table :columns="columns" :loading="loading" :data-source="filteredValidColumns" :pagination="validationPagination.paginationConfig"
                            :scroll="{ x: 1300 }" size="middle" :row-key="record => record.key" class="custom-table-48px">
                            <!-- 序号列 -->
                            <template #bodyCell="{ column, record, index }">
                                <!-- 序号列 -->
                                <template v-if="column.dataIndex === 'index'">
                                    {{ index + 1 }}
                                </template>
                                <template v-if="column.dataIndex === 'created_at'">
                                    {{ record.created_at ? formatTimestamp(record.created_at) : '' }}
                                </template>
                                <template v-if="column.dataIndex === 'expect_deadline'">
                                    {{ record.expect_deadline ? formatTimestamp(record.expect_deadline) : '' }}
                                </template>
                                <template v-if="column.dataIndex === 'validator_id'">
                                    {{ getUserName(record.validator_id) }}
                                </template>
                                <template v-if="column.dataIndex === 'status'">
                                    <a :style="{ color: getStatusTextColor(record.status) }">
                                    {{ 
                                        record.status === 0 ? t('validitionTask.unpublished') : 
                                        record.status === 1 ? t('validitionTask.publishedNotStarted') :
                                        record.status === 2 ? t('validitionTask.inProgress') :
                                        record.status === 3 ? t('validitionTask.completed') : 
                                        record.status 
                                    }}
                                    </a>
                                </template>

                                <!-- 进度列 -->
                                <template v-if="column.dataIndex === 'progress'">
                                    <div class="flex items-center">
                                        <div class="w-24 flex-shrink-0">
                                            <a-progress :percent="getProgressPercent(record.scene_done, record.scene_total)"
                                                size="small" :showInfo="false"
                                                :strokeColor="getProgressColor(record.scene_done, record.scene_total)"/>
                                        </div>
                                        <span class="ml-2">{{ formatProgressText(record.scene_done+((record.saved_line_no || 0)/(record.scene_sku_count || 1)), record.scene_total || 0) }}</span>
                                    </div>
                                </template>

                                <!-- 操作列 -->
                                <template v-if="column.dataIndex === 'action'">
                                    <div class="flex flex-row">
                                        <a-button type="link" size="small" class="text-primary1"
                                            @click="goToTaskDetail(record, 'view')">
                                            View
                                        </a-button>
                                        <a-button type="link" size="small" class="text-primary1" 
                                            v-if="record.scene_done !== record.scene_total" @click="goToTaskDetail(record,'start')">
                                            Start
                                        </a-button>
                                    </div>
                                </template>
                            </template>
                        </a-table>
                    </a-tab-pane>

                    <a-tab-pane key="qa" tab="QA Task">
                        <template #tab>
                            <span>QA Task</span>
                            <a-badge v-if="unfinishedQACount > 0" :count="unfinishedQACount"
                                :number-style="{ backgroundColor: '#ff4d4f' }" />
                        </template>
                        <!-- 搜索和筛选区域 -->
                        <div class="search-filter-container flex justify-between items-center mb-4 relative">
                            <div>
                                <a-input v-model:value="searchKeyword" :placeholder="$t('common.search')" class="w-75" @pressEnter="onSearch('qa')">
                                    <template #suffix>
                                        <a-tooltip title="Extra information">
                                            <SearchOutlined style="color: rgba(0, 0, 0, 0.45)"/>
                                        </a-tooltip>
                                    </template>
                                </a-input>
                                <a-button :type="'text'" ref="filterButtonRef1" :class="['ml-2', isFilterActive ? 'text-blue-500 hover:text-blue-600' : 'text-gray-400 hover:text-gray-500']" @click="handleAdvancedFilter('qa')">
                                    <img class="mt-[-4px]" src="@/assets/images/filter.svg" width="18" height="18" 
                                    :style="{ filter: isFilterActive ? 
                                        'invert(40%) sepia(90%) saturate(2000%) hue-rotate(190deg)' :
                                        'invert(60%) sepia(0%) saturate(0%) brightness(80%)' }"/>
                                    <span class="ml-1">{{ $t('common.advancedFilter') }}</span>
                                </a-button>

                                <AdvancedFilterDialog ref="showFilterRef" :style="filterDialogStyle1" :activeTab="'validation'" @refresh="fetchFilterListData" @close="() => {}" />
                            </div>
                            <div class="flex items-center gap-2">
                                <a-button type="default" class="flex flex-row text-[#1677FF] border border-[#1677FF]"
                                    @click="showQaExecutorModal">
                                    <template #icon>
                                        <img src="@/assets/images/user.svg" width="20" height="20" />
                                    </template>
                                    <div class="ml-1">QA Executor</div>
                                </a-button>
                                <a-button type="primary" class="ml-2" @click="showAddQaTaskModal"> + &nbsp;{{ t('common.new') }}</a-button>
                            </div>
                        </div>
                        <!-- 表格区域 -->
                        <a-table :columns="qaColumns" :loading="loading" :data-source="filteredQaTableData"
                            :pagination="qaPagination.paginationConfig" :scroll="{ x: 1300 }" size="middle"
                            :row-key="record => record.key" class="custom-table-48px">
                            <!-- 序号列 -->
                            <template #bodyCell="{ column, record, index }">
                                <!-- 序号列 -->
                                <template v-if="column.dataIndex === 'index'">
                                    {{ index + 1 }}
                                </template>
                                <template v-if="column.dataIndex === 'status'">
                                    <a :style="{ color: getStatusTextColor(record.status) }">
                                    {{ 
                                        record.status === 0 ? t('validitionTask.unpublished') : 
                                        record.status === 1 ? t('validitionTask.publishedNotStarted') :
                                        record.status === 2 ? t('validitionTask.inProgress') :
                                        record.status === 3 ? t('validitionTask.completed') : 
                                        record.status 
                                    }}
                                    </a>
                                </template>
                                <template v-if="column.dataIndex === 'deadline'">
                                    {{ record.deadline ? formatTimestamp(record.deadline) : '' }}
                                </template>
                                <template v-if="column.dataIndex === 'created_at'">
                                    {{ record.deadline ? formatTimestamp(record.created_at) : '' }}
                                </template>
                                <!-- 进度列 -->
                                <template v-if="column.dataIndex === 'progress'">
                                    <div class="flex items-center">
                                        <a-progress :percent="getProgressPercent(record.sku_done, record.sku_total)"
                                            size="small" :showInfo="false"
                                            :strokeColor="getProgressColor(record.sku_done, record.sku_total)"
                                            style="width: 120px;" />&nbsp;
                                        <span class="ml-2">{{ `${record.sku_done}/${record.sku_total}` }}</span>
                                    </div>
                                </template>

                                <!-- 操作列 -->
                                <template v-if="column.dataIndex === 'action'">
                                    <div class="flex">
                                        <a-button type="link" size="small" class="text-blue-500"
                                            @click="handleEditQA(record.id)" v-if="record.status === 0">
                                            Edit
                                        </a-button>
                                        <a-button type="link" size="small" class="text-blue-500" v-if="record.status !== 0"
                                            @click="goToQATaskDetail(record, 'view')">
                                            View
                                        </a-button>
                                        <a-button type="link" size="small" v-if="record.status === 0" @click="goToPublishTask(record)">
                                            Publish
                                        </a-button>
                                        <a-button type="link" size="small" v-else @click="goToQATaskDetail(record, 'start')" v-if="record.status !== 3">
                                            Start
                                        </a-button>
                                        <a-button type="link" danger class="mt-[-4px]" @click="showDeleteModal(record)" v-if="record.status === 0">{{ t('common.delete') }}</a-button>
                                    </div>
                                </template>
                            </template>
                        </a-table>
                    </a-tab-pane>

                    <a-tab-pane key="error">
                        <template #tab>
                            <span>My Error Results</span>
                            <a-badge v-if="errorCount > 0" :count="errorCount" :offset="[8, -5]"/>
                        </template>
                        <!-- 搜索和筛选区域 -->
                        <div class="search-filter-container flex justify-between items-center mb-4 relative">
                            <div>
                                <a-input v-model:value="searchKeyword" :placeholder="$t('common.search')" class="w-75" @pressEnter="onSearch('error')">
                                    <template #suffix>
                                        <a-tooltip title="Extra information">
                                            <SearchOutlined style="color: rgba(0, 0, 0, 0.45)"/>
                                        </a-tooltip>
                                    </template>
                                </a-input>
                                <a-button :type="'text'" ref="filterButtonRef2" :class="['ml-2', isFilterActive ? 'text-blue-500 hover:text-blue-600' : 'text-gray-400 hover:text-gray-500']" @click="handleAdvancedFilter('error')">
                                    <img class="mt-[-4px]" src="@/assets/images/filter.svg" width="18" height="18" 
                                    :style="{ filter: isFilterActive ? 
                                        'invert(40%) sepia(90%) saturate(2000%) hue-rotate(190deg)' :
                                        'invert(60%) sepia(0%) saturate(0%) brightness(80%)' }"/>
                                    <span class="ml-1">{{ $t('common.advancedFilter') }}</span>
                                </a-button>

                                <AdvancedFilterDialog ref="showFilterRef" :style="filterDialogStyle2" :activeTab="'validation'" @refresh="fetchFilterListData" @close="() => {}" />
                            </div>
                            <div class="flex items-center gap-2">
                                <a-button type="primary" class="ml-2 w-22" @click="handleStartResult">
                                    <template #icon>
                                        <img src="@/assets/images/start.svg" width="16" height="14" />
                                    </template>
                                    <span class="ml-1">&nbsp;{{ t('common.start') }}</span>
                                </a-button>
                            </div>
                        </div>
                        <!-- 表格区域 -->
                        <a-table :columns="errorColumns" :loading="loading" :data-source="filteredErrorTableData"
                            :pagination="errorPagination.paginationConfig" :scroll="{ x: 1300 }" size="middle"
                            :row-key="record => record.key" class="custom-table-48px">
                            <!-- 序号列 -->
                            <template #bodyCell="{ column, record, index }">
                                <!-- 序号列 -->
                                <template v-if="column.dataIndex === 'index'">
                                    {{ index + 1 }}
                                </template>
                                <!-- 状态列 -->
                                <template v-if="column.dataIndex === 'status'">
                                    <a :style="{ color: getStatusColor(record.status) }">
                                        {{ getStatusText(record.status) }}
                                    </a>
                                </template>
                                <!-- 操作列 -->
                                <template v-if="column.dataIndex === 'action'">
                                    <a-button type="link" size="small" class="text-blue-500"
                                        @click="goToErrorResult(record)">
                                        Start
                                    </a-button>
                                </template>
                            </template>
                        </a-table>
                    </a-tab-pane>
                </a-tabs>
            </div>
        </div>
    </div>

    <AddPersonnelDialog ref="showAndPersonnelRef" @save-personnel="handlePersonnelSave" :user-data="userData"/>

    <!-- 添加QA任务弹窗 -->
    <a-modal v-model:open="addQaTaskVisible" :footer="null" width="500px" :closable="true" title="Add QA Task"
        @cancel="closeAddQaTaskModal">
        <div class="add-qa-task-modal mb-2">
            <a-form :model="qaTaskForm" :rules="qaTaskRules" ref="qaTaskFormRef">
                <!-- QA任务名称 -->
                <a-form-item name="name" :rules="[{ required: true, message: 'Please input QA Task Name' }]">
                    <div class="mb-2 font-medium">QA Task Name<span class="text-red-500">*</span></div>
                    <a-input v-model:value="qaTaskForm.name" placeholder="This is QA Task Name" />
                </a-form-item>

                <!-- 预期截止日期 -->
                <a-form-item name="deadline">
                    <div class="mb-2 font-medium">Expected deadline</div>
                    <a-date-picker v-model:value="qaTaskForm.deadline" show-time format="DD/MM/YYYY HH:mm:ss"
                        style="width: 100%" />
                </a-form-item>

                <!-- 底部按钮 -->
                <div class="flex justify-end mt-6 space-x-2">
                    <a-button @click="closeAddQaTaskModal" class="w-20">Cancel</a-button>
                    <a-button type="primary" @click="saveQaTask" class="w-20">Save</a-button>
                </div>
            </a-form>
        </div>
    </a-modal>
    <!-- <AdvancedFilterDialog ref="showFilterRef" :activeTab="'validation'" @refresh="fetchFilterListData" @close="() => {}" /> -->
    
    <!-- 删除确认弹窗 -->
    <a-modal v-model:open="deleteModalVisible" title="Notification" :footer="null" width="400px">
        <div class="py-4">
            <p class="text-base mb-6">Are you sure you want to delete this record?</p>
            <div class="flex justify-end space-x-3">
                <a-button @click="deleteModalVisible = false">Cancel</a-button>
                <a-button type="primary" @click="confirmDelete">Confirm</a-button>
            </div>
        </div>
    </a-modal>
</template>

<script setup>
import { ref, computed, reactive, onMounted, provide, watch } from 'vue';
import AdvancedFilterDialog from './components/AdvancedFilterDialog.vue';
import AddPersonnelDialog from '../project-list/components/components/AddPersonnelDialog.vue';
import { useI18n } from 'vue-i18n'
import { dateToTimestamp } from '@/utils/date';
import { SearchOutlined } from '@ant-design/icons-vue';
import { useRoute } from 'vue-router';

// 使用日期工具组合式API
import { useDateUtils } from '@/composable/useDateUtils';
const { formatTimestamp } = useDateUtils();
import { useRouter } from 'vue-router';
import { useTablePagination } from '@/composable/useTablePagination';
import {
    validationTaskListApi,
    validationTaskSkuApi,
    qaTaskFlowDetailApi,
    qaTaskListApi,
    qaResultListApi,
    qaTaskFlowApi,
    qaTaskDeleteApi,
    validationTaskStartApi,
    qaTaskConfigApi,
    qaTaskPublishApi,
    qaResultCountListApi,
    getQAConfigApi
} from '@/api/task/index';
import { userListApi } from '@/api/system/index';
import { debounceAsync } from '@/utils/debounce';
import { message, Modal } from 'ant-design-vue';

const { t } = useI18n()
const router = useRouter();
const emit = defineEmits(['save', 'user-data']);
const searchKeyword = ref('');
const qaTaskFormRef = ref();
const loading = ref(true);
const showAndPersonnelRef = ref();
const userData = ref([]);
const route = useRoute();
const errorCount = ref(0);
const filterParamsStatus = ref({});
const showFilterRef = ref();
const filterButtonRef = ref();
const filterButtonRef1 = ref();
const filterButtonRef2 = ref();

// 分页配置
const validationPagination = useTablePagination({
    onChange: (page, size) => {
        fetchInitValidColumns(page, size);
    },
});

const qaPagination = useTablePagination({
    onChange: (page, size) => {
        fetchQAListColumns(page, size);
    },
});

const errorPagination = useTablePagination({
    onChange: (page, size) => {
        fetchResultColumns(page, size);
    },
});

// 筛选对话框样式
const filterDialogStyle = computed(() => {
    if (!filterButtonRef.value) return {};
    const buttonRect = filterButtonRef.value.$el.getBoundingClientRect();
    return {
        position: 'absolute',
        top: `${buttonRect.height + 150}px`,
        left: `${buttonRect.left}px`,
        zIndex: 1000
    };
});

const filterDialogStyle1 = computed(() => {
    if (!filterButtonRef1.value) return {};
    const buttonRect = filterButtonRef1.value.$el.getBoundingClientRect();
    return {
        position: 'absolute',
        top: `${buttonRect.height + 150}px`,
        left: `${buttonRect.left}px`,
        zIndex: 1000
    };
});

const filterDialogStyle2 = computed(() => {
    if (!filterButtonRef2.value) return {};
    const buttonRect = filterButtonRef2.value.$el.getBoundingClientRect();
    return {
        position: 'absolute',
        top: `${buttonRect.height + 150}px`,
        left: `${buttonRect.left}px`,
        zIndex: 1000
    };
});

// 计算属性判断是否有激活的筛选条件
const isFilterActive = computed(() => {
  return Object.entries(filterParamsStatus.value).filter(([key]) => key !== 'activeTab').some(([_, value]) => 
    Array.isArray(value) ? value.length > 0 : (value !== undefined && value !== null && value !== '')
  );
});

// 筛选状态管理
const activeFilters = ref({
    validation: {},
    qa: {},
    error: {}
});
const hasActiveFilters = computed(() => {
    return (tab) => {
        const filters = activeFilters.value[tab] || {};
        return Object.keys(filters).some(key => {
            const value = filters[key];
            if (Array.isArray(value)) {
                return value.length > 0;
            }
            return value !== undefined && value !== null && value !== '';
        });
    };
});

// 根据用户ID获取用户名称
const getUserName = (id) => {
    const user = userData.value.find(user => user.id === id);
    return user ? user.name : '';
};
// 检查是否为未授权访问
const isUnauthorized = computed(() => {
    return route.query.authorized === '0';
});

// 删除确认弹窗相关
const deleteModalVisible = ref(false);
const deleteTaskId = ref(null);

const fetchUserList = async () => {
    try {
        const res = await userListApi();
        userData.value = res.data;
        emit('user-data', userData.value);
    } catch (error) {
        console.error('请求失败:', error);
    }
};
const handleStartResult = () => {
    if (errorTableData.value.length > 0) {
        goToErrorResult(errorTableData.value[0].id);
    } else {
        message.warning('No data available for start.');
    }
};
// 筛选后的表格数据
const filteredValidColumns = computed(() => {
    if (!searchKeyword.value) return validColumns.value;
    return validColumns.value.filter(item => {
        // 在所有可搜索字段中查找关键词
        return Object.values(item).some(val =>
            val && typeof val === 'string' &&
            val.toLowerCase().includes(searchKeyword.value.toLowerCase())
        );
    });
});

// 筛选后的QA任务数据
const filteredQaTableData = computed(() => {
    if (!searchKeyword.value) return qaTableData.value;
    return qaTableData.value.filter(item => {
        // 在所有可搜索字段中查找关键词
        return Object.values(item).some(val =>
            val && typeof val === 'string' &&
            val.toLowerCase().includes(searchKeyword.value.toLowerCase())
        );
    });
});

// 筛选后的错误结果数据
const filteredErrorTableData = computed(() => {
    if (!searchKeyword.value) return errorTableData.value;
    return errorTableData.value.filter(item => {
        // 在所有可搜索字段中查找关键词
        return Object.values(item).some(val =>
            val && typeof val === 'string' &&
            val.toLowerCase().includes(searchKeyword.value.toLowerCase())
        );
    });
});

// QA任务弹窗相关
const addQaTaskVisible = ref(false);
const qaTaskForm = reactive({
    name: '',
    deadline: null,
});

const qaTaskRules = {
    name: [
        { required: true, message: 'Please input QA Task Name', trigger: 'blur' },
    ]
};

// 显示添加QA任务弹窗
const showAddQaTaskModal = () => {
    addQaTaskVisible.value = true;
};

// 关闭添加QA任务弹窗
const closeAddQaTaskModal = () => {
    qaTaskFormRef.value?.resetFields();
    addQaTaskVisible.value = false;
};

const handleEditQA = (id) => {
    router.push({
        path: '/model-validation/add-qa-task', 
        query: { id: id }
    });
};

// 保存QA任务
const saveQaTask = () => {
    qaTaskFormRef.value?.validate().then(() => {
        // 创建一个新对象来存储转换后的数据
        const formData = {
            ...qaTaskForm,
            deadline: dateToTimestamp(qaTaskForm.deadline)
        };

        router.push({
            path: '/model-validation/add-qa-task',
            query: { qaTaskForm: JSON.stringify(formData) }
        });
    }).catch(error => {
        console.error('表单验证失败', error);
    });
};

const handleAdvancedFilter = (currentTab) => {
    // 根据当前激活的标签页显示对应的筛选表单
    showFilterRef.value.showAdvancedFilter(currentTab);
};

// 跳转到任务详情页
const goToTaskDetail = debounceAsync(async(record, type) => {
    try {
        const taskId = parseInt(record.id);
        if (isNaN(taskId)) {
            console.error('taskID Invalid', record.id);
            return;
        }
    if (type === 'view') {
        router.push({
            name: 'TaskDetail',
            query: { taskId: record.id, type: type }
        });
    } else {
        const res = await validationTaskSkuApi(taskId);
        if (res.data) {
            const resp = await validationTaskStartApi(taskId, {});
            const saveobj = JSON.parse(localStorage.getItem('task_temp_save'));
            if (saveobj && saveobj.lineNumber) {
                Modal.confirm({
                    title: t('common.notification'),
                    content: `The last time the data was saved, the number of rows stored was ${saveobj.lineNumber}`,
                    okText: t('common.confirm'),
                    cancelText: t('common.cancel'),
                    icon: null,
                    closable: true,
                    onOk: () => {
                        router.push({
                            name: 'TaskDetail',
                            query: { taskId: record.id, type: type, sceneType: resp.data.project.validation_type }
                        });
                    }
                });
            } else {
                router.push({
                    name: 'TaskDetail',
                    query: { 
                        taskId: record.id, 
                        type: type, 
                        sceneType: resp.data.project.validation_type, 
                        requeue: record.requeued_and_validated,
                        oriVal: record.is_ori_val 
                    }
                });
            }
            
        } else {
            message.warning('No data available for start.');
        }
    }
        
    } catch (error) {
        console.error('启动任务失败:', error.message || '未知错误');
    }
}, 300);

// 跳转到QA任务详情页
const goToQATaskDetail = debounceAsync(async(record, type) => {
    try {
        const qaId = parseInt(record.id);
        if (isNaN(qaId)) {
            console.error('QA ID Invalid', record.idd);
            return;
        }
        if (type === 'start') {
            await qaTaskFlowApi(qaId, {});
            const res = await qaTaskFlowDetailApi(qaId, {});
            if (res.data) {
                router.push({
                    name: 'QATaskDetail',
                    query: { qaId: record.id, type: type }
                });
            } else {
                message.warning('QA Task does not exist.');
            }
        } else {
            router.push({
                name: 'QATaskDetail',
                query: { qaId: record.id, type: type }
            });
        }
    } catch (error) {
        console.log(error);
    }
}, 300);

// 跳转到错误结果详情页
const goToErrorResult = (record) => {
    router.push({
        name: 'ErrorResult',
        query: { errorId: record }
    });
};

// 更新错误结果计数（当一条记录处理完成后调用）
const updateErrorCount = () => {
    if (unfinishedErrorCount.value > 0) {
        unfinishedErrorCount.value -= 1;
    }
    // 如果需要，也可以重新获取最新数据
    // getResultColumns();
};

// 提供更新计数的方法给子组件使用
provide('updateErrorCount', updateErrorCount);
const activeTab = ref('validation'); // 默认选中validation标签页

// 处理标签页切换
const handleTabChange = async (key) => {
    activeTab.value = key;
    loading.value = true;

    try {
        // 根据当前选中的标签页加载对应的数据，并重置分页
        if (key === 'error') {
            errorPagination.resetPagination();
            await fetchResultColumns();
        } else if (key === 'qa') {
            qaPagination.resetPagination();
            await fetchQAListColumns();
        } else {
            validationPagination.resetPagination();
            await fetchInitValidColumns();
        }
    } catch (error) {
        console.error('加载数据失败:', error);
    } finally {
        loading.value = false;
    }
}

// 组件挂载时只加载默认标签页的数据
onMounted(async () => {
    // 只有在授权状态下才获取数据
    if (!isUnauthorized.value) {
        if (route.query.tab === 'qa') {
            activeTab.value = 'qa';
            await fetchQAListColumns(1, 10);
        } else if (route.query.tab === 'error') {
            activeTab.value = 'error';
            await fetchResultColumns(1, 10);
        } else {
            activeTab.value = 'validation';
            await fetchInitValidColumns(1, 10);
        }
        fetchUserList();
        fetchQaResultCountList();
    }
}); 
// 表格列定义
const columns = [
{ title: 'Task ID', dataIndex: 'id', width: 100 },
{ title: 'Country', dataIndex: 'country', width: 120 },
{ title: 'Client', dataIndex: 'client', width: 120 },
{ title: 'Model Type', dataIndex: 'model_type', width: 120 },
{ title: 'Created On', dataIndex: 'created_at', width: 180 },
{ title: 'Task Type', dataIndex: 'type', width: 100 },
{ title: 'Total Validator', dataIndex: 'validator_count', width: 140 },
{ title: 'Expected deadline', dataIndex: 'expect_deadline', width: 180 },
{ title: 'Scene', dataIndex: 'scene_total', width: 100 },
{ title: 'Total Progress', dataIndex: 'progress', width: 180 },
{ title: 'Status', dataIndex: 'status', width: 120 },
{ title: 'Action', dataIndex: 'action', fixed: 'right', width: 150 },
];

// QA任务表格列定义
const qaColumns = [
{ title: 'QA ID', dataIndex: 'id', width: 80 },
{ title: 'QA Task Name', dataIndex: 'name', width: 120 },
{ title: 'Expected deadline', dataIndex: 'deadline', width: 180 },
{ title: 'Created On', dataIndex: 'created_at', width: 180 },
{ title: 'SKU Errors', dataIndex: 'sku_errors', width: 120 },
{ title: 'Total Progress', dataIndex: 'progress', width: 180 },
{ title: 'Status', dataIndex: 'status', width: 120 },
{ title: 'Action', dataIndex: 'action', fixed: 'right', width: 200 },
];

// 错误结果表格列定义
const errorColumns = ref([
{ title: 'Country', dataIndex: 'country', width: 120 },
{ title: 'Client', dataIndex: 'client', width: 120 },
{ title: 'Model Type', dataIndex: 'model_type', width: 120 },
{ title: 'Task Id', dataIndex: 'validation_task_qa_id', width: 120 },
{ title: 'QA Name', dataIndex: 'val_task_qa_name', width: 120 },
{ title: 'Sceneid', dataIndex: 'scene_id', width: 120, ellipsis: true },
{ title: 'IR RESKU', dataIndex: 'ir_resku', width: 120, ellipsis: true },
{ title: 'Status', dataIndex: 'status', width: 120 },
]);

// 未完成任务计数
const unfinishedValidationCount = ref(0);
const unfinishedQACount = ref(0);
const unfinishedErrorCount = ref(100);

const validColumns = ref([])
const fetchInitValidColumns = async (page = 1, size = 10) => {
    try {
        const param = {
            status_list: [1, 2, 3],
            page_no: page - 1,
            page_size: size,
        };
        const res = await validationTaskListApi(param);
        if (res.code === '0000') {
            validColumns.value = res.data.data_list;
            validationPagination.total.value = res.data.count || 0;
            // 计算未完成的验证任务数量（状态不为3的任务）
            unfinishedValidationCount.value = res.data.data_list.filter(task => task.status !== 3).length;
            loading.value = false;
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
};
// QA任务表格数据
const qaTableData = ref();
const validators = ref([]);

const getUserInfo = (userId) => {
    const user = userData.value.find(u => u.id === userId) || {};
    return {
        key: user.id,
        user_id: user.id,
        department: user.department,
        user_name: user.username
    };
};
// QA任务表格数据
const fetchQAListColumns = async (page = 1, size = 10) => {
    try {
        const params = {
            page_no: page - 1,
            page_size: size,
        };
        const res = await qaTaskListApi(params);
        qaTableData.value = res.data.data_list;
        qaPagination.total.value = res.data.count || 0;

        // 计算未完成的QA任务数量（状态不为3的任务）
        unfinishedQACount.value = res.data.data_list.filter(task => task.status !== 3).length;
        // validators.value = res.data.qa_user_ids.map(item => getUserInfo(item));
        loading.value = false;
    } catch (error) {
        console.error('请求失败:', error);
    }
};
// 错误结果表格数据
const errorTableData = ref([]);
const fetchResultColumns = async (page = 1, size = 10) => {
    try {
        const params = {
            page_no: page - 1,
            page_size: size,
        };
        const res = await qaResultListApi(params);
        if (res.data.data_list) {
            errorTableData.value = res.data.data_list;
            errorPagination.total.value = res.data.count || 0;
            // 计算未完成的错误结果数量（状态不为2的记录）
            unfinishedErrorCount.value = res.data.data_list.filter(result => result.status !== 2).length;
        } else {
            errorTableData.value = [];
            errorPagination.total.value = 0;
            unfinishedErrorCount.value = 0;
        }
        loading.value = false;
    } catch (error) {
        console.error('请求失败:', error);
    }
};

// 处理搜索
const onSearch = (tabKey) => {
    // 搜索功能已通过计算属性实现，这里可以添加额外的搜索逻辑
};

// 处理高级筛选结果
const fetchFilterListData = async (filterParams) => {
    filterParamsStatus.value = filterParams;
    try {
        // 更新筛选状态
        const { activeTab, ...filters } = filterParams;
        activeFilters.value[activeTab] = filters;
        let response;
        
        if (filterParams.activeTab === 'validation') {
            const params = {
                task_id: filterParams.task_id || undefined,
                country: filterParams.country,
                client: filterParams.client,
                model_type: filterParams.model_type,
                create_on_start: dateToTimestamp(filterParams.createdOn[0]),
                create_on_end: dateToTimestamp(filterParams.createdOn[1]),
                task_type: filterParams.task_type,
                status: filterParams.status,
                page_no: validationPagination.page_no.value - 1,
                page_size: validationPagination.page_size.value,
            };
            response = await validationTaskListApi(params);
            validColumns.value = response.data.data_list;
            validationPagination.total.value = response.data.total || 0;
        } else if (filterParams.activeTab === 'qa') {
            const params = {
                qa_task_id: filterParams.qa_task_id || undefined,
                qa_task_name: filterParams.qa_task_name,
                create_on_start: dateToTimestamp(filterParams.createdOn[0]),
                create_on_end: dateToTimestamp(filterParams.createdOn[1]),
                status: filterParams.status,
                page_no: qaPagination.page_no.value - 1,
                page_size: qaPagination.page_size.value,
            };
            response = await qaTaskListApi(params);
            qaTableData.value = response.data.data_list;
            qaPagination.total.value = response.data.total || 0;
        } else if (filterParams.activeTab === 'error') {
            const params = {
                qa_task_id: filterParams.qa_task_id || undefined,
                country: filterParams.country,
                client: filterParams.client,
                model_type: filterParams.model_type,
                qa_task_name: filterParams.qa_task_name,
                page_no: errorPagination.page_no.value - 1,
                page_size: errorPagination.page_size.value,
            };
            const response = await qaResultListApi(params);
            errorTableData.value = response.data.data_list;
            errorPagination.total.value = response.data.total || 0;
        }
        if (response.code === '0000') {
            message.success(t('common.querySuccess'));
        }
    } catch (error) {
        console.error('筛选数据请求失败:', error);
    }
};

// QA Executor 弹窗相关
const selectedDepartment = ref('');

// 验证器数据
const leftValidators = ref([
    { id: '1', account: 'Account', phone: 'Phone', checked: false },
    { id: '2', account: 'Account', phone: 'Phone', checked: false },
    { id: '3', account: 'Account', phone: 'Phone', checked: false },
    { id: '4', account: 'Account', phone: 'Phone', checked: false },
    { id: '5', account: 'Account', phone: 'Phone', checked: false },
]);

const rightValidators = ref([
    { id: '6', account: 'Account', phone: 'Phone', checked: false },
    { id: '7', account: 'Account', phone: 'Phone', checked: false },
]);

// 搜索关键词
const leftSearchKeyword = ref('');
const rightSearchKeyword = ref('');

// 全选状态
const leftSelectAll = ref(false);
const rightSelectAll = ref(false);

// 过滤后的验证器列表
const filteredLeftValidators = computed(() => {
    if (!leftSearchKeyword.value) return leftValidators.value;
    return leftValidators.value.filter(item =>
        item.account.toLowerCase().includes(leftSearchKeyword.value.toLowerCase()) ||
        item.phone.toLowerCase().includes(leftSearchKeyword.value.toLowerCase())
    );
});

const filteredRightValidators = computed(() => {
    if (!rightSearchKeyword.value) return rightValidators.value;
    return rightValidators.value.filter(item =>
        item.account.toLowerCase().includes(rightSearchKeyword.value.toLowerCase()) ||
        item.phone.toLowerCase().includes(rightSearchKeyword.value.toLowerCase())
    );
});

// 显示QA Executor弹窗
const showQaExecutorModal = async () => {
    let userIds = [];
    try {
        const res = await getQAConfigApi();
        if (res.code === "0000") {
            userIds = res.data.data_list.map(item => item.user_id);
            showAndPersonnelRef.value.showAddPersonelDialog(userIds);
        }
    } catch (error) {
        console.error(error);
    }
};

const handlePersonnelSave = async({ targetKeys }) => {
    try {
        const targetKeysArray = Object.values(targetKeys);
        const res = await qaTaskConfigApi({
            user_ids: targetKeysArray,
        });
        if (res.code === "0000") {
            message.success(t('common.saveSuccess'));
        }
    } catch (error) {
    console.error(t('common.saveFailed'));
    }
};

// 重置弹窗数据
const resetModalData = () => {
    selectedDepartment.value = '';
    leftSelectAll.value = false;
    rightSelectAll.value = false;
    leftValidators.value.forEach(item => item.checked = false);
    rightValidators.value.forEach(item => item.checked = false);
    leftSearchKeyword.value = '';
    rightSearchKeyword.value = '';
};

// 左侧搜索
const onSearchLeft = (value) => {
    leftSearchKeyword.value = value;
};

// 右侧搜索
const onSearchRight = (value) => {
    rightSearchKeyword.value = value;
};

// 处理左侧全选
const handleLeftSelectAll = (e) => {
    const checked = e.target.checked;
    leftValidators.value.forEach(item => item.checked = checked);
};

// 处理右侧全选
const handleRightSelectAll = (e) => {
    const checked = e.target.checked;
    rightValidators.value.forEach(item => item.checked = checked);
};

// 处理单个项目选中
const handleItemCheck = (item, side) => {
    if (side === 'left') {
        leftSelectAll.value = leftValidators.value.every(v => v.checked);
    } else {
        rightSelectAll.value = rightValidators.value.every(v => v.checked);
    }
};

// 移动到右侧
const moveToRight = () => {
    const selectedItems = leftValidators.value.filter(item => item.checked);
    if (selectedItems.length === 0) return;

    // 移动选中项到右侧
    selectedItems.forEach(item => {
        item.checked = false;
        rightValidators.value.push(item);
    });

    // 从左侧移除
    leftValidators.value = leftValidators.value.filter(item => !selectedItems.includes(item));

    // 更新全选状态
    leftSelectAll.value = false;
};

// 移动到左侧
const moveToLeft = () => {
    const selectedItems = rightValidators.value.filter(item => item.checked);
    if (selectedItems.length === 0) return;

    // 移动选中项到左侧
    selectedItems.forEach(item => {
        item.checked = false;
        leftValidators.value.push(item);
    });

    // 从右侧移除
    rightValidators.value = rightValidators.value.filter(item => !selectedItems.includes(item));

    // 更新全选状态
    rightSelectAll.value = false;
};

// 获取状态标签颜色
const getStatusColor = (status) => {
    // 0:未开始,1:已完成
    const colorMap = {
        0: '#1677FF',
        1: '#52C41A',
    };
    return colorMap[status] || 'default';
};

// 获取状态标签文本
const getStatusText = (status) => {
     // 0:未开始,1:已完成
    const textMap = {
        0: 'Not Started',
        1: 'Completed',
    }
    return textMap[status] || 'Unknown';
}
const getStatusTextColor = (status) => {
    const colorMap = {
        0: '#00ABEB',
        1: '#1677FF', 
        2: '#FAAD14', 
        3: '#52C41A',
    };
    return colorMap[status] || '#000000';
};

// 获取进度条百分比
const getProgressPercent = (done, total) => {
    if (!total || total === 0) return 0;
    return Math.floor((done / total) * 100);
};

// 获取进度条颜色
const getProgressColor = (done, total) => {
    return '#1677FF';
};

// 格式化进度文本，整数时不显示小数
const formatProgressText = (done, total) => {
    // 如果done是整数或者小数部分为0，则显示为整数
    const formattedDone = (done % 1 === 0 || done.toFixed(1).endsWith('.0')) ? Math.floor(done).toString() : done.toFixed(1);
    return `${formattedDone}/${total}`;
};

// 显示删除确认弹窗
const showDeleteModal = (record) => {
    deleteTaskId.value = record.id;
    deleteModalVisible.value = true;
};

// 确认删除
const confirmDelete = debounceAsync(async () => {
    try {
        await qaTaskDeleteApi(deleteTaskId.value);
        // 从列表中移除已删除的任务
        qaTableData.value = qaTableData.value.filter(item => item.id !== deleteTaskId.value);
        message.success('Delete successfully');
    } catch (error) {
        message.error('Delete failed');
    } finally {
        deleteModalVisible.value = false;
        deleteTaskId.value = null;
    }
}, 300);

const deleteTask = debounceAsync(async (id) => {
    await qaTaskDeleteApi(id);
    // 从列表中移除已删除的任务
    qaTableData.value = qaTableData.value.filter(item => item.id !== id);
}, 300);

const goToPublishTask = async (record) => {
    // 验证QA数据和QA执行人
    const hasQAData = record.sku_total && parseInt(record.sku_total) > 0;
    const hasQAExecutor = record.qa_user_count && record.qa_user_count > 0;
    
    if (!hasQAData) {
        Modal.warning({
            title: 'Notification',
            content: 'The QA task has no executable data. Please check before publishing.',
            okText: t('common.confirm'),
            icon: null,
            closable: true,
        });
        return;
    }
    
    if (!hasQAExecutor) {
        Modal.warning({
            title: 'Notification',
            content: 'The QA task has no assigned executor, please check before publishing',
            okText: t('common.confirm'),
            icon: null,
            closable: true,
        });
        return;
    }
    
    Modal.confirm({
        title: t('common.notification'),
        content: 'Whether to publish QA tasks',
        okText: t('common.confirm'),
        cancelText: t('common.cancel'),
        icon: null,
        closable: true,
        onOk: async () => {
            try {
                const res = await qaTaskPublishApi(record.id);
                if (res.code === '0000') {
                    await fetchQAListColumns();
                    message.success(t('common.publishSuccess'));
                } else {
                    message.error(t('common.publishFailed'));
                }
            } catch (error) {
                console.error(t('common.publishFailed'));
            }
        },
    });
};

const fetchQaResultCountList = async () => {
    try {
        const res = await qaResultCountListApi();
        errorCount.value = res.data?.count;
    } catch (error) {
        console.error(error);
    }
};

watch(() => route.query.tab, (tab) => {
    if (tab) activeTab.value = tab;
}, { immediate: true });
</script>

<style scoped>
.ant-tabs-content {
    background-color: #fff;
}

:deep(.ant-tabs-nav) {
    padding-left: 16px;
    height: 56px;
    background-color: #fff;
    /* background: #fafafa; */
}

:deep(.ant-tabs-content-holder) {
    height: 86vh;
    margin: 16px;
    border-radius: 8px;
    padding: 16px;
    background-color: #fff;
}

:deep(.ant-tabs-tabpane) {
    overflow-y: auto;
    max-height: calc(100vh - 120px);
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

:deep(.ant-tabs-tabpane)::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.main-container {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.main-container::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.custom-tabs :deep(.ant-tabs-nav\:\:before) {
    border-bottom: none;
    padding-left: 10px;
    background-color: #fff;
}

.custom-tabs :deep(.ant-tabs-tab) {
    padding: 8px 0;
    font-size: 14px;
}

.custom-tabs :deep(.ant-tabs-tab + .ant-tabs-tab) {
    margin-left: 32px;
}

.custom-tabs :deep(.ant-tabs-ink-bar) {
    height: 2px;
}

.custom-tabs .ant-tabs-tab {
    position: relative;
    padding-right: 24px;
}

.custom-tabs .ant-badge {
    position: absolute;
    top: 8px;
    right: 0;
}

.custom-tabs .ant-badge-count {
    font-size: 12px;
    height: 20px;
    line-height: 20px;
    padding: 0 6px;
    border-radius: 10px;
    min-width: 20px;
    box-shadow: 0 0 0 1px #fff;
}

.search-input :deep(.ant-input) {
    border-radius: 4px;
}

.filter-button {
    border-radius: 4px;
}

/* QA Executor 弹窗样式 */
.qa-executor-modal :deep(.ant-select-selector) {
    border-radius: 4px;
}

.qa-executor-modal :deep(.ant-input-search) {
    margin-bottom: 8px;
}

.qa-executor-modal .validator-list {
    max-height: 200px;
    overflow-y: auto;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
    .search-filter-container {
        flex-direction: column;
        align-items: flex-start;
    }

    .search-input {
        width: 100% !important;
        margin-bottom: 12px;
    }

    .qa-executor-modal .flex {
        flex-direction: column;
    }

    .qa-executor-modal .w-12 {
        width: 100%;
        padding: 0;
        margin-bottom: 16px;
    }

    .qa-executor-modal .flex-col {
        flex-direction: row;
        width: 100%;
        justify-content: center;
        margin: 8px 0;
    }
}

.col-100 {
    color: #1677FF;
}

.tabs-container {
    height: 100%;
    overflow: hidden;
}

/* 自定义表格行高48px */
:deep(.custom-table-48px .ant-table-tbody > tr > td) {
    height: 48px;
    padding: 8px 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

:deep(.custom-table-48px .ant-table-thead > tr > th) {
    height: 48px;
    padding: 8px 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 确保表格内容不换行 */
:deep(.custom-table-48px .ant-table-cell) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 进度条容器样式调整 */
:deep(.custom-table-48px .ant-progress) {
    margin: 0;
}

/* 按钮组样式调整 */
:deep(.custom-table-48px .flex) {
    white-space: nowrap;
}

/* 标签样式调整 */
:deep(.custom-table-48px .ant-tag) {
    margin: 0;
    white-space: nowrap;
}
/* 移除阻止表格横向滚动的样式 */
:deep(.ant-table-content) {
    overflow-x: auto !important;
    overflow-y: hidden !important;
}
</style>