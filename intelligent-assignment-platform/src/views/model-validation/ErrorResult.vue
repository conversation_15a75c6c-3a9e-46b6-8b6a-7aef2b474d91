<template>
    <div class="w-100% h-full">
        <div class="p-4 overflow-x-auto overflow-y-hidden rounded-[8px]">
            <a-spin :spinning="loading" tip="加载中...">
            <div class="flex flex-row gap-4 h-full min-w-[1128px]">
                <!-- 左侧图片区域 -->
                <div class="w-[448px] min-w-[360px] flex flex-col h-[89vh]">
                    <div class="border p-3 bg-white rounded-lg flex-grow mb-4 overflow-auto flex flex-col">
                        <div class="flex w-full justify-between mb-2">
                            <div class="text-[14px] font-medium">{{ t('modelValidation.image') }}</div>
                            <div class="flex gap-2">
                                <a-button type="link" shape="circle" size="small" @click="openWebAppVersion(errorInfo.report_url)">
                                    {{ t('modelValidation.webAppVersion')}}
                                </a-button>
                            </div>
                        </div>
                        <div class="flex-1 bg-gray-50 rounded overflow-hidden">
                            <HotFigure class="w-full h-full" :canvasW="imageBoxW" :canvasH="imageBoxH"
                                @imageFinshed="imageFinshed" ref="planeFigure" :showAllDots="showAll" :initialData="hotFigureData"></HotFigure>
                        </div>
                        <div class="mt-6 flex justify-between flex-shrink-0">
                            <div class="flex flex-row">
                                <a-button type="text" shape="circle" size="small" class="text-[#FF4D4F]" @click="decreaseDotSize">
                                    <template #icon>
                                        <img src="@/assets/images/decMin.svg" :alt="t('modelValidation.zoomOut')" width="18" height="18" />
                                    </template>
                                </a-button>
                                <div class="text-[12px] font-400 mt-2">dot</div>
                                <a-button type="text" shape="circle" size="small" class="text-[#1677FF]" @click="increaseDotSize">
                                    <template #icon>
                                        <img src="@/assets/images/addPlus.svg" :alt="t('modelValidation.zoomIn')" width="18" height="18" />
                                    </template>
                                </a-button>
                            </div>
                            <div class="flex gap-2">
                                <a-button type="text" shape="circle" size="small" @click="decreaseZoom"
                                    :disabled="zoomScale <= minZoom">
                                    <template #icon>
                                        <img src="@/assets/images/decrease.svg" :alt="t('modelValidation.zoomOut')"
                                            width="18" height="18" />
                                    </template>
                                </a-button>
                                <a-button type="text" shape="circle" size="small" @click="increaseZoom"
                                    :disabled="zoomScale >= maxZoom">
                                    <template #icon>
                                        <img src="@/assets/images/increase.svg" :alt="t('modelValidation.zoomIn')"
                                            width="18" height="18" />
                                    </template>
                                </a-button>
                            </div>
                        </div>
                    </div>
                    <!-- 产品区域 -->
                    <div class="bg-white rounded-lg">
                        <div class="rounded p-4">
                            <div class="text-[14px] font-medium mb-2">{{ t('modelValidation.product') }}</div>
                            <div class="product-image-container w-[100px] h-[128px] border border-solid border-[1px] border-[#D9D9D9]"
                                 @mousedown="startDrag" @mousemove="handleDrag" @mouseup="stopDrag" @mouseleave="stopDrag">
                                <div class="product-image-wrapper" :style="{ transform: `translate(${dragOffset.x}px, ${dragOffset.y}px)` }">
                                    <img :src="productImageUrl" class="max-w-full w-[82px] h-[128px]" :style="productImageStyle">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧产品信息区域 -->
                <div class="flex-1 h-full rounded-md shadow-sm min-w-[680px] w-[680px]"> 
                    <div class="bg-white w-full rounded-lg p-4 relative h-[89vh]">
                        <!-- 基本信息 -->
                        <div class="mb-6">
                            <div class="text-base font-medium mb-2 flex items-center">
                                <span class="inline-block rounded-[2px] bg-black w-[4px] h-[18px]"></span>
                                <span class="ml-2 mt-1 text-[16px]">{{ $t('modelValidation.basicInfo') }}</span>
                            </div>
                            <div class="grid grid-cols-2 gap-4 p-10">
                                <div class="flex">
                                    <div class="w-32 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.modelType') }}:</div>
                                    <div class="ml-4 text-[14px]">{{ errorInfo.modelType }}</div>
                                </div>
                                <div class="flex">
                                    <div class="w-32 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.country') }}:</div>
                                    <div class="ml-4 text-[14px]">{{ errorInfo.country }}</div>
                                </div>
                                <div class="flex">
                                    <div class="w-32 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.client') }}:</div>
                                    <div class="ml-4 text-[14px]">{{ errorInfo.client }}</div>
                                </div>
                                <div class="flex">
                                    <div class="w-32 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.sceneId') }}:</div>
                                    <div class="ml-4 text-[14px] w-48">
                                        <a-tooltip :title="errorInfo.sceneId" placement="topLeft">
                                            <div class="truncate">{{ errorInfo.sceneId }}</div>
                                        </a-tooltip>
                                    </div>
                                </div>
                                <div class="flex">
                                    <div class="w-32 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.validator') }}:</div>
                                    <div class="ml-4 text-[14px]">{{ getUserName(errorInfo.validator) }}</div>
                                </div>
                                <!-- <div class="flex">
                                    <div class="w-32 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.lastModifiedOn') }}:</div>
                                    <div class="ml-4 text-[14px]">{{ errorInfo.lastModified }}</div>
                                </div> -->
                                <div class="flex">
                                    <div class="w-32 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.projectId') }}:</div>
                                    <div class="ml-4 text-[14px]">{{ errorInfo.projectId }}</div>
                                </div>
                                <div class="flex">
                                    <div class="w-32 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.taskId') }}:</div>
                                    <div class="ml-4 text-[14px]">{{ errorInfo.taskId }}</div>
                                </div>
                                <div class="flex">
                                    <div class="w-32 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.qaTaskName') }}:</div>
                                    <div class="ml-4 text-[14px]">{{ errorInfo.qaTaskName }}</div>
                                </div>
                                <!-- <div class="flex">
                                    <div class="w-32 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.issueType') }}:</div>
                                    <div class="ml-4 text-[14px]">{{ errorInfo.issueType }}</div>
                                </div> -->
                            </div>
                        </div>

                        <!-- SKU信息 -->
                        <div class="mb-6">
                            <div class="text-base font-medium mb-2 flex items-center">
                                <span class="inline-block rounded-[2px] bg-black w-[4px] h-[18px]"></span>
                                <span class="ml-2 mt-1 text-[16px]">{{ $t('modelValidation.skuInfo') }}</span>
                            </div>
                            <div class="grid grid-cols-1 gap-3 p-6">
                                <div class="flex mb-4">
                                    <div class="w-48 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.comments') }}:</div>
                                    <div class="ml-4 text-[14px]">{{ errorInfo.comments }}</div>
                                </div>
                                <div class="flex mb-4">
                                    <div class="w-48 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.irReskuDescription') }}:</div>
                                    <div class="ml-4 text-[14px]">{{ errorInfo.irReskuDescription }}</div>
                                </div>
                                <div class="flex mb-2">
                                    <div class="w-48 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.irProductName') }}:</div>
                                    <div class="ml-4 text-[14px]">{{ errorInfo.irProductName }}</div>
                                </div>
                                <div class="my-2" style="border: 1px solid rgb(240, 240, 240);"></div>
                                <div class="flex mb-4">
                                    <div class="w-48 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.gtReskuDescription') }}:</div>
                                    <div class="ml-4 mt-[-4px] w-120">
                                        <a-select
                                            v-model:value="errorInfo.gtReskuDescription"
                                            class="w-full"
                                            mode="combobox"
                                            :filter-option="filterGtReskuOptions"
                                            show-search
                                            :options="gtReskuOptions"
                                            @change="handleGtReskuChange"
                                        />
                                    </div>
                                </div>
                                <div class="flex mb-4">
                                    <div class="w-48 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.gtProductName') }}:</div>
                                    <div class="ml-4 text-[14px]">{{ errorInfo.gtProductName }}</div>
                                    <!-- <div class="ml-4 mt-[-4px] w-120">
                                        <a-select
                                            v-model:value="errorInfo.gtProductName"
                                            :placeholder="$t('modelValidation.selectGtProductName')"
                                            class="w-full"
                                            mode="combobox"
                                            :filter-option="filterGtProductOptions"
                                            show-search
                                            :options="gtProductOptions"
                                            @change="handleGtProductChange"
                                        />
                                    </div> -->
                                </div>
                            </div>
                        </div>
                        <!-- 底部按钮 -->
                        <div class="flex flex-wrap justify-end gap-2 mt-4 absolute bottom-4 right-4">
                            <a-button @click="saveAndFinish">{{ t('modelValidation.saveAndFinish') }}</a-button>
                            <a-button type="primary" @click="saveAndNext">{{ t('modelValidation.saveAndNext') }}</a-button>
                        </div>
                    </div>
                </div>
            </div>
            </a-spin>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, computed, inject } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { message } from 'ant-design-vue';
import { qaReviewResultApi, updateQaReviewResultApi, skuMapApi } from '@/api/task';
import HotFigure from '@/components/HotFigure.vue';
import { useDateUtils } from '@/composable/useDateUtils';
// import { useAutoUserActionTracking } from '@/composable/useUserActionTracking';
import { useUserDataStore } from '@/store/userDataStore';
import { storeToRefs } from 'pinia';

const store = useUserDataStore();
const { userData } = storeToRefs(store);

const { formatDate } = useDateUtils();
const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const imageBoxW = ref(400);
const imageBoxH = ref(300);
const showAll = ref(true);
// 图片加载完成事件
const imageFinshed = (data) => {
    // console.log('图片加载完成', data);
    // 这里可以处理图片加载完成后的逻辑
};
// 缩放功能
const planeFigure = ref(null);

const increaseDotSize = () => {
    if (planeFigure.value) {
        planeFigure.value.increaseDotSize();
    }
};

const decreaseDotSize = () => {
    if (planeFigure.value) {
        planeFigure.value.decreaseDotSize();
    }
};

const increaseZoom = () => {
    if (planeFigure.value) {
        planeFigure.value.increaseZoom();
    }
};

const decreaseZoom = () => {
    if (planeFigure.value) {
        planeFigure.value.decreaseZoom();
    }
};
// 注入父组件提供的更新计数方法
const updateErrorCount = inject('updateErrorCount');

// 获取错误ID
const errorId = computed(() => Number(route.query.errorId) || 0);

// 产品图片URL
const productImageUrl = ref('');

// HotFigure组件数据
const hotFigureData = ref({});

// 错误信息
const errorInfo = reactive({
    region: '',
    country: '',
    client: '',
    sceneId: '',
    validator: '',
    lastModified: '',
    projectId: '',
    taskId: '',
    issueType: '',
    qaTaskName: '',
    comments: '',
    modelType: '',
    irReskuDescription: '',
    irProductName: '',
    gtReskuDescription: '',
    gtProductName: '',
    selectedOutSkuId: '' // 添加字段来保存选中的out_sku_id
});

// 加载状态
const loading = ref(false);

// GT RESKU Description 选项
const gtReskuOptions = ref([]);

// GT Product Name 选项
const gtProductOptions = ref([]);

// SKU映射数据，用于存储sku_name和product_name的对应关系
const skuMappingData = ref([]);

// 处理 GT RESKU Description 变化
const handleGtReskuChange = (value) => {
    console.log('GT RESKU Description changed:', value);
    
    // 根据选中的sku_name找到对应的product_name和out_sku_id
    const selectedItem = skuMappingData.value.find(item => item.sku_name === value);
    if (selectedItem) {
        errorInfo.gtProductName = selectedItem.product_name;
        errorInfo.selectedOutSkuId = selectedItem.out_sku_id; // 保存选中的out_sku_id
        console.log('自动设置product_name:', selectedItem.product_name);
        console.log('自动设置out_sku_id:', selectedItem.out_sku_id);
    } else {
        // 如果没有找到对应的product_name，清空相关字段
        errorInfo.gtProductName = '';
        errorInfo.selectedOutSkuId = '';
    }
};

// 处理 GT Product Name 变化
const handleGtProductChange = (value) => {
    console.log('GT Product Name changed:', value);
    
    // 根据选中的product_name找到对应的out_sku_id
    const selectedItem = skuMappingData.value.find(item => item.product_name === value);
    if (selectedItem) {
        errorInfo.selectedOutSkuId = selectedItem.out_sku_id; // 同步更新out_sku_id
        console.log('同步更新out_sku_id:', selectedItem.out_sku_id);
    } else {
        // 如果没有找到对应的out_sku_id，清空该字段
        errorInfo.selectedOutSkuId = '';
        console.log('未找到对应的out_sku_id，已清空');
    }
};

// GT RESKU Description 模糊搜索过滤函数
const filterGtReskuOptions = (input, option) => {
    if (!input) return true;
    const inputLower = input.toLowerCase();
    const labelLower = (option.label || '').toLowerCase();
    const valueLower = (option.value || '').toLowerCase();
    
    // 支持标签和值的模糊匹配
    return labelLower.includes(inputLower) || valueLower.includes(inputLower);
};

// 图片缩放相关变量
const zoomScale = ref(1.0);
const minZoom = 0.6;
const maxZoom = 3.0;

// 完成错误记录
const completeErrorRecord = (recordId) => {
    // 这里添加处理完成记录的逻辑，例如调用API更新记录状态
    
    // 更新父组件中的错误计数
    if (updateErrorCount) {
        // updateErrorCount();
    }
};

// 准备保存数据
const prepareDataForSave = () => {
    // 验证下拉选择框的值
    if (!errorInfo.gtReskuDescription || !errorInfo.gtProductName) {
        message.warning(t('modelValidation.pleaseSelectAllFields'));
        return null;
    }
    
    // 构建保存数据结构
    return {
        id: errorId.value,
        gt_result: errorInfo.gtReskuDescription,
        gt_product_name: errorInfo.gtProductName,
        out_sku_id: errorInfo.selectedOutSkuId, // 使用选中的out_sku_id，如果没有则使用sceneId作为备用
    };
};

// 保存数据到服务器
const saveData = async () => {
    const saveData = prepareDataForSave();
    console.log('准备保存的数据:', saveData);
    if (!saveData) return false;
    
    loading.value = true;
    try {
        // 调用API保存数据
        await updateQaReviewResult(errorId.value, saveData);
        console.log('保存的数据:', saveData);
        completeErrorRecord(errorId.value);
        message.success(t('modelValidation.saveSuccess'));
        return true;
    } catch (error) {
        router.push({
            path: '/model-validation',
            query: { tab: 'error' }
        });
        message.info(t('modelValidation.noMoreErrors') || 'There are no error results to be executed.');
        return false;
    } finally {
        loading.value = false;
    }
};

const updateQaReviewResult = async (errorId,data) => {
    // 这里可以添加调用API的逻辑
    await updateQaReviewResultApi(errorId,data);
}

// 保存并完成
const saveAndFinish = async () => {
    if (await saveData()) {
        router.push({
            path: '/model-validation',
            query: { tab: 'error' }
        });
    }
};

// 保存并下一个
const saveAndNext = async () => {
    if (await saveData()) {
        const res = await fetchErrorDetail();
        if (res) {
            console.log('ID:', res);
        }
    }
};

// 打开Web App版本链接
const openWebAppVersion = (url) => {
    window.open(url, '_blank');
};

// 获取错误详情数据
const fetchErrorDetail = async () => {
    if (!errorId.value) {
        message.error(t('modelValidation.invalidErrorId'));
        return;
    }
    
    loading.value = true;
    try {
        const res = await qaReviewResultApi(errorId.value);
        const _res = {
            "data": {
                "id": 6,
                "created_at": 1749203490,
                "updated_at": 1749203490,
                "validation_task_qa_id": 5,
                "validation_task_scene_sku_result_id": 2793,
                "validation_task_qa_detail_id": 6,
                "issue_type": 1,
                "comment": "test",
                "status": 0,
                "sku_result": null,
                "sku_detail": {
                    "product_img_urls": "",
                    "att_info": {},
                    "multipack": "0",
                    "validation_scene_id": 2,
                    "validation_task_id": 8,
                    "validation_scene_sku_id": 231,
                    "group_id": 1,
                    "block_id": 4,
                    "shelf": 7,
                    "column": 1,
                    "stack": 1,
                    "ir_result": "Red Bull The White Edition Coconut Berry Sleek Can 250 ml sw",
                    "gt_result": "Frutko Apple Juice Tetra 1000 ml ad",
                    "out_sku_id": "240379",
                    "orientation_gt": "-1",
                    "orientation_ir": "-1",
                    "ir_product_name": "Red Bull The White Edition Coconut Berry Sleek Can 250 ml sw",
                    "gt_product_name": "Red Bull The White Edition Coconut Berry Sleek Can 250 ml sw",
                    "report": "",
                    "id": 2793,
                    "add_from": 0,
                    "is_changed": true,
                    "created_at": 1749201991,
                    "updated_at": 1749201991
                },
                "project": {
                    "created_at": 1748425255,
                    "updated_at": 1748425255,
                    "country": "Bolivia",
                    "model_type": "EmbPrivMx",
                    "client_reference_id": "1",
                    "validation_type": "SKU/POP",
                    "id": 1,
                    "region": "LATAM",
                    "client": "Baking",
                    "sub_client": "Baking",
                    "creator_id": 0,
                    "out_project_id": "27"
                },
                "validation_task": {
                    "status": 3,
                    "creator_id": 8,
                    "is_ori_val": 0,
                    "start_time": 1749201908,
                    "name": "",
                    "type": "UAT",
                    "expect_deadline": 1749201997,
                    "sub_task_name": "",
                    "extra_info": {
                        "remark": "",
                        "no_scenes": "",
                        "attachments": "",
                        "mds_available": null,
                        "priority_level": "",
                        "image_received_on": null,
                        "specific_scene_ids": null,
                        "additional_document": null,
                        "requeued_and_validated": null
                    },
                    "project_id": 1,
                    "id": 8,
                    "created_at": 1749201806,
                    "updated_at": 1749201997,
                    "finish_time": 1749201997,
                    "task_users": null
                },
                "val_scene": {
                    "out_project_id": "27",
                    "name": "",
                    "type": "1",
                    "sub_scene_type": "",
                    "img_received_on": 1749117874,
                    "img_url": "https://cchproduction.blob.core.windows.net/ir-image/CCH/Switzerland/Field/Cooler/Customer/2025/06/05/80308ef3543a48aa8d1efd3eaadf1108_1_test.jpg?se=2025-10-04T06%3A07%3A37Z&sp=r&sv=2025-05-05&sr=b&sig=HTyQ73Dl7WH7JRgb/UoJtzUccP8fzKVoQDaGNHplu98%3D",
                    "total_imgs": 1,
                    "total_facings": 271,
                    "out_scene_id": "80308ef3543a48aa8d1efd3eaadf1108_1_test",
                    "portal_data": {},
                    "validation_status": 1,
                    "validator_id": 9,
                    "detail_img_urls": [
                        "https://cchproduction.blob.core.windows.net/ir-image/CCH/Switzerland/Field/Cooler/Customer/2025/06/05/80308ef3543a48aa8d1efd3eaadf1108_1_qRWtjzBDsUSSDu0x1cTCbg_1.jpg?se=2025-10-04T06%3A07%3A37Z&sp=r&sv=2025-05-05&sr=b&sig=jcfqveuVW/3SPJu5tS4XQ1YqS28XheA4LCoTtrVGgUg%3D"
                    ],
                    "id": 2,
                    "created_at": 1749190057,
                    "updated_at": 1749202812
                },
                "val_task_qa": {
                    "name": "wrm",
                    "deadline": 1749139200,
                    "status": 2,
                    "creator_id": 9,
                    "id": 5,
                    "created_at": 1749203427,
                    "updated_at": 1749203477
                }
            },
            "code": "string",
            "message": "string"
        }
        if (res && res.data) {
            const { data } = res;
            // 映射基本信息
            errorInfo.region = data.project?.region || '';
            errorInfo.country = data.project?.country || '';
            errorInfo.client = data.project?.client || '';
            errorInfo.sceneId = data.val_scene.out_scene_id || '';
            errorInfo.validator = data.val_scene.validator_id; // 从数据中获取验证者信息
            errorInfo.lastModified = formatDate(data.updated_at);
            errorInfo.projectId = data.project?.id || '';
            errorInfo.taskId = data.validation_task?.id || '';
            errorInfo.issueType = data.project?.validation_type; // 从数据中获取问题类型
            errorInfo.qaTaskName = data.val_task_qa.name; // 从数据中获取QA名称
            // 映射SKU信息
            errorInfo.comments = data.comment || '';
            errorInfo.irReskuDescription = data.sku_detail?.ir_result || '';
            errorInfo.irProductName = data.sku_detail?.ir_product_name || '';
            errorInfo.gtReskuDescription = data.sku_detail?.gt_result || '';
            errorInfo.gtProductName = data.sku_detail?.gt_product_name || '';
            errorInfo.selectedOutSkuId = data.sku_detail?.out_sku_id || '';
            errorInfo.report_url = data.val_scene.report_url || '';
            errorInfo.modelType = data.project?.model_type || '';
            // 设置产品图片
            setProductImage(data.sku_detail, data.val_scene.img_url);
            productImageUrl.value = data.val_scene.img_url;
            await getGtOptions(data.project?.out_project_id);
        } else {
            message.warning(t('modelValidation.noDataFound'));
        }
    } catch (error) {
        console.error('获取错误详情失败:', error);
    } finally {
        loading.value = false;
    }
};
const productImageStyle = ref({});
// 拖拽相关变量
const dragOffset = reactive({ x: 0, y: 0 });
const isDragging = ref(false);
const dragStart = reactive({ x: 0, y: 0 });

// 开始拖拽
const startDrag = (event) => {
    isDragging.value = true;
    dragStart.x = event.clientX - dragOffset.x;
    dragStart.y = event.clientY - dragOffset.y;
};

// 处理拖拽
const handleDrag = (event) => {
    if (isDragging.value) {
        dragOffset.x = event.clientX - dragStart.x;
        dragOffset.y = event.clientY - dragStart.y;
    }
};

// 停止拖拽
const stopDrag = () => {
    isDragging.value = false;
};
// 设置产品图片
const setProductImage = (sku_detail, imageUrl) => {
    // 根据单个sku_detail创建边界框数据
    const boundingBoxes = [];
    
    if (sku_detail && sku_detail.att_info) {
        const { att_info } = sku_detail;
        
        // 处理productDot数据（红点位置）
        if (att_info.productDot) {
            const dot = att_info.productDot;
            boundingBoxes.push({
                sku_name: sku_detail.ir_result || sku_detail.gt_result || 'Unknown SKU',
                out_sku_id: sku_detail.out_sku_id || sku_detail.ir_out_sku_id,
                // 使用productDot的相对坐标作为红点位置
                tl: [dot.x, dot.y], // 左上角
                tr: [dot.x, dot.y], // 右上角
                bl: [dot.x, dot.y], // 左下角
                br: [dot.x, dot.y], // 右下角
                // 保存原始数据以备后用
                originalData: sku_detail
            });
        }
        
        // 处理productBoundingBox数据（边界框）
        if (att_info.productBoundingBox) {
            const bbox = att_info.productBoundingBox;
            productImageStyle.value = {
                width: '100%',
                height: '100%',
                objectFit: 'none',
                objectPosition: `-${bbox.x}px -${bbox.y}px`
            };
            // 将像素坐标转换为相对坐标（需要知道图片尺寸）
            // 这里假设图片尺寸，实际应该从图片加载后获取
            // 或者从接口返回的数据中获取图片尺寸信息
            boundingBoxes.push({
                sku_name: sku_detail.ir_result || sku_detail.gt_result || 'Bounding Box',
                out_sku_id: sku_detail.out_sku_id || sku_detail.ir_out_sku_id,
                // 注意：这里的坐标是像素坐标，需要转换为相对坐标
                // 暂时直接使用，后续需要根据实际图片尺寸进行转换
                tl: [bbox.x, bbox.y],
                tr: [bbox.x + bbox.w, bbox.y],
                bl: [bbox.x, bbox.y + bbox.h],
                br: [bbox.x + bbox.w, bbox.y + bbox.h],
                originalData: sku_detail,
                isBoundingBox: true // 标记为边界框
            });
        }
    }
    
    // 设置HotFigure组件的图片数据
    hotFigureData.value = {
        stitched_image: imageUrl,
        bounding_box_list: boundingBoxes
    };
};
const getGtOptions = async (id) => {
    try {
        const res = await skuMapApi(id);
        const data = res.data;
        // 保存完整的映射数据
        skuMappingData.value = data.data_list || [];
        
        gtReskuOptions.value = (data.data_list || []).map(item => ({
            value: item.sku_name,
            label: item.sku_name
        }));
        gtProductOptions.value = data.data_list.map(item => ({
            value: item.product_name,
            label: item.product_name
        }));
    } catch (error) {
        console.error('获取GT选项失败:', error);
    }
}
// 根据用户ID获取用户名称
const getUserName = (id) => {
    const user = userData.value.find(user => user.id === id);
    return user ? user.name : '';
};
// 用户行为追踪相关
// 用户行为追踪
// const { userActionCount } = useAutoUserActionTracking({
//     taskId: () => errorInfo.taskId,
//     sceneId: () => errorInfo.sceneId,
//     pageName: 'ErrorResult',
//     reportInterval: 30000
// });
// 初始化
onMounted(async() => {
    if (!store.hasData) { // 仅在数据未加载时调用API
        await store.fetchUserData();
    }
    // 获取错误详情
    await fetchErrorDetail();
});

onBeforeUnmount(() => {
    // 组件销毁时的清理工作
});
</script>

<style scoped>
.product-image-container {
    overflow: hidden;
    cursor: grab;
}

.product-image-container:active {
    cursor: grabbing;
}

.product-image-wrapper {
    transition: transform 0.1s ease-out;
}

.image-container img {
    transform-origin: center;
}
</style>