<template>
    <div class="w-full rounded-[8px] overflow-hidden">
        <div class="p-4">
            <div class="flex flex-row gap-4 h-[92vh]">
                <!-- 左侧图片区域 -->
                <div class="flex flex-col w-[468px]">
                    <div class="border p-3 bg-white rounded-lg flex-1 flex flex-col w-[448px]">
                        <div class="flex w-full justify-between mb-2">
                            <div class="text-[14px] font-medium">{{ t('modelValidation.image') }}</div>
                            <div class="flex gap-2">
                                <a-button type="link" shape="circle" size="small" @click="openWebAppVersion">
                                    {{t('modelValidation.webAppVersion')}}
                                </a-button>
                            </div>
                        </div>
                        <div class="flex-1 min-h-[300px] bg-gray-50 rounded overflow-hidden">
                            <HotFigure :canvasW="imageBoxW" :canvasH="imageBoxH" @imageFinshed="imageFinshed" ref="planeFigure" 
                                :showAllDots="showAll" :initialData="hotFigureData" class="w-full h-full"></HotFigure>
                        </div>
                        
                        <div class="mt-3 flex justify-between">
                            <div class="flex flex-row">
                                <a-button type="text" shape="circle" size="small" class="text-[#FF4D4F]" @click="decreaseDotSize">
                                    <template #icon>
                                        <img src="@/assets/images/decMin.svg" :alt="t('modelValidation.zoomOut')" width="18" height="18" />
                                    </template>
                                </a-button>
                                <div class="text-[12px] font-400 mt-2">dot</div>
                                <a-button type="text" shape="circle" size="small" class="text-[#1677FF]" @click="increaseDotSize">
                                    <template #icon>
                                        <img src="@/assets/images/addPlus.svg" :alt="t('modelValidation.zoomIn')" width="18" height="18" />
                                    </template>
                                </a-button>
                            </div>
                            <div class="flex gap-2">
                                <a-button type="text" shape="circle" size="small" @click="decreaseZoom"
                                    :disabled="zoomScale <= minZoom">
                                    <template #icon>
                                        <img src="@/assets/images/decrease.svg" :alt="t('modelValidation.zoomOut')"
                                            width="18" height="18" />
                                    </template>
                                </a-button>
                                <a-button type="text" shape="circle" size="small" @click="increaseZoom"
                                    :disabled="zoomScale >= maxZoom">
                                    <template #icon>
                                        <img src="@/assets/images/increase.svg" :alt="t('modelValidation.zoomIn')"
                                            width="18" height="18" />
                                    </template>
                                </a-button>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg mt-4 p-4 w-[448px]">
                        <div class="flex flex-wrap justify-between gap-2">
                            <div class="text-center p-2 border rounded flex-1 min-w-[30%]">
                                <div class="text-[18px] font-bold mb-2">{{ qaStats.total }}</div>
                                <div class="text-[12px] text-[#8c8c8c] text-[14px]">{{ t('modelValidation.dailyQaTotal') }}</div>
                            </div>
                            <div class="text-center p-2 border rounded flex-1 min-w-[30%]">
                                <div class="text-[18px] font-bold text-green-500 mb-2">{{ qaStats.pass }}</div>
                                <div class="text-[12px] text-[#8c8c8c] text-[14px]">{{ t('modelValidation.qaPass') }}</div>
                            </div>
                            <div class="text-center p-2 border rounded flex-1 min-w-[30%]">
                                <div class="text-[18px] font-bold text-red-500 mb-2">{{ qaStats.fail }}</div>
                                <div class="text-[12px] text-[#8c8c8c] text-[14px]">{{ t('modelValidation.qaFail') }}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 右侧产品信息区域 -->
                <div class="flex-1 rounded-md flex flex-col w-[780px]">
                    <!-- 产品区域 -->
                    <div class="bg-white rounded-lg mb-2">
                        <div class="rounded p-2 flex flex-row">
                            <!-- IR Result Images -->
                            <div class="mb-4 flex-1 p-2 overflow-x-auto min-w-0">
                                <div class="text-[14px] font-medium mb-2">IR Result Images</div>
                                <div class="flex flex-row space-x-2 px-3 py-2 bg-[#f5f5f5] flex-nowrap min-w-max rounded-lg">
                                    <template v-if="irSkuImages.length > 0">
                                        <div v-for="(imageUrl, index) in irSkuImages" :key="'ir-' + index"
                                            class="flex justify-center items-center w-[100px] h-[128px] border border-solid border-[1px] border-[#D9D9D9] bg-gray-50">
                                            <img :src="imageUrl" :alt="`IR Result Images${index + 1}`"
                                                class="max-w-full w-[82px] h-[128px] object-contain">
                                        </div>
                                    </template>
                                    <template v-else>
                                        <div class="flex justify-center items-center w-[100px] h-[128px] border border-solid border-[1px] border-[#D9D9D9] bg-gray-50">
                                            <span class="text-gray-400 text-sm">No Image</span>
                                        </div>
                                    </template>
                                </div>
                            </div>
                            
                            <!-- Ground Truth Images -->
                            <div class="flex-1 p-2 overflow-x-auto min-w-0">
                                <div class="text-[14px] font-medium mb-2">Ground Truth Images</div>
                                <div class="flex flex-row space-x-2 px-3 py-2 bg-[#f5f5f5] flex-nowrap min-w-max rounded-lg">
                                    <template v-if="gtSkuImages.length > 0">
                                        <div v-for="(imageUrl, index) in gtSkuImages" :key="'gt-' + index"
                                            class="flex justify-center items-center w-[100px] h-[128px] border border-solid border-[1px] border-[#D9D9D9] bg-gray-50">
                                            <img :src="imageUrl" :alt="`Ground Truth Images${index + 1}`"
                                                class="max-w-full w-[82px] h-[128px] object-contain">
                                        </div>
                                    </template>
                                    <template v-else>
                                        <div class="flex justify-center items-center w-[100px] h-[128px] border border-solid border-[1px] border-[#D9D9D9] bg-gray-50">
                                            <span class="text-gray-400 text-sm">No Image</span>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white w-full min-w-[780px] rounded-lg p-4 relative flex-1" v-if="queryParams.type === 'start'">
                        <!-- 基本信息 -->
                        <div class="mb-4">
                            <div class="text-base font-medium mb-2 flex items-center">
                                <span class="inline-block rounded-[2px] bg-black w-[4px] h-[18px]"></span>
                                <span class="ml-2 mt-1 text-[16px]">{{ $t('modelValidation.basicInfo') }}</span>
                            </div>

                            <div class="grid grid-cols-2 gap-3 p-6">
                                <div class="flex">
                                    <div class="w-24 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.modelType') }}</div>
                                    <div class="ml-4 text-[14px]">{{ productInfo.modelType }}</div>
                                </div>
                                <div class="flex">
                                    <div class="w-32 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.country') }}</div>
                                    <div class="ml-4 text-[14px]">{{ productInfo.country }}</div>
                                </div>
                                <div class="flex">
                                    <div class="w-24 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.client') }}</div>
                                    <div class="ml-4 text-[14px]">{{ productInfo.client }}</div>
                                </div>
                                <div class="flex">
                                    <div class="w-32 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.sceneId') }}</div>
                                    <div class="ml-4 text-[14px] w-48 truncate" :title="productInfo.sceneId">{{ productInfo.sceneId }}</div>
                                </div>
                                <div class="flex">
                                    <div class="w-24 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.validator') }}</div>
                                    <div class="ml-4 text-[14px]">{{ getUserName(productInfo.validator) }}</div>
                                </div>
                                <div class="flex">
                                    <div class="w-32 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.lastModifiedOn') }}</div>
                                    <div class="ml-4 text-[14px]">{{ productInfo.lastModified }}</div>
                                </div>
                                <div class="flex">
                                    <div class="w-24 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.project') }}</div>
                                    <div class="ml-4 text-[14px]">{{ productInfo.projectId }}</div>
                                </div>
                                <div class="flex">
                                    <div class="w-32 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.taskId') }}</div>
                                    <div class="ml-4 text-[14px]">{{ productInfo.taskId }}</div>
                                </div>
                            </div>
                        </div>
                        <!-- SKU信息 -->
                        <div class="mb-4">
                            <div class="text-base font-medium mb-2 flex items-center">
                                <span class="inline-block rounded-[2px] bg-black w-[4px] h-[18px]"></span>
                                <span class="ml-2 mt-1 text-[16px]">{{ $t('modelValidation.skuInfo') }}</span>
                            </div>
                            <div class="grid grid-cols-1 gap-3 p-6">
                                <div class="flex mb-2">
                                    <div class="w-48 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.irReskuDescription') }}</div>
                                    <div class="ml-4 text-[14px]">{{ productInfo.irReskuDescription }}</div>
                                </div>
                                <div class="flex mb-2">
                                    <div class="w-48 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.irProductName') }}</div>
                                    <div class="ml-4 text-[14px]">{{ productInfo.irProductName }}</div>
                                </div>
                                <div class="flex mb-2 border-b border-b-dashed border-[#f0f0f0] pb-3">
                                    <div class="w-48 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.report') }}</div>
                                    <div class="ml-4 text-[14px]">{{ productInfo.report }}</div>
                                </div>
                                <div class="flex mb-2">
                                    <div class="w-48 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.groundTruth') }}</div>
                                    <div class="ml-4 text-[14px]">{{ productInfo.groundTruth }}</div>
                                </div>
                                <div class="flex mb-2 border-b border-b-dashed border-[#f0f0f0] pb-3">
                                    <div class="w-48 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.gtProductName') }}</div>
                                    <div class="ml-4 text-[14px]">{{ productInfo.gtProductName }}</div>
                                </div>
                                <div class="flex items-center mb-4">
                                    <div class="w-48 text-[#8c8c8c] text-[14px] text-right">
                                        <span class="text-red-500">*</span>&nbsp;{{ $t('modelValidation.error') }}
                                    </div>
                                    <a-radio-group v-model:value="errorStatus" class="mx-4">
                                        <a-radio :value="0">{{ $t('common.no') }}</a-radio>
                                        <a-radio :value="1">{{ $t('common.yes') }}</a-radio>
                                    </a-radio-group>
                                </div>
                                <div class="flex mb-2" v-if="errorStatus">
                                    <div class="w-48 text-[#8c8c8c] text-[14px] text-right">{{ $t('modelValidation.comments') }}:</div>
                                    <a-input size="small" class="ml-4 h-8 mt-[-8px]" placeholder="Enter comment" v-model:value="commentContent"></a-input>
                                </div>
                            </div>
                        </div>
                        <!-- 底部按钮 -->
                        <div class="flex flex-wrap justify-end gap-2 mt-4 absolute bottom-4 right-4">
                            <a-button @click="saveAndFinish">{{ t('modelValidation.saveAndFinish') }}</a-button>
                            <a-button type="primary" @click="saveAndNext">{{ t('modelValidation.saveAndNext') }}</a-button>
                        </div>
                    </div>
                    <div class="flex-1 flex flex-col" v-else>
                        <!-- QA list 列表区域 -->
                        <div class="bg-white rounded-lg p-4 flex-grow flex flex-col">
                            <div class="text-base font-medium mb-3 flex items-center">
                                <span class="inline-block rounded-[2px] bg-black w-[4px] h-[18px]"></span>
                                <span class="ml-2 mt-1 text-[16px]">{{ $t('modelValidation.qaList') }}</span>
                            </div>
                            <div class="table-container flex-grow" style="overflow-y: auto;">
                                <a-table :columns="qaListColumns" :data-source="qaListData" :pagination="false" size="middle" :loading="loading"
                                    bordered :scroll="{ y: 400, x: 900 }"
                                    :custom-row="customRow" :row-class-name="getRowClassName">
                                    <template #bodyCell="{ column, record }">
                                        <template v-if="column.dataIndex === 'issue_type'">
                                            <div class="flex items-center">
                                                <div class="w-2 h-2 rounded-full mr-2">
                                                </div>
                                                <span>{{ record.issue_type ? 'Yes':'No' }}</span>
                                            </div>
                                        </template>
                                        <template v-if="column.dataIndex === 'validator_id'">
                                            {{ getUserName(record.validator_id) }}
                                        </template>
                                        <template v-if="column.ellipsis">
                                            <a-tooltip :title="record[column.dataIndex]" placement="topLeft">
                                                <span>{{ record[column.dataIndex] }}</span>
                                            </a-tooltip>
                                    </template>
                                    </template>
                                </a-table>
                            </div>
                        </div>
                    </div>
                    <!-- 产品图片查看模态框 -->
                    <common-modal v-model:modelValue="showProductImageModal" :title="t('modelValidation.productImage')" :width="600">
                        <div class="flex justify-center items-center">
                            <div class="relative overflow-hidden">
                                <img :src="currentProductImageUrl" :alt="t('modelValidation.productImage')"
                                    class="max-w-full transition-transform duration-200 ease-in-out"
                                    :style="{ transform: `scale(${modalZoomScale})` }">
                            </div>
                        </div>
                        <div class="mt-4 flex justify-center gap-4">
                            <a-button type="text" shape="circle" size="small" @click="decreaseModalZoom"
                                :disabled="modalZoomScale <= minModalZoom">
                                <template #icon>
                                    <img src="@/assets/images/decrease.svg" :alt="t('modelValidation.zoomOut')"
                                        width="18" height="18" />
                                </template>
                            </a-button>
                            <a-button type="text" shape="circle" size="small" @click="increaseModalZoom"
                                :disabled="modalZoomScale >= maxModalZoom">
                                <template #icon>
                                    <img src="@/assets/images/increase.svg" :alt="t('modelValidation.zoomIn')"
                                        width="18" height="18" />
                                </template>
                            </a-button>
                        </div>
                    </common-modal>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { message } from 'ant-design-vue';
import HotFigure from '@/components/HotFigure.vue'
import CommonModal from '@/components/common/CommonModal.vue';
import { qaTaskFlowDetailApi, qaTaskDetailApi, qaTaskFlowSubmitApi, qaCountSummaryApi, skuImgApi } from '@/api/task/index';
import { useDateUtils } from '@/composable/useDateUtils';
// import { useAutoUserActionTracking } from '@/composable/useUserActionTracking';
import { useUserDataStore } from '@/store/userDataStore';
import { storeToRefs } from 'pinia';

const store = useUserDataStore();
const { userData } = storeToRefs(store);

const { formatTimestamp } = useDateUtils();
const { t } = useI18n();
const router = useRouter();
const queryParams = ref({});
const loading = ref(true);
queryParams.value = router.currentRoute.value.query;
// 获取任务ID
const qaId = ref(queryParams.value.qaId);
const commentContent = ref('');
// 图片缩放和拖拽相关
const zoomLevel = ref(1);
const translateX = ref(0);
const translateY = ref(0);
// 图片缩放相关变量 - 现在由HotFigure组件内部管理
const imageBoxW = ref(540);
const imageBoxH = ref(450);
// HotFigure组件数据
const hotFigureData = ref({});
// 图片加载完成事件
const imageFinshed = (data) => {
    // console.log('图片加载完成', data);
    // 这里可以处理图片加载完成后的逻辑
};
// 错误点标记
const selectedDotIndex = ref(-1);
const irSkuImages = ref([]) // IR Result图片
const gtSkuImages = ref([]) // Ground Truth图片
// 产品信息
const productInfo = reactive({
    region: '',
    country: '',
    client: '',
    sceneId: '',
    validator: '',
    modelType:'',
    lastModified: '',
    projectId: '',
    taskId: '',
    irReskuDescription: '',
    irProductName: '',
    gtReskuDescription: '',
    gtProductName: '',
    groundTruth: '',
    report: '',
    qaDetailId: '',
    qaDetailObj: {},
    sceneIds: 0 
});

// 错误状态
const errorStatus = ref(0);
// 控制是否显示所有红点
const showAll = ref(true);
const qaListColumns = [
    {
        title: t('modelValidation.client'),
        dataIndex: 'client',
        key: 'client',
        width: 120,
    },
    {
        title: t('modelValidation.country'),
        dataIndex: 'country',
        key: 'country',
        width: 120,
    },
    {
        title: t('modelValidation.modelType'),
        dataIndex: 'model_type',
        key: 'model_type',
        width: 120,
    },
    {
        title: t('modelValidation.report'),
        dataIndex: 'report',
        key: 'report',
        width: 100,
    },
    {
        title: t('modelValidation.irResult'),
        dataIndex: 'ir_result',
        key: 'ir_result',
        width: 150,
        ellipsis: true,
    },
    {
        title: t('modelValidation.gtResult'),
        dataIndex: 'gt_result',
        key: 'gt_result',
        width: 150,
        ellipsis: true,
    },
    {
        title: t('modelValidation.validator'),
        dataIndex: 'validator_id',
        key: 'validator_id',
        width: 120,
        // sorter: (a, b) => a.validator.localeCompare(b.validator_id),
    },
    {
        title: t('modelValidation.error'),
        dataIndex: 'issue_type',
        key: 'issue_type',
        width: 120,
        // sorter: (a, b) => a.issue_type.localeCompare(b.issue_type),
    },
    {
        title: t('modelValidation.comments'),
        dataIndex: 'comment',
        key: 'comment',
        width: 120,
    }
]
// 产品图片模态框
const showProductImageModal = ref(false);
// 当前显示的产品图片URL
const currentProductImageUrl = ref('');
const modalZoomScale = ref(1.0);
const minModalZoom = 0.6;
const maxModalZoom = 3.0;
const modalZoomStep = 0.2;

// 模态框中的图片缩放功能
const increaseModalZoom = () => {
    if (modalZoomScale.value < maxModalZoom) {
        modalZoomScale.value += modalZoomStep;
    }
};

const decreaseModalZoom = () => {
    if (modalZoomScale.value > minModalZoom) {
        modalZoomScale.value -= modalZoomStep;
    }
};

// 图片缩放相关变量
const zoomScale = ref(1.0);
const minZoom = 0.6;
const maxZoom = 3.0;

// 缩放功能
const planeFigure = ref(null);

const increaseDotSize = () => {
    if (planeFigure.value) {
        (planeFigure.value).increaseDotSize();
    }
};

const decreaseDotSize = () => {
    if (planeFigure.value) {
        (planeFigure.value).decreaseDotSize();
    }
};

const increaseZoom = () => {
    if ((planeFigure.value)) {
        (planeFigure.value).increaseZoom();
    }
};

const decreaseZoom = () => {
    if ((planeFigure.value)) {
        (planeFigure.value).decreaseZoom();
    }
};

// 分页相关
const currentIndex = ref(0);
const totalItems = ref(50);

// QA统计
const qaStats = reactive({
    total: '0/0',
    pass: 0,
    fail: 0
});

// 获取QA统计数据
const getQaCountSummary = async () => {
    try {
        const res = await qaCountSummaryApi(qaId.value);
        if (res && res.data && res.data.count) {
            const { count, completed_count, qa_pass, qa_fail } = res.data.count;
            qaStats.total = `${completed_count}/${count}`;
            qaStats.pass = qa_pass;
            qaStats.fail = qa_fail;
        }
    } catch (error) {
        console.error('getQaCountSummary error:', error);
    }
};

// 保存并完成
const saveAndFinish = async () => {
    // 校验：如果选择了yes，commentContent必须填写
    if (errorStatus.value === 1 && (!commentContent.value || commentContent.value.trim() === '')) {
        message.warning(t('modelValidation.commentRequired'));
        return;
    }
    
    try {
        // 构建保存数据
        const data = {
            "validation_task_qa_id": productInfo.qaDetailObj.validation_task_qa_id,
            "validation_task_scene_sku_result_id": productInfo.qaDetailObj.validation_task_scene_sku_result_id,
            "validation_task_qa_detail_id": productInfo.qaDetailId,
            "issue_type": errorStatus.value ? 1 : 0,
            "comment": commentContent.value || '',
        };
        
        // 调用API保存数据
        if (qaId.value&&productInfo.qaDetailId) {
            await qaTaskFlowSubmitApi(qaId.value, productInfo.qaDetailId, data);
            message.success(t('modelValidation.saveSuccess'));
        } else {
            message.error(t('modelValidation.saveFailed'));
        }
        router.push({
            path: '/model-validation',
            query: { tab: 'qa' }
        });
    } catch (error) {
        console.error('保存失败:', error);
        message.error(t('modelValidation.saveFailed'));
    }
};

// 保存并下一个
const saveAndNext = async () => {
    // 校验：如果选择了yes，commentContent必须填写
    if (errorStatus.value === 1 && 
        (!commentContent.value || commentContent.value.trim() === '')) {
        message.warning(t('modelValidation.commentRequired'));
        return;
    }
    
    try {
        // 这里添加保存逻辑
        if (currentIndex.value < totalItems.value - 1) {
            currentIndex.value++;
            const data = {
                "validation_task_qa_id": productInfo.qaDetailObj.validation_task_qa_id,
                "validation_task_scene_sku_result_id": productInfo.qaDetailObj.validation_task_scene_sku_result_id,
                "validation_task_qa_detail_id": productInfo.qaDetailId,
                "issue_type": errorStatus.value ? 1 : 0,
                "comment": commentContent.value || '',
            }
            if(qaId.value&&productInfo.qaDetailId) {
                const res = await qaTaskFlowSubmitApi(qaId.value, productInfo.qaDetailId, data);
                if(!res.data) {
                    message.warning(t('modelValidation.taskCompleted'));
                } else {
                    await getQaCountSummary();
                }
            } else {
                message.warning(t('modelValidation.taskCompleted'));
            }
            await getQaTaskFlowDetail();
            // 这里添加加载下一个项目的逻辑
            resetView();
        } else {
            message.info(t('modelValidation.lastItem'));
        }
    } catch (error) {
        router.push({
            path: '/model-validation',
            query: { tab: 'qa' }
        });
    }
};

// 重置视图
const resetView = () => {
    zoomLevel.value = 1;
    translateX.value = 0;
    translateY.value = 0;
    selectedDotIndex.value = -1;
    errorStatus.value = false;
    commentContent.value = '';
};

const jumpUrl = ref('');
// 打开Web App版本链接
const openWebAppVersion = () => {
    window.open(jumpUrl.value || qaListData.value[0].report_url, '_blank');
};

const getQaTaskFlowDetail = async () => {
    try {
        const res = await qaTaskFlowDetailApi(qaId.value);
        const _res = {
            "data": {
                "sku_detail": {
                    "report": "rfergregregrgtrgtrhtrhrthgtrhtrhtrhrhrt",
                    "id": 53,
                    "validation_scene_id": 11,
                    "validation_task_id": 4,
                    "updated_at": 1750064131,
                    "out_sku_id": 296374,
                    "ir_out_sku_id": 296374,
                    "att_info": {
                        "productDot": {
                            "x": 0.373,
                            "y": 0.091
                        },
                        "productBoundingBox": {
                            "h": 109.0,
                            "w": 56.0,
                            "x": 301.0,
                            "y": 77.0
                        }
                    }
                },
                "project": {
                    "region": "LATAM",
                    "country": "Switzerland",
                    "client": "Switzerland",
                    "model_type": "Switzerland",
                    "sub_client": "Costa",
                    "client_reference_id": "8",
                    "validation_type": "SKU/POP",
                    "out_project_id": "9",
                    "id": 8,
                    "created_at": 1748425458,
                    "updated_at": 1748425458,
                    "creator_id": 0,
                    "project_config": null,
                    "project_config_users": []
                },
                "val_qa_detail": {
                    "validation_task_scene_sku_result_id": 53,
                    "validation_task_qa_id": 3,
                    "qa_user_id": 2,
                    "id": 7,
                    "created_at": 1750233284,
                    "updated_at": 1750234708,
                    "status": 0
                },
                "val_qa": {
                    "name": "方法",
                    "deadline": 1750233247,
                    "status": 2,
                    "creator_id": 2,
                    "id": 3,
                    "created_at": 1750233284,
                    "updated_at": 1750233308
                },
                "val_task": {
                    "status": 3,
                    "creator_id": 3,
                    "is_ori_val": 0,
                    "start_time": 1750063572,
                    "name": "",
                    "type": "Adhoc",
                    "expect_deadline": 1750063580,
                    "sub_task_name": "",
                    "extra_info": {
                        "remark": "",
                        "no_scenes": "",
                        "attachments": "",
                        "mds_available": null,
                        "priority_level": "",
                        "image_received_on": null,
                        "specific_scene_ids": null,
                        "additional_document": null,
                        "requeued_and_validated": null
                    },
                    "project_id": 8,
                    "finish_time": 1750063580,
                    "task_users": null,
                    "task_user_loaded": false,
                    "id": 4,
                    "task_scene_loaded": false,
                    "task_scene_list": null,
                    "created_at": 1750063497,
                    "updated_at": 1750063580
                },
                "val_scene": {
                    "out_project_id": "9",
                    "name": "",
                    "type": "1",
                    "sub_scene_type": "",
                    "img_received_on": 1749117883,
                    "img_url": "https://cchproduction.blob.core.windows.net/ir-image/CCH/Switzerland/Field/Cooler/Competitor/2025/06/05/6c5cc81b48504fbb9f619d91fdb811d0_4_test.jpg?se=2025-10-14T06%3A08%3A36Z&sp=r&sv=2025-05-05&sr=b&sig=f13wBXFyuxGyT23g%2B722tt4j4hCmqcHRf2uNltv39Y0%3D",
                    "total_imgs": 1,
                    "total_facings": 12,
                    "out_scene_id": "6c5cc81b48504fbb9f619d91fdb811d0_4_test",
                    "portal_data": {},
                    "report_url": "http://snapshot-switzerland.lingmou.ai:8081/h5/demo#/report?task_id=2506690F3099-8B9C-5B4E-3778-ED82D6CE3E1C",
                    "validation_status": 1,
                    "validator_id": 3,
                    "detail_img_urls": [
                        "https://cchproduction.blob.core.windows.net/ir-image/CCH/Switzerland/Field/Cooler/Competitor/2025/06/05/6c5cc81b48504fbb9f619d91fdb811d0_4_K3HZt2VUUCD38hOkv3DzA_1.jpg?se=2025-10-14T06%3A08%3A36Z&sp=r&sv=2025-05-05&sr=b&sig=YA%2BPPe7Ri99eVxj4Ai3%2BAmb/CfsNFI%2BkxBRQeAp1gbE%3D"
                    ],
                    "id": 11,
                    "finished_at": 1750063581,
                    "created_at": 1750054116,
                    "updated_at": 1750063580
                }
            },
            "code": "string",
            "message": "string"
        }
        if (res.data) {
            const { sku_detail, project, val_qa_detail, val_qa, val_scene } = res.data;
            
            if (project) {
                productInfo.region = project.region || '';
                productInfo.country = project.country || '';
                productInfo.client = project.client || '';
                productInfo.projectId = project.id || '';
                productInfo.modelType = project.model_type || '';
            }
            
            if (sku_detail) {
                productInfo.sceneId = val_scene.out_scene_id || '';
                productInfo.lastModified = sku_detail.updated_at ? formatTimestamp(sku_detail.updated_at) : '';
                
                productInfo.ir_out_sku_id = sku_detail.ir_out_sku_id || 0;
                productInfo.out_sku_id = sku_detail.out_sku_id || 0;
                productInfo.report = sku_detail.report || '';
                // 设置图片URL - 从场景数据中获取
                currentProductImageUrl.value = val_scene.scene_img_url || '';
                setProductImage(sku_detail, val_scene.img_url || '');
                // 获取SKU图片
                await getSkuImages(sku_detail.ir_out_sku_id, sku_detail.gt_out_sku_id);
            }
            
            if (val_qa) {
                productInfo.validator = val_scene.validator_id || '';
            }
            
            if (val_qa_detail) {
                productInfo.qaDetailId = val_qa_detail.id || '';
                productInfo.taskId = val_qa_detail.validation_task_qa_id || '';
                productInfo.qaDetailObj = val_qa_detail;
            }
            
            // 设置场景图片到HotFigure组件
            if (val_scene && val_scene.img_url) {
                productInfo.imgUrl = val_scene.img_url;
                jumpUrl.value = val_scene.report_url;
            }
            productInfo.sceneIds = val_scene.id
            
            loading.value = false;
        } else {
            message.warning(t('modelValidation.taskCompleted'));
            router.push({
                path: '/model-validation',
                query: { tab: 'qa' }
            });
        }
    } catch (error) {
        console.log('getQaTaskFlowDetail', error);
    }
}

const setProductImage = (sku_detail, imageUrl) => {
    // 根据单个sku_detail创建边界框数据
    const boundingBoxes = [];
    
    if (sku_detail && sku_detail.att_info) {
        const { att_info } = sku_detail;
        
        // 处理productDot数据（红点位置）
        if (att_info.productDot) {
            const dot = att_info.productDot;
            boundingBoxes.push({
                sku_name: sku_detail.ir_result || sku_detail.gt_result || 'Unknown SKU',
                out_sku_id: sku_detail.out_sku_id || sku_detail.ir_out_sku_id,
                // 使用productDot的相对坐标作为红点位置
                tl: [dot.x, dot.y], // 左上角
                tr: [dot.x, dot.y], // 右上角
                bl: [dot.x, dot.y], // 左下角
                br: [dot.x, dot.y], // 右下角
                // 保存原始数据以备后用
                originalData: sku_detail
            });
        }
        
        // 处理productBoundingBox数据（边界框）
        if (att_info.productBoundingBox) {
            const bbox = att_info.productBoundingBox;
            // 将像素坐标转换为相对坐标（需要知道图片尺寸）
            // 这里假设图片尺寸，实际应该从图片加载后获取
            // 或者从接口返回的数据中获取图片尺寸信息
            boundingBoxes.push({
                sku_name: sku_detail.ir_result || sku_detail.gt_result || 'Bounding Box',
                out_sku_id: sku_detail.out_sku_id || sku_detail.ir_out_sku_id,
                // 注意：这里的坐标是像素坐标，需要转换为相对坐标
                // 暂时直接使用，后续需要根据实际图片尺寸进行转换
                tl: [bbox.x, bbox.y],
                tr: [bbox.x + bbox.w, bbox.y],
                bl: [bbox.x, bbox.y + bbox.h],
                br: [bbox.x + bbox.w, bbox.y + bbox.h],
                originalData: sku_detail,
                isBoundingBox: true // 标记为边界框
            });
        }
    }
    
    // 设置HotFigure组件的图片数据
    hotFigureData.value = {
        stitched_image: imageUrl,
        bounding_box_list: boundingBoxes
    };
};
const qaListData = ref([])
const selectedRowKeys = ref([]) // 选中的行keys

// 行选中变化处理
const onSelectChange = (newSelectedRowKeys) => {
    selectedRowKeys.value = newSelectedRowKeys;
};

// 自定义行点击事件
const customRow = (record, index) => {
    return {
        onClick: () => {
            const key = record.key || index;
            const selectedIndex = selectedRowKeys.value.indexOf(key);
            if (selectedIndex >= 0) {
                selectedRowKeys.value.splice(selectedIndex, 1);
                // 取消选中时恢复默认图片
                if (record.scene_img_url) {
                    setProductImage(record.scene_img_url);
                }
            } else {
                selectedRowKeys.value = [key]; // 单选模式
                // 选中时显示该行的scene_img_url
                if (record.scene_img_url) {
                    setProductImage(record.scene_img_url);
                }
            }
            jumpUrl.value = record.report_url;
            // 获取SKU图片
            getSkuImages(record.ir_out_sku_id, record.gt_out_sku_id);
        }
    };
};

// 获取行样式类名
const getRowClassName = (record, index) => {
    const key = record.key || index;
    return selectedRowKeys.value.includes(key) ? 'selected-row' : '';
};
const getQaTaskDetailApi = async () => {
    try {
        const res = await qaTaskDetailApi(qaId.value);
        const _res = {
                "data": {
                    "qa_user_ids": [0],
                    "sku_list": [
                        {
                            "region": "APAC",
                            "country": "India",
                            "ir_result": "COCA-COLA 12X150ML CAN",
                            "gt_result": "COCA-COLA 12X150ML CAN",
                            "validator_id": 0,
                            "validator_name": "Bavani M",
                            "issue_type": 0,
                            "ir_product_name": "COCA-COLA 12X150ML CAN",
                            "gt_product_name": "COCA-COLA 12X150ML CAN",
                            "out_scene_id": "123456",
                            "scene_img_url": "https://re-sku-1255412942.cos.ap-shanghai.myqcloud.com/RE-SKU-LOGO-0069119a763c2acce29d1133bc50ba1f"
                        },
                        {
                            "region": "APAC",
                            "country": "India",
                            "ir_result": "COCA-COLA 12X150ML CAN",
                            "gt_result": "COCA-COLA 12X150ML CAN",
                            "validator_id": 1,
                            "validator_name": "Bavani M",
                            "issue_type": 1,
                            "ir_product_name": "COCA-COLA 12X150ML CAN",
                            "gt_product_name": "COCA-COLA 12X150ML CAN",
                            "out_scene_id": "123456",
                            "scene_img_url": "https://re-sku-1255412942.cos.ap-shanghai.myqcloud.com/RE-SKU-LOGO-59d5b6d5b5d13c92ddbe180364c04322"
                        }
                    ]
                },
                "code": "string",
                "message": "string"
        }
        // console.log('getQaTaskDetailApi', res);
        if(res.data.sku_list.length > 0) {
            qaListData.value = res.data.sku_list.map((item, index) => ({
                ...item,
                key: index // 为每行添加唯一key
            }));
            hotFigureData.value = {
                stitched_image: res.data.sku_list[0].scene_img_url || '',
                bounding_box_list: [] // 可以根据需要添加边界框数据
            };
            currentProductImageUrl.value = res.data.sku_list[0].scene_img_url || '';
        } else {
            qaListData.value = []
        }
        loading.value = false;
    } catch (error) {
        console.log('getQaTaskDetailApi', error);
    }
}
// 根据用户ID获取用户名称
const getUserName = (id) => {
    const user = userData.value.find(user => user.id === id);
    return user ? user.name : id;
};

// 获取SKU图片
const getSkuImages = async (irOutSkuId, outSkuId) => {
    try {
        // 获取IR结果图片
        if (irOutSkuId) {
            const irRes = await skuImgApi(irOutSkuId);
            if (irRes.data && irRes.data.imgs) {
                irSkuImages.value = irRes.data.imgs;
            }
            productInfo.irReskuDescription = irRes.data.sku_name || '';
            productInfo.irProductName = irRes.data.product_name || '';
        }
        
        // 获取Ground Truth图片
        if (outSkuId) {
            const gtRes = await skuImgApi(outSkuId);
            if (gtRes.data && gtRes.data.imgs) {
                gtSkuImages.value = gtRes.data.imgs;
            }
            productInfo.groundTruth = gtRes.data.sku_name || '';
            productInfo.gtProductName = gtRes.data.product_name || '';
        }
    } catch (error) {
        console.error('获取SKU图片失败:', error);
        message.error('获取SKU图片失败');
    }
};
// 用户行为追踪
// const { userActionCount } = useAutoUserActionTracking({
//     taskId: () => productInfo.taskId,
//     sceneId: () => productInfo.sceneIds,
//     pageName: 'QATaskDetail',
//     reportInterval: 30000
// });
// 初始化
onMounted(async () => {
    if (!store.hasData) { // 仅在数据未加载时调用API
        await store.fetchUserData();
    }
    // 这里添加获取任务详情的逻辑
    if(queryParams.value.type === 'start') {
        await getQaTaskFlowDetail();
    } else {
        await getQaTaskDetailApi();
    }
    await getQaCountSummary();
});
</script>

<style scoped>
.image-container img {
    transform-origin: center;
}

/* 表格行hover效果 */
:deep(.ant-table-tbody > tr) {
    cursor: pointer;
}

:deep(.ant-table-tbody > tr:hover > td) {
    background-color: #f5f5f5 !important;
}

/* 选中行的背景色样式 */
:deep(.selected-row) {
    background-color: #e6f7ff !important;
}

:deep(.selected-row:hover) {
    background-color: #e6f7ff !important;
}
</style>