<template>
    <common-modal v-model:modelValue="modelValue" :title="title" :width="width" :cancel-text="cancelText" :ok-text="okText" :custom-footer="true"
        @cancel="handleCancel" @submit="handleSubmit">
        <!-- 表单内容 -->
        <div class="price-check-form">
            <p class="text-[14px] font-medium mb-4">{{ t('modelValidation.priceCheckForm') }}</p>
            <!-- 标签检测 -->
            <div class="mb-4">
                <div class="flex items-center mb-2">
                    <span class="text-[14px] font-medium">{{ t('modelValidation.missedTagDetection') }}</span>
                </div>
                <a-input-number v-model:value="formData.missedTagDetection" :min="0" class="w-full" />
            </div>

            <!-- 价格值 -->
            <div class="mb-4">
                <div class="flex items-center mb-2">
                    <span class="text-[14px] font-medium">{{ t('modelValidation.missedPriceValue') }}</span>
                </div>
                <a-input-number v-model:value="formData.missedPriceValue" :min="0" class="w-full" />
            </div>

            <!-- 价格和位置 -->
            <div class="mb-4">
                <div class="flex items-center mb-2">
                    <span class="text-[14px] font-medium">{{ t('modelValidation.priceAndPosition') }}</span>
                </div>
                <div class="flex gap-4">
                    <a-checkbox v-model:checked="formData.noPrice">{{ t('modelValidation.noPrice') }}</a-checkbox>
                    <a-checkbox v-model:checked="formData.noPosition">{{ t('modelValidation.noPosition') }}</a-checkbox>
                </div>
            </div>
        </div>
    </common-modal>
</template>

<script setup>
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import CommonModal from '@/components/common/CommonModal.vue';

const { t } = useI18n();

// 定义组件属性
const props = defineProps({
    title: {
        type: String
    },
    width: {
        type: [Number, String],
        default: 520
    },
    cancelText: {
        type: String
    },
    okText: {
        type: String
    },
    // 初始表单数据
    initialData: {
        type: Object,
        default: () => ({
            missedTagDetection: 0,
            missedPriceValue: 0,
            noPrice: false,
            noPosition: false
        })
    }
});

// 定义事件
const emit = defineEmits(['update:modelValue', 'cancel', 'submit']);

// 表单数据
const formData = ref({
    missedTagDetection: props.initialData.missedTagDetection,
    missedPriceValue: props.initialData.missedPriceValue,
    noPrice: props.initialData.noPrice,
    noPosition: props.initialData.noPosition
});

// 监听初始数据变化
watch(() => props.initialData, (newVal) => {
    formData.value = { ...newVal };
}, { deep: true });

// 处理取消事件
const handleCancel = () => {
    emit('update:modelValue', false);
    emit('cancel');
};

// 处理提交事件
const handleSubmit = () => {
    emit('submit', { ...formData.value });
    emit('update:modelValue', false);
};
</script>