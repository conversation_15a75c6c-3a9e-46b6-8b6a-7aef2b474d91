<template>
    <a-modal 
        :title="t('modelValidation.errorTitle')" 
        :open="open" 
        width="1200px"
        @cancel="handleCancel"
        :bodyStyle="{ maxHeight: '672px', overflowY: 'auto' }"
        centered>
      <div style="height: 672px;" class="flex flex-col">
        <div class="mt-3">
          <div class="flex flex-row items-center">
              <div class="ml-2 mr-4 h-8 leading-[32px]">
                <span class="mr-4 font-sourcehans text-14px font-500">{{ status === '0' ? $t('modelValidation.skuError') : $t('modelValidation.selectedError') }}</span>
                <a-radio-group v-model:value="status" class="ml-2" name="radioGroup">
                    <a-radio value="0">{{ $t('common.all') }}</a-radio>
                    <a-radio value="1">
                      <a-badge :count="selectedKeys.length" :offset="[8, -5]">
                        {{ $t('common.selected') }}
                      </a-badge>
                    </a-radio>
                </a-radio-group>
              </div>
            <div>
                <a-form :model="formState" layout="inline" ref="formRef" v-if="status === '0'">
                  <a-form-item name="value4">
                    <a-range-picker v-model:value="value4" :format="dateFormat" @change="fetchSceneDetail" />
                  </a-form-item>
                  <!-- <a-form-item name="region" class="w-40">
                    <a-select v-model:value="formState.region" allowClear :placeholder="$t('modelValidation.region')" class="rounded-md" @change="fetchSceneDetail">
                      <a-select-option v-for="type in types" :key="type.id" :value="type.id">{{ type.label }}</a-select-option>
                    </a-select>
                  </a-form-item> -->
                  <a-form-item name="country" class="w-40">
                    <a-select v-model:value="formState.country" allowClear :placeholder="$t('modelValidation.country')" class="rounded-md" @change="fetchSceneDetail">
                      <a-select-option v-for="country in countryOptions" :key="country" :value="country">{{ country }}</a-select-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item name="client" class="w-40">
                    <a-select v-model:value="formState.client" allowClear :placeholder="$t('modelValidation.client')" class="rounded-md" @change="fetchSceneDetail">
                      <a-select-option v-for="client in clientOptions" :key="client" :value="client">{{ client }}</a-select-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item name="scene_type" class="w-40">
                    <a-select v-model:value="formState.scene_type" allowClear :placeholder="$t('modelValidation.sceneType')" class="rounded-md" @change="fetchSceneDetail">
                      <a-select-option v-for="sceneType in sceneTypeOptions" :key="sceneType" :value="sceneType">{{ sceneType }}</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-form>
            </div>
          </div>
          </div>
          <div v-if="status === '0'" class="flex-1 overflow-auto mb-8 mt-2" @scroll="handleScroll">
          <a-table 
            :columns="columns" 
            :data-source="initTable"  
            :customRow="(record, index) => ({ onClick: () => onMainRow(record, index)})" 
            :row-class-name="(record, index) => selectedMainRowIndex === index ? 'selected-main-row' : ''"
            class="mb-4" 
            :pagination="false"
            :row-height="32"
            :loading="loading"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'error'">
                {{ record.facing_count && Math.round((record.error_facing_count / record.facing_count) * 100) > 0 ? Math.round((record.error_facing_count / record.facing_count) * 100) + '%' : '' }}
              </template>
            </template>
          </a-table>
        </div>
        <div class="ml-2 mr-4">
          <span class="font-sourcehans text-14px font-500">{{  status === '0' ? $t('modelValidation.relatedSceneSku') : '' }}</span>
        </div>
        <div class="flex-1 overflow-auto mt-2">
          <a-table ref="tableRef" 
            :columns="relatedColumns" 
            :data-source="filteredTableData" 
            :row-selection="status === '0' ? rowSelection : null" 
            :class="{'status-selected': status === '1'}" 
            :pagination="false"
            :scroll="{ x: 'max-content' }">
            <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'updated_at'">
                    {{ formatTimestamp(record.updated_at, 'MM/DD/YYYY') }}
                </template>
                <template v-if="column.dataIndex === 'created_at'">
                    {{ formatTimestamp(record.created_at, 'MM/DD/YYYY') }}
                </template>
                <template v-if="column.dataIndex === 'validator_id'">
                  {{ getUserName(record.validator_id) }}
                </template>
                <template v-if="column.dataIndex === 'action' && status === '1'">
                    <a @click="handleRemove(record)" class="text-[#FF4D4F] hover:text-[#FF4D4F]">
                        {{ $t('common.remove') }}
                    </a>
                </template>
            </template>
          </a-table>
        </div>
      </div>
        <template #footer>
          <div class="h-[32px] mt-8">
            <template v-if="status === '1'">
              <a-button @click="handleCancel">{{ t('common.cancel') }}</a-button>
              <a-button type="primary" @click="handleSave">{{ t('common.save') }}</a-button>
            </template>
          </div>
        </template>
    </a-modal>
</template>

<script setup>
import { ref, computed, reactive } from 'vue';
import { useI18n } from 'vue-i18n';
import { errorReportApi, tableLsitFileterApi } from '@/api/task/index';
import dayjs from 'dayjs';
import { dateToTimestamp, formatTimestamp } from '@/utils/date';
import { message } from 'ant-design-vue';

const { t } = useI18n();
const emit = defineEmits(['refresh']);
const open = ref(false);
const loading = ref(false);
const status = ref('0');
const dateFormat = 'MM/DD';
const selectedKeys = ref([]);
const disabledKeys = ref([]);
const formRef = ref();
const initTable = ref([]);
const tableRef = ref();
const selectedMainRowIndex = ref(null);
const allProcessedScenes = ref([]);
const props = defineProps({
  userData: { type: Array, default: () => [] },
});
const value4 = ref([
  dayjs().subtract(7, 'day').startOf('day'),
  dayjs().endOf('day')
]);
// 下拉选项数据
const countryOptions = ref([]);
const clientOptions = ref([]);
const sceneTypeOptions = ref([]);
const formState = reactive({
  sku_time_start: null,
  sku_time_end: null,
  region: null,
  country: null,
  client: null,
  scene_type: null,
  page_no: 0,
  page_size: 20
});

const columns = computed(() => [
    // { title: t('modelValidation.region'), dataIndex: 'region', width: 80 },
    { title: t('modelValidation.country'), dataIndex: 'country', width: 120 },
    { title: t('modelValidation.client'), dataIndex: 'client', width: 120 },
    { title: t('modelValidation.irResku'), dataIndex: 'ir_result', ellipsis: true, width: 300 },
    { title: t('modelValidation.irProduct'), dataIndex: 'ir_product_name', ellipsis: true, width: 300 },
    { title: t('modelValidation.gtResku'), dataIndex: 'gt_result', ellipsis: true, width: 300 },
    { title: t('modelValidation.gtProduct'), dataIndex: 'gt_product_name', ellipsis: true, width: 300 },
    { title: t('modelValidation.error_facing_count'), dataIndex: 'error_facing_count', ellipsis: true, width: 150  },
    { title: t('modelValidation.facing_count'), dataIndex: 'facing_count', ellipsis: true , width: 90},
    { title: t('modelValidation.error'), dataIndex: 'error', width: 80 },
]);

const getUserName = (id) => {
  const user = props.userData.find(user => user.id === id);
  return user ? user.name : id;
};

const relatedColumns = computed(() => {
  const baseColumns = [];
  if (status.value === '1') {
    baseColumns.push(
      // { title: t('modelValidation.region'), dataIndex: 'region', width: 80},
      { title: t('modelValidation.country'), dataIndex: 'country', width: 90  },
      { title: t('modelValidation.client'), dataIndex: 'client', width: 80},
    );
  }
  baseColumns.push(
    { title: t('modelValidation.irResku'), dataIndex: 'ir_result', ellipsis: true, width: 300  },
    { title: t('modelValidation.irProduct'), dataIndex: 'ir_product_name', ellipsis: true, width: 300 },
    { title: t('modelValidation.gtResku'), dataIndex: 'gt_result', ellipsis: true, width: 300 },
    { title: t('modelValidation.gtProduct'), dataIndex: 'gt_product_name', ellipsis: true, width: 300 },
    { title: t('modelValidation.scenneId'), dataIndex: 'out_scene_id' , ellipsis: true, width: 150},
    { title: t('modelValidation.validator'), dataIndex: 'validator_id', ellipsis: true, width: 100 },
    { title: t('modelValidation.lastModifiedOn'), dataIndex: 'updated_at', width: 150 },
    { title: t('modelValidation.created_at'), dataIndex: 'created_at', width: 120},
  );
  if (status.value === '0') {
    baseColumns.splice(8, 0,
      { title: t('modelValidation.requeue_user_id'), dataIndex: 'requeue_user_id', width: 180 },
    );
  } else {
    baseColumns.push({ title: t('common.action'), dataIndex: 'action', width: 100, fixed: 'right'   });
  }
  return baseColumns;
});

function handleScroll(e) {
  if(status.value === '0') {
    const el = e.target;
    const scrollPosition = el.scrollTop;
    const viewportHeight = el.clientHeight;
    const totalHeight = el.scrollHeight;
    
    // 当滚动到距离底部60%的位置时就开始预加载下一页数据,提前加载 降低loading效果
    const threshold = totalHeight - viewportHeight * 1.6;
    
    if (scrollPosition > threshold) {
      // 使用防抖,避免频繁触发加载
      if (handleScroll.timer) {
        clearTimeout(handleScroll.timer);
      }

      handleScroll.timer = requestAnimationFrame(() => {
        // 检查是否正在加载中,避免重复请求
        if(!loading.value && !handleScroll.hasShownWarning) {
          // 预加载数据时不显示loading状态
          const originalLoading = loading.value;
          loading.value = false;
          
          formState.page_no++;
          fetchSceneDetail(true).catch(err => {
            console.error('预加载数据失败:', err);
            formState.page_no--;
          }).finally(() => {
            // 恢复原始loading状态
            loading.value = originalLoading;
          });
        }
      });
    }
  }
}

const fetchSceneDetail = async (append = false) => {
  loading.value = true;
  try {
    if (value4.value && value4.value.length === 2) {
      formState.sku_time_start = dateToTimestamp(dayjs(value4.value[0]).startOf('day'));
      formState.sku_time_end = dateToTimestamp(dayjs(value4.value[1]).endOf('day'));
    }
    const res = await errorReportApi(formState);
    if (append === true) {
      if (res.data?.data_list.length === 0) {
        handleScroll.hasShownWarning = true;
        message.warning(t('common.noMoreData'));
      }
      initTable.value = initTable.value.concat(res.data?.data_list || []);
    } else {
      handleScroll.hasShownWarning = false;
      formState.page_no = 0;
      initTable.value = res.data?.data_list || [];
    }
  } finally {
    loading.value = false;
  }
};

const rowSelection = computed(() => ({
  onChange: (selectedRowKeys) => {
    // 获取当前表格的key
    const currentTableKeys = filteredTableData.value.map(item => item.key);
    // 过滤不在当前表格的已选的key
    const keysNotInCurrentTable = selectedKeys.value.filter(key => !currentTableKeys.includes(key));
    // 合并当前选中的key和之前不在当前表格的已选的key
    selectedKeys.value = [...new Set([...keysNotInCurrentTable, ...selectedRowKeys])];
  },
  getCheckboxProps: (record) => ({
    disabled: disabledKeys.value.includes(record.key)
  }),
  selectedRowKeys: selectedKeys.value
}));

const handleCancel = () => {
  open.value = false;
  formRef.value?.resetFields();
  if(tableRef.value?.store) {
      tableRef.value.store.selectedRowKeys = [];
    } else {
      selectedKeys.value = [];
  }
  selectedMainRowIndex.value = null;
  allProcessedScenes.value = [];
  selectedKeys.value = [];
};

const handleRemove = (record) => {
  selectedKeys.value = selectedKeys.value.filter(key => key !== record.key);
};

const handleSave = () => {
  if (status.value === '1') {
    emit('add-scenes', filteredTableData.value);
  }
  open.value = false;
};

const fetchFilterOptions = async () => {
  try {
    const params = {
      data_source_list: ['project', 'scene']
    };
    const res = await tableLsitFileterApi(params);
    if (res.data && res.data.project) {
      countryOptions.value = res.data.project.country || [];
      clientOptions.value = res.data.project.client || [];
    }
    if (res.data && res.data.scene) {
      sceneTypeOptions.value = res.data.scene.scene_type || [];
    }
  } catch (error) {
    console.error('获取筛选选项失败:', error);
  }
};

const showSelectSceneDialog = (ids = []) => {
  open.value = true;
  disabledKeys.value = ids;
  selectedKeys.value = [];
  formState.page_no = 0;
  initTable.value = [];

  loading.value = true;
  fetchFilterOptions().then(() => {
    return fetchSceneDetail();
  }).catch(error => {
    console.error(error);
  }).finally(() => {
    loading.value = false;
  });
  status.value = '0';
};

const transformSceneData = (scenes) => {
  return scenes.map(scene => ({
    ...scene,
    key: scene.validation_task_scene_sku_result_id
  }));
};

const filteredTableData = computed(() => {
  if (selectedMainRowIndex.value === null) return [];
  
  const currentScenes = initTable.value[selectedMainRowIndex.value]?.related_scene_skus || [];
  const uniqueScenes = transformSceneData(currentScenes);

  const scenesMap = new Map();
  [...allProcessedScenes.value, ...currentScenes].forEach(scene => {
    scenesMap.set(scene.validation_task_scene_sku_result_id, scene);
  });
  allProcessedScenes.value = Array.from(scenesMap.values());
  const processedScenes = transformSceneData(allProcessedScenes.value);

  return status.value === '1' 
    ? processedScenes.filter(scene => selectedKeys.value.includes(scene.key))
    : uniqueScenes;
});

function onMainRow(record, index) {
  selectedMainRowIndex.value = index;
}

defineExpose({
  showSelectSceneDialog
});
</script>

<style scoped>
.ant-modal .ant-modal-title {
  margin-left: 0 !important;
}

.status-selected .ant-table-row-selected > td {
  background: none !important;
}

.selected-row {
  background: #e6f7ff !important;
}

.selected-main-row {
  background: #e6f7ff !important;
}

:deep(.selected-main-row > td) {
  background: #e6f7ff !important;
}

:deep(.ant-table-thead > tr > th),
:deep(.ant-table-tbody > tr > td.ant-table-cell) {
  height: 32px !important;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #e6f7ff !important;
}
</style>