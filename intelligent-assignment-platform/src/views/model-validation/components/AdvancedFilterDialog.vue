<template>
   <a-modal v-model:open="visible" :title="$t('common.advancedFilter')" @cancel="handleCancel" :mask="false">
        <div class="advanced-filter-content">
            <a-form :model="filterForm" ref="formRef" layout="horizontal" class="p-4" label-align="right" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
                <!-- 验证任务筛选表单 / Validation Task Filter Form -->
                <div v-if="activeTab === 'validation'">
                    <a-form-item :label="$t('modelValidation.filter.taskId')" name="task_id">
                        <a-input v-model:value="filterForm.task_id" :placeholder="$t('modelValidation.filter.enterTaskId')" size="small" allowClear class="h-8" />
                    </a-form-item>
                    <a-form-item :label="$t('modelValidation.filter.country')" name="country">
                        <a-select v-model:value="filterForm.country" :placeholder="$t('modelValidation.filter.selectCountry')" allowClear style="height: 32px;">
                            <a-select-option v-for="item in countryOptions" :key="item.value"
                                :value="item.value">
                                {{ item.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item :label="$t('modelValidation.filter.client')" name="client">
                        <a-select v-model:value="filterForm.client" :placeholder="$t('modelValidation.filter.selectClient')" allowClear style="height: 32px;">
                            <a-select-option v-for="item in clientOptions" :key="item.value"
                                :value="item.value">
                                {{ item.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item :label="$t('modelValidation.filter.modelType')" name="model_type">
                        <a-select v-model:value="filterForm.model_type" :placeholder="$t('modelValidation.filter.selectModelType')" allowClear style="height: 32px;">
                            <a-select-option v-for="item in modelTypeOptions" :key="item.value"
                                :value="item.value">
                                {{ item.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item :label="$t('modelValidation.filter.createdOn')" name="createdOn">
                        <a-range-picker v-model:value="filterForm.createdOn" style="width: 100%" size="small" placement="topLeft" allowClear class="h-8" />
                    </a-form-item>
                    <a-form-item :label="$t('modelValidation.filter.taskType')" name="task_type">
                        <a-select v-model:value="filterForm.task_type" :placeholder="$t('modelValidation.filter.selectTaskType')" allowClear style="height: 32px;">
                            <a-select-option v-for="item in taskTypeOptions" :key="item.value"
                                :value="item.value">
                                {{ item.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item :label="$t('modelValidation.filter.status')" name="status" class="h-8">
                        <a-select v-model:value="filterForm.status" :placeholder="$t('modelValidation.filter.selectStatus')" allowClear style="height: 32px;">
                            <a-select-option v-for="item in statusOptions" :key="item.value"
                                :value="item.value">
                                {{ item.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-item>
                </div>

                <!-- QA任务筛选表单 / QA Task Filter Form -->
                <div v-if="activeTab === 'qa'">
                    <a-form-item :label="$t('modelValidation.filter.qaId')" name="qa_task_id">
                        <a-input v-model:value="filterForm.qa_task_id" :placeholder="$t('modelValidation.filter.enterQaId')" size="small" allowClear class="h-8" />
                    </a-form-item>
                    <a-form-item :label="$t('modelValidation.filter.qaName')" name="qa_task_name">
                        <a-input v-model:value="filterForm.qa_task_name" :placeholder="$t('modelValidation.filter.enterQaName')" size="small" allowClear class="h-8"/>
                    </a-form-item>
                    <a-form-item :label="$t('modelValidation.filter.createdOn')" name="createdOn">
                        <a-range-picker v-model:value="filterForm.createdOn" style="width: 100%" size="small" placement="topLeft" allowClear class="h-8"/>
                    </a-form-item>
                    <a-form-item :label="$t('modelValidation.filter.status')" name="status">
                        <a-select v-model:value="filterForm.status" :placeholder="$t('modelValidation.filter.selectStatus')" allowClear style="height: 32px;" class="h-8">
                            <a-select-option v-for="item in statusOptions" :key="item.value"
                                :value="item.value">
                                {{ item.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-item>
                </div>

                <!-- 错误结果筛选表单 / Error Result Filter Form -->
                <div v-if="activeTab === 'error'">
                    <a-form-item :label="$t('modelValidation.filter.qaId')" name="qa_task_id">
                        <a-input v-model:value="filterForm.qa_task_id" :placeholder="$t('modelValidation.filter.enterQaId')" size="small" allowClear class="h-8" />
                    </a-form-item>
                    <a-form-item :label="$t('modelValidation.filter.country')" name="country">
                        <a-select v-model:value="filterForm.country" :placeholder="$t('modelValidation.filter.selectCountry')" allowClear style="height: 32px;">
                            <a-select-option v-for="item in countryOptions" :key="item.value"
                                :value="item.value">
                                {{ item.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item :label="$t('modelValidation.filter.client')" name="client">
                        <a-select v-model:value="filterForm.client" :placeholder="$t('modelValidation.filter.selectClient')" allowClear style="height: 32px;">
                            <a-select-option v-for="item in clientOptions" :key="item.value"
                                :value="item.value">
                                {{ item.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item :label="$t('modelValidation.filter.modelType')" name="model_type">
                        <a-select v-model:value="filterForm.model_type" :placeholder="$t('modelValidation.filter.selectModelType')" allowClear style="height: 32px;">
                            <a-select-option v-for="item in modelTypeOptions" :key="item.value"
                                :value="item.value">
                                {{ item.label }}
                            </a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item :label="$t('modelValidation.filter.qaName')" name="qa_task_name">
                        <a-input v-model:value="filterForm.qa_task_name" :placeholder="$t('modelValidation.filter.enterQaName')" allowClear size="small" class="h-8"/>
                    </a-form-item>
                </div>
            </a-form>
        </div>

        <template #footer>
            <div class="mr-4 text-right">
                <a-button @click="resetFilter">{{ $t('common.reset') }}</a-button>
                <a-button type="primary" @click="applyFilter">{{ $t('common.query') }}</a-button>
            </div>
        </template>
    </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
//@ts-ignore
import { tableLsitFileterApi } from '@/api/task/index';
import { statusOptions, taskTypeOptions } from '../../../utils/dropdownOptions';

const formRef = ref();
const visible = ref(false);
const emit = defineEmits(['refresh', 'close']);
// 接收父组件传递的 activeTab
const props = defineProps<{
    activeTab: string;
}>();
// 当前激活的标签页 / Current active tab
const activeTab = ref(props.activeTab);

// 筛选表单数据 / Filter form data
// 定义三个不同类型的筛选表单对象
// 根据不同tab页面的筛选条件组合成统一的filterForm对象
const filterForm = reactive({
    // 公共筛选字段
    createdOn: [],
    status: undefined,
    task_id: undefined,

    // validation tab的筛选字段
    country: undefined,
    client: undefined,
    model_type: undefined,
    task_type: undefined,
    
    // qa tab的筛选字段
    qa_task_id: undefined,
    qa_task_name: undefined,
    
    // error tab的筛选字段
    region: undefined,
    taskSerial: undefined,
    issueType: undefined
});

// 定义选项接口
interface SelectOption {
    label: string;
    value: string;
}

// 选项数据
const countryOptions = ref<SelectOption[]>([]);
const clientOptions = ref<SelectOption[]>([]);
const modelTypeOptions = ref<SelectOption[]>([]);
// 数据格式化函数，确保选项数据格式统一
const formatOptions = (data: any[]): SelectOption[] => {
    if (!Array.isArray(data)) return [];
    
    return data.map(item => {
        // 如果是字符串，转换为标准格式
        if (typeof item === 'string') {
            return { label: item, value: item };
        }
        // 如果已经是对象格式，确保包含必要字段
        return {
            label: String(item.label || item.name || item.value || item),
            value: String(item.value || item.id || item)
        };
    });
};

const fetchFilterOptions = async () => {
    try {
        const params = {
            data_source_list: ['project']
        };
        const res = await tableLsitFileterApi(params);
        if (res.data) {
            // 格式化选项数据，确保每个选项都有 label 和 value 属性
            countryOptions.value = formatOptions(res.data.project?.country);
            clientOptions.value = formatOptions(res.data.project?.client);
            modelTypeOptions.value = formatOptions(res.data.project?.model_type);
        }
    } catch (error) {
        console.error(error);
    }
};

const handleCancel = () => {
    visible.value = false;
};

// 重置筛选条件 / Reset filter conditions
const resetFilter = () => {
    // 根据当前激活的标签页重置对应的筛选字段 / Reset filter fields based on current active tab
    formRef.value?.resetFields();
    // 应用重置后的筛选条件，清除筛选状态
    applyFilter();
    handleCancel();
};

// 应用筛选条件 / Apply filter conditions
const applyFilter = () => {
    const filterParams = {
        activeTab: activeTab.value,
        ...filterForm
    };
    // 触发刷新事件，将筛选参数传递给父组件 / Trigger refresh event, pass filter parameters to parent component
    emit('refresh', filterParams);
    handleCancel();
};

onMounted(() => {
    fetchFilterOptions();
})

const showAdvancedFilter = (tab = 'validation') => {
    activeTab.value = tab;
    visible.value = true;
};

defineExpose({
    showAdvancedFilter
});

</script>

<style scoped>
.advanced-filter-modal {
    max-height: 70vh;
    overflow-y: auto;
}
</style>