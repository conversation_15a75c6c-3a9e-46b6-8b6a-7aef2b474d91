<template>
    <div>
        <div class="w-100% rounded-[8px]">
            <div class="min-h-screen p-4 pt-0 rounded-[8px]">
                <a-card :title="$t('common.add')" class="mt-[14px] h-[93vh]">
                    <template #extra>
                        <a-button type="link" class="text-[#8C8C8C]" @click="cancelEdit">
                            {{ $t('common.cancel') }}
                        </a-button>
                        <a-button type="link" @click="saveChanges" :loading="saving">
                            {{ $t('common.save') }}
                        </a-button>
                    </template>
                    <a-spin :spinning="loading">
                        <div class="flex gap-6 relative">
                            <div class="w-3/5">
                                <div class="flex justify-between items-center mb-4">
                                    <div class="text-sm font-medium">{{ $t('modelValidation.sceneSkuList') }}</div>
                                    <div class="flex">
                                        <a-button type="link" class="text-[#8C8C8C]" @click="removeValidator()">
                                            {{ $t('common.remove') }}
                                        </a-button>
                                        <a-button type="link" @click="handleScene">
                                            {{ $t('common.add') }}
                                        </a-button>
                                    </div>
                                </div>
                                <div class="sku-table">
                                    <a-table :columns="skuColumns" :data-source="skuList"
                                        :row-selection="{ selectedRowKeys: selectedSkuKeys, onChange: onSelectSkuChange }">
                                        <template #bodyCell="{ column, record }">
                                            <template v-if="column.dataIndex === 'action'">
                                                <a @click="() => removeValidator(record.key)" class="text-[#FF4D4F] hover:text-[#FF4D4F]">
                                                    {{ $t('common.remove') }}
                                                </a>
                                            </template>
                                        </template>
                                    </a-table>
                                </div>
                            </div>

                            <div class="absolute left-[60%] top-0 bottom-0 w-[1px] bg-[#f0f0f0]"></div>

                            <div class="w-2/5">
                                <AccountList :validators="validators" @save="handleSave" @user-data="handleUserData" :isSave="isSave" :showDropdown="true"/>
                            </div>
                        </div>
                    </a-spin>
                </a-card>
            </div>
        </div>
        <select-scene-dialog ref="showSelectSceneRef" :userData="userData" @add-scenes="handleAddScenes"/>
    </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import { useI18n } from 'vue-i18n';
import AccountList from '../project-list/components/AccountList.vue';
import SelectSceneDialog from '../model-validation/components/SelectSceneDialog.vue';
import { SaveQAApi, qaDetailApi, editQAApi } from '../../api/task/index';
import { useRoute, useRouter } from 'vue-router';

const { t } = useI18n();
const showSelectSceneRef = ref();

const loading = ref(false);
const saving = ref(false);
const validators = ref([]);
const route = useRoute();
const router = useRouter();
const qaTaskForm = ref(route.query.qaTaskForm ? JSON.parse(route.query.qaTaskForm) : null);
const userData = ref([]);
const isSave = ref(false);
const skuList = ref([]);
const selectedSkuKeys = ref([]);
const id = ref(route.query.id ?? null); 

const initialFormState = reactive({
    user_ids: [],
    val_task_sku_result_ids: [],
});

const getUserInfo = (userId) => {
  const user = userData.value.find(u => u.id === userId) || {};
  return {
    key: user.id,
    user_id: user.id,
    department: user.department,
    name: user.name,
  };
};

const fetchQATaskDetail = async () => {
  if(id.value) {
    loading.value = true;
    try {
      if( id.value) {
        const res = await qaDetailApi(id.value);
        if (!res.data) return;

        const { sku_list = [], qa_user_ids = [] } = res.data;
        validators.value = res.data.qa_user_ids;
        skuList.value = sku_list.map((item) => ({
            ...item,
            key: item.validation_task_scene_sku_result_id,
        }));
        validators.value = qa_user_ids.map(userId => getUserInfo(userId));
      }
    }  catch (error) {
      console.log("error", error);
    } finally {
      loading.value = false;
    }
  }
}

const handleSave = ({ allValidators, targetKeys }) => {
  validators.value = allValidators;
  initialFormState.user_ids = targetKeys;
};

const handleUserData = (data) => {
    userData.value = data;
    fetchQATaskDetail();
}

const saveChanges = async () => {
  saving.value = true;
  try {
    initialFormState.val_task_sku_result_ids = skuList.value.map(item => item.validation_task_scene_sku_result_id);
    initialFormState.user_ids = validators.value.map(validator => validator.user_id);
    const submitData = {
        ...initialFormState,
        ...qaTaskForm.value,
    };
    let res;
    if(!id.value) {
        res = await SaveQAApi(submitData);
    } else {
        res = await editQAApi(id.value, submitData);
    }
    if (res.code === "0000") {
      message.success(t('common.saveSuccess'));
      isSave.value = true;
    } 
    router.push({
      path: '/model-validation',
      query: { tab: 'qa' }
    });
  } catch (error) {
    console.log("error", error);
  } finally {
    saving.value = false;
  }
}; 

const handleAddScenes = (scenes) => {
    const existingKeys = new Set(skuList.value.map(item => item.key || item.out_scene_id));
    const newScenes = scenes.filter(item => !existingKeys.has(item.key || item.id));
    skuList.value = [...skuList.value, ...newScenes.map(scene => ({
      ...scene,
      key: scene.validation_task_scene_sku_result_id
    }))];
};

const skuColumns = [
    { title: t('modelValidation.irResku'), dataIndex: 'ir_result', ellipsis: true, width: 150 },
    { title: t('modelValidation.irProduct'), dataIndex: 'ir_product_name', ellipsis: true, width: 150   },
    { title: t('modelValidation.gtResku'), dataIndex: 'gt_result', ellipsis: true },
    { title: t('modelValidation.gtProduct'), dataIndex: 'gt_product_name', ellipsis: true  },
    { title: t('modelValidation.scenneId'), dataIndex: 'out_scene_id', ellipsis: true },
    { title: t('common.action'), dataIndex: 'action', width: 100 }
];


const onSelectSkuChange = (selectedRowKeys) => {
    selectedSkuKeys.value = selectedRowKeys;
};

const handleScene = () => {
    const existedIds = skuList.value.map(item => item.validation_task_scene_sku_result_id);
    showSelectSceneRef.value.showSelectSceneDialog(existedIds);
};

const removeValidator = (key) => {
  if (key) {
    skuList.value = skuList.value.filter(item => item.key !== key && item.validation_task_scene_sku_result_id !== key);
    selectedSkuKeys.value = selectedSkuKeys.value.filter(k => k !== key);
  } else {
    skuList.value = skuList.value.filter(item => !selectedSkuKeys.value.includes(item.validation_task_scene_sku_result_id));
    selectedSkuKeys.value = [];
  }
};

const cancelEdit = () => {
    if(id.value) {
        fetchQATaskDetail();
    } else {
        initialFormState.val_task_sku_result_ids = [];
        validators.value = [];
        initialFormState.user_ids = [];
        skuList.value = [];
    }
    router.push({
      path: '/model-validation',
      query: { tab: 'qa' }
    });
};
</script>

<style scoped>
.sku-table :deep(.ant-table-thead > tr > th),
.sku-table :deep(.ant-table-tbody > tr > td.ant-table-cell) {
  height: 32px !important;
}
</style>