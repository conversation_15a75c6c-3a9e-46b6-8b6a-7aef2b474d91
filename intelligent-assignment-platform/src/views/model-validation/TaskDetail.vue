<template>
    <div class="w-full rounded-[8px] overflow-hidden">
        <div class="p-4">
            <div class="flex flex-row gap-4 h-full">
                <!-- 图片和信息区域 -->
                <div class="flex flex-col w-[448px] h-[92vh]">
                    <!-- 图片区域 -->
                    <div class="bg-white rounded-lg overflow-hidden flex-1">
                        <div class="border rounded p-3 flex flex-col h-full">
                            <div class="flex w-full justify-between mb-2">
                                <div class="text-[14px] font-medium">{{ t('modelValidation.image') }}</div>
                                <div class="flex gap-2">
                                    <a-button type="link" shape="circle" size="small" @click="openWebAppVersion">
                                        {{ t('modelValidation.webAppVersion') }}
                                    </a-button>
                                    <a-button type="link" shape="circle" size="small"
                                        @click="showImageQualityModal = true" v-if="queryParams.type !== 'view'">
                                        {{ t('modelValidation.selectImageQuality') }}
                                    </a-button>
                                </div>
                            </div>
                            <div class="flex-1 min-h-[300px] bg-gray-50 rounded overflow-hidden">
                                <HotFigure class="w-full h-full" :canvasW="imageBoxW" :canvasH="imageBoxH"
                                    @imageFinshed="imageFinshed" @boxClick="handleBoxClick" ref="planeFigure" :showAllDots="showAll" :initialData="hotFigureData"></HotFigure>
                            </div>
                            <div class="mt-3 flex justify-between">
                                <div class="flex flex-row" :style="queryParams.type === 'view' ? 'opacity: 0':''">
                                    <a-button type="text" shape="circle" size="small" class="text-[#FF4D4F]"
                                        @click="decreaseDotSize">
                                        <template #icon>
                                            <img src="@/assets/images/decMin.svg"
                                                :alt="t('modelValidation.decreaseDotSize')" width="18" height="18" />
                                        </template>
                                    </a-button>
                                    <div class="text-[12px] font-400 mt-2">dot</div>
                                    <a-button type="text" shape="circle" size="small" class="text-[#1677FF]"
                                        @click="increaseDotSize">
                                        <template #icon>
                                            <img src="@/assets/images/addPlus.svg"
                                                :alt="t('modelValidation.increaseDotSize')" width="18" height="18" />
                                        </template>
                                    </a-button>
                                </div>
                                <div class="flex gap-2">
                                    <a-button type="text" shape="circle" size="small" @click="decreaseZoom">
                                        <template #icon>
                                            <img src="@/assets/images/decrease.svg" :alt="t('modelValidation.zoomOut')"
                                                width="18" height="18" />
                                        </template>
                                    </a-button>
                                    <a-button type="text" shape="circle" size="small" @click="increaseZoom">
                                        <template #icon>
                                            <img src="@/assets/images/increase.svg" :alt="t('modelValidation.zoomIn')"
                                                width="18" height="18" />
                                        </template>
                                    </a-button>
                                </div>
                            </div>

                        </div>
                    </div>
                    <!-- 底部统计信息 -->
                    <div class="bg-white rounded-lg mt-4 p-4 w-[448px] h-45 overflow-x-auto" v-if="queryParams.type === 'start'">
                        <template v-if="queryParams.sceneType.includes('SKU')">
                            <div class="flex flex-row justify-between items-center mt-4 gap-4 min-w-max">
                                <div class="w-35">
                                    <div class="text-[16px] font-bold mb-4">Info</div>
                                    <div class="text-[14px] mb-3">Completion: <span class="font-medium">{{ skuSummaryData.task_count.completion }}/{{ skuSummaryData.task_count.count }}</span></div>
                                    <div class="text-[14px] mb-3">Missed detection: <span class="font-medium">{{ getCurrentSceneData().missed_detection }}</span></div>
                                    <div class="text-[14px] mb-3">Total Facing: <span class="font-medium">{{ getCurrentSceneData().total_facing }}</span></div>
                                </div>
                                <div class="w-35">
                                    <div class="text-[16px] font-bold mb-4">SKU</div>
                                    <div class="text-[14px] mb-3">Facing: <span class="font-medium">{{ getCurrentSceneData().sku_count.facing }}</span></div>
                                    <div class="text-[14px] mb-3">Error: <span class="font-medium">{{ getCurrentSceneData().sku_count.error }}</span></div>
                                    <div class="text-[14px] mb-3">Accuracy: <span class="font-medium">{{ Math.round(getCurrentSceneData().sku_count.accuracy_count*100) }}%</span></div>
                                </div>
                                <div class="w-35">
                                    <div class="text-[16px] font-bold mb-4">New SKU</div>
                                    <div class="text-[14px] mb-3">Facing: <span class="font-medium">{{ getCurrentSceneData().new_sku_count.facing }}</span></div>
                                    <div class="text-[14px] mb-3">Error: <span class="font-medium">{{ getCurrentSceneData().new_sku_count.error }}</span></div>
                                    <div class="text-[14px] mb-3">Accuracy: <span class="font-medium">{{ Math.round(getCurrentSceneData().new_sku_count.accuracy_count*100) }}%</span></div>
                                </div>
                                <div class="w-35" v-if="skuSummaryData.task && skuSummaryData.task.is_ori_val">
                                    <div class="text-[16px] font-bold mb-4">Orientation</div>
                                    <div class="text-[14px] mb-3">Facing: <span class="font-medium">{{ getCurrentSceneData().ori_count.facing }}</span></div>
                                    <div class="text-[14px] mb-3">Error: <span class="font-medium">{{ getCurrentSceneData().ori_count.error }}</span></div>
                                    <div class="text-[14px] mb-3">Accuracy: <span class="font-medium">{{ Math.round(getCurrentSceneData().ori_count.accuracy_count*100) }}%</span></div>
                                </div>
                            </div>
                        </template>
                        <template v-else>
                            <div class="flex flex-wrap justify-between gap-2">
                                <div class="text-center p-2 border rounded flex-1 min-w-[30%]">
                                    <div class="text-[18px] font-bold mb-2">{{ priceSummaryData.count.completion }}/{{ priceSummaryData.count.count }}</div>
                                    <div class="text-sm text-gray-500">{{ t('modelValidation.completion') }}</div>
                                </div>
                                <div class="text-center p-2 border rounded flex-1 min-w-[30%]">
                                    <div class="text-[18px] font-bold text-red-500 mb-2">{{ priceSummaryData.count.price_type }}</div>
                                    <div class="text-sm text-gray-500">{{ t('modelValidation.priceType') }}</div>
                                </div>
                                <div class="text-center p-2 border rounded flex-1 min-w-[30%]">
                                    <div class="text-[18px] font-bold text-red-500 mb-2">{{ priceSummaryData.count.price }}</div>
                                    <div class="text-sm text-gray-500">{{ t('modelValidation.price') }}</div>
                                </div>
                            </div>
                            <div class="flex flex-wrap justify-between gap-2 mt-4">
                                <div class="text-center p-2 border rounded flex-1 min-w-[30%]">
                                    <div class="text-[18px] font-bold text-red-500 mb-2">{{ priceSummaryData.count.tag_maping }}</div>
                                    <div class="text-sm text-gray-500">{{ t('modelValidation.tagMaping') }}</div>
                                </div>
                                <div class="text-center p-2 border rounded flex-1 min-w-[30%]">
                                    <div class="text-[18px] font-bold text-red-500 mb-2">{{ priceSummaryData.count.price_value }}</div>
                                    <div class="text-sm text-gray-500">{{ t('modelValidation.priceValue') }}</div>
                                </div>
                                <div class="text-center p-2 border rounded flex-1 min-w-[30%]">
                                    <div class="text-[18px] font-bold text-red-500 mb-2">{{ priceSummaryData.count.price_tag_class }}</div>
                                    <div class="text-sm text-gray-500">{{ t('modelValidation.priceTagClass') }}</div>
                                </div>
                            </div>
                        </template>
                    </div>
                    <div class="bg-white rounded-lg mt-4 p-4 w-[448px] h-45 overflow-x-auto" v-else-if="queryParams.type === 'view'">
                        <div class="flex flex-row justify-between items-center mt-4 gap-4 min-w-max">
                            <div class="w-35">
                                <div class="text-[16px] font-bold mb-4">Info</div>
                                <div class="text-[14px] mb-3">Completion: <span class="font-medium">{{ skuSummaryData.task_count.completion }}/{{ skuSummaryData.task_count.count }}</span></div>
                                <div class="text-[14px] mb-3">Missed detection: <span class="font-medium">{{ getCurrentSceneData().missed_detection }}</span></div>
                                <div class="text-[14px] mb-3">Total Facing: <span class="font-medium">{{ getCurrentSceneData().total_facing }}</span></div>
                            </div>
                            <div class="w-35">
                                <div class="text-[16px] font-bold mb-4">SKU</div>
                                <div class="text-[14px] mb-3">Facing: <span class="font-medium">{{ getCurrentSceneData().sku_count.facing }}</span></div>
                                <div class="text-[14px] mb-3">Error: <span class="font-medium">{{ getCurrentSceneData().sku_count.error }}</span></div>
                                <div class="text-[14px] mb-3">Accuracy: <span class="font-medium">{{ Math.round(getCurrentSceneData().sku_count.accuracy_count*100) }}%</span></div>
                            </div>
                            <div class="w-35">
                                <div class="text-[16px] font-bold mb-4">New SKU</div>
                                <div class="text-[14px] mb-3">Facing: <span class="font-medium">{{ getCurrentSceneData().new_sku_count.facing }}</span></div>
                                <div class="text-[14px] mb-3">Error: <span class="font-medium">{{ getCurrentSceneData().new_sku_count.error }}</span></div>
                                <div class="text-[14px] mb-3">Accuracy: <span class="font-medium">{{ Math.round(getCurrentSceneData().new_sku_count.accuracy_count*100) }}%</span></div>
                            </div>
                            <div class="w-35" v-if="skuSummaryData.task && skuSummaryData.task.is_ori_val">
                                <div class="text-[16px] font-bold mb-4">Orientation</div>
                                <div class="text-[14px] mb-3">Facing: <span class="font-medium">{{ getCurrentSceneData().ori_count.facing }}</span></div>
                                <div class="text-[14px] mb-3">Error: <span class="font-medium">{{ getCurrentSceneData().ori_count.error }}</span></div>
                                <div class="text-[14px] mb-3">Accuracy: <span class="font-medium">{{ Math.round(getCurrentSceneData().ori_count.accuracy_count*100) }}%</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 验证区域 -->
                <div class="flex-1 flex flex-col overflow-hidden" v-if="queryParams.type === 'start'">
                    <!-- 产品区域 -->
                    <div class="bg-white rounded-lg">
                        <div class="rounded p-2 flex flex-row">
                            <!-- IR Result Images -->
                            <div class="mb-4 flex-1 p-2 overflow-x-auto min-w-0">
                                <div class="text-[14px] font-medium mb-2">IR Result Images</div>
                                <div class="flex flex-row space-x-2 px-3 py-2 bg-[#f5f5f5] flex-nowrap min-w-max rounded-lg">
                                    <template v-if="irSkuImages.length > 0">
                                        <div v-for="(imageUrl, index) in irSkuImages" :key="'ir-' + index"
                                            class="flex justify-center items-center w-[100px] h-[128px] border border-solid border-[1px] border-[#D9D9D9] bg-gray-50">
                                            <img :src="imageUrl" :alt="`IR Result Images${index + 1}`"
                                                class="max-w-full w-[82px] h-[128px] object-contain">
                                        </div>
                                    </template>
                                    <template v-else>
                                        <div class="flex justify-center items-center w-[100px] h-[128px] border border-solid border-[1px] border-[#D9D9D9] bg-gray-50">
                                            <img :src="sceneObj.img_url" alt="IR Result Images" v-if="sceneObj.img_url&false"
                                                class="max-w-full w-[82px] h-[128px] object-contain">
                                                <span class="text-gray-400 text-sm">No Image</span>
                                        </div>
                                    </template>
                                </div>
                            </div>
                            
                            <!-- Ground Truth Images -->
                            <div class="flex-1 p-2 overflow-x-auto min-w-0">
                                <div class="text-[14px] font-medium mb-2">Ground Truth Images</div>
                                <div class="flex flex-row space-x-2 px-3 py-2 bg-[#f5f5f5] flex-nowrap min-w-max rounded-lg">
                                    <template v-if="gtSkuImages.length > 0">
                                        <div v-for="(imageUrl, index) in gtSkuImages" :key="'gt-' + index"
                                            class="flex justify-center items-center w-[100px] h-[128px] border border-solid border-[1px] border-[#D9D9D9] bg-gray-50">
                                            <img :src="imageUrl" :alt="`Ground Truth Images${index + 1}`"
                                                class="max-w-full w-[82px] h-[128px] object-contain">
                                        </div>
                                    </template>
                                    <template v-else>
                                        <div class="flex justify-center items-center w-[100px] h-[128px] border border-solid border-[1px] border-[#D9D9D9] bg-gray-50">
                                            <img :src="sceneObj.img_url" alt="Ground Truth Images" v-if="sceneObj.img_url&false"
                                                class="max-w-full w-[82px] h-[128px] object-contain">
                                                <span class="text-gray-400 text-sm">No Image</span>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white w-full h-[74vh] flex-grow rounded-lg mt-4 p-4 relative flex flex-col">
                        <!-- 验证区域 -->
                        <div class="flex flex-wrap justify-between items-center mb-2 gap-2 w-100%">
                            <div class="text-[14px] font-medium">{{ t('modelValidation.validation')  }}</div>
                            <div class="flex flex-wrap items-center gap-1">
                                <span class="mr-2 text-[14px]">{{ t('modelValidation.showAll') }}</span>
                                <a-switch v-model:checked="showAll" />
                                <template v-if="queryParams.sceneType.includes('SKU')">
                                    <a-button type="text" class="ml-2" @click="pushListPage" v-if="queryParams.requeue === '0'&false">
                                        <template #icon>
                                            <img src="@/assets/images/push.svg" alt="push" width="24" height="24" />
                                        </template>
                                    </a-button>
                                    <a-button type="text" @click="toggleDeleteMode">
                                        <template #icon>
                                            <img :src="deleteIconSrc" alt="delete" width="24" height="24" />
                                        </template>
                                    </a-button>
                                </template>
                                <a-popover placement="bottomRight" trigger="click" :open="showPriceCheckPopover"
                                    @openChange="handlePriceCheckPopoverChange">
                                    <template #content>
                                        <div class="p-4 min-w-[300px]">
                                            <div class="text-[16px] font-medium mb-4">Price Check Form</div>
                                            <div class="mb-4">
                                                <div class="text-[14px] font-medium mb-2">Missed Tag Detection</div>
                                                <a-input-number v-model:value="priceCheckForm.missedTagDetection"
                                                    :min="0" class="w-full" />
                                            </div>
                                            <div class="mb-4">
                                                <div class="text-[14px] font-medium mb-2">Missed Price Value</div>
                                                <a-input-number v-model:value="priceCheckForm.missedPriceValue" :min="0"
                                                    class="w-full" />
                                            </div>
                                            <div class="mb-4">
                                                <div class="text-[14px] font-medium mb-2">Price&Position</div>
                                                <div class="flex gap-4">
                                                    <a-checkbox v-model:checked="priceCheckForm.noPrice">No Price</a-checkbox>
                                                    <a-checkbox v-model:checked="priceCheckForm.noPosition">No Position</a-checkbox>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                    <a-button type="text" class="ml-2" v-if="!queryParams.sceneType.includes('SKU')">
                                        <template #icon>
                                            <img src="@/assets/images/path.svg" alt="setting" width="20" height="20" />
                                        </template>
                                    </a-button>
                                </a-popover>

                                <a-popover placement="bottomRight" trigger="click" :open="showColumnSettingsPopover"
                                    @openChange="handleColumnSettingsPopoverChange" v-if="false">
                                    <template #content>
                                        <div class="p-4 min-w-[600px]">
                                            <div class="flex justify-between items-center mb-4">
                                                <div class="text-[16px] font-medium">Select List Field</div>
                                                <a-button type="text" @click="handleColumnSettingsCancel">
                                                    <template #icon>
                                                        <CloseOutlined />
                                                    </template>
                                                </a-button>
                                            </div>
                                            <div class="flex gap-6">
                                                <!-- 左侧：可选字段 -->
                                                <div class="flex-1">
                                                    <div class="text-[14px] font-medium mb-3">Optional Fields</div>
                                                    <div
                                                        class="space-y-2 p-4 border border-solid border-[1px] border-[#D9D9D9]">
                                                        <div v-for="column in allColumns" :key="column.key">
                                                            <a-checkbox
                                                                v-model:checked="tempColumnVisibility[column.key]"
                                                                @change="handleTempColumnChange(column.key, $event.target.checked)">
                                                                {{ column.title }}
                                                            </a-checkbox>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- 右侧：已选字段 -->
                                                <div class="flex-1">
                                                    <div class="text-[14px] font-medium mb-3">Selected Fields</div>
                                                    <div
                                                        class="space-y-2 p-4 border border-solid border-[1px] border-[#D9D9D9]">
                                                        <div v-for="column in tempSelectedColumns" :key="column.key"
                                                            class="flex items-center justify-between bg-gray-50 p-2 rounded">
                                                            <span class="text-[14px]">{{ column.title }}</span>
                                                            <a-button type="text" size="small"
                                                                @click="removeTempSelectedColumn(column.key)">
                                                                <template #icon>
                                                                    <CloseOutlined />
                                                                </template>
                                                            </a-button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex justify-end gap-2 mt-6 pt-4 border-t">
                                                <a-button @click="handleColumnSettingsCancel">Cancel</a-button>
                                                <a-button type="primary" @click="handleColumnSettingsOk">Save</a-button>
                                            </div>
                                        </div>
                                    </template>
                                    <a-button type="text">
                                        <template #icon>
                                            <img src="@/assets/images/setting-field.svg" alt="field" width="24"
                                                height="24" />
                                        </template>
                                    </a-button>
                                </a-popover>

                                <a-button type="primary" class="ml-2" @click="addUndetectedItem"
                                    v-if="queryParams.sceneType.includes('SKU')">+ {{t('modelValidation.undetected') }}
                                </a-button>
                            </div>
                        </div>

                        <!-- 验证表格 -->
                        <div class="flex-grow overflow-x-auto lg:w-[840px] xl:w-100%"
                            v-if="queryParams.sceneType.includes('SKU')"
                            style="min-height: 400px; overflow-y: auto; margin-bottom: 60px;">
                            <a-table ref="validationTable" :columns="validationColumns" :loading="loading"
                                :data-source="validationSkuData" :pagination="false" size="small" bordered
                                :defaultSortOrder="'ascend'" :sortDirections="['ascend', 'descend']"
                                @change="handleTableChange" :scroll="{ y: 500, x: 1200 }"
                                :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange, type: 'checkbox', 
                                    hideSelectAll: false, getCheckboxProps: (record) => ({ disabled: !(record.isNewItem || record.add_from === 1) }) }"
                                :custom-row="customSkuRow" :row-class-name="getRowClassName" class="custom-table-48px">
                                <template #bodyCell="{ column, record }">
                                    <template v-if="column.dataIndex === 'group_id'">
                                        <div>
                                            <a-input
                                                v-if="editableData[record.key] && editableData[record.key].group_id !== undefined"
                                                v-model:value="editableData[record.key].group_id"
                                                @pressEnter="saveEdit(record.key)" @blur="saveEdit(record.key)"
                                                :data-key="record.key" data-field="group_id"
                                                size="small" class="w-full" />
                                            <div v-else class="editable-cell"
                                                @click="(record.isNewItem || record.add_from === 1) && !isDeleteMode && edit(record.key, 'group_id', record.group_id)">
                                                {{ record.group_id }}
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="column.dataIndex === 'block_id'">
                                        <div>
                                            <a-input
                                                v-if="editableData[record.key] && editableData[record.key].block_id !== undefined"
                                                v-model:value="editableData[record.key].block_id"
                                                @pressEnter="saveEdit(record.key)" @blur="saveEdit(record.key)"
                                                :data-key="record.key" data-field="block_id"
                                                size="small" class="w-full" />
                                            <div v-else class="editable-cell"
                                                @click="(record.isNewItem || record.add_from === 1) && !isDeleteMode && edit(record.key, 'block_id', record.block_id)">
                                                {{ record.block_id }}
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="column.dataIndex === 'shelf'">
                                        <div>
                                            <a-input
                                                v-if="editableData[record.key] && editableData[record.key].shelf !== undefined"
                                                v-model:value="editableData[record.key].shelf"
                                                @pressEnter="saveEdit(record.key)" @blur="saveEdit(record.key)"
                                                :data-key="record.key" data-field="shelf"
                                                size="small" class="w-full" />
                                            <div v-else class="editable-cell"
                                                @click="(record.isNewItem || record.add_from === 1) && !isDeleteMode && edit(record.key, 'shelf', record.shelf)">
                                                {{ record.shelf }}
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="column.dataIndex === 'column'">
                                        <div>
                                            <a-input
                                                v-if="editableData[record.key] && editableData[record.key].column !== undefined"
                                                v-model:value="editableData[record.key].column"
                                                @pressEnter="saveEdit(record.key)" @blur="saveEdit(record.key)"
                                                :data-key="record.key" data-field="column"
                                                size="small" class="w-full" />
                                            <div v-else class="editable-cell"
                                                @click="(record.isNewItem || record.add_from === 1) && !isDeleteMode && edit(record.key, 'column', record.column)">
                                                {{ record.column }}
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="column.dataIndex === 'stack'">
                                        <div>
                                            <a-input
                                                v-if="editableData[record.key] && editableData[record.key].stack !== undefined"
                                                v-model:value="editableData[record.key].stack"
                                                @pressEnter="saveEdit(record.key)" @blur="saveEdit(record.key)"
                                                :data-key="record.key" data-field="stack"
                                                size="small" class="w-full" />
                                            <div v-else class="editable-cell"
                                                @click="(record.isNewItem || record.add_from === 1) && !isDeleteMode && edit(record.key, 'stack', record.stack)">
                                                {{ record.stack }}
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="column.dataIndex === 'ir_result'">
                                        <div>
                                            <a-input
                                                v-if="editableData[record.key] && editableData[record.key].ir_result !== undefined"
                                                v-model:value="editableData[record.key].ir_result"
                                                @pressEnter="saveEdit(record.key)" @blur="saveEdit(record.key)"
                                                :data-key="record.key" data-field="ir_result"
                                                size="small" class="w-full" />
                                            <div v-else class="editable-cell"
                                                @click="!isDeleteMode && edit(record.key, 'ir_result', record.ir_result)">
                                                {{ record.ir_result }}
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="column.dataIndex === 'gt_result'">
                                        <div>
                                            <a-select
                                                v-if="editableData[record.key] && editableData[record.key].gt_result !== undefined"
                                                v-model:value="editableData[record.key].gt_result"
                                                @blur="saveEdit(record.key)" size="small" class="w-full"
                                                :data-key="record.key" data-field="gt_result"
                                                :options="groundTruthOptions" show-search :filter-option="filterOption"
                                                placeholder="Please Select Ground Truth" @change="(value) => handleTruthChange(value, record)"/>
                                            <div v-else class="editable-cell"
                                                @click="!isDeleteMode && edit(record.key, 'gt_result', record.gt_result)">
                                                {{ record.gt_result }}
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="column.dataIndex === 'orientation_gt'">
                                        <div>
                                            <a-select
                                                v-if="editableData[record.key] && editableData[record.key].orientation_gt !== undefined"
                                                v-model:value="editableData[record.key].orientation_gt"
                                                @blur="saveEdit(record.key)" size="small" class="w-full"
                                                :data-key="record.key" data-field="orientation_gt"
                                                :options="orientationOptions" placeholder="Please Select Orientation GT" @change="(value) => handleGTChange(value, record)" />
                                            <div v-else class="editable-cell"
                                                @click="!isDeleteMode && edit(record.key, 'orientation_gt', record.orientation_gt)">
                                                {{ orientationMap[record.orientation_gt] }}
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="column.dataIndex === 'orientation_ir'">
                                        <div>
                                            <a-select
                                                v-if="editableData[record.key] && editableData[record.key].orientation_ir !== undefined"
                                                v-model:value="editableData[record.key].orientation_ir"
                                                @blur="saveEdit(record.key)" size="small" class="w-full"
                                                :data-key="record.key" data-field="orientation_ir"
                                                :options="orientationOptions" placeholder="Please Select Orientation IR" />
                                            <div v-else class="editable-cell"
                                                @click="!isDeleteMode && edit(record.key, 'orientation_ir', record.orientation_ir)">
                                                {{ orientationMap[record.orientation_ir] || record.orientation_ir }}
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="column.dataIndex === 'report'">
                                        <div>
                                            <a-select
                                                v-if="editableData[record.key] && editableData[record.key].report !== undefined"
                                                v-model:value="editableData[record.key].report"
                                                @blur="saveEdit(record.key)" size="small" class="w-full"
                                                :data-key="record.key" data-field="report"
                                                :options="reportOptions" show-search :filter-option="filterOption"
                                                placeholder="Please Select Report" />
                                            <div v-else class="editable-cell"
                                                @click="!isDeleteMode && edit(record.key, 'report', record.report)">
                                                {{ getReportLabel(record.report) }}
                                            </div>
                                        </div>
                                    </template>
                                </template>
                            </a-table>
                        </div>

                        <div class="flex-grow overflow-x-auto lg:w-[840px] xl:w-100%" v-else
                            style="min-height: 400px; overflow-y: auto; overflow-x: auto; margin-bottom: 60px;">
                            <a-table ref="validationTable" :columns="validationColumns" :loading="loading"
                                :data-source="validationPriceData" :pagination="false" size="small" bordered
                                :defaultSortOrder="'ascend'" :sortDirections="['ascend', 'descend']"
                                @change="handleTableChange" :scroll="{ y: 500, x: 1200 }"
                                :row-selection="isDeleteMode ? { selectedRowKeys: selectedRowKeys, onChange: onSelectChange, type: 'checkbox', 
                                    hideSelectAll: false, columnWidth: 0, getCheckboxProps: (record) => ({ disabled: !record.isNewItem }) } : null"
                                :custom-row="customRow" :row-class-name="getRowClassName" @row-click="handleRowClick" class="custom-table-48px">
                                <template #bodyCell="{ column, record }">
                                    <!-- 所有列都支持编辑 -->
                                    <template v-if="column.dataIndex === 'price_gt'">
                                        <div>
                                            <a-input
                                                v-if="editableData[record.key] && editableData[record.key].price_gt!== undefined"
                                                v-model:value="editableData[record.key].price_gt"
                                                @pressEnter="saveEdit(record.key)" @blur="saveEdit(record.key)"
                                                size="small" class="w-full" />
                                            <div v-else class="editable-cell"
                                                @click="!isDeleteMode && edit(record.key,'price_gt', record.price_gt)">
                                                {{ record.price_gt }}
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="column.dataIndex === 'price_tag_mapping_gt'">
                                        <div>
                                            <a-input
                                                v-if="editableData[record.key] && editableData[record.key].price_tag_mapping_gt!== undefined"
                                                v-model:value="editableData[record.key].price_tag_mapping_gt"
                                                @pressEnter="saveEdit(record.key)" @blur="saveEdit(record.key)"
                                                size="small" class="w-full" />
                                            <div v-else class="editable-cell"
                                                @click="!isDeleteMode && edit(record.key,'price_tag_mapping_gt', record.price_tag_mapping_gt)">
                                                {{ record.price_tag_mapping_gt }}
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="column.dataIndex === 'price_tag_class_gt'">
                                        <div>
                                            <a-select
                                                v-if="editableData[record.key] && editableData[record.key].price_tag_class_gt !== undefined"
                                                v-model:value="editableData[record.key].price_tag_class_gt"
                                                @pressEnter="saveEdit(record.key)" @blur="saveEdit(record.key)"
                                                size="small" class="w-full" :options="priceTagClassOptions" />
                                            <div v-else class="editable-cell"
                                                @click="!isDeleteMode && edit(record.key, 'price_tag_class_gt', record.price_tag_class_gt)">
                                                {{ record.price_tag_class_gt || ' ' }}
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="column.dataIndex === 'crossed_price_gt'">
                                        <div>
                                            <a-select
                                                v-if="editableData[record.key] && editableData[record.key].crossed_price_gt !== undefined"
                                                v-model:value="editableData[record.key].crossed_price_gt"
                                                @pressEnter="saveEdit(record.key)" @blur="saveEdit(record.key)"
                                                size="small" class="w-full" :options="crossedPriceGtOptions" />
                                            <div v-else class="editable-cell"
                                                @click="!isDeleteMode && edit(record.key, 'crossed_price_gt', record.crossed_price_gt)">
                                                {{ record.crossed_price_gt || ' ' }}
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="column.dataIndex === 'price_type_gt'">
                                        <div>
                                            <a-select
                                                v-if="editableData[record.key] && editableData[record.key].price_type_gt !== undefined"
                                                v-model:value="editableData[record.key].price_type_gt"
                                                @pressEnter="saveEdit(record.key)" @blur="saveEdit(record.key)"
                                                size="small" class="w-full" :options="priceTypeGtOptions" />
                                            <div v-else class="editable-cell"
                                                @click="!isDeleteMode && edit(record.key, 'price_type_gt', record.price_type_gt)">
                                                {{ record.price_type_gt || ' ' }}
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="column.dataIndex === 'price_bg_color_gt'">
                                        <div>
                                            <a-select
                                                v-if="editableData[record.key] && editableData[record.key].price_bg_color_gt !== undefined"
                                                v-model:value="editableData[record.key].price_bg_color_gt"
                                                @pressEnter="saveEdit(record.key)" @blur="saveEdit(record.key)"
                                                size="small" class="w-full" :options="priceBgGtOptions" />
                                            <div v-else class="editable-cell"
                                                @click="!isDeleteMode && edit(record.key, 'price_bg_color_gt', record.price_bg_color_gt)">
                                                {{ record.price_bg_color_gt || ' ' }}
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="column.dataIndex === 'price_location_gt'">
                                        <div>
                                            <a-select
                                                v-if="editableData[record.key] && editableData[record.key].price_location_gt !== undefined"
                                                v-model:value="editableData[record.key].price_location_gt"
                                                @pressEnter="saveEdit(record.key)" @blur="saveEdit(record.key)"
                                                size="small" class="w-full" :options="priceLocationGtOptions" />
                                            <div v-else class="editable-cell"
                                                @click="!isDeleteMode && edit(record.key, 'price_location_gt', record.price_location_gt)">
                                                {{ record.price_location_gt || ' ' }}
                                            </div>
                                        </div>
                                    </template>
                                    <template v-if="column.dataIndex === 'price_tag_type_gt'">
                                        <div>
                                            <a-select
                                                v-if="editableData[record.key] && editableData[record.key].price_tag_type_gt !== undefined"
                                                v-model:value="editableData[record.key].price_tag_type_gt"
                                                @pressEnter="saveEdit(record.key)" @blur="saveEdit(record.key)"
                                                size="small" class="w-full" mode="multiple"
                                                :options="priceTagTypeGtOptions" placeholder="选择标签类型" />
                                            <div v-else class="editable-cell"
                                                @click="!isDeleteMode && edit(record.key, 'price_tag_type_gt', record.price_tag_type_gt)">
                                                {{ Array.isArray(record.price_tag_type_gt) ?
                                                record.price_tag_type_gt.join(', ') : (record.price_tag_type_gt || ' ')
                                                }}
                                            </div>
                                        </div>
                                    </template>
                                    <template v-else-if="column.ellipsis">
                                        <a-tooltip :title="record[column.dataIndex]" placement="topLeft">
                                            <span>{{ record[column.dataIndex] }}</span>
                                        </a-tooltip>
                                    </template>

                                </template>
                            </a-table>
                        </div>
                        <!-- 底部按钮 -->
                        <div class="flex flex-wrap justify-end gap-2 mt-4 absolute bottom-5 right-4">
                            <a-button class="bg-[#FAAD14] text-white" @click="showSaveModal = true"
                                v-if="queryParams.sceneType.includes('SKU')">
                                {{ t('modelValidation.save') }}
                            </a-button>
                            <a-button @click="showSaveFinish" class="text-[#1677FF] border-[#1677FF] border solid">{{ t('modelValidation.saveAndFinish') }}</a-button>
                            <a-button type="primary" @click="handleCheckSubmit"
                                v-if="queryParams.sceneType.includes('SKU')">
                                {{ t('modelValidation.saveAndNext') }}
                            </a-button>
                            <a-button type="primary" @click="showPriceCheckModal = true" v-else>
                                {{ t('modelValidation.saveAndNext') }}
                            </a-button>
                        </div>
                    </div>
                </div>
                <div class="flex-1 h-full flex flex-col overflow-hidden" v-else>
                    <div class="bg-white rounded-lg p-4 flex-grow flex flex-col h-[92vh]">
                        <!-- Scene列表区域 -->
                        <div class="text-base font-medium mb-2 flex items-center">
                            <span class="inline-block rounded-[2px] bg-black w-[4px] h-[18px]"></span>
                            <span class="ml-2 my-2 text-[14px]">{{ $t('modelValidation.scene') }}</span>
                        </div>
                        <div class="table-container flex-grow w-full" style="max-height: 360px;position: relative;">
                            <a-table :columns="sceneColumns" :loading="loading" :data-source="sceneData"
                                :pagination="false" size="small" bordered 
                                :scroll="{ y: 300, x: 900 }"
                                :row-class-name="getSceneRowClassName" :custom-row="customSceneRow">
                                <template #bodyCell="{ column, record }">
                                    <template v-if="column.dataIndex === 'validator_id'">
                                            {{ getUserName(record.validator_id) }}
                                    </template>
                                    <template v-if="column.dataIndex === 'validation_status'">
                                        <div class="flex items-center">
                                            <div class="w-2 h-2 rounded-full mr-2"
                                                :class="record.validation_status ? 'bg-green-500' : 'bg-blue-500'">
                                            </div>
                                            <span>{{ record.validation_status ? 'Verified':'Unverified' }}</span>
                                        </div>
                                    </template>
                                    <template v-else-if="column.ellipsis">
                                        <a-tooltip :title="record[column.dataIndex]" placement="topLeft">
                                            <span>{{ record[column.dataIndex] }}</span>
                                        </a-tooltip>
                                    </template>
                                </template>
                            </a-table>
                        </div>

                        <div class="text-base font-medium mb-2 flex items-center">
                            <span class="inline-block rounded-[2px] bg-black w-[4px] h-[18px]"></span>
                            <span class="ml-2 my-2 text-[14px]">{{ $t('modelValidation.sku') }}</span>
                        </div>
                        <div class="table-container flex-grow w-full" style="max-height: 400px; position: relative;">
                            <a-table :columns="sceneSkuColumns" :loading="loading" :data-source="sceneSkuListData"
                                :pagination="false" size="small" bordered 
                                :scroll="{ y: 300, x: 900 }"
                                >
                                <template #bodyCell="{ column, record }">
                                    <template v-if="column.dataIndex === 'validator_id'">
                                            {{ getUserName(record.validator_id) }}
                                    </template>
                                    <template v-if="column.dataIndex === 'validation_status'">
                                        <div class="flex items-center">
                                            <div class="w-2 h-2 rounded-full mr-2"
                                                :class="record.validation_status ? 'bg-green-500' : 'bg-blue-500'">
                                            </div>
                                            <span>{{ record.validation_status ? 'Verified':'Unverified' }}</span>
                                        </div>
                                    </template>
                                    <template v-else-if="column.ellipsis">
                                        <a-tooltip :title="record[column.dataIndex]" placement="topLeft">
                                            <span>{{ record[column.dataIndex] }}</span>
                                        </a-tooltip>
                                    </template>
                                </template>
                            </a-table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 图片质量选择弹窗 -->
    <common-modal v-model:modelValue="showImageQualityModal" :title="t('modelValidation.selectImageQuality')"
        :width="700" :custom-footer=false :cancel-text="t('common.cancel')" :ok-text="t('common.save')"
        @cancel="handleImageQualityCancel" @ok="handleImageQualityOk">
        <div class="grid grid-cols-2 gap-4">
            <div>
                <a-checkbox v-model:checked="imageQualityOptions['16']">{{ t('modelValidation.imageBlur') }}</a-checkbox>
            </div>
            <div>
                <a-checkbox v-model:checked="imageQualityOptions['13']">{{ t('modelValidation.pictureOfPicture') }}</a-checkbox>
            </div>
            <div>
                <a-checkbox v-model:checked="imageQualityOptions['15']">{{ t('modelValidation.capturedFromBadAngle') }}</a-checkbox>
            </div>
            <div>
                <a-checkbox v-model:checked="imageQualityOptions['36']">{{ t('modelValidation.duplicateImages') }}</a-checkbox>
            </div>
            <div>
                <a-checkbox v-model:checked="imageQualityOptions['25']">{{ t('modelValidation.incompleteSceneType') }}</a-checkbox>
            </div>
            <div>
                <a-checkbox v-model:checked="imageQualityOptions['33']">{{ t('modelValidation.imageNotAvailable') }}</a-checkbox>
            </div>
            <div>
                <a-checkbox v-model:checked="imageQualityOptions['34']">{{ t('modelValidation.incorrectUseOfNextDoor') }}</a-checkbox>
            </div>
            <div>
                <a-checkbox v-model:checked="imageQualityOptions['38']">{{ t('modelValidation.blockingObstacles') }}</a-checkbox>
            </div>
            <div>
                <a-checkbox v-model:checked="imageQualityOptions['35']">{{ t('modelValidation.noUseOfNextDoor') }}</a-checkbox>
            </div>
            <div>
                <a-checkbox v-model:checked="imageQualityOptions['2']">{{ t('modelValidation.bodyPart') }}</a-checkbox>
            </div>
            <div>
                <a-checkbox v-model:checked="imageQualityOptions['21']">{{ t('modelValidation.notEnoughOverlap') }}</a-checkbox>
            </div>
            <div>
                <a-checkbox v-model:checked="imageQualityOptions['32']">{{ t('modelValidation.junkImage') }}</a-checkbox>
            </div>
            <div>
                <a-checkbox v-model:checked="imageQualityOptions['37']">{{ t('modelValidation.wrongImageSequence') }}</a-checkbox>
            </div>
            <div>
                <a-checkbox v-model:checked="imageQualityOptions['4']">{{ t('modelValidation.tooDark') }}</a-checkbox>
            </div>
            <div>
                <a-checkbox v-model:checked="imageQualityOptions['31']">{{ t('modelValidation.productsCapturedOutOfScene') }}</a-checkbox>
            </div>
            <div>
                <a-checkbox v-model:checked="imageQualityOptions['5']">{{ t('modelValidation.tooBright') }}</a-checkbox>
            </div>
            <div>
                <a-checkbox v-model:checked="imageQualityOptions['20']">{{ t('modelValidation.mixedImagesInSingleScene') }}</a-checkbox>
            </div>
            <div>
                <a-checkbox v-model:checked="imageQualityOptions['39']">{{ t('modelValidation.mvStitchingIssue') }}</a-checkbox>
            </div>
            <div>
                <a-checkbox v-model:checked="imageQualityOptions['40']">{{ t('modelValidation.mvWrongOrientation') }}</a-checkbox>
            </div>
            <div>
                <a-checkbox v-model:checked="imageQualityOptions['27']">{{ t('modelValidation.closedDoor') }}</a-checkbox>
            </div>
            <div>
                <a-checkbox v-model:checked="imageQualityOptions['26']">{{ t('modelValidation.multipleDisconnectedPos') }}</a-checkbox>
            </div>
            <div>
                <a-checkbox v-model:checked="imageQualityOptions['42']">{{ t('modelValidation.emptyCoolersShelves') }}</a-checkbox>
            </div>
        </div>
    </common-modal>

    <!-- 行数输入弹窗 -->
    <common-modal v-model:modelValue="showSaveModal" :title="t('modelValidation.save')" :width="400" :cancel-text="t('common.cancel')"
        :ok-text="t('common.save')" :custom-footer=false @cancel="handleSaveCancel" @ok="handleSaveOk">
        <div class="flex items-center mb-4">
            <span class="text-[14px] font-medium">{{ t('modelValidation.numberOfLine') }}</span>
            <span class="text-red-500 ml-1">*</span>
        </div>
        <a-input-number v-model:value="lineNumber" :min="1" class="w-full" :status="!lineNumber ? 'error' : ''" />
    </common-modal>

    <!-- 价格检查表单弹窗 -->
    <price-check-form-modal v-model:modelValue="showPriceCheckModal" :initial-data="priceCheckFormData"
        :custom-footer=false :ok-text="t('common.save')" @cancel="handlePriceCheckCancel"
        @ok="handlePriceCheckSubmit" />
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed, reactive, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { message, Modal } from 'ant-design-vue'
import { CloseOutlined } from '@ant-design/icons-vue'
import CommonModal from '@/components/common/CommonModal.vue';
import HotFigure from '@/components/HotFigure.vue';
import PriceCheckFormModal from './components/PriceCheckFormModal.vue';
import {
    validationTaskSkuApi,
    validationTaskSceneApi,
    validationTaskDetailApi,
    validationTaskSkuSaveApi,
    validationTaskPriceSaveApi,
    qaImgCheckApi, skuMapApi,
    saveScenesApi,
    skuImgApi,
    skuSummaryApi,
    priceSummaryApi,
    sceneSkuListApi
} from '@/api/task/index';
// import { useAutoUserActionTracking } from '@/composable/useUserActionTracking';
import { useUserDataStore } from '@/store/userDataStore';
import { storeToRefs } from 'pinia';

const store = useUserDataStore();
const { userData } = storeToRefs(store);
// @ts-ignore
import deleteIcon from '@/assets/images/delete.svg';
// @ts-ignore
import deleteHoverIcon from '@/assets/images/delete-hover.svg';
const { t } = useI18n();
const loading = ref(true);
const router = useRouter();
const validationTable = ref(null);
const showAll = ref(true);
const showImageQualityModal = ref(false);
const showSaveModal = ref(false);
const showPriceCheckPopover = ref(false);
const showPriceCheckModal = ref(false);
const showColumnSettingsPopover = ref(false); // 表格字段设置悬浮菜单
const lineNumber = ref(1); // 默认行数为1
const isDeleteMode = ref(false); // 删除模式标志
const selectedRowKeys = ref([]); // 选中的行keys
const currentSkuImages = ref([]); // 当前选中SKU的图片URL数组
const currentImageIndex = ref(0); // 当前显示的图片索引
const initialSkuImages = ref([]); // 第一次点击时的SKU图片，后续保持不变
const initialImageIndex = ref(0); // 第一次点击时的图片索引，后续保持不变
const irSkuImages = ref([]) // IR Result图片
const gtSkuImages = ref([]) // Ground Truth图片
const queryParams = ref({})
queryParams.value = router.currentRoute.value.query;
const hotFigureData = ref({})
const deleteIconSrc = computed(() => {
    if (selectedRowKeys.value.length === 0) {
        return deleteIcon;
    }
    // 根据场景类型确定数据源
    const currentData = queryParams.value.sceneType.includes('SKU') ? validationSkuData.value : validationPriceData.value;
    
    // 检查选中的项目是否都是新添加的数据
    const selectedItems = currentData.filter(item => selectedRowKeys.value.includes(item.key));
    const allSelectedAreNewItems = selectedItems.length > 0 && selectedItems.every(item => item.isNewItem);
    
    return allSelectedAreNewItems ? deleteHoverIcon : deleteIcon;
});
// SKU统计数据
const skuSummaryData = ref({
    task_count: {
        count: 0,
        completion: 0
    },
    scene_count_list: []
});

// 价格统计数据
const priceSummaryData = ref({
    count: {
        count: 0,
        completion: 0,
        price_type: 0,
        price: 0,
        tag_maping: 0,
        price_value: 0,
        price_tag_class: 0
    }
});

// 获取当前场景的统计数据
const getCurrentSceneData = () => {
    if (selectedSceneId.value && skuSummaryData.value.scene_count_list 
        && skuSummaryData.value.scene_count_list.length > 0) {
        // 根据选中的场景ID查找对应的统计数据
        const sceneData = skuSummaryData.value.scene_count_list.find(scene => 
            scene.scene_id === selectedSceneId.value);
        if (sceneData) {
            return sceneData;
        }
    }
    // 如果没有选中场景或找不到对应数据，返回第一个场景的数据或默认数据
    if (skuSummaryData.value.scene_count_list && 
        skuSummaryData.value.scene_count_list.length > 0) {
        return skuSummaryData.value.scene_count_list[0];
    }
    return {
        scene_id: 0,
        missed_detection: 0,
        total_facing: 0,
        sku_count: {
            facing: 0,
            error: 0,
            accuracy_count: 0
        },
        new_sku_count: {
            facing: 0,
            error: 0,
            accuracy_count: 0
        },
        ori_count: {
            facing: 0,
            error: 0,
            accuracy_count: 0
        }
    };
};

// 更新场景统计数据显示
const updateSceneStatistics = (sceneId) => {
    console.log('Selected scene ID:', sceneId);
};

// 获取SKU统计数据
const getSkuSummaryApi = async () => {
    try {
        const val_task_id = queryParams.value.taskId;
        
        if (!val_task_id) {
            console.error('val_task_id is required for skuSummaryApi');
            return;
        }
        
        const res = await skuSummaryApi(val_task_id);
        const _res = {
                "data": {
                    "task_count": {
                        "count": 2,
                        "completion": 2
                    },
                    "task": {
                        "is_ori_val": true
                    },
                    "scene_count_list": [
                        {
                            "scene_id": 5,
                            "missed_detection": 0,
                            "total_facing": 34,
                            "sku_count": {
                                "facing": 34,
                                "error": 16
                            },
                            "new_sku_count": {
                                "facing": 0,
                                "error": 0
                            },
                            "ori_count": {
                                "facing": 34,
                                "error": 0
                            }
                        },
                        {
                            "scene_id": 6,
                            "missed_detection": 0,
                            "total_facing": 22,
                            "sku_count": {
                                "facing": 22,
                                "error": 10
                            },
                            "new_sku_count": {
                                "facing": 0,
                                "error": 0
                            },
                            "ori_count": {
                                "facing": 22,
                                "error": 1
                            }
                        }
                    ]
                },
                "code": "0000",
                "message": "success"
            }
        if (res && res.data) {
            skuSummaryData.value = res.data;
        }
    } catch (error) {
        console.error('getSkuSummaryApi error:', error);
    }
};

// 获取价格统计数据
const getPriceSummaryApi = async () => {
    try {
        const val_task_id = queryParams.value.taskId;
        const scene_id = sceneObj.value.id;
        
        if (!val_task_id || !scene_id) {
            console.error('val_task_id and scene_id are required for priceSummaryApi');
            return;
        }
        
        const res = await priceSummaryApi(val_task_id, scene_id);
        if (res && res.data) {
            priceSummaryData.value = res.data;
        }
    } catch (error) {
        console.error('getPriceSummaryApi error:', error);
    }
};
// 价格检查表单数据
const priceCheckForm = reactive({
    missedTagDetection: 3,
    missedPriceValue: 3,
    noPrice: true,
    noPosition: false
});

// Price Tag Class GT 下拉选项
const priceTagClassOptions = [
    { label: 'Shelf-type', value: 'Shelf-type' },
    { label: 'Display-type', value: 'Display-type' },
    { label: 'Channel Strip', value: 'Channel Strip' },
    { label: 'Runner Tag', value: 'Runner Tag' }
];

// Crossed Price GT 下拉选项
const crossedPriceGtOptions = [
    { label: 'None', value: 'None' },
    { label: 'Yes', value: 'Yes' },
    { label: 'No', value: 'No' }
];

// Price Type GT 下拉选项
const priceTypeGtOptions = [
    { label: 'Na special characters', value: 'Na special characters' },
    { label: 'Unknown special characters', value: 'Unknown special characters' },
    { label: 'With slash', value: 'With slash' },
    { label: 'Price in Cents', value: 'Price in Cents' }
];

// Price BG GT 下拉选项
const priceBgGtOptions = [
    { label: 'Black', value: 'Black' },
    { label: 'Blue', value: 'Blue' },
    { label: 'Green', value: 'Green' },
    { label: 'Grey', value: 'Grey' },
    { label: 'Orange', value: 'Orange' },
    { label: 'Pink', value: 'Pink' },
    { label: 'Red', value: 'Red' },
    { label: 'White', value: 'White' },
    { label: 'Yellow', value: 'Yellow' }
];

// Price Location GT 下拉选项
const priceLocationGtOptions = [
    { label: 'Primary', value: 'Primary' },
    { label: 'Secondary', value: 'Secondary' }
];

// Price TagType GT 下拉选项（多选）
const priceTagTypeGtOptions = [
    { label: 'discount', value: 'discount' },
    { label: 'deposit', value: 'deposit' },
    { label: 'promo', value: 'promo' },
    { label: 'special', value: 'special' },
    { label: 'Price', value: 'Price' },
    { label: 'standard', value: 'standard' }
];

// Ground Truth 下拉选项（需要从接口获取）
const groundTruthOptions = ref([]);

// Orientation GT 下拉选项
const orientationOptions = [
    { label: 'Front', value: '16546' },
    { label: 'Front 90', value: '16547' },
    { label: 'Side', value: '16548' },
    { label: 'Side 90', value: '16549' },
    { label: 'Top', value: '16550' },
    { label: 'Top 90', value: '16551' },
    { label: 'Untrained', value: '16552' },
];
const getOrientationLabel = (reportId) => {
    if (!reportId) return '';
    const option = orientationOptions.find(item => item.value === reportId);
    return option ? option.label : reportId;
};
// Orientation 映射对象，用于根据数字值显示对应文本
const orientationMap = {
    0: 'Front',
    1: 'Front 90',
    2: 'Side',
    3: 'Side 90',
    4: 'Top',
    5: 'Top 90',
    '-1': 'Untrained'
};

// Report 下拉选项
const reportOptions = [
    { label: 'New Label', value: '16422' },
    { label: 'New Product', value: '16423' },
    { label: 'Wrong Orientation', value: '16588' },
    { label: 'Wrong reference image', value: '16589' },
    { label: 'No Reference Image', value: '16591' },
    { label: 'Missed Detection', value: '16592' },
    { label: 'Product Detected As Empty', value: '16584' },
    { label: 'Empty Detected as Product', value: '16585' },
    { label: 'Combo detected as single', value: '16594' },
    { label: 'K-Clip detected as single', value: '16595' },
    { label: 'Single detected as combo', value: '16596' },
    { label: 'Multi detection in single product', value: '16597' },
    { label: 'Mapping Issue', value: '16598' },
    { label: 'No Ref image for Merged SKU', value: '16655' },
    { label: 'No Ref image for New Label', value: '16654' },
    { label: 'Wrong SKU Name', value: '16621' },
    { label: 'Incomplete SKU Name', value: '16622' },
    { label: 'Partially visible product', value: '16627' },
    { label: 'SKU Name in foreign language', value: '16629' },
    { label: 'Empty Assets', value: '16633' },
    { label: 'Unwanted/Wrong detection', value: '16653' },
    { label: 'POP faded/damaged', value: '16770' },
    { label: 'Blur Product/SKU', value: '16801' }
];
const truthChange = ref(false);
const modifiedGtOutSkuIdForDisplay = ref(null);
const isLoadingSkuImages = ref(false); // 是否正在加载SKU图片

// 根据 report ID 获取对应的标签文本
const getReportLabel = (reportId) => {
    if (!reportId) return '';
    const option = reportOptions.find(item => item.value === reportId);
    return option ? option.label : reportId;
};
const handleTruthChange = async (selectedValue, record) => {
    const item = skuTruthMap.value.find(item => item.sku_name === selectedValue);
    if (item && item.out_sku_id) {
        modifiedGtOutSkuIdForDisplay.value = item.out_sku_id; // 存储修改后的ID

        // 更新 gt_result 字段用于保存
        const skuIndex = validationSkuData.value.findIndex(i => i.key === record.key);
        if (skuIndex !== -1) {
            validationSkuData.value[skuIndex].gt_result = selectedValue; // 更新选择的GT名称
            // 保存修改后的 out_sku_id 到 validationSkuData 以便提交
            validationSkuData.value[skuIndex].out_sku_id = item.out_sku_id;
            validationSkuData.value[skuIndex].gt_out_sku_id = item.out_sku_id;
        }

        isLoadingSkuImages.value = true;
        try {
            const res = await skuImgApi(item.out_sku_id); // 使用修改后的ID获取图片
            if (res.data && res.data.imgs && res.data.imgs.length > 0) {
                gtSkuImages.value = res.data.imgs; //  显示修改后的图片
            } else {
                gtSkuImages.value = [];
            }
            currentImageIndex.value = 0;
            truthChange.value = true; // 标记GT已修改
        } catch (error) {
            console.error('获取SKU图片(truthChange)失败:', error);
            gtSkuImages.value = [];
            currentImageIndex.value = 0;
        } finally {
            isLoadingSkuImages.value = false;
        }
    }
};
const handleGTChange = (selectedValue, record) => {
    record.orientation_gt = selectedValue+'';
}
// 下拉选择过滤函数
const filterOption = (input, option) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};
const skuTruthMap = ref([]);
// 获取 Ground Truth 选项
const fetchGroundTruthOptions = async () => {
    try {
        if(sceneObj.value.out_project_id) {
            const res = await skuMapApi(sceneObj.value?.out_project_id);
            skuTruthMap.value = res.data.data_list;
            groundTruthOptions.value = res.data.data_list.map(item => ({
                label: item.sku_name,
                value: item.sku_name
            }));
        } else {
            loading.value = false;
            console.error('sceneObj.value.out_project_id is undefined');
        }
    } catch (error) {
        loading.value = false;
        console.error('获取Ground Truth选项失败:', error);
    }
};
// Scene表格列定义
const sceneColumns = ref([
    {
        title: 'Scene',
        dataIndex: 'name',
        key: 'name',
        width: 80
    },
    {
        title: 'Subscene Type',
        dataIndex: 'sub_scene_type',
        key: 'sub_scene_type',
        width: 100,
        ellipsis: true,
    },
    {
        title: 'SceneId',
        dataIndex: 'out_scene_id',
        key: 'out_scene_id',
        ellipsis: true,
        width: 150,
    },
    {
        title: 'Validator',
        dataIndex: 'validator_id',
        key: 'validator_id',
        width: 150,
    },
    {
        title: 'Verification Status',
        dataIndex: 'validation_status',
        key: 'validation_status',
        width: 100,
        ellipsis: true,
    },
    {
        title: 'Total Facings',
        dataIndex: 'total_facings',
        key: 'total_facings',
        width: 150
    },
    {
        title: 'Missed detection',
        dataIndex: 'missed_detection',
        key: 'missed_detection',
        width: 150
    },
]);
// Scene表格数据
const sceneData = ref([]);
// 当前选中的场景ID
const selectedSceneId = ref(null);
const getTaskSceneDetail = async () => {
    try {
        const res = await validationTaskDetailApi(queryParams.value.taskId);
        const _res = {
            "data": {
                "status": 2,
                "creator_id": 0,
                "project_id": 6,
                "start_time": 1747712484,
                "name": "",
                "type": "",
                "expect_deadline": 1747712484,
                "sub_task_name": "",
                "extra_info": {},
                "id": 2,
                "created_at": 1747020823,
                "updated_at": 1747712484,
                "finished_at": null,
                "scene_total": 0,
                "scene_done": 0,
                "scene_list": [
                    {
                        "scene": "Frozen Meals Home Set",
                        "subscene_type": "Freezer Door",
                        "sceneid": "3837c10c-7...",
                        "total_facings": 1977,
                        "missed_detection": 10,
                        "out_project_id": "",
                        "name": "Frozen Meals Home Set",
                        "type": "",
                        "sub_scene_type": "",
                        "img_received_on": "string",
                        "img_url": "string",
                        "total_imgs": 0,
                        "out_scene_id": "3837c10c3837c10cs",
                        "validation_status": 0,
                        "id": 0,
                        "created_at": 1747020823,
                        "updated_at": 1747020823
                    },
                    {
                        "scene": "Frozen Meals Home Set",
                        "subscene_type": "Freezer Door",
                        "sceneid": "3837c10c-7...",
                        "total_facings": 1977,
                        "missed_detection": 109,
                        "out_project_id": "",
                        "name": "Frozen Meals Home Set",
                        "type": "",
                        "sub_scene_type": "",
                        "img_received_on": "string",
                        "img_url": "string",
                        "total_imgs": 0,
                        "out_scene_id": "3837c10c3837c10cs",
                        "validation_status": 1,
                        "id": 1,
                        "created_at": 1747020823,
                        "updated_at": 1747020823
                    },
                    {
                        "scene": "Frozen Meals Home Set",
                        "subscene_type": "Freezer Door",
                        "sceneid": "3837c10c-7...",
                        "total_facings": 1977,
                        "missed_detection": 109,
                        "out_project_id": "",
                        "name": "Frozen Meals Home Set",
                        "type": "",
                        "sub_scene_type": "",
                        "img_received_on": "string",
                        "img_url": "string",
                        "total_imgs": 0,
                        "out_scene_id": "3837c10c3837c10cs",
                        "validation_status": 1,
                        "id": 2,
                        "created_at": 1747020823,
                        "updated_at": 1747020823
                    },
                    {
                        "scene": "Frozen Meals Home Set",
                        "subscene_type": "Freezer Door",
                        "sceneid": "3837c10c-7...",
                        "total_facings": 1977,
                        "missed_detection": 109,
                        "out_project_id": "",
                        "name": "Frozen Meals Home Set",
                        "type": "",
                        "sub_scene_type": "",
                        "img_received_on": "string",
                        "img_url": "string",
                        "total_imgs": 0,
                        "out_scene_id": "3837c10c3837c10cs",
                        "validation_status": 1,
                        "id": 2,
                        "created_at": 1747020823,
                        "updated_at": 1747020823
                    }
                ],
                "user_list": []
            },
            "code": "0000",
            "message": "success"
        };

        // 优先使用API返回的数据，如果没有则使用测试数据
        const dataToUse = (res && res.data && res.data.scene_list) ? res : _res;

        // console.log('Scene API response:', res);
        if (dataToUse && dataToUse.data) {
            sceneData.value = dataToUse.data.scene_list;
            sceneData.value.forEach((item, index) => {
                item.key = index + 1;
            });
            hotFigureData.value = {
                stitched_image: sceneData.value[0].img_url,
                bounding_box_list: []
            };
            // console.log('sceneData loaded:', sceneData.value);
        }
        loading.value = false;
    } catch (error) {
        console.log('error', error);
        loading.value = false;
    }
}
// 列可见性设置
const columnVisibility = reactive({
    // SKU/POP验证相关列
    group_id: true,
    block_id: true,
    shelf: true,
    column: true,
    stack: true,
    ir_result: true,
    gt_result: true,
    orientation_gt: true,
    orientation_ir: true,
    report: true,
    // Price验证相关列
    price_tag_mapping_ir: true,
    price_tag_mapping_gt: true,
    price_ir: true,
    price_gt: true,
    price_type: true,
    price_tag_class_gt: true,
    crossed_price_gt: true,
    price_tag_type_gt: true,
    price_type_gt: true,
    price_bg_color_gt: true,
    price_location_gt: true
});

// 临时列可见性设置（用于弹窗中的交互，只有点击Save时才应用到实际的columnVisibility）
const tempColumnVisibility = reactive({});

// 价格检查表单数据
const priceCheckFormData = reactive({
    missedTagDetection: 3,
    missedPriceValue: 4,
    noPrice: true,
    noPosition: false
});
const pushListPage = () => {
    console.log('pushListPage');
}
// 图片质量选项 - 使用ID作为键名
const imageQualityOptions = reactive({
    '2': false,  // Body Part
    '4': false,  // Too Dark
    '5': false,  // Too Bright
    '10': false, // Image OK
    '13': false, // Picture of Picture
    '14': false, // Wrong Scene Type
    '15': false, // Captured from Bad Angle
    '16': false, // Image Blur
    '20': false, // Mixed Images in Single Scene
    '21': false, // Not Enough Overlap
    '25': false, // Incomplete Scene Type
    '26': false, // Multiple Disconnected POS in one scene
    '27': false, // Closed Door
    '31': false, // Products captured out of the Scene
    '32': false, // Junk image
    '33': false, // Image Not Available
    '34': false, // Incorrect use of Next Door/Next Side
    '35': false, // No use of Next Door/Next Side
    '36': false, // Duplicate Images
    '37': false, // Wrong Image sequence
    '38': false, // Blocking Obstacles
    '39': false, // MV – Stitching Issue
    '40': false, // MV – Wrong Orientation
    '42': false  // Empty Coolers/Shelves
});

// 处理图片质量弹窗确认
const handleImageQualityOk = () => {
    showImageQualityModal.value = false;
    if (queryParams.value.type === 'view') {
        getTaskSceneDetail();
    } else {
        getTaskSkuDetail();
    }
    // 获取所有选中的图片质量项key
    const selectedKeys = Object.keys(imageQualityOptions).filter(key => imageQualityOptions[key]);
    // 直接使用字符串数组格式
    const image_quality = selectedKeys;
    const params = {
        image_quality: image_quality
    };
    if(sceneObj.value.id) {
        qaImgCheckApi(queryParams.value.taskId, sceneObj.value.id, params)
        .then((res) => {
            if (res) {
                message.success(t('modelValidation.saveSuccess'));
                //重置图片质量选项
                resetImageQualityOptions();
            }
        })
        .catch((error) => {
            console.log('error', error);
        });
    } else {
        message.error(t('Please Check The Scene'));
    }
};
const resetImageQualityOptions = () => {
    Object.keys(imageQualityOptions).forEach(key => {
        imageQualityOptions[key] = false;
    });
};
// 处理图片质量弹窗取消
const handleImageQualityCancel = () => {
    showImageQualityModal.value = false;
};

// 处理保存弹窗确认
const handleSaveOk = () => {
    // 校验行数是否填写
    if (!lineNumber.value) {
        message.error(t('modelValidation.pleaseEnterLineNumber'));
        return;
    }

    showSaveModal.value = false;
    // 保存当前页面数据到localStorage
    const taskId = queryParams.value.taskId;
    const saveData = {
        taskId,
        lineNumber: lineNumber.value,
        // 确保价格检查表单数据也被保存
        priceCheckForm: {
            missedTagDetection: priceCheckForm.missedTagDetection,
            missedPriceValue: priceCheckForm.missedPriceValue,
            noPrice: priceCheckForm.noPrice,
            noPosition: priceCheckForm.noPosition
        },
        validationSkuData: validationSkuData.value,
        timestamp: new Date().getTime()
    };
    localStorage.setItem(`task_temp_save`, JSON.stringify(saveData));

    
    const params = {
        line_no: lineNumber.value,
    }
    showSaveModal.value = false;
    saveScenesApi(queryParams.value.taskId, sceneObj.value.id, params)
        .then((res) => {
            if (res) {
                message.success(t('modelValidation.saveSuccess'));
                router.push({
                    path: '/model-validation',
                    query: { type: 'validation'}
                });
            }
        })
        .catch((error) => {
            console.log('error', error);
        });
    console.log('临时保存数据:', saveData);
};

// 处理保存弹窗取消
const handleSaveCancel = () => {
    showSaveModal.value = false;
};

// 处理价格检查弹窗取消
const handlePriceCheckCancel = () => {
    showPriceCheckModal.value = false;
};



const handlePriceCheckSubmit = () => {
    const params = {
        missed_tag_detection: priceCheckForm.missedTagDetection,
        missed_price_value: priceCheckForm.missedPriceValue,
        no_price: priceCheckForm.noPrice,
        no_position: priceCheckForm.noPosition,
        info: validationPriceData.value
    };
    console.log('priceCheckForm', priceCheckForm);
    validationTaskPriceCheckApi(queryParams.value.taskId, sceneObj.value.id, params)
        .then((res) => {
            if (res) {
                message.success(t('modelValidation.saveSuccess'));
                // getTaskSceneDetail();
            }
        })
    .catch((error) => {
            console.log('error', error);
        });
}


// 处理表格字段设置确认
const handleColumnSettingsOk = () => {
    // 将临时状态应用到实际的列可见性设置
    Object.keys(tempColumnVisibility).forEach(key => {
        columnVisibility[key] = tempColumnVisibility[key];
    });
    showColumnSettingsPopover.value = false;
    message.success(t('modelValidation.columnSettingsUpdated'));
};

// 处理列设置弹窗开关变化
const handleColumnSettingsPopoverChange = (visible) => {
    if (visible) {
        // 弹窗打开时，初始化临时状态
        initTempColumnVisibility();
    }
    showColumnSettingsPopover.value = visible;
};

// 处理列设置取消
const handleColumnSettingsCancel = () => {
    showColumnSettingsPopover.value = false;
    // 取消时不保存临时状态的更改
};

// 处理价格检查弹窗开关变化
const handlePriceCheckPopoverChange = (visible) => {
    showPriceCheckPopover.value = visible;

    // 当弹窗关闭时，缓存数据到localStorage
    if (!visible) {
        savePriceCheckFormData();
    }
};

// 保存价格检查表单数据到localStorage
const savePriceCheckFormData = () => {
    const taskId = queryParams.value.taskId;
    if (!taskId) return;

    const saveData = {
        taskId,
        priceCheckForm: {
            missedTagDetection: priceCheckForm.missedTagDetection,
            missedPriceValue: priceCheckForm.missedPriceValue,
            noPrice: priceCheckForm.noPrice,
            noPosition: priceCheckForm.noPosition
        },
        timestamp: new Date().getTime()
    };

    localStorage.setItem(`price_check_form_${taskId}`, JSON.stringify(saveData));
    // console.log('保存价格检查表单数据:', saveData);
};

// 加载价格检查表单数据
const loadPriceCheckFormData = () => {
    const taskId = queryParams.value.taskId;
    if (!taskId) return;

    const savedDataStr = localStorage.getItem(`price_check_form_${taskId}`);
    if (!savedDataStr) return;

    try {
        const savedData = JSON.parse(savedDataStr);
        // 检查数据是否过期（24小时）
        const now = new Date().getTime();
        const savedTime = savedData.timestamp || 0;
        const isExpired = (now - savedTime) > (24 * 60 * 60 * 1000);

        if (isExpired) {
            localStorage.removeItem(`price_check_form_${taskId}`);
            return;
        }

        // 恢复保存的数据
        if (savedData.priceCheckForm) {
            priceCheckForm.missedTagDetection = savedData.priceCheckForm.missedTagDetection;
            priceCheckForm.missedPriceValue = savedData.priceCheckForm.missedPriceValue;
            priceCheckForm.noPrice = savedData.priceCheckForm.noPrice;
            priceCheckForm.noPosition = savedData.priceCheckForm.noPosition;

            // 同步到提交用的数据对象
            priceCheckFormData.missedTagDetection = savedData.priceCheckForm.missedTagDetection;
            priceCheckFormData.missedPriceValue = savedData.priceCheckForm.missedPriceValue;
            priceCheckFormData.noPrice = savedData.priceCheckForm.noPrice;
            priceCheckFormData.noPosition = savedData.priceCheckForm.noPosition;

        }
    } catch (error) {
        console.error('加载价格检查表单数据失败:', error);
        localStorage.removeItem(`price_check_form_${taskId}`);
    }
};


const showSaveFinish = () => {
    // 根据场景类型区分处理逻辑
    if (queryParams.value.sceneType.includes('SKU')) {
        // SKU列表数据处理
        // 保存当前行数据
        currentLine.value = validationSkuData.value.length;

        // 校验新增的场景数据参数不为空
        const newItems = validationSkuData.value.filter(item => item.isNewItem);
        const emptyNewItems = newItems.filter(item => {
            return !item.group_id || isNaN(Number(item.group_id)) ||
                   !item.block_id || isNaN(Number(item.block_id)) ||
                   !item.shelf || isNaN(Number(item.shelf)) ||
                   !item.stack || isNaN(Number(item.stack)) ||
                   !item.column || isNaN(Number(item.column)) ||
                   !item.gt_result;
        });

        if (emptyNewItems.length > 0) {
            Modal.warning({
                title: t('common.notification'),
                content: t('modelValidation.pleaseFillInAllRequiredFieldsForNewItems'),
                okText: t('common.confirm'),
                icon: null,
            });
            return;
        }

        Modal.confirm({
            title: t('common.notification'),
            content: `Whether to save the data and finish`,
            okText: t('common.confirm'),
            cancelText: t('common.cancel'),
            icon: null,
            closable: true,
            onOk: () => {
                showPriceCheckModal.value = false;
                // 保存临时任务数据
                saveTempTask();

                // 调用API时需要传递三个参数：val_task_id、scene_id和data
                validationSkuData.value.forEach((item) => {
                    item.orientation_gt = item.orientation_gt +'';
                    item.orientation_ir = item.orientation_ir +'';
                });
                
                // 过滤掉与列表无关的字段，只保留业务数据字段（保留out_sku_id）
                const filteredSkuData = validationSkuData.value.map(item => {
                    const { key, isNewItem, ...businessData } = item;
                    return businessData;
                });
                
                const params = {
                    task_scene_skus: filteredSkuData
                }
                // 从当前选中的场景获取scene_id，如果没有选中场景，使用默认值0
                const currentSceneId = sceneObj.value.id;
                validationTaskSkuSaveApi(
                    Number(queryParams.value.taskId), // 确保taskId是数字类型
                    currentSceneId, // 传递场景ID
                    params // 传递表单数据
                ).then((res) => {
                    if (res) {
                        message.success(t('modelValidation.saveSuccess'));
                        // 清除临时保存数据
                        localStorage.removeItem('task_temp_save');
                        router.push({
                            path: '/model-validation',
                            query: { tab: 'validation'}
                        });
                    }
                })
            }
        })
    } else {
        // 价签列表数据处理
        // 保存当前行数据
        currentLine.value = validationPriceData.value.length;

        // 校验新增的价签数据参数不为空（根据价签字段进行校验）
        const newItems = validationPriceData.value.filter(item => item.isNewItem);
        const emptyNewItems = newItems.filter(item => {
            // 价签数据的必填字段校验，根据实际业务需求调整
            return !item.price_gt || !item.price_tag_mapping_gt;
        });

        if (emptyNewItems.length > 0) {
            Modal.warning({
                title: t('common.notification'),
                content: 'Please enter required price tag fields.',
                okText: t('common.confirm'),
                icon: null,
            });
            return;
        }

        Modal.confirm({
            title: t('common.notification'),
            content: `Whether to save the price data and finish`,
            okText: t('common.confirm'),
            cancelText: t('common.cancel'),
            icon: null,
            closable: true,
            onOk: () => {
                showPriceCheckModal.value = false;
                // 保存临时任务数据
                saveTempTask();

                // 确保价格检查表单数据是最新的
                priceCheckFormData.missedTagDetection = priceCheckForm.missedTagDetection;
                priceCheckFormData.missedPriceValue = priceCheckForm.missedPriceValue;
                priceCheckFormData.noPrice = priceCheckForm.noPrice;
                priceCheckFormData.noPosition = priceCheckForm.noPosition;

                // 过滤掉与列表无关的字段，只保留业务数据字段
                const filteredPriceData = validationPriceData.value.map(item => {
                    const { key, isNewItem, ...businessData } = item;
                    return businessData;
                });
                
                // 调用价签保存API
                const params = {
                    info: filteredPriceData,
                    result: {
                        missedTagDetection: priceCheckForm.missedTagDetection,
                        missedPriceValue: priceCheckForm.missedPriceValue,
                        noPrice: priceCheckForm.noPrice,
                        noPosition: priceCheckForm.noPosition
                    }
                }
                // 从当前选中的场景获取scene_id
                const currentSceneId = sceneObj.value.id;
                validationTaskPriceSaveApi(
                    Number(queryParams.value.taskId), // 确保taskId是数字类型
                    currentSceneId, // 传递场景ID
                    params // 传递表单数据
                ).then((res) => {
                    if (res) {
                        message.success(t('modelValidation.saveSuccess'));
                        // 清除临时保存数据
                        localStorage.removeItem('task_temp_save');
                        router.push({
                            path: '/model-validation',
                            query: { tab: 'validation'}
                        });
                    }
                })
            }
        })
    }
}
// 处理提交
const handleCheckSubmit = () => {
    const currentSceneId = sceneObj.value.id;
    const newItems = validationSkuData.value.filter(item => item.isNewItem);
    const emptyNewItems = newItems.filter(item => {
        return !item.group_id || isNaN(Number(item.group_id)) ||
                !item.block_id || isNaN(Number(item.block_id)) ||
                !item.shelf || isNaN(Number(item.shelf)) ||
                !item.stack || isNaN(Number(item.stack)) ||
                !item.column || isNaN(Number(item.column)) ||
                !item.gt_result;
    });

    if (emptyNewItems.length > 0) {
        Modal.warning({
            title: t('common.notification'),
            content: t('modelValidation.pleaseFillInAllRequiredFieldsForNewItems'),
            okText: t('common.confirm'),
            icon: null,
        });
        return;
    }
    Modal.confirm({
        title: t('common.notification'),
        content: `Whether to save and load the next data`,
        okText: t('common.confirm'),
        cancelText: t('common.cancel'),
        icon: null,
        closable: true,
        onOk: async () => {
            if(currentSceneId !== undefined) {
                const processedSkuData = validationSkuData.value.map(item => ({
                    ...item,
                    block_id: item.block_id !== '' ? Number(item.block_id) : null, // Or handle empty string as needed
                    group_id: item.group_id !== '' ? Number(item.group_id) : null,
                    shelf: item.shelf !== '' ? Number(item.shelf) : null,
                    stack: item.stack !== '' ? Number(item.stack) : null,
                    column: item.column !== '' ? Number(item.column) : null,
                    orientation_gt: item.orientation_gt+''
                }));
                const params = {
                    task_scene_skus: processedSkuData
                }
                // 从当前选中的场景获取scene_id，如果没有选中场景，使用默认值0
                const res = await validationTaskSkuSaveApi(
                    Number(queryParams.value.taskId), // 确保taskId是数字类型
                    currentSceneId, // 传递场景ID
                    params // 传递表单数据
                )

                await validationTaskSkuApi(
                    Number(queryParams.value.taskId), // 确保taskId是数字类型
                ).then(async(res) => {
                    if (res) {
                        irSkuImages.value = [];
                        gtSkuImages.value = [];
                        message.success(t('modelValidation.saveSuccess'));
                        await getTaskSkuDetail();
                        await getSkuSummaryApi();
                    }
                })
            } else {
                message.error(t('Please Check The Scene'));
                router.push({
                    path: '/model-validation',
                    query: { tab: 'validation'}
                });
            }
        }
    })
};
// 图片缩放相关变量 - 现在由HotFigure组件内部管理
const imageBoxW = ref(448);
const imageBoxH = ref(516);

// 临时保存相关变量
const currentLine = ref(0);
// 图片加载完成事件
const imageFinshed = (data) => {
    // console.log('图片加载完成', data);
    // 这里可以处理图片加载完成后的逻辑
};

// 处理红点点击事件
const handleBoxClick = (box) => {
    // 如果有原始数据，可以根据out_sku_id找到对应的表格行
    if (box.originalData && box.out_sku_id) {
        const targetRow = validationSkuData.value.find(item => item.out_sku_id === box.out_sku_id);
        if (targetRow) {
            // 高亮对应的表格行
            selectedRowKeys.value = [targetRow.key];
            
            // 可以滚动到对应行或其他交互逻辑
            // console.log('找到对应的表格行:', targetRow);
        }
    }
};

// 临时保存任务进度
const saveTempTaskData = () => {
    // 保存当前任务ID和完成的行数
    const tempTaskData = {
        taskId: queryParams.value.taskId,
        currentLine: currentLine.value || validationSkuData.value.length,
        // 保存价格检查表单数据
        priceCheckForm: {
            missedTagDetection: priceCheckForm.missedTagDetection,
            missedPriceValue: priceCheckForm.missedPriceValue,
            noPrice: priceCheckForm.noPrice,
            noPosition: priceCheckForm.noPosition
        },
        timestamp: new Date().getTime()
    };
    localStorage.setItem('tempSavedTask', JSON.stringify(tempTaskData));

    // 同时保存价格检查表单数据到专用存储
    // savePriceCheckFormData();
};

// 缩放功能
const planeFigure = ref(null);

const increaseDotSize = () => {
    if (planeFigure.value) {
        planeFigure.value.increaseDotSize();
    }
};

const decreaseDotSize = () => {
    if (planeFigure.value) {
        planeFigure.value.decreaseDotSize();
    }
};

const increaseZoom = () => {
    if (planeFigure.value) {
        planeFigure.value.increaseZoom();
    }
};

const decreaseZoom = () => {
    if (planeFigure.value) {
        planeFigure.value.decreaseZoom();
    }
};

// SKU/POP验证的列定义
const validationSkuColumns = computed(() => {
    const baseColumns = [
        {
            title: t('modelValidation.groupId'),
            dataIndex: 'group_id',
            key: 'group_id',
            width: 100,
            // sorter: (a, b) => a.group_id.localeCompare(b.group_id),
        },
        {
            title: t('modelValidation.blockId'),
            dataIndex: 'block_id',
            key: 'block_id',
            width: 100,
            // sorter: (a, b) => a.block_id.localeCompare(b.block_id),
        },
        {
            title: t('modelValidation.shelf'),
            dataIndex: 'shelf',
            key: 'shelf',
            width: 100,
            // sorter: (a, b) => a.shelf.localeCompare(b.shelf),
        },
        {
            title: t('modelValidation.column'),
            dataIndex: 'column',
            key: 'column',
            width: 100,
            // sorter: (a, b) => a.column.localeCompare(b.column),
        },
        {
            title: t('modelValidation.stack'),
            dataIndex: 'stack',
            key: 'stack',
            width: 100,
            // sorter: (a, b) => a.stack.localeCompare(b.stack),
        },
        {
            title: t('modelValidation.irResult'),
            dataIndex: 'ir_result',
            key: 'ir_result',
            width: 240,
            ellipsis: true,
        },
        {
            title: t('modelValidation.groundTruth'),
            dataIndex: 'gt_result',
            key: 'gt_result',
            width: 240,
            ellipsis: true,
        }
    ];

    // 根据 oriVal 的值决定是否显示 orientation 相关字段
    if (queryParams.value.oriVal === '1') {
        baseColumns.push(
            {
                title: t('modelValidation.orientationGT'),
                dataIndex: 'orientation_gt',
                key: 'orientation_gt',
                width: 120
            },
            {
                title: t('modelValidation.orientation'),
                dataIndex: 'orientation_ir',
                key: 'orientation_ir',
                width: 120
            }
        );
    }

    // 添加 report 字段
    baseColumns.push({
        title: t('modelValidation.report'),
        dataIndex: 'report',
        key: 'report',
        width: 240
    });

    return baseColumns;
});

// 根据场景类型获取所有可用的列定义
const allColumns = computed(() => {
    if (queryParams.value.sceneType.includes('SKU')) {
        return validationSkuColumns.value;
    } else {
        return validationPriceColumns;
    }
});
const validationPriceColumns = [
    {
        title: t('modelValidation.irResult'),
        dataIndex: 'ir_result',
        key: 'ir_result',
        editable: true,
    },
    {
        title: t('modelValidation.priceTagMapingIR'),
        dataIndex: 'price_tag_mapping_ir',
        key: 'price_tag_mapping_ir',
    },
    {
        title: t('modelValidation.priceTagMapingGT'),
        dataIndex: 'price_tag_mapping_gt',
        key: 'price_tag_mapping_gt',
    },
    {
        title: t('modelValidation.priceIR'),
        dataIndex: 'price_ir',
        key: 'price_ir',
        editable: true,
    },
    {
        title: t('modelValidation.priceGT'),
        dataIndex: 'price_gt',
        key: 'price_gt',
    },
    {
        title: t('modelValidation.priceType'),
        dataIndex: 'price_type',
        key: 'price_type',
    },
    {
        title: t('modelValidation.priceTagClassGt'),
        dataIndex: 'price_tag_class_gt',
        key: 'price_tag_class_gt',
    },
    {
        title: t('modelValidation.crossedPriceGt'),
        dataIndex: 'crossed_price_gt',
        key: 'crossed_price_gt',
    },
    {
        title: t('modelValidation.priceTagTypeGt'),
        dataIndex: 'price_tag_type_gt',
        key: 'price_tag_type_gt',
    },
    {
        title: t('modelValidation.priceTypeGt'),
        dataIndex: 'price_type_gt',
        key: 'price_type_gt',
    },
    {
        title: t('modelValidation.priceBgGt'),
        dataIndex: 'price_bg_color_gt',
        key: 'price_bg_color_gt',
    },
    {
        title: t('modelValidation.priceLocationGt'),
        dataIndex: 'price_location_gt',
        key: 'price_location_gt',
    }
]

const sceneSkuColumns = [
    {
        title: t('modelValidation.groupId'),
        dataIndex: 'group_id',
        key: 'group_id',
        width: 120,
    },
    {
        title: t('modelValidation.blockId'),
        dataIndex: 'block_id',
        key: 'block_id',
        width: 100,
    },
    {
        title: t('modelValidation.shelf'),
        dataIndex: 'shelf',
        key: 'shelf',
        width: 100,
    },
    {
        title: t('modelValidation.column'),
        dataIndex: 'column',
        key: 'column',
        width: 100,
    },
    {
        title: t('modelValidation.stack'),
        dataIndex: 'stack',
        key: 'stack',
        width: 100,
    },
    {
        title: t('modelValidation.irResult'),
        dataIndex: 'ir_sku_name',
        key: 'ir_result',
        width: 240,
        ellipsis: true,
    },
    {
        title: t('modelValidation.groundTruth'),
        dataIndex: 'gt_sku_name',
        key: 'gt_result',
        width: 240,
        ellipsis: true,
    },
    {
        title: t('modelValidation.report'),
        dataIndex: 'report',
        key: 'report',
        width: 240,
        ellipsis: true,
    }
]
// 根据可见性过滤后的表格列定义
const validationColumns = computed(() => {
    return allColumns.value.filter(column => columnVisibility[column.key]);
});

// 已选择的列（用于右侧显示）
const selectedColumns = computed(() => {
    return allColumns.value.filter(column => columnVisibility[column.key]);
});

// 临时已选择的列（用于弹窗右侧显示）
const tempSelectedColumns = computed(() => {
    return allColumns.value.filter(column => tempColumnVisibility[column.key]);
});
// 处理临时列可见性变化的函数
const handleTempColumnChange = (key, checked) => {
    if (tempColumnVisibility.value) {
        tempColumnVisibility.value[key] = checked;
    }
};
// 移除临时已选择的列
const removeTempSelectedColumn = (columnKey) => {
    tempColumnVisibility[columnKey] = false;
};

// 初始化临时列可见性状态
const initTempColumnVisibility = () => {
    Object.keys(columnVisibility).forEach(key => {
        tempColumnVisibility[key] = columnVisibility[key];
    });
};

// 可编辑单元格数据
const editableData = reactive({});

// 编辑单元格
const edit = (key, field, value) => {
    // 查找当前行数据
    const skuIndex = validationSkuData.value.findIndex(item => item.key === key);
    const priceIndex = validationPriceData.value.findIndex(item => item.key === key);

    // 允许编辑新添加的SKU行（isNewItem为true的行）或者price相关字段
    const isPriceField = ['price_gt', 'price_tag_mapping_gt', 'price_ir', 'price_tag_class_gt', 'crossed_price_gt', 
        'price_type_gt', 'price_bg_color_gt', 'price_location_gt', 'price_tag_type_gt'].includes(field);
    const isSkuField = ['group_id', 'block_id', 'shelf', 'column', 'stack'].includes(field);
    const isAlwaysEditableField = ['gt_result', 'orientation_gt','report'].includes(field);
    const canEditSku = skuIndex !== -1 && (validationSkuData.value[skuIndex].isNewItem || validationSkuData.value[skuIndex].add_from === 1) && isSkuField;
    const canEditPrice = priceIndex !== -1 && isPriceField;
    const canEditAlways = (skuIndex !== -1 || priceIndex !== -1) && isAlwaysEditableField;

    if (canEditSku || canEditPrice || canEditAlways) {
        // 创建一个新的编辑对象，如果不存在
        if (!editableData[key]) {
            editableData[key] = {};
        }
        // 设置当前编辑字段的值
        // 对于orientation字段，确保使用数字值而不是字符串
        let editValue = value;
        if (field === 'orientation_gt' || field === 'orientation_ir') {
            // 如果value是字符串形式的数字，转换为数字
            editValue = typeof value === 'string' ? parseInt(value) : value;
            // 确保值在有效范围内
            if (isNaN(editValue) || !orientationOptions.some(option => option.value === editValue)) {
                editValue = -1; // 默认为Untrained
            }
        }
        editableData[key][field] = editValue;
        
        // 自动聚焦到输入框
        nextTick(() => {
            const inputElement = document.querySelector(`input[data-key="${key}"][data-field="${field}"]`);
            if (inputElement) {
                inputElement.focus();
            }
        });
    }
};

// 保存编辑
const saveEdit = (key) => {
    if (editableData[key]) {
        // 更新SKU表格数据
        const skuIndex = validationSkuData.value.findIndex(item => item.key === key);
        if (skuIndex !== -1) {
            // 对于新添加的行或add_from为1的行，允许编辑所有字段
            if (validationSkuData.value[skuIndex].isNewItem || validationSkuData.value[skuIndex].add_from === 1) {
                const fields = ['group_id', 'block_id', 'shelf', 'column', 'stack', 'gt_result', 'orientation_gt', 'report'];
                fields.forEach(field => {
                    if (editableData[key][field] !== undefined) {
                        let value = editableData[key][field];
                        // 确保orientation_ir和report字段是字符串类型
                        if ((field === 'orientation_ir' || field === 'report') && value !== null && value !== undefined) {
                            value = String(value);
                        }
                        validationSkuData.value[skuIndex][field] = value;
                    }
                });
            } else {
                // 对于现有行，只允许编辑gt_result、orientation_gt和ir_result
                const alwaysEditableFields = ['gt_result', 'orientation_gt', 'report'];
                alwaysEditableFields.forEach(field => {
                    if (editableData[key][field] !== undefined) {
                        let value = editableData[key][field];
                        // 确保orientation_ir和report字段是字符串类型
                        if ((field === 'orientation_ir' || field === 'report') && value !== null && value !== undefined) {
                            value = String(value);
                        }
                        validationSkuData.value[skuIndex][field] = value;
                    }
                });
            }
        }

        // 更新Price表格数据
        const priceIndex = validationPriceData.value.findIndex(item => item.key === key);
        if (priceIndex !== -1) {
            // 更新price相关字段
            const priceFields = ['price_gt', 'price_tag_mapping_gt', 'price_ir', 'price_tag_class_gt', 
                'crossed_price_gt', 'price_type_gt', 'price_bg_color_gt', 'price_location_gt', 'price_tag_type_gt'];
            priceFields.forEach(field => {
                if (editableData[key][field] !== undefined) {
                    validationPriceData.value[priceIndex][field] = editableData[key][field];
                }
            });
        }
        // 清除编辑状态
        delete editableData[key];
    }
};

// 验证数据
const sceneObj = ref({});
const validationSkuData = ref([]);
const getTaskSkuDetail = async () => {
    try {
        const res = await validationTaskSkuApi(queryParams.value.taskId);
        if (res && res.data) {
            lineNumber.value = res.data.saved_line_no;
            validationSkuData.value = res.data.scene_skus.reverse();
            if(res.data.scene_skus.length > 0) {
                validationSkuData.value.forEach((item, index) => {
                    item.key = index + 1;
                    // 确保orientation_ir字段是字符串类型
                    if (item.orientation_ir !== null && item.orientation_ir !== undefined) {
                        item.orientation_ir = String(item.orientation_ir);
                    } else {
                        item.orientation_ir = '';
                    }
                    // 确保report字段存在且是字符串类型
                    if (item.report !== null && item.report !== undefined) {
                        item.report = String(item.report);
                    } else {
                        item.report = ''; // 为没有report字段的数据添加默认空字符串
                    }
                });
            } else {
                validationSkuData.value = []
                // router.push({
                //     path: '/model-validation',
                //     query: { tab: 'validation'}
                // }); 
            }
            sceneObj.value = res.data.scene || {};
            
            // 将scene_id存储到localStorage中，供TagsCard解绑时使用
            if (sceneObj.value.id) {
                const taskTempSave = localStorage.getItem('task_temp_save');
                let tempData = {};
                if (taskTempSave) {
                    try {
                        tempData = JSON.parse(taskTempSave);
                    } catch (error) {
                        console.error('解析task_temp_save失败:', error);
                    }
                }
                tempData.sceneId = sceneObj.value.id;
                localStorage.setItem('task_temp_save', JSON.stringify(tempData));
            }
        } else {
            router.push({
                path: '/model-validation',
                query: { tab: 'validation'}
            }); 
        }
        loading.value = false;
        if(sceneObj.value.img_url) {
            setProductImage(sceneObj.value.img_url);
        }
    } catch (error) {
        console.error('Error fetching data:', error);
        loading.value = false;
    }
}
const setProductImage = (imageUrl) => {
    // 将SKU数据转换为HotFigure组件需要的边界框格式
    const boundingBoxes = validationSkuData.value.map(item => {
        if (item.att_info && item.att_info.productDot) {
            const dot = item.att_info.productDot;
            
            // productDot中的x,y已经是相对坐标（0-1之间的比例）
            // 直接使用这些坐标作为红点位置
            return {
                sku_name: item.ir_result || item.gt_result || 'Unknown SKU',
                out_sku_id: item.out_sku_id,
                // 使用productDot的相对坐标作为四个角点（因为我们只需要一个点来显示红点）
                tl: [dot.x, dot.y], // 左上角
                tr: [dot.x, dot.y], // 右上角
                bl: [dot.x, dot.y], // 左下角
                br: [dot.x, dot.y], // 右下角
                // 保存原始数据以备后用
                originalData: item
            };
        }
        return null;
    }).filter(Boolean); // 过滤掉null值
    
    // 设置HotFigure组件的图片数据
    hotFigureData.value = {
        stitched_image: imageUrl,
        bounding_box_list: boundingBoxes
    };
};

const validationPriceData = ref([]);
const getTaskPriceDetail = async () => {
    try {
        const res = await validationTaskSceneApi(queryParams.value.taskId);
        const _res = {
            data: {
                scene_prices: [
                    {
                        "id": 1,
                        "created_at": 1716253200,
                        "updated_at": 1716253800,
                        "product_img_url": "https://re-sku-1255412942.cos.ap-shanghai.myqcloud.com/RE-SKU-LOGO-59d5b6d5b5d13c92ddbe180364c04322",
                        "ir_result": "{\"result\": \"OK\"}",
                        "price_tag_mapping_ir": "A1",
                        "price_tag_mapping_gt": "B1",
                        "price_ir": "5.99",
                        "price_gt": "6.50",
                        "price_type": "吊牌价",
                        "validation_scene_id": 1,
                        "group_id": 0,
                        "block_id": 0,
                        "shelf": 0,
                        "column": 0,
                        "stack": 0,
                        "multipack": null,
                        "price_type_gt": "99",
                        "price_location": "2",
                        "price_location_gt": "4",
                        "price_bg_color": "1",
                        "price_bg_color_gt": "3",
                        "price_front_size": "456",
                        "price_front_size_gt": "123",
                        "price_tag_type": "",
                        "price_tag_type_gt": "555",
                        "price_tag_class": "",
                        "price_tag_class_gt": "123",
                        "crossed_price": "",
                        "crossed_price_gt": "888"
                    },
                    {
                        "id": 2,
                        "created_at": 1716253200,
                        "updated_at": 1716253800,
                        "product_img_url": "https://re-sku-1255412942.cos.ap-shanghai.myqcloud.com/RE-SKU-LOGO-0069119a763c2acce29d1133bc50ba1f",
                        "ir_result": "{\"result\": \"OK\"}",
                        "price_tag_mapping_ir": "A1",
                        "price_tag_mapping_gt": "B1",
                        "price_ir": "5.99",
                        "price_gt": "6.50",
                        "price_type": "吊牌价",
                        "validation_scene_id": 1,
                        "group_id": 0,
                        "block_id": 0,
                        "shelf": 0,
                        "column": 0,
                        "stack": 0,
                        "multipack": null,
                        "price_type_gt": "3450",
                        "price_location": "",
                        "price_location_gt": "eeee",
                        "price_bg_color": "",
                        "price_bg_color_gt": "vfvr",
                        "price_front_size": "",
                        "price_front_size_gt": "rrr",
                        "price_tag_type": "",
                        "price_tag_type_gt": "666",
                        "price_tag_class": "",
                        "price_tag_class_gt": "234",
                        "crossed_price": "",
                        "crossed_price_gt": "987"
                    }
                ]
            }
        };
        sceneObj.value = res.data.scene || {};
        // 优先使用API返回的数据，如果没有则使用测试数据
        const dataToUse = (res && res.data && res.data.scene_prices) ? res : _res;

        console.log('Price API response:', res);
        if (dataToUse && dataToUse.data) {
            validationPriceData.value = dataToUse.data.scene_prices;
            validationPriceData.value.forEach((item, index) => {
                item.key = index + 1;
            });
            console.log('validationPriceData loaded:', validationPriceData.value);
        }
        loading.value = false;
    } catch (error) {
        console.error('Error fetching data:', error);
        loading.value = false;
    }
}
const showSelectItem = ref(false)
// 添加未检测项目
const addUndetectedItem = () => {
    // 清除Validation列表的选中状态
    selectedSkuRowKeys.value = [];
    
    // 重置顶部的图片为空
    initialSkuImages.value = [];
    currentSkuImages.value = [];
    initialImageIndex.value = 0;
    currentImageIndex.value = 0;
    
    // 重置truthChange状态
    truthChange.value = false;
    modifiedGtOutSkuIdForDisplay.value = null;
    sceneObj.value.img_url = ''
    showSelectItem.value = true;
    
    const newKey = validationSkuData.value.length + 1;
    validationSkuData.value.push({
        key: newKey,
        group_id: '',
        block_id: '',
        shelf: '',
        column: '',
        stack: '',
        ir_result: '',
        gt_result: '',
        orientation_gt: '',
        orientation_ir: '', // 添加orientation_ir字段
        report: '', // 添加report字段
        isNewItem: true, // 标记为新添加的数据
    });

    // 滚动到表格最后一项
    nextTick(() => {
        setTimeout(() => {
            if (validationTable.value && validationTable.value.$el) {
                const tableBody = validationTable.value.$el.querySelector('.ant-table-tbody');
                if (tableBody && tableBody.children.length > 0) {
                    const lastRow = tableBody.lastElementChild;
                    if (lastRow) {
                        lastRow.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                }
            }
        }, 200);
    });
};

// 切换删除模式
const toggleDeleteMode = () => {
    if (isDeleteMode.value) {
        // 退出删除模式时执行删除
        deleteSelectedItems();
        isDeleteMode.value = false;
    } else {
        // 进入删除模式
        isDeleteMode.value = true;
        deleteSelectedItems();
    }
};

// 删除选中的项目
const deleteSelectedItems = () => {
    if (selectedRowKeys.value.length === 0) {
        message.warning(t('modelValidation.pleaseSelectItems'));
        return;
    }

    // 根据场景类型确定数据源
    const currentData = queryParams.value.sceneType.includes('SKU') ? validationSkuData.value : validationPriceData.value;
    
    // 检查选中的项目是否都是新添加的数据或add_from为1的数据
    const selectedItems = currentData.filter(item => selectedRowKeys.value.includes(item.key));
    const hasNonNewItems = selectedItems.some(item => !item.isNewItem && item.add_from === 0);

    if (hasNonNewItems) {
        message.warning(t('modelValidation.canOnlyDeleteNewItems'));
        isDeleteMode.value = false;
        return;
    }

    Modal.confirm({
        title: t('common.notification'),
        content: t('modelValidation.confirmDeleteSelected', { count: selectedRowKeys.value.length }),
        okText: t('common.yes'),
        cancelText: t('common.no'),
        okType: 'danger',
        icon: null,
        onOk: () => {
            // 根据场景类型删除对应数据源中的选中数据
            if (queryParams.value.sceneType.includes('SKU')) {
                validationSkuData.value = validationSkuData.value.filter(item => !selectedRowKeys.value.includes(item.key));
                // 删除后重新生成key，确保key的连续性
                validationSkuData.value.forEach((item, index) => {
                    item.key = index + 1;
                });
            } else {
                validationPriceData.value = validationPriceData.value.filter(item => !selectedRowKeys.value.includes(item.key));
                // 删除后重新生成key，确保key的连续性
                validationPriceData.value.forEach((item, index) => {
                    item.key = index + 1;
                });
            }
            isDeleteMode.value = false;
            selectedRowKeys.value = [];
            message.success(t('modelValidation.deleteSuccess'));
        }
    });
};

// 表格变更处理函数
const handleTableChange = (pagination, filters, sorter) => {
    console.log('表格排序变更:', sorter);
    // 可以根据需要在这里添加额外的排序逻辑
};

// 保存临时任务数据到localStorage
const saveTempTask = () => {
    const taskId = queryParams.value.taskId;
    if (!taskId) return;

    const saveData = {
        taskId,
        lineNumber: lineNumber.value,
        currentLine: currentLine.value,
        validationSkuData: validationSkuData.value,
        timestamp: new Date().getTime()
    };

    localStorage.setItem(`task_temp_save`, JSON.stringify(saveData));

    // 同步到提交用的数据对象
    priceCheckFormData.missedTagDetection = priceCheckForm.missedTagDetection;
    priceCheckFormData.missedPriceValue = priceCheckForm.missedPriceValue;
    priceCheckFormData.noPrice = priceCheckForm.noPrice;
    priceCheckFormData.noPosition = priceCheckForm.noPosition;
    console.log('自动保存临时数据:', saveData);
};
// SKU表格选中的行keys
const selectedSkuRowKeys = ref([]);
// SKU表格自定义行属性
const customSkuRow = (record) => {
    return {
        onClick: (event) => {
            // 只有在真正编辑状态下（输入框获得焦点或正在输入）才不处理行选中逻辑
            const editableCell = event.target.closest('.editable-cell');
            const inputElement = event.target.closest('.ant-input');
            if ((editableCell && editableCell.querySelector('.ant-input:focus')) || 
                (inputElement && inputElement === document.activeElement)) {
                return;
            }
            
            const key = record.key;
            
            // 删除模式下的处理逻辑
            if (isDeleteMode.value) {
                // 只有新添加的数据才能在删除模式下被选中
                if (record.isNewItem) {
                    const selectedIndex = selectedRowKeys.value.indexOf(key);
                    if (selectedIndex > -1) {
                        // 如果已选中，则取消选中
                        selectedRowKeys.value.splice(selectedIndex, 1);
                    } else {
                        // 如果未选中，则选中（多选模式）
                        selectedRowKeys.value.push(key);
                    }
                } else {
                    message.warning(t('modelValidation.canOnlyDeleteNewItems'));
                }
                return;
            }
            
            // 非删除模式下的正常选中逻辑 - 用于显示图片
            handleSkuRowClick(record);
            
            // 同时更新表格选中状态用于删除功能
            const selectedIndex = selectedRowKeys.value.indexOf(key);
            if (selectedIndex > -1) {
                // 如果已选中，则取消选中
                selectedRowKeys.value.splice(selectedIndex, 1);
            } else {
                // 如果未选中，则选中（单选模式）
                selectedRowKeys.value = [key];
            }
        },
    };
};

// 处理SKU表格行点击
const handleSkuRowClick = async (record) => {
    const index = selectedSkuRowKeys.value.indexOf(record.key);

    // 辅助函数，只获取图片，不改变选中状态或 truthChange 状态
    const fetchImagesForDisplay = async (irOutSkuId, gtOutSkuId) => {
        if (!irOutSkuId) {
            irSkuImages.value = []
            return;
        }
        isLoadingSkuImages.value = true;
        try {
            const res = await skuImgApi(irOutSkuId);
            if (res.data && res.data.imgs && res.data.imgs.length > 0) {
                irSkuImages.value = res.data.imgs;
            } else {
                irSkuImages.value = [];
            }
        } catch (error) {
            console.error('获取SKU图片(fetchImagesForDisplay)失败:', error);
            irSkuImages.value = [];
        } finally {
            isLoadingSkuImages.value = false;
        }
        if (!gtOutSkuId) {
            gtSkuImages.value = [];
            return;
        }
        isLoadingSkuImages.value = true;
        try {
            const res = await skuImgApi(gtOutSkuId);
            if (res.data && res.data.imgs && res.data.imgs.length > 0) {
                gtSkuImages.value = res.data.imgs;
            } else {
                gtSkuImages.value = [];
            }
        } catch (error) {
            console.error('获取SKU图片(fetchImagesForDisplay)失败:', error);
            gtSkuImages.value = [];
        } finally {
            isLoadingSkuImages.value = false;
        }
    };

    if (truthChange.value) {
        // truthChange 为 true 时，currentSkuImages 已经由 handleTruthChange 设置为修改后的 GT 图片
        // initialSkuImages 需要设置为当前点击行的 IR 图片 (record.ir_out_sku_id)
        if (index > -1) {
            await fetchImagesForDisplay(record.ir_out_sku_id, record.gt_out_sku_id);
            initialImageIndex.value = 0;
            currentImageIndex.value = 0;
        } else {
            selectedSkuRowKeys.value = [record.key]; // 选中当前行
            await fetchImagesForDisplay(record.ir_out_sku_id, record.gt_out_sku_id); // record.ir_out_sku_id 是IR结果图片
            initialImageIndex.value = 0;
        }
    } else {
        if (index > -1) {
            selectedSkuRowKeys.value.splice(index, 1);
            initialSkuImages.value = [];
            currentSkuImages.value = [];
            initialImageIndex.value = 0;
            currentImageIndex.value = 0;
        } else {
            selectedSkuRowKeys.value = [record.key];
            // 正常情况下，initialSkuImages显示IR结果图片，currentSkuImages显示GT图片
            await fetchImagesForDisplay(record.ir_out_sku_id, record.gt_out_sku_id);
            initialImageIndex.value = 0;
            currentImageIndex.value = 0;
        }
    }
};

// 行选择变更处理函数
const onSelectChange = (newSelectedRowKeys) => {
    selectedRowKeys.value = newSelectedRowKeys;
};

// 自定义行属性，处理行点击选中
const customRow = (record) => {
    return {
        onClick: (event) => {
            // 检查点击是否发生在编辑单元格内
            if (event.target.closest('.editable-cell')) {
                return; // 如果是编辑单元格，不执行行选中逻辑
            }
            
            const key = record.key;
            
            // 删除模式下的处理逻辑
            if (isDeleteMode.value) {
                // 只有新添加的数据才能在删除模式下被选中
                if (record.isNewItem) {
                    const selectedIndex = selectedRowKeys.value.indexOf(key);
                    if (selectedIndex > -1) {
                        // 如果已选中，则取消选中
                        selectedRowKeys.value.splice(selectedIndex, 1);
                    } else {
                        // 如果未选中，则选中（多选模式）
                        selectedRowKeys.value.push(key);
                    }
                } else {
                    message.warning(t('modelValidation.canOnlyDeleteNewItems'));
                }
                return;
            }
            
            // 非删除模式下的正常选中逻辑
            const selectedIndex = selectedRowKeys.value.indexOf(key);
            if (selectedIndex > -1) {
                // 如果已选中，则取消选中
                selectedRowKeys.value.splice(selectedIndex, 1);
                currentSkuImages.value.push(record.product_img_url);
            } else {
                // 如果未选中，则选中（单选模式）
                selectedRowKeys.value = [key];
            }
        },
    };
};

// 获取价格表格行的类名，用于在删除模式下高亮显示可删除的行
const getRowClassName = (record) => {
    const key = record.key;
    let className = '';
    
    // 选中行的背景色
    if (selectedRowKeys.value.includes(key)) {
        className += 'selected-row';
    }
    // 可删除行的背景色
    return className.trim();
};

// Scene表格行样式
const getSceneRowClassName = (record) => {
    return selectedSceneId.value === record.id ? 'selected-row' : '';
};

const jumpUrl = ref('')
// 自定义Scene表格行属性，处理行点击选中
const customSceneRow = (record) => {
    return {
        onClick: async () => {
            // 设置当前选中的场景ID
            selectedSceneId.value = record.id;
            hotFigureData.value = {
                stitched_image: record.img_url,
                bounding_box_list: []
            };
            // 更新统计数据显示
            updateSceneStatistics(record.id);
            jumpUrl.value = record.report_url;
            await fetchSceneSkuListApi(queryParams.value.taskId, record.id);
        }
    };
};
const sceneSkuListData = ref([])
const fetchSceneSkuListApi = async (taskId, sceneId) => {
    try {
        const res = await sceneSkuListApi(taskId, sceneId);
        if (res.data && res.data.data_list) {
            sceneSkuListData.value = res.data.data_list;
        } else {
            sceneSkuListData.value = [];
        }
    } catch (error) {
        console.error('获取场景SKU列表失败:', error);
        sceneSkuListData.value = [];
    }
};
// 打开Web App版本链接
const openWebAppVersion = () => {
    if(queryParams.value.type === 'view') {
        jumpUrl.value = sceneData.value[0].report_url;
    } else {
        jumpUrl.value = sceneObj.value.report_url;
    }
    window.open(jumpUrl.value, '_blank');
};
// 根据用户ID获取用户名称
const getUserName = (id) => {
    const user = userData.value.find(user => user.id === id);
    return user ? user.name : '';
};

// 用户行为追踪
// const { userActionCount } = useAutoUserActionTracking({
//     taskId: () => queryParams.value.taskId,
//     sceneId: () => selectedSceneId.value,
//     pageName: 'TaskDetail',
//     reportInterval: 30000
// });

onMounted(async () => {
    if (!store.hasData) { // 仅在数据未加载时调用API
        await store.fetchUserData();
    }
    // 初始化临时列可见性状态
    initTempColumnVisibility();
    if (queryParams.value.type === 'view') {
        await getTaskSceneDetail();
        await getSkuSummaryApi();
        // 不再默认选中第一个场景
        // if (sceneData.value.length > 0) {
        //     selectedSceneId.value = sceneData.value[0].id;
        // }
    } else {
        if (queryParams.value.sceneType.includes('SKU')) {
            await getTaskSkuDetail();
            await getSkuSummaryApi();

            // 获取 Ground Truth 选项
            await fetchGroundTruthOptions();
        } else {
            await getTaskPriceDetail();
            await getPriceSummaryApi();
        }
        // 检查并加载临时保存的数据
        // loadTempTaskSku();
        // 加载价格检查表单数据
        loadPriceCheckFormData();
    }
});

// 组件卸载前保存临时数据
onBeforeUnmount(() => {
    if (queryParams.value.type !== 'view') {
        saveTempTaskData();
    }
});
</script>

<style scoped>
.editable-cell {
    padding: 5px 8px;
    cursor: pointer;
    border: 1px dashed transparent;
    transition: all 0.3s;
}

/* 自定义表格行高48px */
:deep(.custom-table-48px .ant-table-tbody > tr > td) {
    height: 48px;
    padding: 8px 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

:deep(.custom-table-48px .ant-table-thead > tr > th) {
    height: 48px;
    padding: 8px 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 确保表格内容不换行 */
:deep(.custom-table-48px .ant-table-cell) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 输入框样式调整 */
:deep(.custom-table-48px .ant-input) {
    height: 32px;
    line-height: 32px;
}

/* 确保表格自适应容器宽度 */
:deep(.table-container .ant-table) {
    min-width: 100%;
}

/* 表格滚动条样式优化 */
.table-container::-webkit-scrollbar {
    height: 8px;
    width: 8px;
}

.table-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
/* 表格行hover效果 */
:deep(.ant-table-tbody > tr) {
    cursor: pointer;
}

:deep(.ant-table-tbody > tr:hover > td) {
    background-color: #bae7ff !important;
}
/* 选中行的背景色样式 */
:deep(.selected-row) {
    background-color: #bae7ff !important;
}

:deep(.selected-row:hover) {
    background-color: #bae7ff !important;
}
:deep(.custom-table-48px .ant-table-cell) {
    text-overflow: initial !important;
}
</style>