import { createRouter, createWebHistory } from "vue-router";
import { basePath } from "../config/env";
import { refreshApi } from "../api/user";
import globalUserActionTracker from "../composable/useGlobalUserActionTracking";

const routes = [
    {
        path: "/login",
        name: "Login",
        component: () => import("@/views/login/index.vue"),
    },
    {
        path: "/",
        name: "Index",
        redirect: "/client",
        component: () => import("@/layout/index.vue"),
        children: [
            {
                path: "/dashboard",
                name: "Dashboard",
                component: () => import("@/views/dashboard/index.vue"),
                meta: { title: "首页", breadcrumb: true },
            },
            // 系统管理相关路由
            {
                path: "/system",
                name: "System",
                component: () => import("@/views/system/index.vue"),
                meta: { title: "系统管理", breadcrumb: true },
            },
            // 项目相关路由
            {
                path: "/client",
                name: "Client",
                redirect: "/project-list",
                children: [
                    {
                        path: "/project-list",
                        name: "ProjectList",
                        component: () => import("@/views/project-list/ProjectList.vue"),
                        meta: { title: "项目列表", breadcrumb: true },
                    },
                    {
                        path: "/project-detail",
                        name: "ProjectDetail",
                        component: () => import("@/views/project-list/ProjectDetail.vue"),
                        meta: { title: "项目详情", breadcrumb: true },
                    },
                    {
                        path: "/task-detail",
                        name: "taskDetail",
                        component: () => import('@/views/project-list/components/TaskDetail.vue'),
                        meta: { title: "任务详情", breadcrumb: true },
                    },
                ],
            },
            // 模型验证相关路由
            {
                path: "/model-validation",
                name: "ModelValidation",
                component: () => import("@/views/model-validation/MyTaskView.vue"),
                meta: { title: "模型验证", breadcrumb: true },
                beforeEnter: (to, from, next) => {
                    if (!to.query.tab) {
                        next({ ...to, query: { ...to.query, tab: 'validation' } });
                    } else {
                        next();
                    }
                },
            },
            {
                path: "/model-validation/task-detail",
                name: "TaskDetail",
                component: () => import("@/views/model-validation/TaskDetail.vue"),
                meta: { title: "任务详情", breadcrumb: true },
            },
            {
                path: "/model-validation/qa-task-detail",
                name: "QATaskDetail",
                component: () => import("@/views/model-validation/QATaskDetail.vue"),
                meta: { title: "QA 任务详情", breadcrumb: true },
            },
            {
                path: "/model-validation/add-qa-task",
                name: "AddQATask",
                component: () => import("@/views/model-validation/AddQATask.vue"),
                meta: { title: "添加QA任务", breadcrumb: true },
            },
            {
                path: "/model-validation/error-result",
                name: "ErrorResult",
                component: () => import("@/views/model-validation/ErrorResult.vue"),
                meta: { title: "错误结果", breadcrumb: true },
            },
            // MD Validation 路由
            {
                path: "/md",
                name: "MD",
                redirect: '/md-validation',
                children: [
                    {
                        path: "/md-validation",
                        name: "MDValidation",
                        component: () => import("@/views/md-validation/MDValidation.vue"),
                        meta: { title: "MD 验证", breadcrumb: true },
                    },
                    {
                        path: "/md-validation/detail",
                        name: "MDDetail",
                        component: () => import("@/views/md-validation/MDDetail.vue"),
                        meta: { title: "MD 详情", breadcrumb: true },
                    },
                ],
            },
        ],
    },
];

const router = createRouter({
    history: createWebHistory(basePath),
    routes,
});

router.beforeEach(async(to, from, next) => {
    const exp = localStorage.getItem("exp");
    const now = Math.floor(Date.now() / 1000);

    // 清除登录相关信息的函数
    const clearLoginInfo = async () => {
        // 清除localStorage中的数据
        localStorage.clear();
        // 清除cookie中的token
        // 停止全局用户行为追踪
        try {
            await globalUserActionTracker.destroy();
        } catch (error) {
            console.error('停止用户行为追踪失败:', error);
        }
    };

    if (!exp) {
        if (to.path !== "/login") {
            await clearLoginInfo();
            next("/login");
            return;
        }
    } else {
        if (to.path === "/login") {
            await clearLoginInfo();
            next("/");
            return;
        }
    }

    if (exp && Number(exp) < now) {
        try {
            const res = await refreshApi();
            if (res.code === 0 && res.data && res.data.exp) {
                localStorage.setItem('exp', res.data.exp);
                next();
                return;
            } else {
                await clearLoginInfo();
                next('/login');
                return;
            }
        } catch (e) {
            await clearLoginInfo();
            next('/login');
            return;
        }
    }
    next();
});

// 路由后置守卫：确保登录用户的行为追踪正常运行
router.afterEach((to, from) => {
    const exp = localStorage.getItem("exp");
    // 如果用户已登录且不在登录页面，确保全局追踪已启用
    if (exp && to.path !== '/login') {
        // 全局追踪器会自动处理路由变化，这里只需要确保它已经初始化
        // console.log('路由变化:', from.path, '->', to.path);
    }
});

export default router;
