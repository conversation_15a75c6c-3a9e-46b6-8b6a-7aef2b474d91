import { createApp } from 'vue'
import { createPinia } from 'pinia'
import Antd from 'ant-design-vue'

// 只在开发环境下引入mock数据
if (import.meta.env.DEV) {
  import("./mock/index.ts").then(() => {
    console.log('Mock data initialized in development mode')
  })
}
console.log('env',import.meta.env)
// 从路由文件导入路由实例
import router from './router'
import App from './App.vue'
// 引入国际化配置
import i18n from './i18n'
// 引入样式
import './assets/main.css'
import 'ant-design-vue/dist/reset.css'
import 'virtual:uno.css'
// 引入全局用户行为追踪
import { initGlobalUserActionTracking } from './composable/useGlobalUserActionTracking'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(Antd)
app.use(i18n)

// 初始化全局用户行为追踪
initGlobalUserActionTracking(router)

app.mount('#app')