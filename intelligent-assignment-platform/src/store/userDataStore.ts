import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { userListApi } from '../api/system/index';

export const useUserDataStore = defineStore('userData', () => {
  // 状态：用户数据
  const userData = ref<any[]>([]);
  // 状态：加载状态
  const isLoading = ref(false);
  // 状态：错误信息
  const error = ref<string | null>(null);
  
  // 计算属性：判断数据是否已加载
  const hasData = computed(() => userData.value.length > 0);

  // 获取用户列表的方法
  const fetchUserData = async (forceRefresh = false) => {
    // 如果已有数据且不强制刷新，则直接返回现有数据
    if (hasData.value && !forceRefresh) {
      return userData.value;
    }
    
    try {
      isLoading.value = true;
      error.value = null;
      
      const res = await userListApi();
      if (res.data && Array.isArray(res.data)) {
        userData.value = res.data;
      }
      return userData.value;
    } catch (err: any) {
      error.value = err.message || '获取用户数据失败';
      console.error('获取用户数据出错:', error.value);
      return [];
    } finally {
      isLoading.value = false;
    }
  };

  // 刷新数据的方法（强制重新调用API）
  const refreshUserData = () => fetchUserData(true);
  
  return {
    userData,
    isLoading,
    error,
    hasData,
    fetchUserData,
    refreshUserData
  };
});  