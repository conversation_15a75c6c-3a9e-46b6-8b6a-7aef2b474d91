import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useGlobalLoadingStore = defineStore('globalLoading', () => {
  // 状态：是否正在加载
  const isLoading = ref(false)
  
  // 状态：加载文本
  const loadingText = ref('')
  
  // 状态：加载计数器（支持多个并发请求）
  const loadingCount = ref(0)
  
  // 显示全局加载状态
  const showLoading = (text?: string) => {
    loadingCount.value++
    isLoading.value = true
    if (text) {
      loadingText.value = text
    }
  }
  
  // 隐藏全局加载状态
  const hideLoading = () => {
    loadingCount.value = Math.max(0, loadingCount.value - 1)
    if (loadingCount.value === 0) {
      isLoading.value = false
      loadingText.value = ''
    }
  }
  
  // 强制隐藏加载状态（清除所有计数）
  const forceHideLoading = () => {
    loadingCount.value = 0
    isLoading.value = false
    loadingText.value = ''
  }
  
  // 设置加载文本（不改变加载状态）
  const setLoadingText = (text: string) => {
    loadingText.value = text
  }
  
  return {
    isLoading,
    loadingText,
    loadingCount,
    showLoading,
    hideLoading,
    forceHideLoading,
    setLoadingText
  }
})