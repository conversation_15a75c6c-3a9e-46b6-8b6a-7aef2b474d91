import { defineStore } from 'pinia'

export const useTreeStore = defineStore('tree', {
  state: () => ({
    treeDataMap: [],
    loading: false,
  }),
  actions: {
    async fetchTreeData(key: string, treeApi: Function) {
      if (this.treeDataMap[key] && this.treeDataMap[key].length > 0) return this.treeDataMap[key];
      this.loading = true;
      try {
        const res = await treeApi();
        this.treeDataMap[key] = res.data;
        return res.data;
      } catch (e) {
        console.error('请求失败:', e);
      } finally {
        this.loading = false;
      }
    }
  },
  getters: {
    getTreeOptions: (state) => (key: string, valueKey = 'id', labelKey = 'name', childrenKey = 'children') => {
      const toCascaderOptions = (tree) => {
        return (tree || []).map(item => ({
          value: item[valueKey],
          label: item[labelKey],
          children: item[childrenKey] && item[childrenKey].length
            ? toCascaderOptions(item[childrenKey])
            : undefined
        }));
      };
      return toCascaderOptions(state.treeDataMap[key]);
    }
  }
})