import { defineStore } from 'pinia';

// 菜单项接口定义
interface MenuItem {
  name: string;
  type: string;
  level?: number;
  authorized?: boolean;
  parent_id?: number;
  children?: MenuItem[];
  [key: string]: any; // 允许其他属性
}

// 通用的菜单扁平化函数
const flattenMenus = (menus: MenuItem[]): MenuItem[] => {
  let result: MenuItem[] = [];
  menus.forEach(menu => {
    // 确保保留所有原始字段，包括level
    result.push({...menu});
    if (menu.children && menu.children.length > 0) {
      result = result.concat(flattenMenus(menu.children));
    }
  });
  return result;
};

export const useMenuStore = defineStore('menu', {
  state: () => ({
    userMenus: [] as MenuItem[],
    loading: false,
  }),
  actions: {
    // 设置用户菜单数据
    setUserMenus(menus: MenuItem[]) {
      this.userMenus = menus;
    },
    
    // 从localStorage加载菜单数据
    loadMenusFromStorage() {
      const storedMenus = localStorage.getItem('userMenus');
      if (storedMenus) {
        try {
          const parsedMenus = JSON.parse(storedMenus) as MenuItem[];
          this.userMenus = parsedMenus;
        } catch (error) {
          this.userMenus = [];
        }
      }
    },
    
    // 清除菜单数据
    clearMenus() {
      this.userMenus = [] as MenuItem[];
      localStorage.removeItem('userMenus');
    }
  },
  getters: {
    // 获取菜单类型的数据
    getMenusByType: (state) => (type: string) => {
      const allMenus = flattenMenus(state.userMenus);
      return allMenus.filter(menu => menu.type === type);
    },
    
    // 获取顶级目录菜单（只返回已授权的）
    getTopLevelMenus: (state) => {
      return state.userMenus.filter(menu => 
        menu.type === '目录' && 
        menu.parent_id === 0 && 
        menu.authorized === true
      );
    },
    
    // 获取目录数据
    getDirectories: (state) => {
      const allMenus = flattenMenus(state.userMenus);
      const directories = allMenus.filter(menu => menu.type === '目录');
      return directories;
    },
    
    // 获取按钮数据
    getButtons: (state) => {
      const allMenus = flattenMenus(state.userMenus);
      return allMenus.filter(menu => menu.type === '按钮');
    },
    
    // 检查用户是否有某个菜单的权限
    hasMenuPermission: (state) => (menuName: string) => {
      const allMenus = flattenMenus(state.userMenus);
      return allMenus.some(menu => 
        (menu.type === '目录' || menu.type === '页面') && 
        menu.name === menuName
      );
    },
    
    // 检查菜单是否被授权（可以跳转）
    isMenuAuthorized: (state) => (menuName: string) => {
      const allMenus = flattenMenus(state.userMenus);
      const menu = allMenus.find(menu => 
        (menu.type === '目录' || menu.type === '页面') && 
        menu.name === menuName
      );
      return menu ? menu.authorized : false;
    },
    
    // 检查用户是否有某个按钮的权限
    hasButtonPermission: (state) => (buttonName: string) => {
      const allMenus = flattenMenus(state.userMenus);
      return allMenus.some(menu => 
        menu.type === '按钮' && 
        menu.name === buttonName
      );
    }
  }
})