# 用户行为追踪系统

本项目包含两套用户行为追踪系统：

## 1. 全局用户行为追踪 (推荐)

### 文件位置
- `useGlobalUserActionTracking.ts` - 全局用户行为追踪实现

### 特性
- **自动初始化**：应用启动时自动初始化
- **全局监听**：监听所有页面的用户行为（点击、鼠标移动、滚轮）
- **路由感知**：自动识别当前路由并提取任务信息
- **智能上报**：根据路由参数自动提取 taskId、sceneId 等信息
- **生命周期管理**：用户登录后启用，登出时自动停止并上报数据

### 使用方法

全局追踪已在 `main.ts` 中自动初始化，无需手动配置：

```typescript
// main.ts 中已包含
import { initGlobalUserActionTracking } from './composable/useGlobalUserActionTracking'
initGlobalUserActionTracking(router)
```

### 路由参数映射

系统会自动从路由中提取以下参数：
- `query.taskId` → `task_id`
- `query.sceneId` → `scene_id`
- `query.qaId` → `task_id`（QA任务）
- `query.sceneIds` → `scene_id`（多场景）

### 页面名称映射

根据路由名称自动设置页面名称：
- `TaskDetail` → "TaskDetail"
- `QATaskDetail` → "QATaskDetail"
- `ErrorResult` → "ErrorResult"
- `ModelValidation` → "ModelValidation"
- `Dashboard` → "Dashboard"

## 2. 组件级用户行为追踪

### 文件位置
- `useUserActionTracking.ts` - 组件级用户行为追踪实现

### 特性
- **组件级控制**：在特定组件中使用
- **手动配置**：需要手动传入 taskId、sceneId 等参数
- **灵活配置**：支持自定义上报间隔和页面名称

### 使用方法

#### 自动模式（推荐）
```typescript
import { useAutoUserActionTracking } from '@/composable/useUserActionTracking'

// 在组件中使用
const { userActionCount } = useAutoUserActionTracking({
  taskId: () => productInfo.taskId,
  sceneId: () => productInfo.sceneId,
  pageName: 'TaskDetail',
  reportInterval: 30000 // 可选，默认30秒
})
```

#### 手动模式
```typescript
import { useUserActionTracking } from '@/composable/useUserActionTracking'

const tracking = useUserActionTracking({
  taskId: () => productInfo.taskId,
  sceneId: () => productInfo.sceneId,
  pageName: 'TaskDetail'
})

// 手动初始化和销毁
onMounted(() => {
  tracking.initUserActionTracking()
})

onBeforeUnmount(async () => {
  await tracking.flushAllActions()
  tracking.destroyUserActionTracking()
})
```

## 数据上报格式

```typescript
{
  task_id: number,     // 任务ID
  scene_id: number,    // 场景ID
  ops: [{
    type: 'c' | 's' | 'k',  // c=点击, s=鼠标移动, k=滚轮
    num: number              // 行为次数
  }],
  page_name: string    // 页面名称
}
```

## 行为类型说明

- `c` (click): 用户点击行为
- `s` (scroll/mousemove): 用户鼠标移动行为
- `k` (keyboard/wheel): 用户滚轮行为

## 注意事项

1. **全局追踪优先**：建议使用全局追踪，避免重复监听
2. **登录状态**：只有登录用户的行为会被追踪
3. **路由变化**：路由变化时会自动上报当前页面数据并重置计数
4. **错误处理**：上报失败不会影响用户正常使用
5. **性能优化**：使用防抖机制，避免频繁上报

## 迁移指南

如果你的组件中已经使用了组件级追踪，可以考虑移除，因为全局追踪已经覆盖了所有页面：

```typescript
// 可以移除这些代码
// import { useAutoUserActionTracking } from '@/composable/useUserActionTracking'
// const { userActionCount } = useAutoUserActionTracking({...})
```

全局追踪会自动处理所有页面的用户行为监听和上报。