import { useIntersectionObserver } from '@vueuse/core'
import { ref } from 'vue'
// target 监听的目标 dom对象
/**
 * 数据懒加载函数
 * @param {Function} apiCallback 接收一个返回Promise的异步函数，用于获取数据
 */
// 传入api
export const useLazyData = (apiCallback) => {
  const result = ref([])
  const target = ref(null)
  // stop 停止观察
  const { stop } = useIntersectionObserver(
    // 监听的目标
    target,
    // isIntersecting 用来判断是否进入可视区
    // observerElement 被监听的DOM
   async ([{ isIntersecting }], observerElement) => {
      // isIntersecting 是否进入可视区
      if (isIntersecting) {
        stop()
        try {
          // 调用api函数获取数据
          const response = await apiCallback()
          if(response.code === '0000' || response.code === 0){
            result.value = response
          }
        } catch (error) {
          console.error('数据加载失败:', error)
        }
      }
    },
    // 配置选项 相交比例
    {
      threshold: 0
    }
  )
  return { result, target }
}
