import { ref, computed } from 'vue';

// 定义分页配置接口
interface PaginationOptions {
  defaultPageSize?: number;
  onChange?: (page: number, size: number) => void;
}

export function useTablePagination(options: PaginationOptions = {}) {
  const { defaultPageSize = 10 } = options;
  
  // 分页状态
  const page_no = ref<number>(1);
  const page_size = ref<number>(defaultPageSize);
  const total = ref<number>(0);

  // 计算得出的分页配置
  const paginationConfig = computed(() => ({
    total: total.value,
    current: page_no.value,
    pageSize: page_size.value,
    showSizeChanger: false,
    showTotal: (total: number): string => `Total ${total} items`,
    onChange: (page: number, size: number): void => {
      page_no.value = page;
      page_size.value = size;
      // 触发外部的表格数据加载函数
      if (typeof options.onChange === 'function') {
        options.onChange(page, size);
      }
    },
  }));

  // 重置分页到第一页
  const resetPagination = (): void => {
    page_no.value = 1;
  };

  return {
    page_no,
    page_size,
    total,
    paginationConfig,
    resetPagination,
  };
}