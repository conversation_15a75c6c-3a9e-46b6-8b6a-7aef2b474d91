import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';
// @ts-ignore
import { modelValidationReportApi } from '@/api/task/index';

/**
 * 用户行为追踪 Composable
 * 提供用户点击、鼠标移动、滚轮事件的监听和上报功能
 */
export function useUserActionTracking(options: {
  taskId: () => number | string;
  sceneId: () => number | string;
  pageName: string;
  reportInterval?: number; // 上报间隔，默认30秒
}) {
  const router = useRouter();
  const { taskId, sceneId, pageName, reportInterval = 30000 } = options;

  // 用户行为计数
  const userActionCount = ref({
    click: 0,
    mousemove: 0,
    wheel: 0
  });

  // 定时器引用
  let mouseMoveReportTimer: NodeJS.Timeout | null = null;
  let clickReportTimer: NodeJS.Timeout | null = null;
  let wheelReportTimer: NodeJS.Timeout | null = null;
  let routeChangeUnwatch: (() => void) | null = null;

  // actionType 映射
  const actionTypeMap = {
    'c': 'click',
    's': 'mousemove',
    'k': 'wheel'
  } as const;

  /**
   * 上报用户行为数据
   */
  const reportUserAction = async (actionType: 'c' | 's' | 'k') => {
    try {
      const countField = actionTypeMap[actionType];
      
      const reportData = {
        task_id: parseInt(String(taskId())) || 0,
        scene_id: parseInt(String(sceneId())) || 0,
        ops: [
          {
            type: actionType,
            num: userActionCount.value[countField] || 0
          }
        ],
        page_name: pageName
      };
      
      console.log('上报用户行为数据:', reportData);
      await modelValidationReportApi(reportData);
      console.log('用户行为上报成功');
      
      // 重置对应的计数
      userActionCount.value[countField] = 0;
    } catch (error) {
      console.error('上报用户行为失败:', error);
    }
  };

  /**
   * 处理用户点击事件
   */
  const handleUserClick = (event: MouseEvent) => {
    userActionCount.value.click++;
    
    // 如果定时器不存在，创建定时器
    if (!clickReportTimer) {
      clickReportTimer = setTimeout(() => {
        reportUserAction('c');
        clickReportTimer = null;
      }, reportInterval);
    }
  };

  /**
   * 处理用户鼠标移动事件
   */
  const handleUserMouseMove = (event: MouseEvent) => {
    userActionCount.value.mousemove++;
    
    // 如果定时器不存在，创建定时器
    if (!mouseMoveReportTimer) {
      mouseMoveReportTimer = setTimeout(() => {
        reportUserAction('s');
        mouseMoveReportTimer = null;
      }, reportInterval);
    }
  };

  /**
   * 处理用户滚轮事件
   */
  const handleUserWheel = (event: WheelEvent) => {
    userActionCount.value.wheel++;
    
    // 如果定时器不存在，创建定时器
    if (!wheelReportTimer) {
      wheelReportTimer = setTimeout(() => {
        reportUserAction('k');
        wheelReportTimer = null;
      }, reportInterval);
    }
  };

  /**
   * 添加事件监听器
   */
  const addUserActionListeners = () => {
    document.addEventListener('click', handleUserClick);
    document.addEventListener('mousemove', handleUserMouseMove);
    document.addEventListener('wheel', handleUserWheel);
  };

  /**
   * 移除事件监听器
   */
  const removeUserActionListeners = () => {
    document.removeEventListener('click', handleUserClick);
    document.removeEventListener('mousemove', handleUserMouseMove);
    document.removeEventListener('wheel', handleUserWheel);
    
    // 清除所有定时器
    clearAllTimers();
  };

  /**
   * 清除所有定时器
   */
  const clearAllTimers = () => {
    if (mouseMoveReportTimer) {
      clearTimeout(mouseMoveReportTimer);
      mouseMoveReportTimer = null;
    }
    if (clickReportTimer) {
      clearTimeout(clickReportTimer);
      clickReportTimer = null;
    }
    if (wheelReportTimer) {
      clearTimeout(wheelReportTimer);
      wheelReportTimer = null;
    }
  };

  /**
   * 监听路由变化
   */
  const watchRouteChange = () => {
    routeChangeUnwatch = router.afterEach((to, from) => {
      // 路由变化时清除定时器并重置计数
      clearAllTimers();
      userActionCount.value = {
        click: 0,
        mousemove: 0,
        wheel: 0
      };
      // console.log('路由变化，重置用户行为计数');
    });
  };

  /**
   * 停止监听路由变化
   */
  const stopWatchRouteChange = () => {
    if (routeChangeUnwatch) {
      routeChangeUnwatch();
      routeChangeUnwatch = null;
    }
  };

  /**
   * 初始化用户行为追踪
   */
  const initUserActionTracking = () => {
    addUserActionListeners();
    watchRouteChange();
  };

  /**
   * 销毁用户行为追踪
   */
  const destroyUserActionTracking = () => {
    removeUserActionListeners();
    stopWatchRouteChange();
  };

  /**
   * 手动上报所有待上报的行为数据
   */
  const flushAllActions = async () => {
    const promises = [];
    
    if (userActionCount.value.click > 0) {
      promises.push(reportUserAction('c'));
    }
    if (userActionCount.value.mousemove > 0) {
      promises.push(reportUserAction('s'));
    }
    if (userActionCount.value.wheel > 0) {
      promises.push(reportUserAction('k'));
    }
    
    if (promises.length > 0) {
      await Promise.allSettled(promises);
    }
  };

  return {
    userActionCount,
    initUserActionTracking,
    destroyUserActionTracking,
    flushAllActions,
    reportUserAction,
    addUserActionListeners,
    removeUserActionListeners,
    clearAllTimers
  };
}

/**
 * 自动初始化和销毁的用户行为追踪 Hook
 * 在组件挂载时自动初始化，卸载时自动销毁
 */
export function useAutoUserActionTracking(options: {
  taskId: () => number | string;
  sceneId: () => number | string;
  pageName: string;
  reportInterval?: number;
}) {
  const tracking = useUserActionTracking(options);

  onMounted(() => {
    tracking.initUserActionTracking();
  });

  onBeforeUnmount(async () => {
    // 组件卸载前上报所有待上报的数据
    await tracking.flushAllActions();
    tracking.destroyUserActionTracking();
  });

  return tracking;
}