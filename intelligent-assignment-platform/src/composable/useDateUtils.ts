/**
 * 日期工具函数组合式API
 * 提供各种日期格式化和处理方法
 */
export function useDateUtils() {
  /**
   * 格式化时间戳为可读日期时间格式
   * @param timestamp 时间戳（秒或毫秒）
   * @param format 可选的格式化模式，默认为 'MM/DD/YYYY HH:MM:SS'
   * @returns 格式化后的日期字符串
   */
  const formatTimestamp = (timestamp: number | string, format = 'MM/DD/YYYY HH:MM:SS') => {
    if (!timestamp) return '';

    // 判断是否为数字类型的时间戳，如果是字符串且已经是格式化的日期，则直接返回
    if (isNaN(Number(timestamp)) && !timestamp.toString().match(/^\d+$/)) {
      return timestamp.toString();
    }

    // 将时间戳转换为毫秒（如果是秒级时间戳，需要乘以1000）
    const date = new Date(Number(timestamp).toString().length === 10 ? Number(timestamp) * 1000 : Number(timestamp));

    // 格式化为指定格式
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    // 根据格式返回不同的日期格式
    switch (format) {
      case 'YYYY-MM-DD':
        return `${year}-${month}-${day}`;
      case 'DD/MM/YYYY':
        return `${day}/${month}/${year}`;
      case 'MM/DD/YYYY':
        return `${month}/${day}/${year}`;
      case 'YYYY-MM-DD HH:MM:SS':
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      case 'MM/DD/YYYY HH:MM:SS':
      default:
        return `${month}/${day}/${year} ${hours}:${minutes}:${seconds}`;
    }
  };

  /**
   * 格式化日期（兼容性方法，内部调用formatTimestamp）
   * @param timestamp 时间戳（秒）
   * @returns 格式化后的日期字符串 DD/MM/YYYY HH:MM:SS
   */
  const formatDate = (timestamp: number) => {
    if (!timestamp) return '';
    return formatTimestamp(timestamp, 'DD/MM/YYYY HH:MM:SS');
  };

  /**
   * 获取当前日期时间的格式化字符串
   * @param format 格式化模式
   * @returns 当前日期的格式化字符串
   */
  const getCurrentDate = (format = 'YYYY-MM-DD') => {
    return formatTimestamp(Date.now(), format);
  };

  /**
   * 计算两个日期之间的差值（天数）
   * @param date1 第一个日期（时间戳或Date对象）
   * @param date2 第二个日期（时间戳或Date对象）
   * @returns 两个日期之间的天数差
   */
  const getDaysDifference = (date1: number | Date, date2: number | Date) => {
    const d1 = date1 instanceof Date ? date1 : new Date(typeof date1 === 'number' && date1.toString().length === 10 ? date1 * 1000 : date1);
    const d2 = date2 instanceof Date ? date2 : new Date(typeof date2 === 'number' && date2.toString().length === 10 ? date2 * 1000 : date2);
    
    // 计算毫秒差并转换为天数
    const diffTime = Math.abs(d2.getTime() - d1.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  };

  return {
    formatTimestamp,
    formatDate,
    getCurrentDate,
    getDaysDifference
  };
}