import { useGlobalLoadingStore } from '../store/useGlobalLoadingStore'
import { useI18n } from 'vue-i18n'

/**
 * 全局加载状态管理 composable
 * 提供便捷的方法来控制全局加载状态
 */
export function useGlobalLoading() {
  const globalLoadingStore = useGlobalLoadingStore()
  const { t } = useI18n()

  /**
   * 显示全局加载状态
   * @param text 可选的加载文本，如果不提供则使用默认的国际化文本
   */
  const showLoading = (text?: string) => {
    const loadingText = text || t('common.loading')
    globalLoadingStore.showLoading(loadingText)
  }

  /**
   * 隐藏全局加载状态
   */
  const hideLoading = () => {
    globalLoadingStore.hideLoading()
  }

  /**
   * 强制隐藏加载状态（清除所有计数）
   */
  const forceHideLoading = () => {
    globalLoadingStore.forceHideLoading()
  }

  /**
   * 设置加载文本（不改变加载状态）
   * @param text 加载文本
   */
  const setLoadingText = (text: string) => {
    globalLoadingStore.setLoadingText(text)
  }

  /**
   * 显示刷新状态
   */
  const showRefreshing = () => {
    showLoading(t('common.refreshing'))
  }

  /**
   * 显示处理状态
   */
  const showProcessing = () => {
    showLoading(t('common.processing'))
  }

  /**
   * 显示提交状态
   */
  const showSubmitting = () => {
    showLoading(t('common.submitting'))
  }

  /**
   * 显示保存状态
   */
  const showSaving = () => {
    showLoading(t('common.saving'))
  }

  /**
   * 包装异步函数，自动显示和隐藏加载状态
   * @param asyncFn 异步函数
   * @param loadingText 可选的加载文本
   * @returns 包装后的异步函数
   */
  const withLoading = <T extends (...args: any[]) => Promise<any>>(
    asyncFn: T,
    loadingText?: string
  ): T => {
    return (async (...args: Parameters<T>) => {
      try {
        showLoading(loadingText)
        const result = await asyncFn(...args)
        return result
      } finally {
        hideLoading()
      }
    }) as T
  }

  return {
    // 状态
    isLoading: globalLoadingStore.isLoading,
    loadingText: globalLoadingStore.loadingText,
    loadingCount: globalLoadingStore.loadingCount,
    
    // 基础方法
    showLoading,
    hideLoading,
    forceHideLoading,
    setLoadingText,
    
    // 便捷方法
    showRefreshing,
    showProcessing,
    showSubmitting,
    showSaving,
    
    // 高级方法
    withLoading
  }
}