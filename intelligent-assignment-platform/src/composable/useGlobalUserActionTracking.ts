import { ref, App } from 'vue';
import { Router } from 'vue-router';
// @ts-ignore
import { modelValidationReportApi } from '@/api/task/index';

/**
 * 全局用户行为追踪管理器
 * 在应用启动时初始化，监听所有页面的用户行为
 */
class GlobalUserActionTracker {
  private userActionCount = ref({
    click: 0,
    mousemove: 0,
    wheel: 0
  });

  private mouseMoveReportTimer: NodeJS.Timeout | null = null;
  private clickReportTimer: NodeJS.Timeout | null = null;
  private wheelReportTimer: NodeJS.Timeout | null = null;
  private router: Router | null = null;
  private currentRoute: any = null;
  private reportInterval = 30000; // 30秒上报间隔
  private isInitialized = false;

  // actionType 映射
  private actionTypeMap = {
    'c': 'click',
    's': 'mousemove',
    'k': 'wheel'
  } as const;

  /**
   * 初始化全局用户行为追踪
   */
  init(router: Router) {
    if (this.isInitialized) {
      console.warn('全局用户行为追踪已经初始化');
      return;
    }

    this.router = router;
    this.addEventListeners();
    this.watchRouteChange();
    this.isInitialized = true;
    
    // console.log('全局用户行为追踪已初始化');
  }

  /**
   * 销毁全局用户行为追踪
   */
  async destroy() {
    if (!this.isInitialized) {
      return;
    }

    // 先上报所有待上报的数据
    await this.flushAllActions();
    
    this.removeEventListeners();
    this.clearAllTimers();
    this.isInitialized = false;
    
    // console.log('全局用户行为追踪已销毁');
  }

  /**
   * 获取当前路由的任务信息
   */
  private getCurrentTaskInfo() {
    if (!this.currentRoute) {
      return { taskId: 0, sceneId: 0, pageName: 'Unknown' };
    }

    const route = this.currentRoute;
    const query = route.query || {};
    const params = route.params || {};
    
    // 根据路由名称和参数提取任务信息
    let taskId = 0;
    let sceneId = 0;
    let pageName = route.name || 'Unknown';

    // 从查询参数中提取任务信息
    if (query.taskId) {
      taskId = parseInt(String(query.taskId)) || 0;
    }
    if (query.sceneId) {
      sceneId = parseInt(String(query.sceneId)) || 0;
    }
    if (query.qaId) {
      taskId = parseInt(String(query.qaId)) || 0;
    }
    if (query.sceneIds) {
      sceneId = parseInt(String(query.sceneIds)) || 0;
    }

    // 根据路由名称设置页面名称
    switch (route.name) {
      case 'TaskDetail':
        pageName = 'TaskDetail';
        break;
      case 'QATaskDetail':
        pageName = 'QATaskDetail';
        break;
      case 'ErrorResult':
        pageName = 'ErrorResult';
        break;
      case 'ModelValidation':
        pageName = 'ModelValidation';
        break;
      case 'Dashboard':
        pageName = 'Dashboard';
        break;
      default:
        pageName = String(route.name || 'Unknown');
    }

    return { taskId, sceneId, pageName };
  }

  /**
   * 上报用户行为数据
   */
  private async reportUserAction(actionType: 'c' | 's' | 'k') {
    try {
      const countField = this.actionTypeMap[actionType];
      const { taskId, sceneId, pageName } = this.getCurrentTaskInfo();
      
      const reportData = {
        task_id: taskId,
        scene_id: sceneId,
        ops: [
          {
            type: actionType,
            num: this.userActionCount.value[countField] || 0
          }
        ],
        page_name: pageName
      };
      
      // console.log('全局上报用户行为数据:', reportData);
      await modelValidationReportApi(reportData);
      // console.log('全局用户行为上报成功');
      
      // 重置对应的计数
      this.userActionCount.value[countField] = 0;
    } catch (error) {
      console.error('全局上报用户行为失败:', error);
    }
  }

  /**
   * 处理用户点击事件
   */
  private handleUserClick = (event: MouseEvent) => {
    this.userActionCount.value.click++;
    
    if (!this.clickReportTimer) {
      this.clickReportTimer = setTimeout(() => {
        this.reportUserAction('c');
        this.clickReportTimer = null;
      }, this.reportInterval);
    }
  };

  /**
   * 处理用户鼠标移动事件
   */
  private handleUserMouseMove = (event: MouseEvent) => {
    this.userActionCount.value.mousemove++;
    
    if (!this.mouseMoveReportTimer) {
      this.mouseMoveReportTimer = setTimeout(() => {
        this.reportUserAction('s');
        this.mouseMoveReportTimer = null;
      }, this.reportInterval);
    }
  };

  /**
   * 处理用户滚轮事件
   */
  private handleUserWheel = (event: WheelEvent) => {
    this.userActionCount.value.wheel++;
    
    if (!this.wheelReportTimer) {
      this.wheelReportTimer = setTimeout(() => {
        this.reportUserAction('k');
        this.wheelReportTimer = null;
      }, this.reportInterval);
    }
  };

  /**
   * 添加事件监听器
   */
  private addEventListeners() {
    document.addEventListener('click', this.handleUserClick);
    document.addEventListener('mousemove', this.handleUserMouseMove);
    document.addEventListener('wheel', this.handleUserWheel);
  }

  /**
   * 移除事件监听器
   */
  private removeEventListeners() {
    document.removeEventListener('click', this.handleUserClick);
    document.removeEventListener('mousemove', this.handleUserMouseMove);
    document.removeEventListener('wheel', this.handleUserWheel);
  }

  /**
   * 清除所有定时器
   */
  private clearAllTimers() {
    if (this.mouseMoveReportTimer) {
      clearTimeout(this.mouseMoveReportTimer);
      this.mouseMoveReportTimer = null;
    }
    if (this.clickReportTimer) {
      clearTimeout(this.clickReportTimer);
      this.clickReportTimer = null;
    }
    if (this.wheelReportTimer) {
      clearTimeout(this.wheelReportTimer);
      this.wheelReportTimer = null;
    }
  }

  /**
   * 监听路由变化
   */
  private watchRouteChange() {
    if (!this.router) {
      return;
    }

    // 监听路由变化
    this.router.afterEach((to, from) => {
      // 路由变化时先上报当前页面的数据
      this.flushAllActions();
      
      // 更新当前路由信息
      this.currentRoute = to;
      
      // 重置计数
      this.userActionCount.value = {
        click: 0,
        mousemove: 0,
        wheel: 0
      };
      
      // console.log('路由变化，重置用户行为计数:', to.name);
    });

    // 设置初始路由
    this.currentRoute = this.router.currentRoute.value;
  }

  /**
   * 手动上报所有待上报的行为数据
   */
  private async flushAllActions() {
    const promises = [];
    
    if (this.userActionCount.value.click > 0) {
      promises.push(this.reportUserAction('c'));
    }
    if (this.userActionCount.value.mousemove > 0) {
      promises.push(this.reportUserAction('s'));
    }
    if (this.userActionCount.value.wheel > 0) {
      promises.push(this.reportUserAction('k'));
    }
    
    if (promises.length > 0) {
      await Promise.allSettled(promises);
    }
  }

  /**
   * 获取当前用户行为计数（用于调试）
   */
  getUserActionCount() {
    return this.userActionCount.value;
  }
}

// 创建全局实例
const globalUserActionTracker = new GlobalUserActionTracker();

/**
 * Vue 插件：全局用户行为追踪
 */
export const GlobalUserActionTrackingPlugin = {
  install(app: App, options: { router: Router }) {
    const { router } = options;
    
    // 在应用挂载后初始化
    app.config.globalProperties.$nextTick(() => {
      globalUserActionTracker.init(router);
    });

    // 在应用卸载时销毁
    app.config.globalProperties.$onBeforeUnmount = () => {
      globalUserActionTracker.destroy();
    };

    // 提供全局访问
    app.config.globalProperties.$userActionTracker = globalUserActionTracker;
    app.provide('userActionTracker', globalUserActionTracker);
  }
};

/**
 * 直接初始化函数（推荐使用）
 */
export function initGlobalUserActionTracking(router: Router) {
  globalUserActionTracker.init(router);
  
  // 页面卸载时上报数据
  window.addEventListener('beforeunload', () => {
    globalUserActionTracker.destroy();
  });
  
  return globalUserActionTracker;
}

export default globalUserActionTracker;