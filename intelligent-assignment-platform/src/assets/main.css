body {
    border-radius: 8px;
    background: #005290B3;
}
html, body, #app {
    height: 100%;
}

.ant-modal .ant-modal-footer .ant-btn + .ant-btn:not(.ant-dropdown-trigger) {
    margin-inline-start: 20px;
}
.ant-modal-footer .ant-btn {
    width: 80px !important;
}
.ant-tabs-nav {
    margin: 0;
}
.ant-tabs-top >.ant-tabs-nav {
    margin: 0;
    border-radius: 8px;
    line-height: 24px;
}
.ant-input,.ant-input-number,.ant-btn {
    border-radius: 4px !important;
}
.ant-table-thead > tr > th,
.ant-table-tbody > tr > td.ant-table-cell {
  height: 48px !important; 
  padding: 0 16px !important; 
}
.ant-checkbox-disabled .ant-checkbox-inner {
    background: #f0f0f0!important;
}
.ant-tabs-ink-bar {
    border-radius: 10px !important;
}