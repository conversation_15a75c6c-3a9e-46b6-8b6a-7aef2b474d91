<template>
  <div class="flex justify-center items-center w-full h-screen">
    <div class="bg-white rounded-3 w-[480px] h-[610px]">
      <div v-if="!isRegister">
        <!-- <span class="w-[274px] h-[46px] text-8 pl-28 pr-28 text-black font-sans font-500 leading-60">Validation System</span> -->
        <div class="flex justify-center items-center my-12">
          <img src="@/assets/images/login-logo.svg" alt="">
        </div>
        <a-form :model="formState" autocomplete="off" @finish="onFinish" class="mx-auto w-80">
          <a-form-item name="username" :rules="[{ required: true, message: t('user.usernameRequired') }]">
            <a-input v-model:value="formState.username" :placeholder="t('user.account')" class="bg-btn-primary h-10" />
          </a-form-item>
          <a-form-item name="password" :rules="[{ required: true, message: t('user.passwordRequired') }]">
            <a-input-password v-model:value="formState.password" :placeholder="t('user.password')" class="bg-btn-primary h-10"/>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" html-type="submit" class="mt-2 text-[16px] h-10" block>{{ t('user.login') }}</a-button>
          </a-form-item>
          <div>
            <a-checkbox v-model:checked="formState.remember" class="text-[#BFBFBF]">{{ t('user.autoLogin') }}</a-checkbox>
            <div class="float-end">
              <!-- <a href="#" class="text-[#BFBFBF]">忘记密码</a> -->
              <!-- <span class="mx-2 text-[#BFBFBF]">|</span> -->
              <!-- <a href="#" class="text-[#BFBFBF]" @click.prevent="handleRegister">{{ t('user.register') }}</a> -->
            </div>
          </div>
        </a-form>
      </div>
      <div class="mb-2" v-else>
        <Register @backToLogin="isRegister = false" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import Register from '@/page/login/register.vue'
import { loginByUserApi } from '@/api/user';
import { message } from 'ant-design-vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

const router = useRouter();
const isRegister = ref(false);
const formState = ref({
  username: '',
  password: '',
  remember: false,
});

const onFinish = async () => {
  const data = {
    username: formState.value.username,
    password: formState.value.password,
  };
  try {
    const res = await loginByUserApi(data);
    if (res.code === 0) {
      message.success(t('user.loginSuccess'));
      localStorage.setItem('exp', res.data.exp);
      if (formState.value.remember) {
        localStorage.setItem('autoLoginInfo', JSON.stringify({
          username: formState.value.username,
          password: formState.value.password,
          remember: true
        }));
      } else {
        localStorage.removeItem('autoLoginInfo');
      }
      router.push({ path: '/' });
    } else {
      message.error(res.msg || t('user.loginFailed'));
    }
  } catch (err) {
    message.error(err.message || t('user.loginFailed'));
  }
};

const handleRegister = () => {
  isRegister.value = true;
};

onMounted(() => {
  const autoLoginInfo = localStorage.getItem('autoLoginInfo');
  if (autoLoginInfo) {
    const { username, password, remember } = JSON.parse(autoLoginInfo);
    formState.value.username = username;
    formState.value.password = password;
    formState.value.remember = remember;
    // 自动登录
    if (remember && username && password) {
      onFinish();
    }
  }
});
</script>

<style scoped>
:deep(.ant-form-item) {
  margin-bottom: 16px;
}
</style>