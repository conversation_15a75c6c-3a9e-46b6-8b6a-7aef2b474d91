<template>
  <div class="flex items-center">
    <a href="#" class="text-[#000000] text-[14px] p-5 no-underline" @click.prevent="handleLoginBack">
      <LeftOutlined />&nbsp;返回
    </a>
  </div>
  <div class="text-center text-[24px] font-medium mb-7">注册智能作业平台</div>
  <a-form :model="registerForm" autocomplete="off" @finish="onRegister" @finishFailed="onFinishFailed"
    class="mx-auto w-80">
    <a-form-item name="name" :rules="[{ required: true, message: '请输入注册姓名!' }]">
      <a-input v-model:value="registerForm.name" placeholder="请输入注册姓名" class="bg-btn-primary h-10" />
    </a-form-item>
    <a-form-item name="username" :rules="[{ required: true, message: '请输入注册账号!' }]">
      <a-input v-model:value="registerForm.username" placeholder="请输入注册账号" class="bg-btn-primary h-10" />
    </a-form-item>
    <a-form-item name="password" :rules="[{ required: true, message: '请输入密码!' }]">
      <a-input-password v-model:value="registerForm.password" placeholder="请输入密码" class="bg-btn-primary h-10" />
    </a-form-item>
    <a-form-item name="phone" :rules="[{ required: true, message: '请输入注册手机号!' }]">
      <a-input v-model:value="registerForm.phone" placeholder="请输入注册手机号" class="bg-btn-primary h-10">
        <template #suffix>
          <span class="text-[#1677FF]" @click="handleSendCode">发送验证码</span>
        </template>
      </a-input>
    </a-form-item>
    <a-form-item name="code" :rules="[{ required: true, message: '请输入验证码!' }]">
      <a-input v-model:value="registerForm.code" placeholder="请输入验证码" class="bg-btn-primary h-10 rounded-lg border-0" />
    </a-form-item>
    <a-form-item name="desc">
      <a-input v-model:value="registerForm.desc" placeholder="请输入注册说明"
        class="bg-btn-primary h-10 rounded-lg border-0" />
    </a-form-item>
    <a-form-item>
      <a-button type="primary" html-type="submit" class="w-full h-10 text-[18px] mt-2" block>注册</a-button>
    </a-form-item>
  </a-form>
</template>

<script setup>
import { ref } from 'vue'
import { LeftOutlined } from '@ant-design/icons-vue';
import { registerByUserApi } from '@/api/user';
import { message } from 'ant-design-vue';

const emit = defineEmits(['backToLogin'])
const registerForm = ref({
  name: '',
  username: '',
  phone: '',
  code: '',
  password: ''
});

const onRegister = async () => {
  const data = {
    username: registerForm.value.username,
    name: registerForm.value.name,
    phone: registerForm.value.phone,
    password: registerForm.value.password
  };
  try {
    const res = await registerByUserApi(data);
    if (res.code === 0) {
      message.success('注册成功');
    }
    emit('backToLogin');
  } catch (err) {
    message.error(err.message);
  }
};

const handleLoginBack = () => {
  emit('backToLogin')
};

const handleSendCode = () => {
  console.log('发送验证码');
};
</script>
