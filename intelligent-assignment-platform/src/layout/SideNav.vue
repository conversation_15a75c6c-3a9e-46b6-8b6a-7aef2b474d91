<template>
  <div class="side-nav p-2" :class="{ 'side-nav-collapsed': collapsed }">
    <!-- 用户信息区域 -->
    <div class="user-info-container">
      <div class="avatar-container">
        <a-dropdown :trigger="['click']" placement="bottomRight" ref="userDropdown" v-model:open="userDropdownVisible">
          <a-avatar :size="collapsed ? 48:32" :icon="iconName" class="cursor-pointer bg-green-6 text-[12px]" />
          <template #overlay>
            <a-menu class="user-dropdown-menu">
              <div class="user-info-header p-3">
                <div class="flex items-center">
                  <a-avatar :size="48" :icon="iconName" class="cursor-pointer bg-green-6"/>
                  <div class="ml-4 flex flex-col w-full">
                    <div class="font-medium overflow-hidden whitespace-nowrap text-ellipsis flex flex-row">
                      <div class="text-[20px]">{{ userData.name }}</div>
                      <div class="m-1" v-show="!collapsed" @click="showUserInfoEdit">
                          <img src="@/assets/images/edit.svg" alt="" class="w-4 h-4">
                      </div>
                    </div>
                    <div class="text-[14px] text-gray-500">{{ userData.role }}</div>
                  </div>
                </div>
              </div>
              <a-menu-item key="modify-password" @click="showModifyPassword">
                <div class="m-2 rounded-lg">{{ $t('user.modifyPassword') }}</div>
              </a-menu-item>
              <a-menu-item key="logout" @click="handleLogout">
                <div class="m-2 rounded-lg">{{ $t('user.logout') }}</div>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
      <div class="user-details" v-show="!collapsed">
        <div class="user-name font-medium overflow-hidden whitespace-nowrap text-ellipsis max-w-[80px]">
          {{ userData.name }}
        </div>
        <div class="user-role">{{ userData.role }}</div>
      </div>
    </div>

    <!-- 用户信息编辑对话框 -->
    <UserInfoEditDialog v-model:open="userInfoEditVisible" :userData="userData" @submit="handleUserInfoSubmit" />

    <!-- 修改密码对话框 -->
    <ModifyPasswordDialog v-model:open="modifyPasswordVisible" @submit="handlePasswordSubmit" />

    <!-- 菜单区域 -->
    <div class="menu-container">
      <div v-for="(item, index) in menuItems" :key="index" class="menu-item"
        :class="{ active: activeMenu === item.path }" @click="navigateTo(item)">
        <!-- <span class="menu-icon">
          <component :is="item.icon" />
        </span> -->
        <img class="w-5 h-5 mr-2" :src="getIcon(activeMenu === item.path ? item.hoverIcon : item.icon)" alt="">
        <span class="menu-title" v-show="!collapsed">{{ $t(item.titleKey) }}</span>
      </div>
    </div>

    <!-- Logo区域 -->
    <div class="logo-container">
      <img src="@/assets/images/retail-eye.png" alt="Logo" class="logo" />
    </div>

    <!-- 语言切换 -->
    <div class="language-switcher-container" v-if="false">
      <LanguageSwitcher />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, provide, reactive } from 'vue';
import { useI18n } from 'vue-i18n';
import LanguageSwitcher from '../components/LanguageSwitcher.vue';
import UserInfoEditDialog from '../components/UserInfoEditDialog.vue';
import ModifyPasswordDialog from '../components/ModifyPasswordDialog.vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { changePasswordApi, logoutApi, userApi, realNameApi } from '../api/user';
import { debounceAsync } from '@/utils/debounce';
import { useMenuStore } from '@/store/useMenuStore';

function getIcon(filename) {
  return new URL(`../assets/images/${filename}`, import.meta.url).href
}
const router = useRouter();
const route = useRoute();
const { t } = useI18n();
const menuStore = useMenuStore();
// 侧边栏收起状态
const collapsed = ref(false);
// 是否为移动设备视图
const isMobileView = ref(false);
// 当前系统管理选中的子页面
const activeSystemTab = ref('menu');
// 用户信息编辑对话框可见性
const userInfoEditVisible = ref(false);
// 修改密码对话框可见性
const modifyPasswordVisible = ref(false);
// 用户下拉菜单引用
const userDropdown = ref(null);
// 用户下拉菜单可见性
const userDropdownVisible = ref(false);
// 用户数据
const userData = reactive({
  name: '',
  team: '',
  role: ''
});

const getUser = async() => {
  const res = await userApi();
  userData.name = res.data.name;
  userData.team = res.data.department;
  userData.role = Array.isArray(res.data.role_name) ? res.data.role_name[0] : res.data.role_name;
}

const iconName = computed(() => {
  const names = userData.name.split(' ');
  let result = '';
  for (let i = 0; i < Math.min(3, names.length); i++) {
    result += names[i][0]?.toUpperCase() || ''; // 防止 undefined
  }
  return result; // 返回 "SAT"
});
// 检测是否为移动设备视图
const checkMobileView = () => {
  isMobileView.value = window.innerWidth <= 768;
  // 在移动视图下，默认收起侧边栏
  // collapsed.value = isMobileView.value && window.innerWidth <= 480;
};

// 监听窗口大小变化
onMounted(() => {
  checkMobileView();
  window.addEventListener('resize', checkMobileView);
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', checkMobileView);
});

// 暴露属性给父组件
defineExpose({
  collapsed,
  activeSystemTab
});

// 提供系统管理选中的子页面给其他组件
provide('activeSystemTab', activeSystemTab);

// 默认菜单项配置（作为fallback）
const defaultMenuItems = [
  {
    title: '客户列表',
    titleKey: 'nav.projectList',
    path: '/project-list',
    icon: 'project.svg',
    hoverIcon: 'project-hover.svg'
  },
  {
    title: '模型验证',
    titleKey: 'nav.modelValidation',
    path: '/model-validation',
    icon: 'task.svg',
    hoverIcon: 'task-hover.svg'
  },
  {
    title: 'MD Validation',
    titleKey: 'nav.mdValidation',
    path: '/md-validation',
    icon: 'md.svg',
    hoverIcon: 'md-hover.svg'
  },
  {
    title: '系统管理',
    titleKey: 'nav.systemManagement',
    path: '/system',
    icon: 'setting.svg',
    hoverIcon: 'setting-hover.svg'
  },
  {
    title: 'Dashboard',
    titleKey: 'nav.dashboard',
    path: '/dashboard',
    icon: 'dashboard.svg',
    hoverIcon: 'dashboard-hover.svg'
  }
];

// 菜单项映射配置
const menuItemsMapping = {
  'Project List': {
    title: '客户列表',
    titleKey: 'nav.projectList',
    path: '/project-list',
    icon: 'project.svg',
    hoverIcon: 'project-hover.svg'
  },
  'My Task': {
    title: '模型验证',
    titleKey: 'nav.modelValidation',
    path: '/model-validation',
    icon: 'task.svg',
    hoverIcon: 'task-hover.svg'
  },
  'MD Validation': {
    title: 'MD Validation',
    titleKey: 'nav.mdValidation',
    path: '/md-validation',
    icon: 'md.svg',
    hoverIcon: 'md-hover.svg'
  },
  'Setting': {
    title: '系统管理',
    titleKey: 'nav.systemManagement',
    path: '/system',
    icon: 'setting.svg',
    hoverIcon: 'setting-hover.svg'
  },
  'Dashboard': {
    title: 'Dashboard',
    titleKey: 'nav.dashboard',
    path: '/dashboard',
    icon: 'dashboard.svg',
    hoverIcon: 'dashboard-hover.svg'
  }
};

// 动态菜单项
const menuItems = computed(() => {
  // 从store获取用户有权限的顶级目录菜单
  const userMenus = menuStore.getDirectories;
  
  if (userMenus.length === 0) {
    // 如果没有权限数据，使用默认菜单
    return defaultMenuItems;
  }
  
  // 按level字段从小到大排序
  const sortedMenus = [...userMenus].sort((a, b) => {
    // 如果level字段存在，则按level排序
    if (a.level !== undefined && b.level !== undefined) {
      return a.level - b.level;
    }
    // 如果level字段不存在，保持原顺序
    return 0;
  });
  
  // 根据用户权限生成菜单项
  const mappedMenuItems = sortedMenus.map(menu => {
    // 如果有预配置的映射，使用映射配置
    if (menuItemsMapping[menu.name]) {
      const mappedItem = {
        ...menuItemsMapping[menu.name],
        title: menu.name,
        name: menu.name,
        id: menu.id,
        level: menu.level, // 保留level字段
        authorized: menu.authorized, // 传递授权状态
        path: menuItemsMapping[menu.name].path || menu.path // 优先使用接口返回的路径
      };
      return mappedItem;
    }
    
    // 如果没有预配置，使用默认配置
    const defaultItem = {
      title: menu.name,
      titleKey: menu.name, // 使用菜单名称作为titleKey
      path: menu.path || `/${menu.name.toLowerCase().replace(/\s+/g, '-')}`, // 生成默认路径
      icon: 'setting.svg', // 默认图标
      hoverIcon: 'setting-hover.svg', // 默认悬停图标
      name: menu.name,
      id: menu.id,
      level: menu.level, // 保留level字段
      authorized: menu.authorized // 传递授权状态
    };
    return defaultItem;
  });
  
  return mappedMenuItems;
});
// 系统管理子菜单
// const systemSubMenus = [
//   {
//     title: '菜单管理',
//     titleKey: 'system.menu',
//     path: '/menu',
//     key: 'menu'
//   },
//   {
//     title: '角色管理',
//     titleKey: 'system.role',
//     path: '/role',
//     key: 'role'
//   },
//   {
//     title: '部门管理',
//     titleKey: 'system.department',
//     path: '/depart',
//     key: 'depart'
//   },
//   {
//     title: '用户管理',
//     titleKey: 'system.user',
//     path: '/user',
//     key: 'user'
//   },
//   {
//     title: '注册审核',
//     titleKey: 'system.registration',
//     path: '/registration',
//     key: 'registration'
//   }
// ];

// 当前激活的菜单
const activeMenu = computed(() => {
  return route.path;
});

// 导航方法
const navigateTo = (menuItem) => {
  // 如果传入的是字符串路径（兼容旧的调用方式）
  if (typeof menuItem === 'string') {
    router.push(menuItem);
    return;
  }
  
  // 如果有路径，则进行跳转
  if (menuItem.path) {
    // 检查菜单是否被授权
    if (menuItem.authorized === false) {
      // 未授权时添加authorized=0参数
      router.push({
        path: menuItem.path,
        query: { authorized: '0' }
      });
    } else {
      // 有权限时正常跳转
      router.push(menuItem.path);
    }
  }
};

// 显示用户信息编辑对话框
const showUserInfoEdit = (event) => {
  // 阻止事件冒泡，避免触发导航
  if (event) event.stopPropagation();
  userInfoEditVisible.value = true;
  // 关闭avatar下拉菜单
  userDropdownVisible.value = false;
};

// 显示修改密码对话框
const showModifyPassword = () => {
  modifyPasswordVisible.value = true;
  userDropdownVisible.value = false;
};

// 处理密码修改提交
const handlePasswordSubmit = debounceAsync(async(data) => {
  const res = await changePasswordApi(data);
  if(res.code === 0) {
    message.success(res.msg);
  }
  modifyPasswordVisible.value = false;
}, 300);

// 处理登出
const handleLogout = debounceAsync(async() => {
  //清除token过期时间戳
  localStorage.clear();
  // 清除菜单权限数据
  menuStore.clearMenus();
  //请求退出登录接口
  const res = await logoutApi();
  if(res.code === 0) {
    message.success(res.msg || '退出成功');
  }
  router.push('/login');
}, 300);

const handleUserInfoSubmit = debounceAsync(async(data) => {
  try {
    userData.name = data.name;
    const res = await realNameApi({real_name: userData.name});
    if(res.code === 0) {
      message.success(t('user.updateSuccess'));
      await getUser();
    } else {
      message.error(t('common.updateFailed'));
    }
  } catch(error) {
    console.error('更新用户信息失败:', error);
  }
}, 300);

// 初始化
const initializeApp = async () => {
  await getUser();
  // 从localStorage加载菜单权限数据
  menuStore.loadMenusFromStorage();
};

initializeApp();
</script>

<style scoped>
.side-nav {
  width: 156px;
  height: 100vh;
  color: white;
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;

  .language-switcher-container {
    margin-top: 10px;
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
  }

  transition: all 0.3s ease;
  z-index: 1000;
}

.side-nav-collapsed {
  width: 80px;
}

/* 用户信息区域样式 */
.user-info-container {
  padding: 20px 16px;
  display: flex;
  align-items: center;
  /* border-bottom: 1px solid rgba(255, 255, 255, 0.1); */
  position: relative;
}

:deep(.user-info-header .ant-avatar) {
  width: 70px !important;
}

.avatar-container {
  margin-right: 12px;
  display: flex;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

/* 用户下拉菜单样式 */
.user-dropdown-menu {
  min-width: 240px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
}

.user-dropdown-menu .user-info-header {
  /* background-color: #f9f9f9; */
  border-bottom: 1px solid #f0f0f0;
}

:deep(.ant-dropdown-menu-item) {
  margin: 8px !important;
  padding: 12px 16px;
  transition: all 0.3s;
}

.user-dropdown-menu .ant-dropdown-menu-item:hover {
  background-color: #f5f5f5;
}

.user-details {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.user-role {
  font-size: 12px;
  opacity: 0.8;
  color: #fff;
}

.edit-button {
  position: absolute;
  right: 36%;
  top: 14%;
  transform: translateY(-50%);
  cursor: pointer;
  font-size: 16px;
  opacity: 0.8;
  transition: all 0.3s;
}

.edit-button:hover {
  opacity: 1;
  color: #1890ff;
}

.logo-container {
  padding: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 64px;
  /* border-bottom: 1px solid rgba(255, 255, 255, 0.1); */
  overflow: hidden;
  margin-bottom: 20px;
  /* 将logo推到底部 */
}

.logo {
  height: 32px;
  transition: all 0.3s;
}

.menu-container {
  flex: 1;
  padding: 12px 0;
  overflow-y: auto;
  overflow-x: hidden;
}

.menu-item {
  padding: 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 8px;
  height: 36px;
  line-height: 36px;
  width: 132px;
}

.menu-item:hover {
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.1);
}

.menu-item.active {
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.1);
}

.menu-icon {
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.3s;
}

.menu-title {
  font-size: 14px;
  transition: all 0.3s;
  opacity: 1;
}

.toggle-button {
  padding: 16px;
  display: none;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s;
}

.toggle-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.side-nav-collapsed .menu-item {
  padding: 12px;
  justify-content: center;
}

.side-nav-collapsed .menu-icon {
  margin-right: 0;
  font-size: 18px;
}

.side-nav-collapsed .user-info-container {
  justify-content: center;
  padding: 16px 8px;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .side-nav {
    width: 60px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  }

  /* 保持侧边栏始终显示，不自动隐藏 */
  /* .side-nav-collapsed {
    width: 0;
    overflow: hidden;
    transform: translateX(-100%);
  } */
  .side-nav-collapsed {
    width: 60px;
    overflow: visible;
    transform: none;
  }

  .menu-title {
    display: none;
  }

  .menu-item {
    padding: 12px;
    justify-content: center;
  }

  .menu-icon {
    margin-right: 0;
  }

  .logo {
    height: 24px;
  }

  .logo-container {
    padding: 12px 8px;
    height: 50px;
  }

  .toggle-button {
    font-size: 14px;
  }

  .user-details {
    display: none;
  }

  .user-info-container {
    padding: 12px 8px;
    justify-content: center;
  }

  .avatar-container {
    margin-right: 0;
  }
}

@media screen and (max-width: 480px) {
  .side-nav {
    width: 50px;
  }

  .toggle-button {
    padding: 12px 8px;
  }

  /* 增强小屏幕上的触摸区域 */
  .menu-item {
    padding: 14px 8px;
  }
  
  .menu-icon {
    font-size: 16px;
  }
}
</style>