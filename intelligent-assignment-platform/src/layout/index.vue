<template>
    <div class="flex w-full h-full overflow-hidden">
        <SideNav ref="sideNavRef" />
        <div class="flex-1 transition-all duration-300 overflow-hidden" :style="contentStyle">
            <div class="p-2 h-full flex flex-col">
                <div class="bg-btn-primary rounded-lg flex-1 flex flex-col overflow-hidden">
                    <tags-card />
                    <div class="flex-1 overflow-auto">
                        <router-view />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, onUnmounted } from 'vue'
import SideNav from './SideNav.vue';
import { useRoute } from 'vue-router';
// @ts-ignore
import TagsCard from '../layout/TagsCard.vue';

export default defineComponent({
    name: 'App',
    components: {
        SideNav,
        TagsCard
    },
    setup() {
        const route = useRoute();
        const sideNavRef = ref(null);
        const sideNavCollapsed = ref(false);
        const isMobileView = ref(false);

        // 检测是否为移动设备视图
        const checkMobileView = () => {
            isMobileView.value = window.innerWidth <= 768;
        };

        onMounted(() => {
            // 初始检测视图类型
            checkMobileView();

            // 监听窗口大小变化
            window.addEventListener('resize', checkMobileView);

            // 监听侧边栏收起状态变化
            if (sideNavRef.value) {
                // 使用watch监听collapsed属性变化
                (sideNavRef.value as any).$watch('collapsed', (newVal) => {
                    sideNavCollapsed.value = newVal;
                });
            }
        });

        // 组件卸载时移除事件监听
        onUnmounted(() => {
            window.removeEventListener('resize', checkMobileView);
        });

        // 根据侧边栏状态和屏幕尺寸动态计算内容区域样式
        const contentStyle = computed(() => {
            // 移动设备视图下的样式
            if (isMobileView.value) {
                if (window.innerWidth <= 480) {
                    // 小屏幕手机
                    return {
                        marginLeft: sideNavCollapsed.value ? '0' : '50px'
                    };
                } else {
                    // 平板设备
                    return {
                        marginLeft: sideNavCollapsed.value ? '0' : '60px'
                    };
                }
            } else {
                // 桌面视图
                return {
                    marginLeft: sideNavCollapsed.value ? '80px' : '156px'
                };
            }
        });

        return {
            sideNavRef,
            contentStyle,
        };
    }
})
</script>

<style scoped>
/* 响应式布局 */
@media screen and (max-width: 768px) {
    .content-container {
        margin-left: 60px !important;
    }
}

@media screen and (max-width: 480px) {
    .content-container {
        margin-left: 50px !important;
    }
}
</style>