<template>
    <a-tabs v-model:activeKey="activeKey" hide-add type="editable-card" @edit="onEdit" @change="onChange">
        <a-tab-pane v-for="pane in panes" :key="pane.key" :closable="pane.closable && !['ModelValidation','ProjectList','System', 'Dashboard', 'MDValidation'].includes(pane.name || '')">
            <template #tab>
                <div class="flex items-center">
                    <img class="w-5 h-5 mr-1" :src="getIconList(pane)" alt="" v-if="pane.name && ['ModelValidation','ProjectList','System', 'Dashboard', 'MDValidation'].includes(pane.name)">
                    <span>{{ getTabTitle(pane) }}</span>
                </div>
            </template>
        </a-tab-pane>
    </a-tabs>
</template>
<script setup lang="ts" >
import { ref, watch, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
// @ts-ignore
import { modelTaskUnBindApi } from '@/api/task/index.ts';
// @ts-ignore
import taskHoverIcon from '@/assets/images/task-hover.svg';
// @ts-ignore
import projectHoverIcon from '@/assets/images/project-hover.svg';
// @ts-ignore
import settingHoverIcon from '@/assets/images/setting-hover.svg';
// @ts-ignore
import homeHoverIcon from '@/assets/images/dashboard-hover.svg';
// @ts-ignore
import mdHoverIcon from '@/assets/images/md-hover.svg';
const router = useRouter();
const route = useRoute();
const { t } = useI18n();

interface TabPane {
    title: string;
    key: string;
    closable?: boolean;
    fullPath?: string;
    name?: string;
    meta?: any;
    query?: any;
}

const panes = ref<TabPane[]>([]);
const affixTags = ref<TabPane[]>([]);
const activeKey = ref<string>('/');

// 获取所有路由
const allRoutes = computed(() => router.getRoutes());

// 过滤固定标签（affix）
function filterAffixTags(routes: any[], basePath = '/') {
    let tags: TabPane[] = [];
    routes.forEach(route => {
        if (route.meta && route.meta.affix) {
            const tagPath = basePath === '/' ? route.path : `${basePath}/${route.path}`;
            tags.push({
                fullPath: tagPath,
                // path: tagPath, 
                key: tagPath,
                name: route.name,
                title: route.meta.title ? t(`route.${route.meta.title}`) : route.name,
                meta: { ...route.meta }
            });
        }
        if (route.children && route.children.length) {
            const tempTags = filterAffixTags(route.children, route.path);
            if (tempTags.length >= 1) {
                tags = [...tags, ...tempTags];
            }
        }
    });
    return tags;
}

// 初始化固定标签
function initTags() {
    const tags = filterAffixTags(allRoutes.value);
    affixTags.value = tags;
    tags.forEach(tag => {
        if (tag.name && !panes.value.some(p => p.key === tag.key)) {
            panes.value.push({
                ...tag,
                closable: false
            });
        }
    });
}

// 添加标签
function addTags() {
    const { name, path, fullPath, meta, query } = route;
    const parentTitles = ['模型验证', '项目列表', '系统管理', '首页', 'MD 验证'];
    const currentParent = meta?.title;
    if (currentParent && parentTitles.includes(currentParent as string)) {
        // 过滤掉所有其他父级及其子标签，只保留当前父级及其子标签
        panes.value = panes.value.filter(p => p.meta?.title === currentParent);
    }
    if (name && meta && meta.title && !panes.value.some(p => p.key === path)) {
        // 确保首页标签或第一个标签不显示关闭按钮
        const isFirstTab = panes.value.length === 0;
        panes.value.push({
            key: path,
            title: meta?.title ? t(`route.${meta.title}`) : (name as string),
            closable: path !== '/' && !isFirstTab,
            fullPath,
            name: name as string,
            meta,
            query,
        });
    }
    activeKey.value = path;
}

// 移除标签
function removeTab(targetKey: string) {
    const idx = panes.value.findIndex(p => p.key === targetKey);
    if (idx !== -1) {
        const removedTab = panes.value[idx];
        panes.value.splice(idx, 1);
        // 切换到最后一个标签
        const last = panes.value[panes.value.length - 1];
        // 根据被关闭的tab类型，决定跳转到对应的列表页
        if (removedTab.name === 'TaskDetail'&&last.key === '/model-validation') {
            // 关闭TaskDetail时，调用解绑API并跳转到模型验证页面的validation tab
            const taskId = removedTab.query?.taskId;
            if (taskId) {
                // 从localStorage获取当前任务的scene_id
                const taskTempSave = localStorage.getItem('task_temp_save');
                let sceneId = null;
                if (taskTempSave) {
                    try {
                        const tempData = JSON.parse(taskTempSave);
                        sceneId = tempData.sceneId;
                    } catch (error) {
                        console.error('解析task_temp_save失败:', error);
                    }
                }
                
                // 调用解绑API，传递taskId和sceneId
                const data = sceneId ? { scene_id: sceneId } : {};
                modelTaskUnBindApi(Number(taskId), data).catch(error => {
                    console.error('任务解绑失败:', error);
                });
            }
            activeKey.value = '/model-validation';
            router.push({ path: '/model-validation', query: { tab: 'validation' } });
        } else if (removedTab.name === 'ErrorResult') {
            activeKey.value = '/model-validation';
            router.push({ path: '/model-validation', query: { tab: 'error' } });
        } else if (removedTab.name && ['QATaskDetail','AddQATask'].includes(removedTab.name)) {
            // 关闭QATaskDetail时，跳转到模型验证页面的qa tab
            activeKey.value = '/model-validation';
            router.push({ path: '/model-validation', query: { tab: 'qa' } });
        } else {
            if (last) {
                // 是否需要带上tab参数
                if (last.key.includes('/project-detail')) {
                    router.push({ path: last.key, query: { id: last.query?.id, tab: last.query?.tab } });
                } else {
                    router.push(last.key);
                }
            } else {
                activeKey.value = '/';
                router.push('/');
            }
        }
    }
}

// 切换标签
function onChange(key: string) {
    const tag = panes.value.find(p => p.key === key);
    if (tag) {
        activeKey.value = key;
        router.push({ path: tag.key, query: tag.query });
    }
}

// 编辑标签（关闭）
function onEdit(targetKey: string, action: string) {
    if (action === 'remove') {
        // 防止关闭首页标签或第一个标签
        const targetTab = panes.value.find(p => p.key === targetKey);
        if (targetTab && targetTab.closable) {
            removeTab(targetKey);
        }
    }
}

// 监听路由变化自动添加标签
watch(
    () => route.path,
    () => {
        addTags();
    }
);

const getIconList = computed(() => {
    return (pane: TabPane) => {
        if (pane.meta.title === '模型验证') {
            return taskHoverIcon;
        } else if (pane.meta.title === '项目列表') {
            return projectHoverIcon; 
        } else if (pane.meta.title === '系统管理') {
            return settingHoverIcon;
        } else if (pane.meta.title === '首页') {
            return homeHoverIcon;
        } else if (pane.meta.title === 'MD 验证') {
            return mdHoverIcon;
        } else {
            return '';
        } 
    }
})

// 根据路由参数动态获取标题
const getTabTitle = computed(() => {
    return (pane: TabPane) => {
        // 如果是任务详情页面，根据 type 参数显示不同标题
        if (pane.query && pane.query.type) {
            if (pane.query.type === 'start') {
                return 'Task';
            } else if (pane.query.type === 'view') {
                if(pane.name === 'QATaskDetail'){
                    return 'QA Task Detail';
                }
                return 'Task Detail';
            }
        }
        // 默认返回原标题
        return pane.title;
    }
})
// 组件挂载时初始化
onMounted(() => {
    initTags();
    addTags();
    
    // 监听来自其他组件的关闭tab事件
    window.addEventListener('removeTab', (event: any) => {
        const targetPath = event.detail;
        const targetTab = panes.value.find(pane => pane.key === targetPath);
        if (targetTab) {
            removeTab(targetPath);
        }
    });
});
</script>


<style scoped>
:deep(.ant-tabs-nav) {
    margin-bottom: 0;
}
:deep(.ant-tabs-nav .ant-tabs-tab-active) {
    background: #fff !important;
}
:deep(.ant-tabs-tab) {
    border: none !important;
    background: transparent !important;
}
:deep(.ant-tabs-nav::before), :where(.css-dev-only-do-not-override-1p3hq3p).ant-tabs-top >.ant-tabs-nav::before, :where(.css-dev-only-do-not-override-1p3hq3p).ant-tabs-bottom >div>.ant-tabs-nav::before {
    border-bottom: none !important;
}
:deep(.ant-tabs-nav-wrap) {
    background: #FAFAFA;
}
</style>