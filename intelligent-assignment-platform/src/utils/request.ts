import axios from 'axios'
import { h } from 'vue'
import router from "../router"
import config from '../config/web'
import { message, Modal  } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
// @ts-ignore
import i18n from "@/i18n"
// import { useGlobalLoadingStore } from '@/store/useGlobalLoadingStore'

const { t } = i18n.global;

// 获取全局 loading store 实例
// let globalLoadingStore: ReturnType<typeof useGlobalLoadingStore> | null = null;
// const getGlobalLoadingStore = () => {
//   if (!globalLoadingStore) {
//     globalLoadingStore = useGlobalLoadingStore();
//   }
//   return globalLoadingStore;
// };

const request = axios.create({
    baseURL: config.baseUrl,
    timeout: config.timeout,
    headers: config.headers,
    withCredentials: config.withCredentials,
    paramsSerializer: {
        serialize: (params) => {
            const searchParams = new URLSearchParams();
            for (const key in params) {
                if (Array.isArray(params[key])) {
                    params[key].forEach(value => {
                        if (value !== null && value !== undefined) {
                            searchParams.append(key, value);
                        }
                    });
                } else {
                    if (params[key]!== null && params[key]!== undefined) {
                        searchParams.append(key, params[key]);  
                    }
                }
            }
            return searchParams.toString();
        }
    }
})

//添加请求拦截器
request.interceptors.request.use((config) => {
    // 显示全局加载状态（除非明确禁用）
    if ((config as any).showLoading !== false) {
        try {
            // const loadingStore = getGlobalLoadingStore();
            // loadingStore.showLoading();
        } catch (error) {
            console.warn('Failed to show global loading:', error);
        }
    }
    return config;
}, (error) => {
	 	// 1. 判断请求超时
    if (error.message.indexOf("timeout") !== -1) {
        console.log("Request timeout");
    }
		// 2. 需要重定向到错误页面
    const errorInfo = error.response;
    if (errorInfo) {
        error = errorInfo.data;
        const errorStatus = errorInfo.status; // 404 403 500 ...
        router.push({
            path: `/error/${errorStatus}`
        });
    }
    return Promise.reject(error);
});

//添加响应拦截器
request.interceptors.response.use(
    (response) => {
        // 隐藏全局加载状态
        try {
            // const loadingStore = getGlobalLoadingStore();
            // loadingStore.hideLoading();
        } catch (error) {
            console.warn('Failed to hide global loading:', error);
        }
        let res = response.data
        let code = res.code
        let msg = res.msg || res.message || 'Error'
        // 检查响应是否包含Blob数据或配置中是否有Blob相关属性
        // 检查响应头的Content-Type来判断是否为二进制数据
        if (
            response.headers['content-type']?.includes('application/octet-stream') ||
            response.headers['content-type']?.includes('application/pdf') ||
            response.headers['content-type']?.includes('application/vnd.ms-excel') ||
            response.headers['content-type']?.includes('application/vnd.openxmlformats-officedocument') ||
            response.request.responseType === 'blob' ||
            response.request.responseType === 'arraybuffer' ||
            response.data instanceof Blob
        ) {
            return response
        }
        if (code === 402) {
            Modal.confirm({
                title: 'System Notice',
                icon: h(ExclamationCircleOutlined),
                content: 'Your login session has expired. You can stay on this page or log in again',
                okText: 'Login Again',
                cancelText: 'Cancel',
                centered: true,
                onOk: async () => {
                    try {
                        location.href = '/login';
                    } catch (error) {
                        console.error(error);
                    }
                },
                onCancel: () => {}
            });
            return Promise.reject('Invalid session or session has expired, please login again.')
        }
        if (code != 0 || code != "0000" ) {
            message.warning(msg)
            return res
        }
        return res
    },
    (error) => {
        // 隐藏全局加载状态
        try {
            // const loadingStore = getGlobalLoadingStore();
            // loadingStore.hideLoading();
        } catch (loadingError) {
            console.warn('Failed to hide global loading:', loadingError);
        }
        
        if(error && error.response) {
            switch (error.status) {
                case 400:
                    error.message = t('error.badRequest') || "Bad Request";
                    break;
                case 401:
                    // 显示未授权消息
                    message.error(t('user.unauthorized') || 'Unauthorized, please login');
                    // 清除登录状态
                    localStorage.removeItem('exp'); // 清除过期的token
                    localStorage.removeItem('autoLoginInfo'); // 清除自动登录信息
                    // 重定向到登录页面
                    router.push("/login");
                    break;
                case 403:
                    message.error(t('user.accessDenied') || "Access Denied"); // 显示拒绝访问消息
                    localStorage.removeItem('exp');
                    localStorage.removeItem('autoLoginInfo');
                    router.push("/login");
                    break;
                case 404:
                    error.message = `${t('error.notFound')}: ${error.response.config.url}`;
                    break;
                case 408:
                    error.message = t('error.timeout') || "Request Timeout";
                    break;
                case 500:
                    error.message = t('error.serverError') || "Internal Server Error";
                    break;
                case 501:
                    error.message = t('error.notImplemented') || "Service Not Implemented";
                    break;
                case 502:
                    error.message = t('error.badGateway') || "Bad Gateway";
                    break;
                case 503:
                    error.message = t('error.serviceUnavailable') || "Service Unavailable";
                    break;
                case 504:
                    error.message = t('error.gatewayTimeout') || "Gateway Timeout";
                    break;
                case 505:
                    error.message = t('error.httpVersionNotSupported') || "HTTP Version Not Supported";
                    break;
                default:
                    error.message = `${t('error.connectionError')}(${error.response.status})!`;
            }
        } else {
            error.message = "Network Error";
        } 
        message.error(error.message)
        return Promise.reject(error)
    }
)

export default request;
