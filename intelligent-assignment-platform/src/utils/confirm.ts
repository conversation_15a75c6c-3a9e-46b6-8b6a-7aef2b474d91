import { Modal } from 'ant-design-vue';
import { createVNode, VNode } from 'vue';
import { ExclamationCircleOutlined, InfoCircleOutlined, CheckCircleOutlined, CloseCircleOutlined, WarningOutlined } from '@ant-design/icons-vue';
import { ModalFuncProps } from 'ant-design-vue/es/modal/Modal';

export interface ConfirmOptions extends ModalFuncProps {
  // 允许 content/title 为函数
  title?: string | VNode | (() => VNode);
  content?: string | VNode | (() => VNode);
  // onOk/onCancel 支持返回任意类型
  onOk?: () => any | Promise<any>;
  onCancel?: () => any | Promise<any>;
  okButtonProps?: {
    type?: 'primary' | 'default' | 'dashed' | 'link' | 'text';
    danger?: boolean;
    loading?: boolean;
    disabled?: boolean;
  };
  cancelButtonProps?: {
    type?: 'primary' | 'default' | 'dashed' | 'link' | 'text';
    danger?: boolean;
    loading?: boolean;
    disabled?: boolean;
  };
}

type ModalType = 'confirm' | 'info' | 'success' | 'error' | 'warning';

const iconMap: Record<ModalType, VNode> = {
  confirm: createVNode(ExclamationCircleOutlined),
  info: createVNode(InfoCircleOutlined),
  success: createVNode(CheckCircleOutlined),
  error: createVNode(CloseCircleOutlined),
  warning: createVNode(WarningOutlined),
};

function renderContent(content: any): VNode | string {
  if (typeof content === 'function') return content();
  return content;
}

/**
 * 通用 confirm/info/success/error/warning 弹窗
 * @param type 弹窗类型
 * @param options 配置项
 */
export function showModal(type: ModalType, options: ConfirmOptions): Promise<any> {
  return new Promise((resolve, reject) => {
    Modal[type]({
      centered: true,
      maskClosable: true,
      ...options,
      icon: options.icon ?? iconMap[type],
      title: renderContent(options.title),
      content: renderContent(options.content),
      async onOk() {
        if (options.onOk) {
          try {
            const result = await options.onOk();
            resolve(result);
          } catch (e) {
            return Promise.reject(e);
          }
        } else {
          resolve(true);
        }
      },
      onCancel() {
        if (options.onCancel) options.onCancel();
        reject();
      },
    });
  });
}

// 快捷方法
export function showConfirm(options: ConfirmOptions) {
  return showModal('confirm', options);
}
export function showInfo(options: ConfirmOptions) {
  return showModal('info', options);
}
export function showSuccess(options: ConfirmOptions) {
  return showModal('success', options);
}
export function showError(options: ConfirmOptions) {
  return showModal('error', options);
}
export function showWarning(options: ConfirmOptions) {
  return showModal('warning', options);
}
