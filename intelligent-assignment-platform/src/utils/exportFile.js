/**
 * 通用文件导出工具函数
 * @param {Object} options - 导出配置
 * @param {Object} options.export - 接口请求参数
 * @returns {Promise<void>}
 */
import { message } from 'ant-design-vue';

export const exportFile = async (options) => {
    try {
      const res = options.export;
      if (res.statusTest === 'OK') {
        throw new Error(`HTTP error! status: ${res.status}`);
      }
      const blob = await res.data;

      // 校验 content-type
      const contentType = res.headers.get('content-type') || '';
      
      // 如果不是Excel文件，尝试解析错误信息
      if (!contentType.includes('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')) {
        const text = await blob.text();
        try {
          const errorData = JSON.parse(text);
          throw new Error(errorData.msg || 'Export failed: API returned non-Excel format data');
        } catch (e) {
          throw new Error('Export failed: Invalid API response format');
        }
      }

      // 小于100字节的文件，尝试解析错误信息
      if (blob.size < 100) {
        const text = await blob.text();
        try {
          const errorData = JSON.parse(text);
          throw new Error(errorData.msg || 'Export failed: Invalid file content');
        } catch (e) {
          console.warn('Small file content:', text);
        }
      }

      // 解析文件名
      const contentDisposition = res.headers.get('content-disposition') || '';
      let fileName = 'exported_file.xlsx';
      const match = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
      if (match && match[1]) {
        fileName = match[1].replace(/['"]/g, '').trim();
        fileName = decodeURIComponent(fileName);
      }

      // 触发下载
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
      message.destroy();
      return Promise.reject(error);
    }
  };