/**
 * 防抖函数
 * @param {Function} func - 需要防抖的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @param {boolean} immediate - 是否立即执行
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay = 300, immediate = false) {
  let timeoutId = null;
  let result;

  return function debounced(...args) {
    const context = this;
    const callNow = immediate && !timeoutId;

    // 清除之前的定时器
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    // 设置新的定时器
    timeoutId = setTimeout(() => {
      timeoutId = null;
      if (!immediate) {
        result = func.apply(context, args);
      }
    }, delay);

    // 如果是立即执行模式且是第一次调用
    if (callNow) {
      result = func.apply(context, args);
    }

    return result;
  };
}

/**
 * 创建防抖的异步函数
 * @param {Function} asyncFunc - 需要防抖的异步函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 防抖后的异步函数
 */
export function debounceAsync(asyncFunc, delay = 300) {
  let timeoutId = null;
  let pendingPromise = null;

  return function debouncedAsync(...args) {
    const context = this;

    // 如果有正在执行的Promise，直接返回
    if (pendingPromise) {
      return pendingPromise;
    }

    // 清除之前的定时器
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    // 创建新的Promise
    pendingPromise = new Promise((resolve, reject) => {
      timeoutId = setTimeout(async () => {
        try {
          const result = await asyncFunc.apply(context, args);
          resolve(result);
        } catch (error) {
          reject(error);
        } finally {
          pendingPromise = null;
          timeoutId = null;
        }
      }, delay);
    });

    return pendingPromise;
  };
}

/**
 * 节流函数
 * @param {Function} func - 需要节流的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, delay = 300) {
  let lastExecTime = 0;
  let timeoutId = null;

  return function throttled(...args) {
    const context = this;
    const currentTime = Date.now();

    // 如果距离上次执行时间超过延迟时间，立即执行
    if (currentTime - lastExecTime >= delay) {
      lastExecTime = currentTime;
      return func.apply(context, args);
    }

    // 否则设置定时器在剩余时间后执行
    if (!timeoutId) {
      timeoutId = setTimeout(() => {
        lastExecTime = Date.now();
        timeoutId = null;
        func.apply(context, args);
      }, delay - (currentTime - lastExecTime));
    }
  };
}