/**
 * 在树形结构中查找目标节点的父路径
 * @param {Array} treeData - 树形结构数据
 * @param {string|number} targetId - 目标节点的ID
 * @param {string} [valueKey='value'] - 节点值的键名
 * @param {string} [childrenKey='children'] - 子节点的键名
 * @returns {Array|null} - 包含父节点值的数组，如果未找到则返回null
 */
  export const findParentPath = (treeData, targetId, valueKey = 'value', childrenKey = 'children') => {
    // 使用栈来模拟递归调用，避免栈溢出
    const stack = [...treeData.map(node => ({ node, path: [] }))];
  
    while (stack.length > 0) {
      const { node, path } = stack.pop();
  
      // 如果当前节点是目标节点，返回路径
      if (node[valueKey] === targetId) {
        return path;
      }
  
      // 如果当前节点有子节点，将子节点压入栈
      if (node[childrenKey] && Array.isArray(node[childrenKey])) {
        // 逆序压栈以保持原顺序
        for (let i = node[childrenKey].length - 1; i >= 0; i--) {
          stack.push({
            node: node[childrenKey][i],
            path: [...path, node[valueKey]]
          });
        }
      }
    }
  
    // 未找到目标节点
    return null;
  }    