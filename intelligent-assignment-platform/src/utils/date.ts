import dayjs from 'dayjs';

/**
 * 将后端返回的整型时间戳（秒或毫秒）格式化为指定格式
 * @param {number} timestamp - 时间戳（秒或毫秒）
 * @param {string} formatStr - 格式化字符串，默认 'MM/DD/YYYY HH:mm:ss'
 * @returns {string}
 */
export function formatTimestamp(timestamp, formatStr = 'MM/DD/YYYY HH:mm:ss') {
  if (!timestamp) return '';
  // 判断是秒还是毫秒
  const ts = timestamp > 1e12 ? timestamp : timestamp * 1000;
  return dayjs(ts).format(formatStr);
}

/**
 * 将 dayjs/Date/字符串等对象转为整型时间戳（秒）
 * @param {any} dateObj - 可能是 dayjs 对象、Date 对象、字符串等
 * @returns {number|null}
 */
export function dateToTimestamp(dateObj) {
    if (!dateObj) return null;
    // dayjs 对象有 unix 方法
    if (typeof dateObj.unix === 'function') {
      return dateObj.unix();
    }
    // Date 对象
    if (dateObj instanceof Date) {
      return Math.floor(dateObj.getTime() / 1000);
    }
    // 字符串或其他情况
    const ts = Date.parse(dateObj);
    if (!isNaN(ts)) {
      return Math.floor(ts / 1000);
    }
    return null;
  }