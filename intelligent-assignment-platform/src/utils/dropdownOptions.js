export const targetTypeOptions = [
    { value: 0, label: 'scene' },
    { value: 1, label: 'image' },
]

export const statusOptions = [
    { value: 0, label: 'Unpublished' },
    { value: 1, label: 'Not Started' },
    { value: 2, label: 'In Progress' },
    { value: 3, label: 'Completed' }
]

export const taskTypeOptions = [
    { value: 'Adhoc', label: 'Adhoc' },
    { value: 'UAT', label: 'UAT' },
    { value: 'Regular', label: 'Regular' },
]

export const truthValueOptions = [
    { value: 0, label: 'No' },
    { value: 1, label: 'Yes' },
]

export const loginStatusOptions = [
    { value: 'Online', label: 'Online' },
    { value: 'Offline', label: 'Offline' },
]

export const workStatusOptions = [
    { value: 'Idle', label: 'Idle' },
    { value: 'Active', label: 'Active' },
    { value: 'Loafing', label: 'Loafing' },
]

export const workPageOptions = [
    { value: 'Validation Task', label: 'Validation Task' },
    { value: 'My Error', label: 'My Error' },
    { value: 'QA Task', label: 'QA Task' },
]