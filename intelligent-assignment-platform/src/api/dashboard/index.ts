import { apiRequest } from '../api'

// 验证任务大致信息统计
export const baseApi = (params?: any) => apiRequest({ url: '/validation/val_tasks/dashboard/base', params });
// 用户状态统计
export const accountStatusApi = (data?: any) => apiRequest({ url: '/validation/dashboard/account/status', method: 'post', data });
// 用户维度导出
export const accountExportApi = (data?: any) => apiRequest({ url: '/validation/dashboard/account/status/export', method: 'post', data, responseType: 'blob' });
// 用户排名
export const rankApi = () => apiRequest({ url: '/validation/dashboard/rank' });
// 小时时间维度返回
export const hourApi = (params?: any) => apiRequest({ url: '/validation/val_tasks/dashboard/hour', params });
// 小时时间维度导出
export const hourExportApi = (params?: any) => apiRequest({ url: '/validation/val_tasks/dashboard/hour/export', params, responseType: 'blob' });
// 场景纬度返回
export const sceneApi = (params?: any) => apiRequest({ url: '/validation/val_tasks/dashboard/scene', params });
// 场景纬度导出
export const sceneExportApi = (params?: any) => apiRequest({ url: '/validation/val_tasks/dashboard/scene/export', params, responseType: 'blob' });
// 任务维度返回
export const taskApi = (params?: any) => apiRequest({ url: '/validation/val_tasks/dashboard/task', params });
// 任务纬度导出
export const taskExportApi = (params?: any) => apiRequest({ url: '/validation/val_tasks/dashboard/task/export', params, responseType: 'blob' });
// 任务日期维度统计
export const taskDateApi = (params?: any) => apiRequest({ url: '/validation/val_tasks/dashboard/task/date', params });
