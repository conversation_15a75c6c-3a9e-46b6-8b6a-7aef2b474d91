// @ts-ignore
import request from '@/utils/request.ts';

// Validation 验证任务管理
export const validationTaskListApi = (params) => request({ url: '/validation/val_tasks', params: params, method: 'get' }); //Validation任务列表
export const validationTaskDetailApi = (val_task_id: number) => request({ url: `/validation/val_tasks/${val_task_id}`, method: 'get' }); //Validation任务详情
export const validationTaskStartApi = (val_task_id: number, data: any) => request({ url: `/validation/val_tasks/${val_task_id}/start`, method: 'put', data }); //Validation任务开始
//Validation任务 获取并绑定一个 sku 场景
export const validationTaskSkuApi = (val_task_id: number, data: any) => request({ url: `/validation/val_tasks/${val_task_id}/scenes/skus`, method: 'put', data });
//Validation任务 获取并绑定一个 价签场景
export const validationTaskSceneApi = (val_task_id: number, data: any) => request({ url: `/validation/val_tasks/${val_task_id}/scenes/prices`, method: 'put', data });
//Validation任务 场景sku 保存并结束
export const validationTaskSkuSaveApi = (val_task_id: number, scene_id: number, data: any) => request({ url: `/validation/val_tasks/${val_task_id}/scenes/${scene_id}/skus/finish`, method: 'post', data });
//Validation任务 场景价签 保存并结束
export const validationTaskPriceSaveApi = (val_task_id: number, scene_id: number, data: any) => request({ url: `/validation/val_tasks/${val_task_id}/scenes/${scene_id}/prices/finish`, method: 'post', data });

export const saveScenesApi = (val_task_id: number, scene_id: number, data: any) => request({ url: `/validation/val_tasks/${val_task_id}/scenes/${scene_id}/save`, method: 'post', data });

// QA 列表
export const qaTaskListApi = (params) => request({ url: '/validation/val_qa_tasks', params: params, method: 'get' });

export const getQAConfigApi = () => request({ url: '/validation/val_qa_tasks/config', method: 'get' });

export const qaTaskConfigApi = (data) => request({ url: '/validation/val_qa_tasks/config', data: data, method: 'put' });

export const errorReportApi = (params) => request({ url: '/validation/val_tasks/scenes/error_report', params: params, method: 'get' });

export const SaveQAApi = (data) => request({ url: '/validation/val_qa_tasks', data: data, method: 'post' });

export const qaDetailApi = (val_task_qa_id) => request({ url: '/validation/val_qa_tasks/' + val_task_qa_id, method: 'get' });

export const editQAApi = (val_task_qa_id, data) => request({ url: '/validation/val_qa_tasks/' + val_task_qa_id, data: data, method: 'put' });

//QA 任务详情
export const qaTaskDetailApi = (val_task_qa_id: number) => request({ url: '/validation/val_qa_tasks/' + val_task_qa_id, method: 'get' });
//QA任务删除
export const qaTaskDeleteApi = (val_qa_id: number) => request({ url: '/validation/val_qa_tasks/' + val_qa_id, method: 'delete' });

export const qaErrorReportApi = () => request({ url: '/validation/val_qa_tasks/error_report', method: 'get' }); //QA任务错误报告
export const qaTaskCreateApi = (data: any) => request({ url: '/validation/val_qa_tasks', method: 'post', data }); //QA任务创建
export const qaTaskPublishApi = (val_qa_id: number) => request({ url: `/validation/val_qa_tasks/${val_qa_id}/action_publish`, method: 'post' }); //QA任务发布
//QA 作业管理 场景sku 保存并结束
export const qaTaskSceneApi = (val_task_id: number, scene_id: number, data: any) => request({ url: `/validation/val_tasks/${val_task_id}/scenes/${scene_id}/save`, method: 'put', data }); 

// QA 作业流
export const qaTaskFlowApi = (val_qa_id: number) => request({ url: '/validation/val_qa_tasks/' + val_qa_id + '/action_start', method: 'post' }); //QA 作业流 开始 QA 作业
export const qaTaskFlowDetailApi = (val_qa_id: number) => request({ url: '/validation/val_qa_tasks/' + val_qa_id + '/detail', method: 'put' }); //QA 作业流 详情
export const qaTaskFlowSubmitApi = (val_qa_id: number, val_qa_detail_id: number, data: any) => request({ url: '/validation/val_qa_tasks/' + val_qa_id + '/detail/' + val_qa_detail_id, method: 'post' , data }); //QA 作业流 提交 QA 结果

//Error Result QA 结果管理
export const qaResultListApi = (params) => request({ url: '/validation/val_qa_review_tasks/result', params: params, method: 'get' }); //QA 结果列表
export const qaReviewResultApi = (result_id: number) => request({ url: '/validation/val_qa_review_tasks/result/' + result_id, method: 'get' }); //QA结果详情
export const updateQaReviewResultApi = (result_id: number, data: any) => request({ url: `/validation/val_qa_review_tasks/result/${result_id}/review`, method: 'put', data }); //QA 结果Review
export const qaResultCountListApi = () => request({ url: '/validation/val_qa_review_tasks/result/count', method: 'get' });

//QA 图片检查
export const qaImgCheckApi = (val_task_id: number, scene_id: number, data: any) => request({ url: `/validation/val_tasks/${val_task_id}/scenes/${scene_id}/img_check`, method: 'put', data });
// common/sku_map
export const skuMapApi = (out_project_id: number) => request({ url: '/validation/common/simple_sku_map?out_project_id='+ out_project_id, method: 'get' }); //sku 映射
//SKU 图片
export const skuImgApi = (out_sku_id: number) => request({ url: `/validation/common/sku_map/${out_sku_id}`, method: 'get' }); //sku 图片
//SKU 统计信息
export const skuSummaryApi = (val_task_id: number) => request({ url: `/validation/val_tasks/${val_task_id}/scenes/sku/summary`, method: 'get' }); 
// 价签 统计信息
export const priceSummaryApi = (val_task_id: number, scene_id: number) => request({ url: `/validation/val_tasks/${val_task_id}/scenes/${scene_id}/prices/summary`, method: 'get'});
export const qaCountSummaryApi = (val_qa_id: number) => request({ url: `/validation/val_qa_tasks/${val_qa_id}/summary`, method: 'get' }); 

export const tableLsitFileterApi = (params?: any) => request({ url: '/validation/common/data_source', method: 'get', params });

// 模型验证上报
export const modelValidationReportApi = (data: any) => request({ url: '/validation/track', method: 'post', data });

export const modelTaskUnBindApi = (val_task_id: number, data: any) => request({ url: `/validation/val_tasks/${val_task_id}/scenes/skus/unbind`, method: 'put', data });

export const sceneSkuListApi = (val_task_id: number, val_scene_id: number) => request({ url: `/validation/val_tasks/${val_task_id}/${val_scene_id}/result`, method: 'get' });
