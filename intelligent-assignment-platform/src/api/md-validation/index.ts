import { apiRequest } from '../api'

export const syncApi = (project_id?: number) => apiRequest({ url: `/validation/md/${project_id}/md_skus/sync`, method: 'post' });
export const exportApi = (project_id?: number, params?: any) => apiRequest({ url: `/validation/md/${project_id}/md_skus/export`, params, responseType: 'blob' });
export const dataSourceApi = () => apiRequest({ url: '/validation/md/md_skus/data_source'});
export const mdListApi = (project_id?: number, params?: any) => apiRequest({ url: `/validation/md/${project_id}/md_skus`, params});
export const newSkuListApi = (params?: any) => apiRequest({ url: '/validation/md/md_skus', params});
export const mdSkuDetailApi = (md_sku_id?: number) => apiRequest({ url: `/validation/md/md_skus/${md_sku_id}`});
export const putMdSkuApi = (md_sku_id?: number, data?: any) => apiRequest({ url: `/validation/md/md_skus/${md_sku_id}`, method: 'put', data});
export const reskusApi = (md_sku_id?: number) => apiRequest({ url: `/validation/md/md_skus/${md_sku_id}/reskus`});
export const duplicatesApi = (md_sku_id?: number) => apiRequest({ url: `/validation/md/md_skus/${md_sku_id}/duplicates`});
export const activeActionApi = (md_sku_id?: number) => apiRequest({ url: `/validation/md/md_skus/${md_sku_id}/duplicates/active_action`, method: 'post'});
export const reprojectInfoApi = (md_sku_id?: number) => apiRequest({ url: `/validation/md/md_skus/${md_sku_id}/reproject_info`});
export const reprojectInfoSyncApi = (md_sku_id?: number) => apiRequest({ url: `/validation/md/md_skus/${md_sku_id}/reproject_info/sync`, method: 'post'});
