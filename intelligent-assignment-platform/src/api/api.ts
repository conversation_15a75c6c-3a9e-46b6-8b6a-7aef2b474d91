import request from '../utils/request';

interface ApiRequestOptions {
  url: string;
  method?: 'get' | 'post' | 'put' | 'delete' | 'patch';
  data?: any;
  params?: any;
  responseType?: 'json' | 'blob' | 'arraybuffer';
}

export function apiRequest<T = any>(options: ApiRequestOptions): Promise<T> {
  const { url, method = 'get', data, params, responseType } = options;
  return request({
    url: url,
    method,
    data,
    params,
    responseType,
  });
}