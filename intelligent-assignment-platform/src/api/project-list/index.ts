import request from '../../utils/request.ts';

//项目管理
export const projectApi = (data) => request({ url: '/validation/projects', data: data, method: 'post' });

export const queryProjectApi = (params) => request({ url: '/validation/projects', params: params, method: 'get' });

export const projectListApi = (project_id) => request({ url: `/validation/projects/${project_id}`, method: 'get' });

export const projectEqueryApi = (params) => request({ url: '/validation/projects/equery', params: params, method: 'get' });

export const putProjectApi = (project_id, data) => request({ url: `/validation/projects/${project_id}/configs`, data: data, method: 'put' });

//验证任务
export const taskApi = (params) => request({ url: '/validation/val_tasks', params: params, method: 'get' });

export const createTaskApi = (data) => request({ url: '/validation/val_tasks', data: data, method: 'post' });

export const DetailSqueryApi = (params) => request({ url: '/validation/val_tasks/squery', params: params, method: 'get' });

export const taskDetailApi = (val_task_id) => request({ url: `/validation/val_tasks/${val_task_id}`, method: 'get' });

export const putDetailApi = (val_task_id, data) => request({ url: `/validation/val_tasks/${val_task_id}`, data: data, method: 'put' });

export const delDetailApi = (val_task_id) => request({ url: `/validation/val_tasks/${val_task_id}`, method: 'delete' });

export const uploadScenesApi = (val_task_id, data) => request({ url: `/validation/val_tasks/${val_task_id}/scenes/upload`, data: data, method: 'post', headers: { 'Content-Type': 'multipart/form-data' }  });

export const publishScenesApi = (val_task_id) => request({ url: `/validation/val_tasks/${val_task_id}/publish`, method: 'put' });

export const putTaskApi = (val_task_id) => request({ url: `/validation/val_tasks/${val_task_id}/scenes`, method: 'put' });

export const startTaskApi = (val_task_id) => request({ url: `/validation/val_tasks/${val_task_id}/start`, method: 'put' });

export const skusScenesApi = (val_task_id) => request({ url: `/validation/val_tasks/${val_task_id}/scenes/skus`, method: 'put' });

export const saveScenesApi = (val_task_id, scene_id) => request({ url: `/validation/val_tasks/${val_task_id}/scenes/${scene_id}/save`, method: 'post' });

export const finishScenesApi = (val_task_id, scene_id) => request({ url: `/validation/val_tasks/${val_task_id}/scenes/${scene_id}/skus/finish`, method: 'post' });

export const pricesScenesApi = (val_task_id) => request({ url: `/validation/val_tasks/${val_task_id}/scenes/prices`, method: 'put' });

export const finishPricesApi = (val_task_id, scene_id) => request({ url: `/validation/val_tasks/${val_task_id}/scenes/${scene_id}/prices/finish`, method: 'post' });

//场景筛选
export const scenesListApi = (params) => request({ url: '/validation/scenes', params: params, method: 'get' });

//匹配数据上传
export const uploadMatchDataApi = (data) => request({ url: '/validation/mp/import', data: data, method: 'post', headers: { 'Content-Type': 'multipart/form-data' } });

export const updateMatchDataApi = (data) => request({ url: '/validation/mp/sync', data: data, method: 'post' });
