import request from './../utils/request';

export const loginByUserApi = (data) => request({ url: '/sso/login', method: 'post', data: data });

export const registerByUserApi = (data) => request({ url: '/sso/register', method: 'post', data: data });

export const refreshApi = () => request({ url: '/sso/refresh', method: 'post' });

export const logoutApi = () => request({ url: '/sso/logout', method: 'post' });

export const changePasswordApi = (data) => request({ url: '/sso/change/password', method: 'post', data : data });

export const userApi = () => request({ url: '/auth/user', method: 'get' });

export const realNameApi = (data) => request({ url: '/sso/change/real_name', data: data,  method: 'post' });