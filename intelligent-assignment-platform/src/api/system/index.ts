import request from '../../utils/request';

//部门管理
export const departmentsApi = () => request({ url: '/auth/departments', method: 'get' });

export const departmentAddApi = (data: any) => request({ url: '/auth/department/add', method: 'post', data: data });

export const departmentUpdateApi = (data: any) => request({ url: '/auth/department/update', method: 'post', data: data });

export const departmentDelApi = (id) => request({ url: '/auth/department/del', method: 'post', data: id });

export const departmentFilterApi = (params) => request({ url: '/auth/department/filter', method: 'get', params: params});

export const departmentUsersApi = (id) => request({ url: '/auth/department/users', method: 'get', params: id});

//用户管理
export const userListApi = () => request({ url: '/auth/users', method: 'get' });

export const userAddApi = (data) => request({ url: '/auth/user/add', method: 'post', data: data });

export const userUpdateApi = (data) => request({ url: '/auth/user/update', method: 'post', data: data });

export const userDelApi = (id) => request({ url: '/auth/user/del', method: 'post', data: id });

export const userFilterApi = (params) => request({ url: '/auth/user/filter', method: 'get', params: params });

//角色管理
export const rolesListApi = () => request({ url: '/auth/roles', method: 'get' });

export const roleDelApi = (id) => request({ url: '/auth/role/del', method: 'post', data: id });

export const roleAddApi = (data) => request({ url: '/auth/role/add', method: 'post', data: data });

export const roleUpdateApi = (data) => request({ url: '/auth/role/update', method: 'post', data: data });

export const roleFilterApi = (params) => request({ url: '/auth/role/filter', method: 'get', params: params });

export const roleElementsApi = (params) => request({ url: '/auth/role/elements', method: 'get', params: params });

//菜单管理
export const menusApi = () => request({ url: '/auth/menus', method: 'get' });

export const menuAddApi = (data) => request({ url: '/auth/menu/add', method: 'post', data: data });

export const menuUpdateApi = (data) => request({ url: '/auth/menu/update', method: 'post', data: data });

export const menuDelApi = (id) => request({ url: '/auth/menu/del', method: 'post', data: id });

export const menufilterApi = (params) => request({ url: '/auth/menu/filter', method: 'get', params: params });

export const authMenuListApi = () => request({ url: '/auth/user/menu', method: 'get' });
