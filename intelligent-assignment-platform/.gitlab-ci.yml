variables:
  IMAGE_NAME: XXX.com/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME
  IMAGE_TAG: $CI_COMMIT_TAG.$CI_COMMIT_SHORT_SHA
  APP_DOMAIN: XXX

stages:
  - verify
  - package
  - release

lint-job:
  stage: verify
  allow_failure: false
  image: XXX
  variables:
    ESLINT_CODE_QUALITY_REPORT: gl-codequality-script.json
    STYLELINT_CODE_QUALITY_REPORT: gl-codequality-style.json
  script:
    - npm i
    - npm run lint
    - npx deepmerge-cli gl-codequality-script.json gl-codequality-style.json > gl-codequality.json
  artifacts:
    name: "$CI_JOB_NAME-$CI_JOB_STAGE-$CI_COMMIT_SHORT_SHA-$CI_COMMIT_TIMESTAMP"
    when: always
    reports:
      codequality: gl-codequality.json
  only:
    - merge_requests
    - main
    - tags
  tags:
    - k8s

sonarqube-check:
  stage: verify
  allow_failure: false
  image:
    name: XXX
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"
    GIT_DEPTH: "0"
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - sonar-scanner
  only:
    - merge_requests
    - main
    - tags
  tags:
    - k8s

build-job:
  stage: package
  allow_failure: false
  image: XXX
  script:
    - npm i
    - npm run build
    - echo $IMAGE_NAME:$IMAGE_TAG
    - docker -v
    - docker build --rm --no-cache -t $IMAGE_NAME:$IMAGE_TAG `pwd`
    - docker push $IMAGE_NAME:$IMAGE_TAG
  only:
    - tags
  tags:
    - k8s

deploy-job:
  stage: release
  allow_failure: false
  image: XXX
  script:
    - sed -i "s/xxx/${IMAGE_TAG}/g" .kube/serverless.yml
    - export KUBECONFIG=`pwd`/.kube/config
    - kubectl delete -f .kube/serverless.yml || true
    - kubectl apply -f .kube/serverless.yml
  only:
    - tags
  tags:
    - k8s
  environment:
    name: dev
    url: $APP_DOMAIN
