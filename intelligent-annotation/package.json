{"name": "zhi-biao-intelligence", "version": "2.2.18", "homepage": "http://www.zhibiaoai.vip", "private": true, "scripts": {"serve-dev": "vue-cli-service serve --mode development", "serve-test": "vue-cli-service serve --mode test", "serve-prod": "vue-cli-service serve --mode prod", "serve-prod-domain": "vue-cli-service serve --mode prod-domain", "build-dev": "vue-cli-service build --mode development", "build-test": "vue-cli-service build --mode test", "build-prod": "vue-cli-service build --mode prod", "build-prod-domain": "vue-cli-service build --mode prod-domain", "lint": "vue-cli-service lint", "predeploy": "yarn build", "docs:dev": "vuepress dev docs", "docs:build": "vuepress build docs"}, "dependencies": {"@antv/data-set": "^0.11.4", "@tinymce/tinymce-vue": "^3.2.6", "animate.css": "^4.1.0", "ant-design-vue": "^1.7.8", "axios": "^0.19.2", "clipboard": "^2.0.6", "core-js": "^3.6.5", "date-fns": "^2.14.0", "enquire.js": "^2.1.6", "highlight.js": "^10.2.1", "jquery": "^1.12.4", "js-base64": "^3.6.0", "js-cookie": "^2.2.1", "js-sha1": "^0.6.0", "localforage": "^1.10.0", "lodash.clonedeep": "^4.5.0", "moment": "^2.29.1", "nprogress": "^0.2.0", "tinymce": "^5.6.2", "viser-vue": "^2.4.8", "vue": "^2.6.11", "vue-contextmenujs": "^1.3.13", "vue-i18n": "^8.18.2", "vue-infinite-scroll": "^2.0.2", "vue-lazyload": "^1.3.3", "vue-router": "^3.3.4", "vue-virtual-scroller": "^1.0.10", "vuedraggable": "^2.23.2", "vuex": "^3.4.0", "webuploader": "^0.1.8"}, "devDependencies": {"@ant-design/colors": "^4.0.1", "@vue/cli-plugin-babel": "^4.4.0", "@vue/cli-plugin-eslint": "^4.4.0", "@vue/cli-service": "^4.4.0", "@vuepress/plugin-back-to-top": "^1.5.2", "babel-eslint": "^10.1.0", "babel-plugin-transform-remove-console": "^6.9.4", "babel-polyfill": "^6.26.0", "compression-webpack-plugin": "^2.0.0", "crypto-js": "^4.1.1", "deepmerge": "^4.2.2", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "fast-deep-equal": "^3.1.3", "gh-pages": "^3.1.0", "less-loader": "^6.1.1", "style-resources-loader": "^1.3.2", "vue-cli-plugin-style-resources-loader": "^0.1.4", "vue-template-compiler": "^2.6.11", "vuepress": "^1.5.2", "webpack-theme-color-replacer": "^1.3.12", "whatwg-fetch": "^3.0.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"vue/no-v-model-argument": 0, "vue/valid-v-model": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"]}