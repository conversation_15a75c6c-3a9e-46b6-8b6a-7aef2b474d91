<template>
  <a-layout-sider v-model="collapsed" :theme="sideTheme" :class="['side-menu', 'beauty-scroll']" width="256px" :collapsible="collapsible" :trigger="null">
    <div :class="['logo', theme]">
      <router-link to="/personal/task">
        <img v-if="theme === 'night'" src="@/assets/img/white-logo.png">
        <img v-else src="@/assets/img/black-logo.png">
        <!-- <h1>{{systemName}}</h1> -->
      </router-link>
    </div>
    <i-menu :theme="theme" :collapsed="collapsed" :options="menuData" :class="['menu', theme]" @select="onSelect" />
  </a-layout-sider>
</template>

<script>
import IMenu from './menu'
import { mapState } from 'vuex'
export default {
  name: 'SideMenu',
  components: { IMenu },
  props: {
    collapsible: {
      type: Boolean,
      required: false,
      default: false
    },
    collapsed: {
      type: Boolean,
      required: false,
      default: false
    },
    menuData: {
      type: Array,
      required: true
    },
    theme: {
      type: String,
      required: false,
      default: 'dark'
    }
  },
  computed: {
    sideTheme() {
      return this.theme === 'light' ? this.theme : 'dark'
    },
    ...mapState('setting', ['isMobile', 'systemName'])
  },
  methods: {
    onSelect(obj) {
      this.$emit('menuSelect', obj)
    }
  }
}
</script>
<style lang="less">
  .menu.ant-menu-dark.ant-menu-inline {
    .ant-menu-item {
      overflow: visible;
      .my_menu {
        padding-left: 52px;
      }
    }
    .ant-menu-submenu {
      .my_menu {
        padding-left: 52px;
      }
    }
    .ant-menu-sub {
      padding: 10px 0;
    }
  }
  .menu .cover {
    display: none;
  }
  .menu.ant-menu.ant-menu-dark {
    .ant-menu-item-selected {
      position: relative;
      background-color: transparent;
      .cover {
        display: block;
        width: calc(~"100% - 28px");
        height: 32px;
        position: absolute;
        right: 0;
        top: 4px;
        border-radius: 22px 0 0 22px;
        background-color: @layout-body-background;
        &::before, &::after {
          display: block;
          content: '';
          width: 20px;
          height: 20px;
          position: absolute;
          right: 0px;
          background: url('../../assets/img/radio50.png') no-repeat;
          background-size: 100% 100%;
        }
        &::before {
          top: -20px;
          transform-origin: center;
          transform: rotate(90deg);
        }
        &::after {
          bottom: -20px;
        }
      }
    }
    &.night {
      .ant-menu-item-selected {
        .cover {
          &::before, &::after {
            background: url('../../assets/img/black-radio50.png') no-repeat;
            background-size: 100% 100%;
          }
        }
      }
    }
    .ant-menu-item-selected .anticon {
      color: @primary-color;
    }
    .ant-menu-item-selected > a {
      position: relative;
      z-index: 5;
      color: @primary-color;
    }
  }
</style>
<style lang="less" scoped>
@import "index";
</style>
