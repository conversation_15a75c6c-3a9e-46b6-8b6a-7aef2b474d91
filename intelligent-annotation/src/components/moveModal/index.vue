<template>
  <div v-if="show" v-drag-modal class="move_modal_contianer" :style="{'zIndex': zIndex, 'width': width + 'px', 'height': height + 'px'}">
    <div class="move_modal_header">
      <div class="title">{{ title }}</div>
      <div class="close" @click="onCancel">
        <a-icon type="close" />
      </div>
    </div>
    <div class="move_modal_body">
      <slot />
    </div>
    <div class="drop_size">
      <a-icon type="radius-bottomright" />
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
export default {
  name: 'MoveModal',
  directives: {
    'drag-modal'(el, binding, vnode) {
      Vue.nextTick(() => {
        // const { visible, destroyOnClose } = vnode.componentInstance
        // if (!visible) return
        const modal = el
        const header = el.getElementsByClassName('move_modal_header')[0]
        const dropDom = el.getElementsByClassName('drop_size')[0]
        let left = 0
        let top = 0
        // if (!destroyOnClose) {
        //   left = modal.left || 0
        //   top = modal.top || 0
        // }
        top = top || modal.offsetTop
        header.onmousedown = e => {
          const startX = e.clientX
          const startY = e.clientY
          header.left = header.offsetLeft
          header.top = header.offsetTop
          el.onmousemove = event => {
            const endX = event.clientX
            const endY = event.clientY
            modal.left = header.left + (endX - startX) + left
            modal.top = header.top + (endY - startY) + top
            modal.style.left = modal.left + 'px'
            modal.style.top = modal.top + 'px'
          }
          el.onmouseup = event => {
            left = modal.left
            top = modal.top
            el.onmousemove = null
            el.onmouseup = null
            header.releaseCapture && header.releaseCapture()
          }
          header.setCapture && header.setCapture()
        }
        dropDom.onmousedown = e => {
          const startX = e.clientX
          const startY = e.clientY
          const modelW = modal.offsetWidth
          const modelH = modal.offsetHeight
          el.onmousemove = event => {
            const endX = event.clientX
            const endY = event.clientY
            modal.width = modelW + (endX - startX)
            modal.height = modelH + (endY - startY)
            modal.style.width = modal.width + 'px'
            modal.style.height = modal.height + 'px'
          }
          el.onmouseup = event => {
            el.onmousemove = null
            el.onmouseup = null
            el.onmouseleave = null
            dropDom.releaseCapture && dropDom.releaseCapture()
          }
          el.onmouseleave = event => {
            el.onmousemove = null
            el.onmouseup = null
            el.onmouseleave = null
            dropDom.releaseCapture && dropDom.releaseCapture()
          }
          dropDom.setCapture && dropDom.setCapture()
        }
      })
    }
  },
  props: {
    width: {
      type: Number,
      default: 520
    },
    height: {
      type: Number,
      default: 520
    },
    title: {
      type: String,
      default: '弹框'
    },
    mask: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    zIndex: {
      type: Number,
      default: 9999
    },
    show: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  data() {
    return {
      that: this
    }
  },
  methods: {
    onCancel() {
      this.$emit('onClose')
    }
  }
}
</script>

<style lang="less" scoped>
.move_modal_contianer {
  position: fixed;
  left: 0;
  top: 0;
  background-color: #fff;
  background-clip: padding-box;
  border: 0;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgb(0 0 0 / 15%);
  pointer-events: auto;
  display: flex;
  flex-direction: column;
  .move_modal_header {
    padding: 16px 24px;
    color: rgba(0,0,0,.65);
    background: #fff;
    border-bottom: 1px solid #e8e8e8;
    border-radius: 4px 4px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: move;
    .close, .title {
      cursor: pointer;
    }
  }
  .move_modal_body {
    flex:1;
    padding: 24px;
    font-size: 14px;
    line-height: 1.5;
    word-wrap: break-word;
    overflow: hidden;
  }
  .drop_size {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 24px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    cursor: se-resize;
  }
}
</style>
