<template>
  <a-modal
    v-model="visible"
    ok-text="确认"
    cancel-text="取消"
    :title="$t('confirm.title') + '：' + $t('confirm.cnt4')"
    class="input-pop-model"
    @ok="giveUpHandleOk"
    @cancel="giveUpHandleCancel"
  >
    <p />
    <p :style="reasonRequireError?{'color':'red'}:null">{{ $t('confirm.giveUpReasonTip') }}:</p>
    <a-select
      v-model="giveUpReasonOption"
      :placeholder="$t('confirm.giveUpReasonTip')"
      show-arrow
      allow-clear
      auto-focus
      :filter-option="false"
      mode="combobox"
      class="input-select"
      :options="giveUpReasonOptions"
      @change="giveUpReasonHandleChange"
    />
  </a-modal>
</template>
<script>

import { mapGetters } from 'vuex'

export default {
  name: 'InputSelectPopConfirm',
  model: {
    prop: 'value',
    event: 'change.value'
  },
  props: {
    giveUpVisible: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      visible: this.giveUpVisible,
      giveUpLoading: false,
      reasonRequireError: false,
      giveUpReasonOption: ''
    }
  },
  computed: {
    ...mapGetters('account', ['user']),
    giveUpReasonOptions() {
      if (this.user.team_id === 3) { // 印度放弃原因
        return [
          {
            value: this.$t('stopRestoreReason.uncertaintiesAwaitingClarification'),
            label: this.$t('stopRestoreReason.uncertaintiesAwaitingClarification')
          },
          {
            value: this.$t('stopRestoreReason.ProjectHoldDueToSequenceChange'),
            label: this.$t('stopRestoreReason.ProjectHoldDueToSequenceChange')
          },
          {
            value: this.$t('stopRestoreReason.shiftEnds'),
            label: this.$t('stopRestoreReason.shiftEnds')
          },
          {
            value: this.$t('stopRestoreReason.movingInspectorsBackToTheInspection'),
            label: this.$t('stopRestoreReason.movingInspectorsBackToTheInspection')
          }
        ]
      }
      return [
        {
          value: this.$t('stopRestoreReason.uncertaintiesAwaitingClarification'),
          label: this.$t('stopRestoreReason.uncertaintiesAwaitingClarification')
        },
        {
          value: this.$t('stopRestoreReason.ProjectHoldDueToSequenceChange'),
          label: this.$t('stopRestoreReason.ProjectHoldDueToSequenceChange')
        },
        {
          value: this.$t('stopRestoreReason.shiftEnds'),
          label: this.$t('stopRestoreReason.shiftEnds'),
          disabled: false
        },
        {
          value: this.$t('stopRestoreReason.movingInspectorsBackToTheInspection'),
          label: this.$t('stopRestoreReason.movingInspectorsBackToTheInspection')
        }
      ]
    }
  },

  watch: {
    giveUpVisible(val) {
      console.log('val:giveUpVisible', val)
      this.visible = val
    },
    visible(val) {
      console.log('val:visible', val)
      this.$emit('update:giveUpVisible', val)
    }
  },
  methods: {
    giveUpHandleCancel() {
      // 提交后台请求，这需要你自己来完成
      // this.$http.post('/api/submitOption', {option: this.selectedOption});
      // this.givUpDirApi()
      // 关闭对话框
      this.visible = false
      this.giveUpLoading = false
      this.giveUpReasonOption = ''
    },
    giveUpReasonHandleChange(value) {
      if (value) {
        this.reasonRequireError = false
      }
      console.log(`selected ${value}`)
    },
    giveUpHandleOk() {
      this.$emit('ok', this.giveUpReasonOption)
    }
  }

}
</script>
<style lang="less">

</style>
<style scoped lang="less">

.input-select {
  width: 100%;
  color: #0b4bd7;
}
</style>
