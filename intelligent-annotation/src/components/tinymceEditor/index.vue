<template>
  <div class="tinymce-editor">
    <editor 
        api-key="no-api-key"
        v-model="myValue"
        :init="init"
        :disabled="disabled"
    >
    </editor>
  </div>
</template>

<script>
import Editor from '@tinymce/tinymce-vue'
export default {
    name: 'TinymceEditor',
    components: { Editor },
    props: {
        //传入一个value，使组件支持v-model绑定
        value: {
            type: String,
            default: ''
        },
        disabled: {
            type: Boolean,
            default: false
        },
        plugins: {
            type: [String, Array],
            default: 'lists image media table wordcount'
        },
        toolbar: {
            type: [String, Array],
            default: 'undo redo | formatselect | bold italic forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist | image table'
        }
    },
    data() {
        return {
            //初始化配置
            init: {
                language_url: '/tinymce/langs/zh_CN.js',
                language: 'zh_CN',
                base_url: '/tinymce',
                height: 500,
                plugins: this.plugins,
                toolbar: this.toolbar,
                branding: false,
                menubar: false,
                //此处为图片上传处理函数，这个直接用了base64的图片形式上传图片，
                //如需ajax上传可参考https://www.tiny.cloud/docs/configure/file-image-upload/#images_upload_handler
                images_upload_handler: (blobInfo, success) => {
                    const img = 'data:image/jpeg;base64,' + blobInfo.base64()
                    success(img)
                }
            },
            myValue: this.value
        }
    },
    methods: {
        // 可以添加一些自己的自定义事件，如清空内容
        clear() {
            this.myValue = ''
        }
    },
     watch: {
        value(newValue) {
            this.myValue = newValue
        },
        myValue(newValue) {
            this.$emit('input', newValue)
        }
    }
}
</script>

<style>
    .tox-notifications-container {
        display: none !important;
    }
</style>