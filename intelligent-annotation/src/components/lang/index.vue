<template>
  <a-dropdown class="lang header-item">
    <div><a-icon type="global" /> {{ lang<PERSON>lia<PERSON> }}</div>
    <a-menu
      slot="overlay"
      :selected-keys="[lang]"
      @click="(val) => setLang(val.key)"
    >
      <a-menu-item v-for="lang in langList" :key="lang.key">
        {{ lang.key.toLowerCase() + " " + lang.name }}
      </a-menu-item>
    </a-menu>
  </a-dropdown>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
export default {
  name: 'Lang',
  data() {
    return {
      langList: [
        { key: 'CN', name: '简体中文', alias: '简体' },
        // {key: 'HK', name: '繁體中文', alias: '繁體'},
        { key: 'US', name: 'English', alias: 'English' }
      ]
    }
  },
  computed: {
    ...mapState('setting', ['lang']),
    langAlias() {
      const lang = this.langList.find(item => item.key === this.lang)
      if (lang) {
        return lang.alias
      } else {
        this.setLang('CN')
        return this.langList[0].alias
      }
    }
  },
  methods: {
    ...mapMutations('setting', ['setLang'])
  }
}
</script>

<style>
</style>
