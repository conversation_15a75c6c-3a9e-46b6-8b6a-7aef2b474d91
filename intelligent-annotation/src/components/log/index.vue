<template>
  <formDrawer :title="$t('log')" :visible="show" @closeDrawer="toggleShow">
    <div class="logo">
      <img v-if="theme.mode === 'night'" src="@/assets/img/white-logo.png">
      <img v-else src="@/assets/img/black-logo.png">
    </div>
    <div class="version">
      <h2>智标数据管理中心</h2>
      <h4>当前版本: {{ version }}</h4>
    </div>
    <h3>更新日志：</h3>
    <div v-for="(it, i) in data" :key="'art' + i" class="article">
      <h4 class="title">智标数据管理中心 {{ it.version }} 版本更新内容({{ it.time }})</h4>
      <ul>
        <li v-for="(item, index) in it.list" :key="'li' + index">
          <span v-if="item.state === 0" class="state">🌟</span>
          <span v-else class="state">🐞</span>
          {{ item.dec }}
        </li>
      </ul>
    </div>
  </formDrawer>
</template>

<script>
import FormDrawer from '../formDrawer'
import { mapState } from 'vuex'
export default {
  name: 'Log',
  components: { FormDrawer },
  data() {
    return {
      show: false,
      version: '0.8.4',
      data: [ // 0 优化，1 修复
        { time: '2021-09-23', version: '0.8.4', list: [
          { state: 0, dec: '优化 页面默认主题色' },
          { state: 0, dec: '优化 页面菜单栏样式' },
          { state: 0, dec: '优化 自定义主题设置项' }
        ] },
        { time: '2021-09-18', version: '0.8.3', list: [
          { state: 0, dec: '优化 清洗页SKU悬浮窗尺寸(原：300，现：500)' },
          { state: 0, dec: '增加 清洗页SKU图片点击，显示全屏大图' },
          { state: 1, dec: '修复 清洗页底部图片放大缩小按钮失效' }
        ] },
        { time: '2021-08-31', version: '0.8.2', list: [
          { state: 1, dec: '修复 清洗页待开始任务列表卡顿，调整加载策略，使用懒加载' },
          { state: 1, dec: '修复 清洗页SKU列表大数据时卡顿，调整加载策略，使用懒加载' }
        ] }
      ]
    }
  },
  computed: {
    ...mapState('setting', ['theme'])
  },
  methods: {
    toggleShow() {
      this.show = !this.show
    }
  }
}
</script>

<style lang="less" scoped>
.logo {
  width: 50%;
  margin: 0 auto;
  img {
    width: 100%;
  }
}
.version {
  text-align: center;
  margin-bottom: 25px;
  margin-top: 15px;
}
.article {
  padding-left: 2em;
  ul {
    li {
      list-style-type: circle;
      margin-bottom: 5px;
    }
  }
}
</style>
