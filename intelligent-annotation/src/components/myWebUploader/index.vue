<template>
  <div class="upload" />
</template>

<script>
import WebUploader from 'webuploader'
export default {
  name: 'MyWebUploader',
  props: {
    accept: {
      type: String,
      default: 'file'
    },
    // 上传地址
    url: {
      type: String,
      default: ''
    },
    // 上传最大数量 默认为100
    fileNumLimit: {
      type: Number,
      default: 100
    },
    // 大小限制 默认2M
    fileSingleSizeLimit: {
      type: Number,
      default: 2048000
    },
    // 上传时传给后端的参数，一般为token，key等
    formData: {
      type: Object,
      default: null
    },
    // 生成formData中文件的key，下面只是个例子，具体哪种形式和后端商议
    keyGenerator: {
      type: Function,
      default(file) {
        const currentTime = new Date().getTime()
        const key = `${currentTime}.${file.name}`
        return key
      }
    },
    multiple: {
      type: Boolean,
      default: false
    },
    // 上传按钮ID
    uploadButton: {
      type: String,
      default: ''
    },
    // 分片大小
    chunkSize: {
      type: Number,
      default: () => {
        return 10 * 1024 * 1024
      }
    }
  },
  data() {
    return {
      uploader: null
    }
  },
  mounted() {
    this.initWebUpload()
  },
  methods: {
    initWebUpload() {
      this.uploader = WebUploader.create({
        auto: true, // 选完文件后，是否自动上传
        server: this.url, // 文件接收服务端
        pick: {
          id: this.uploadButton, // 选择文件的按钮
          multiple: this.multiple, // 是否多文件上传 默认false
          label: ''
        },
        accept: this.getAccept(this.accept), // 允许选择文件格式。
        threads: 1, // 上传并发数。允许同时最大上传进程数
        fileNumLimit: this.fileNumLimit, // 限制上传个数
        chunkRetry: 2, // 如果某个分片由于网络问题出错，允许自动重传多少次？
        // fileSingleSizeLimit: this.fileSingleSizeLimit, // 限制单个上传图片的大小
        formData: this.formData, // 上传所需参数
        chunked: true, // 分片上传
        chunkSize: this.chunkSize, // 分片大小
        duplicate: true // 重复上传
      })

      // 当有文件被添加进队列的时候，添加到页面预览
      this.uploader.on('fileQueued', (file) => {
        this.$emit('fileChange', file)
      })

      this.uploader.on('uploadStart', (file) => {
        // 在这里可以准备好formData的数据file
        console.log('############')
        console.log(file)
        // 当文件大小小于等于切片大小时，手动添加切片总数和当前切片下标参数
        if (file.size <= this.chunkSize) {
          this.uploader.options.formData.chunks = 1
          this.uploader.options.formData.chunk = 0
        }
      })

      // 文件上传过程中创建进度条实时显示。
      this.uploader.on('uploadProgress', (file, percentage) => {
        this.$emit('progress', file, percentage)
      })

      this.uploader.on('uploadSuccess', (file, response) => {
        this.$emit('success', file, response)
      })

      this.uploader.on('uploadError', (file, reason) => {
        console.error(reason)
        this.$emit('uploadError', file, reason)
      })

      this.uploader.on('error', (type) => {
        let errorMessage = ''
        if (type === 'F_EXCEED_SIZE') {
          errorMessage = `文件大小不能超过${this.fileSingleSizeLimit / (1024 * 1000)}M`
        } else if (type === 'Q_EXCEED_NUM_LIMIT') {
          errorMessage = '文件上传已达到最大上限数'
        } else {
          errorMessage = `上传出错！请检查后重新上传！错误代码${type}`
        }

        console.error(errorMessage)
        this.$emit('error', errorMessage)
      })

      this.uploader.on('uploadComplete', (file, response) => {
        this.$emit('complete', file, response)
      })
    },

    upload(file) {
      this.uploader.upload(file)
    },
    stop(file) {
      this.uploader.stop(file)
    },
    // 取消并中断文件上传
    cancelFile(file) {
      this.uploader.cancelFile(file)
    },
    // 在队列中移除文件
    removeFile(file, bool) {
      this.uploader.removeFile(file, bool)
    },

    getAccept(accept) {
      switch (accept) {
        case 'file':
          return {
            title: 'Files',
            exteensions: 'zip',
            mimeTypes: '.zip'
          }
        case 'json':
          return {
            title: 'Json',
            exteensions: 'json',
            mimeTypes: '.json'
          }
        case 'text':
          return {
            title: 'Texts',
            exteensions: 'doc,docx,xls,xlsx,ppt,pptx,pdf,txt',
            mimeTypes: '.doc,docx,.xls,.xlsx,.ppt,.pptx,.pdf,.txt'
          }
        case 'video':
          return {
            title: 'Videos',
            exteensions: 'mp4',
            mimeTypes: '.mp4'
          }
        case 'image':
          return {
            title: 'Images',
            exteensions: 'gif,jpg,jpeg,bmp,png',
            mimeTypes: '.gif,.jpg,.jpeg,.bmp,.png'
          }
        default: return accept
      }
    }

  }
}
</script>

<style>
</style>
