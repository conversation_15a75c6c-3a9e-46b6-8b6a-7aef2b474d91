<template>
  <div class="level_icon_box" :class="[grade ? 'v2' : '']">
    <div class="v_img">
      <img :src="grade ? gradeUrl : grade0Url" alt="等级图标">
    </div>
    <span>{{ grade ? lang === 'US' ? grade.name_en : grade.name : $t('contents.noLevel') }}</span>
    <span v-if="grade && grade.grade" style="padding-left:3px;">{{ grade.grade }}</span>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'LevelIcon',
  props: {
    grade: {
      type: Object,
      default: () => {
        return null
      }
    }
  },
  data() {
    return {
      gradeUrl: require('../../assets/img/grade.png'),
      grade0Url: require('../../assets/img/grade0.png')
    }
  },
  computed: {
    ...mapGetters('setting', ['lang'])
  }
}
</script>

<style lang="less" scoped>
.level_icon_box {
    display: inline-block;
    background-color: rgba(0,0,0,.6);
    color: #fff;
    border-radius: 8px;
    padding: 5px 10px 5px 35px;
    position: relative;
    font-size: 14px;
    border: 2px dashed #000;
    line-height: 1;
    // &::before {
    //   content: '';
    //   position: absolute;
    //   left: 0;
    //   top: 0;
    //   width: 100%;
    //   height: 100%;
    //   background-image: linear-gradient(to right, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, .2) 50%, rgba(255, 255, 255, 0) 70%);
    //   background-size: 200%;
    //   animation: moveBg 1s infinite;
    // }
    .v_img {
      position: absolute;
      left: -2px;
      top: -10px;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      border: 2px dashed #000;
      background-color: #FCF9C6;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        height: 20px;
      }
    }
    &.v1 {
      background-color: #FB9297;
      border-color: #E5787B;
      .v_img {
        border-color: #E5787B;
      }
    }
    &.v2 {
      background-color: #F7AF43;
      border-color: #CE7500;
      .v_img {
        border-color: #CE7500;
      }
    }
    &.v3 {
      background-color: #CD7DDE;
      border-color: #942BBC;
      .v_img {
        border-color: #942BBC;
      }
    }
}
@keyframes moveBg {
  0% { background-position: 0 0; }
  100% { background-position: 100% 0; }
}
</style>
