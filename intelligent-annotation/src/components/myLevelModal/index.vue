<template>
  <a-modal
    :visible="visible"
    wrap-class-name="my_level_modal"
    width="60%"
    :title="$t('contents.myLevel')"
    :centered="true"
    :footer="null"
    @cancel="hideModal"
  >
    <div id="level_tab" class="level_tab">
      <div class="level_list" :style="{ transform: 'translateX(' + translateX +'px)'}">
        <div v-for="(item, i) in levelList" :key="item.grade_id" class="item" :class="{'scale': clickItem === i }" @click="levelTabClick(item, i)">
          <div class="tab_level_img" :style="{ backgroundImage: item.grade_id ? 'url('+ gradeUrl +')' : 'url('+ grade0Url +')' }">
            <!-- <div v-if="item.grade" class="code">{{ item.grade }}</div> -->
            <div v-if="grade && grade.grade_pk === item.grade_id" class="my_level_item">{{ $t("contents.mLevel") }}</div>
          </div>
          <div class="tab_level_name">{{ lang === 'CN' ? `${item.name}${item.grade}` : `${item.name_en}${item.grade}` }}</div>
        </div>
      </div>
    </div>
    <div class="level_dec">
      <div class="res_msg">
        {{ grade ? $t("contents.grade2", { a: grade.avg, b: grade.error_rate, c: grade.number}) : $t("contents.grade1") }}
        <a-tooltip>
          <template slot="title">
            <div style="padding: 0 10px;word-break: break-all;">
              <h3 style="color: #fff;">{{ $t('contents.grade') }}</h3>
              <p v-html="$t('contents.explain')" />
            </div>
          </template>
          <a-icon type="question-circle" style="margin-left: 5px;" />
        </a-tooltip>
      </div>
      <div class="m_table">
        <a-table
          size="middle"
          bordered
          row-key="id"
          :columns="columns"
          :data-source="levelDec"
          :pagination="false"
        />
      </div>
    </div>
    <a-divider />
    <div class="history_log">
      <h3 class="title">{{ $t("contents.hLevel") }}</h3>
      <a-table :row-key="(record, index) => index" bordered size="middle" :columns="columns2" :data-source="historyLevel" :pagination="false">
        <template slot="name" slot-scope="text, record">{{ lang === 'CN' ? `${record.name}${record.grade}` : `${record.name_en}${record.grade}` }}</template>
        <template slot="icon" slot-scope="text, record">
          <level-icon :grade="record" style="transform: scale(0.9);transformOrigin: 0 50%;" />
        </template>
      </a-table>
    </div>
  </a-modal>
</template>

<script>
import LevelIcon from '@/components/levelIcon'
export default {
  name: 'MyLevelModal',
  components: { LevelIcon },
  props: {
    visible: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    grade: {
      type: Object,
      default: () => {
        return null
      }
    },
    levelList: {
      type: Array,
      default: () => {
        return []
      }
    },
    historyLevel: {
      type: Array,
      default: () => {
        return []
      }
    },
    lang: {
      type: String,
      default: 'CN'
    }
  },
  data() {
    return {
      translateX: 0,
      gradeUrl: require('../../assets/img/grade.png'),
      grade0Url: require('../../assets/img/grade0.png'),
      paginations: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30', '50'],
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => {
          return this.$t('table.total') + total + this.$t('table.strip')
        },
        total: 0
      },
      levelDec: [],
      clickItem: 0
    }
  },
  computed: {
    defaultIndex() {
      if (this.grade) {
        const idx = this.levelList.findIndex(it => it.grade_id === this.grade.grade_pk)
        if (idx > 0) {
          return idx
        }
        return 0
      } else {
        return 0
      }
    },
    columns() {
      return [
        {
          title: this.$t('label.level'),
          dataIndex: 'level',
          align: 'center'
        },
        {
          title: this.$t('table.rc'),
          dataIndex: 'dec',
          align: 'center'
        }
      ]
    },
    columns2() {
      return [
        {
          title: this.$t('table.date'),
          dataIndex: 'date',
          align: 'center'
        },
        {
          title: this.$t('label.level'),
          key: 'name',
          align: 'center',
          scopedSlots: { customRender: 'name' }
        },
        {
          title: this.$t('table.icon'),
          key: 'icon',
          align: 'center',
          scopedSlots: { customRender: 'icon' }
        }
      ]
    }
  },
  watch: {
    visible(value) {
      if (value) {
        this.$nextTick(() => {
          this.moveLevelTab()
        })
      }
    }
  },
  methods: {
    // 移动指定等级到中间位置
    moveLevelTab(idx) {
      const index = idx || this.defaultIndex
      this.clickItem = index
      const tab_w = document.body.clientWidth * 0.6
      const li_w = 120
      this.translateX = parseInt((tab_w / 2) - (li_w / 2) - (index * li_w))
      if (index === 0) {
        this.levelDec = [{ id: 1, level: this.$t('contents.not'), dec: this.$t('contents.noLevel') }]
      } else {
        const item = this.levelList[index]
        this.levelDec = [
          {
            id: 1,
            level: this.lang === 'CN' ? `${item.name}${item.grade}` : `${item.name_en}${item.grade}`,
            dec: this.$t('contents.grade3', { a: `${item.avg_min}-${item.avg_max}`, b: `${item.error_rate_min}%-${item.error_rate_max}%`, c: `${item.number_min}-${item.number_max}` })
          }
        ]
      }
    },
    hideModal() {
      this.$emit('onHideModal')
    },
    levelTabClick(item, idx) {
      this.moveLevelTab(idx)
    },
    // 分页改变
    handleTableChange(obj) {
      this.paginations.current = obj.current
      if (obj.showSizeChanger) {
        this.paginations.pageSize = obj.pageSize
      }
      // this.handleSubmit()
    }
  }
}
</script>

<style lang="less">
  .my_level_modal {
    .ant-modal {
      height: 90%;
      overflow: hidden;
    }
    .ant-modal-content {
      height: 100%;
    }
    .ant-modal-body {
      height: calc(~"100% - 55px");
      overflow-y: auto;
      padding: 0;
    }
    .level_tab {
      width: 100%;
      overflow: hidden;
      padding: 54px 0 34px;
      background-color: rgba(58,81,255,.6);
      position: relative;
      &::after {
        content: "";
        display: none;
        width: 100%;
        height: 4px;
        background: #3a51ff;
        position: absolute;
        left: 0;
        top: 50%;
        margin-top: 2px;
        z-index: 1;
      }
    }
    .level_list {
      display: flex;
      flex-wrap: nowrap;
      transition: all .6s linear;
      position: relative;
      z-index: 5;
      .item {
        flex: 0 0 120px;
        text-align: center;
        cursor: pointer;
        .tab_level_img {
          height: 50px;
          position: relative;
          background-position: center;
          background-size: auto 100%;
          background-repeat: no-repeat;
          display: flex;
          align-items: center;
          justify-content: center;
          .code {
            color:#3a51ff;
            font-weight: bold;
            margin-top: 5px;
          }
          .my_level_item {
            width: 100%;
            text-align: center;
            position: absolute;
            left: 0;
            top: -25px;
            color: #fff;
            word-break: break-all;
          }
        }
        .tab_level_name {
          color: #ffffff;
          margin-top: 5px;
        }
      }
      .scale {
        position: relative;
        transition: all .6s linear;
        transform: scale3d(1.2, 1.2, 1.2);
        transform-origin: center;
      }
    }
    .level_dec {
      text-align: center;
      position: relative;
      padding: 54px 0 10px;
      &::after {
          content: '';
          display: block;
          width: 0;
          height: 0;
          border-top: none;
          border-bottom: 14px solid #fff;
          border-left: 14px solid transparent;
          border-right: 14px solid transparent;
          position: absolute;
          left: 50%;
          top: -14px;
          margin-left: -14px;
      }
      .m_table {
        margin: 0 5%;
        // .ant-table-thead > tr > th {
        //   background-color: rgba(0,0,0,.5);
        //   border-right: none;
        // }
      }
      .res_msg {
        position: absolute;
        left: 0;
        top: 20px;
        width: 100%;
      }
    }
    .history_log{
      padding: 0 5% 24px;
      .title {
        margin-bottom: 24px;
      }
    }
  }

</style>
