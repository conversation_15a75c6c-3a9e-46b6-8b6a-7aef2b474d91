<template>
  <div class="inupt_select">
    <a-input
      :class="{'text_red': newItem}"
      :value="value"
      @focus="onFocus"
      @blur="onBlur"
      @change="onChange"
    >
      <a-checkbox v-if="showCheck" slot="prefix" :checked="isChecked" @click.stop @change="onCheckboxChange" />
      <a-icon slot="suffix" style="color: rgba(0,0,0,.25);font-size:12px;" type="down" />
    </a-input>
    <div v-show="show" class="list_box">
      <div class="list">
        <div v-for="(it, index) in filter_list" :key="index" class="item" data-id="it.id" unselectable="on" @click.stop="itemClick(it)"> {{ it.name }} </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InputSelect',
  props: {
    value: {
      type: String,
      default: ''
    },
    lists: {
      type: Array,
      default: () => {
        return []
      }
    },
    showCheck: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    isChecked: {
      type: Boolean,
      default: () => {
        return true
      }
    }
  },
  data() {
    return {
      show: false,
      filter_data: [],
      old_value: null
    }
  },
  computed: {
    filter_list() {
      const data = this.value ? this.filter_data : this.lists
      return data
    },
    newItem() {
      if (this.value) {
        const idx = this.lists.findIndex(it => it.name.toLowerCase() === this.value.toLowerCase())
        if (idx === -1) {
          return true
        }
      }
      return false
    }
  },
  methods: {
    itemClick(item) {
      this.$emit('change', item.name)
    },
    onFocus() {
      this.show = true
      this.filter_data = [...this.lists]
      if (this.value) {
        this.old_value = JSON.parse(JSON.stringify(this.value))
      } else {
        this.old_value = null
      }
    },
    onBlur() {
      setTimeout(() => {
        this.show = false
        if (this.old_value !== this.value) {
          this.$emit('onResult', this.value)
        }
      }, 150)
    },
    onChange(e) {
      const value = e.target.value
      this.$emit('change', value)
      if (value) {
        this.filter_data = this.lists.filter(it => it.name.toLowerCase().indexOf(value.toLowerCase()) !== -1)
      } else {
        this.filter_data = []
      }
    },
    onCheckboxChange(e) {
      const value = e.target.checked
      this.$emit('oncheckboxed', value)
    }
  }
}
</script>

<style lang="less" scoped>
.inupt_select{
  width: 100%;
  position: relative;
  .text_red {
    color: #ff0000;
  }
  .list_box {
    position: absolute;
    left: 0;
    top: 100%;
    z-index: 1050;
    width: 100%;
    padding: 4px 0;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0 , 0.15);
    background-color: #fff;
  }
  .list {
    width: 100%;
    max-height: 250px;
    overflow: auto;
    .item {
      padding: 5px 12px;
      overflow: hidden;
      color: rgba(0, 0, 0, 0.65);
      font-weight: normal;
      font-size: 14px;
      line-height: 22px;
      white-space: nowrap;
      text-overflow: ellipsis;
      user-select: none;
      cursor: pointer;
      &:hover {
        background-color: #f0f4ff;
      }
    }
  }
}
</style>
