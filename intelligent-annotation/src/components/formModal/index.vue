<template>
  <a-modal
    :width="width"
    :title="title"
    :visible="visible"
    :confirm-loading="loading"
    destroy-on-close
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <slot />
    <template slot="footer">
      <slot v-if="customFooter" name="footer" />
      <template v-else>
        <a-button key="back" @click="handleCancel">
          {{ $t('buttons.cancel') }}
        </a-button>
        <a-button key="submit" type="primary" @click="handleOk">
          {{ $t('buttons.define' ) }}
        </a-button>
      </template>
    </template>
  </a-modal>
</template>

<script>
export default {
  name: 'FormModal',
  props: {
    width: {
      type: [Number, String],
      default: () => {
        return 520
      }
    },
    title: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    customFooter: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    loading: {
      type: <PERSON>olean,
      default: () => {
        return false
      }
    }
  },
  methods: {
    handleOk() {
      this.$emit('submitOk')
    },
    handleCancel() {
      this.$emit('onlyClose')
    }
  }
}
</script>

<style>

</style>
