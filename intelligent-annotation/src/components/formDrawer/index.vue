<template>
  <a-drawer
    :title="title"
    placement="right"
    :destroy-on-close="true"
    :closable="false"
    :visible="visible"
    :width="width"
    :body-style="{ paddingBottom: '80px' }"
    @close="closeDrawerFun"
  >
    <slot />
    <div v-if="loading" class="data_loading">
      <a-spin size="large" :tip="loadingTitle" />
    </div>
  </a-drawer>
</template>

<script>
export default {
  name: 'FormDrawer',
  props: {
    title: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    loadingTitle: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '780'
    }
  },
  methods: {
    closeDrawerFun() {
      this.$emit('closeDrawer')
    }
  }
}
</script>
<style>
  .ant-spin.ant-spin-show-text .ant-spin-text {
    font-size: 16px;
    color: #ffffff;
  }
</style>
<style scoped>
  .data_loading {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.4);
    z-index: 10;
  }
</style>
