import Vue from 'vue'
import App from './App.vue'
import { initRouter } from './router'
import './theme/index.less'
import Antd from 'ant-design-vue'
import Viser from 'viser-vue'
import store from './store'
import 'animate.css/source/animate.css'
import Plugins from '@/plugins'
import { initI18n } from '@/utils/i18n'
import bootstrap from '@/bootstrap'
import moment from 'moment'
import 'moment/locale/zh-cn'
import jquery from 'jquery'
import pkg from '../package.json'

const router = initRouter(store.state.setting.asyncRoutes)
const i18n = initI18n()
import VueLazyload from 'vue-lazyload'
Vue.use(VueLazyload, {
  preLoad: 1.1
})
import Contextmenu from 'vue-contextmenujs'
Vue.use(Contextmenu)
Vue.use(Antd)
Vue.config.productionTip = false
Vue.use(Viser)
Vue.use(Plugins)
Vue.prototype.$moment = moment
import localForage from 'localforage'
Vue.prototype.$forage = localForage
bootstrap({ router, store, i18n, message: Vue.prototype.$message })

new Vue({
  router,
  store,
  i18n,
  jquery,
  render: h => h(App)
}).$mount('#app')
// 或者直接使用
console.log('当前版本:', pkg.version)
