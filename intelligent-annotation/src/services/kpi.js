import {
  GetAchievements,
  DownloadPicLog,
  GetTodayData,
  GetPersonalTodayData,
  GetDayAch,
  GetProjectAllData,
  ChangeProjectAllData,
  GetProjectInventoryLog,
  RegisterLog,
  GetTaskGroupName,
  GetWorkAchievements,
  GetErrorLog,
  GetErrorCenterLog,
  ChangeErrorType,
  GetRestoreLog
} from '@/services/api'
import { request, METHOD } from '@/utils/request'

/**
 * 绩效查询列表
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getAchievements(obj) {
  return request(GetAchievements, METHOD.GET, obj)
}
/**
 * 绩效查询列表
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function downloadPicLog(obj) {
  return request(DownloadPicLog, METHOD.GET, obj, {
    responseType: 'blob'
  })
}

/**
 * 印度当天数据
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getTodayData(obj) {
  return request(GetTodayData, METHOD.GET, obj)
}

/**
 * 印度当天个人数据
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getPersonalTodayData(obj) {
  return request(GetPersonalTodayData, METHOD.GET, obj)
}
/**
 * 查看当月截止到目前绩效
 */
export async function getDayAch(obj) {
  return request(GetDayAch, METHOD.GET, obj)
}
/**
 * 获取月度项目汇总列表
 */
export async function getProjectAllData(obj) {
  return request(GetProjectAllData, METHOD.GET, obj)
}
/**
 * 修改月度项目汇总列表
 */
export async function changeProjectAllData(obj) {
  return request(ChangeProjectAllData, METHOD.POST, obj)
}

/**
 * 获取项目盘点列表
 */
export async function getProjectInventoryLog(obj) {
  return request(GetProjectInventoryLog, METHOD.GET, obj)
}

/**
 * sku注册绩效
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function registerLog(obj) {
  return request(RegisterLog, METHOD.GET, obj)
}

/**
 * 获取郑州团队当天有数据的班级名和班级id
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getTaskGroupName(obj) {
  return request(GetTaskGroupName, METHOD.GET, obj)
}

/**
 * 获取小时数据绩效
 * @task_flow_name 任务名
 * @group_id 班级id
 * @date_time 时间
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getWorkAchievements(obj) {
  return request(GetWorkAchievements, METHOD.GET, obj)
}

/*
 * 获取个人错误信息
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getErrorLog(obj) {
  return request(GetErrorLog, METHOD.GET, obj)
}

/*
 * 获取错误中心列表
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getErrorCenterLog(obj) {
  return request(GetErrorCenterLog, METHOD.GET, obj)
}

/*
 * 获取错误中心列表-修改
 * @param error_log_id 日志id, error_type_id 类型id
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function changeErrorType(obj) {
  return request(ChangeErrorType, METHOD.POST, obj)
}

/*
 * 获取还原日志列表
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getRestoreLog(obj) {
  return request(GetRestoreLog, METHOD.GET, obj)
}

