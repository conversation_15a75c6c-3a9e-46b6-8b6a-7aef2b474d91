import {
  GetJurisdictionList,
  AddJurisdiction,
  ChangeJurisdiction
} from '@/services/api'
import { request, METHOD } from '@/utils/request'

/**
 * 菜单列表
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getJurisdictionList(obj) {
  return request(GetJurisdictionList, METHOD.GET, obj)
}

/**
 * 菜单新增
 * @jurisdiction_id ID
 * @name
 * @is_delete 是否删除(T or F)
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function addJurisdiction(obj) {
  return request(AddJurisdiction, METHOD.POST, obj)
}

/**
 * 菜单修改
 * @jurisdiction_id ID
 * @name
 * @is_delete 是否删除(T or F)
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function changeJurisdiction(obj) {
  return request(ChangeJurisdiction, METHOD.POST, obj)
}
