import {
  GetRuleName,
  AddRuleName,
  DelRuleName,
  ChangeRuleName,
  GetDicts,
  AddDicts,
  ChangeDicts,
  ChangeGrade,
  AddGrade,
  GetGrades,
  GetHisGrades,
  GetSwithchState,
  ChangeSwithchState
} from '@/services/api'
import { request, METHOD } from '@/utils/request'

/**
 * 查询项目大类
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getRuleName(obj) {
  return request(GetRuleName, METHOD.GET, obj)
}

/**
 * 新增项目大类
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function addRuleName(obj) {
  return request(AddRuleName, METHOD.POST, obj)
}

/**
 * 删除项目大类
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function delRuleName(obj) {
  return request(DelRuleName, METHOD.POST, obj)
}

/**
 * 修改项目大类
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function changeRuleName(obj) {
  return request(ChangeRuleName, METHOD.POST, obj)
}

/**
 * 字典列表
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getDicts(obj) {
  return request(GetDicts, METHOD.GET, obj)
}

/**
 * 字典-新增
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function addDicts(obj) {
  return request(AddDicts, METHOD.POST, obj)
}

/**
 * 字典-修改
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function changeDicts(obj) {
  return request(ChangeDicts, METHOD.POST, obj)
}

/**
 * 等级列表
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getGrades(obj) {
  return request(GetGrades, METHOD.GET, obj)
}

/**
 * 等级-新增
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function addGrade(obj) {
  return request(AddGrade, METHOD.POST, obj)
}

/**
 * 等级-修改
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function changeGrade(obj) {
  return request(ChangeGrade, METHOD.POST, obj)
}

/**
 * 历史等级列表
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getHisGrades(obj) {
  return request(GetHisGrades, METHOD.GET, obj)
}

/**
 * 获取是否可以查询我的错误
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getSwithchState(obj) {
  return request(GetSwithchState, METHOD.GET, obj)
}

/**
 * 修改是否可以查询我的错误
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function changeSwithchState(obj) {
  return request(ChangeSwithchState, METHOD.POST, obj)
}
