import {
  CreateProject,
  ChangeProject,
  GetProjectList,
  UploadProjectFile,
  ReleaseProject,
  GetProjectInfo,
  CreateTaskFlow,
  UploadTaskFlow,
  DeleteTaskFlow,
  GetTaskFlowList,
  DownloadProjectData,
  CleanTaskFlowUser,
  GetProjectTemplate,
  ChangeProjectTemplate,
  StopDir,
  AdminGiveupDir,
  AdminGetStopDir, SendBackProject, GetUserTodayActionAmount
} from '@/services/api'
import { request, METHOD } from '@/utils/request'

/**
 * 创建项目
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function createProject(obj) {
  return request(CreateProject, METHOD.POST, obj)
}

/**
 * 配置项目
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function changeProject(obj) {
  return request(ChangeProject, METHOD.POST, obj)
}

/**
 * 获取项目列表
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getProjectList(obj) {
  return request(GetProjectList, METHOD.GET, obj)
}

/**
 * 上传项目文件 formData 格式
 * @project_id
 * @file
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function uploadProjectFile(obj) {
  return request(UploadProjectFile, METHOD.POST, obj)
}

/**
 * 发布项目
 * @project_id
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function releaseProject(project_id) {
  return request(ReleaseProject, METHOD.POST, { project_id })
}

/**
 * 发布项目
 * @project_id
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function sendBackProject(project_id) {
  return request(SendBackProject, METHOD.POST, { project_id })
}

/**
 * 获取项目详情
 * @project_id
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getProjectInfo(project_id) {
  return request(GetProjectInfo, METHOD.GET, { project_id })
}

/**
 * 创建任务
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function createTaskFlow(obj) {
  return request(CreateTaskFlow, METHOD.POST, obj)
}

/**
 * 获取项目下任务列表
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getTaskFlowList(project_id) {
  return request(GetTaskFlowList, METHOD.GET, { project_id })
}

/**
 * 修改任务
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function uploadTaskFlow(obj) {
  return request(UploadTaskFlow, METHOD.POST, obj)
}

/**
 * 删除任务
 * @task_flow_id
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function deleteTaskFlow(task_flow_id) {
  return request(DeleteTaskFlow, METHOD.POST, { task_flow_id })
}

/**
 * 下载项目数据
 * @project_id
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function downloadProjectData(project_id) {
  return request(DownloadProjectData, METHOD.GET, { project_id })
}

/**
 * 下线流程下指定的用户
 * @task_flow_id
 * @user_id_list
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function cleanTaskFlowUser(obj) {
  return request(CleanTaskFlowUser, METHOD.POST, obj)
}
/**
 * 获取任务流程模板
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getProjectTemplate() {
  return request(GetProjectTemplate, METHOD.GET, {})
}

/**
 * 修改任务模板接口
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function changeProjectTemplate(obj) {
  return request(ChangeProjectTemplate, METHOD.POST, obj)
}

/**
 * 暂停任务文件夹
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function stopDir(obj) {
  return request(StopDir, METHOD.POST, obj)
}

/**
 * 管理员放弃成员占用的文件夹
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function adminGiveupDir(obj) {
  return request(AdminGiveupDir, METHOD.POST, obj)
}

/**
 * 管理员获取暂停文件夹列表
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function adminGetStopDir(obj) {
  return request(AdminGetStopDir, METHOD.GET, obj)
}

/**
 * 获取用户今日提交文件夹和图片数量
 * @user_id
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getUserTodayActionAmount(user_id) {
  return request(GetUserTodayActionAmount, METHOD.GET, { user_id })
}
