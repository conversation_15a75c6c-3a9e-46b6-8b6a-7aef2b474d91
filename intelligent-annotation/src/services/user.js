import { REGISTER, GETPHONEVC, LOGIN, LOGOUT, COUNTRYLIST, RESETPASSWORD, CHANGEPASSWORD, LOGINAUTH, GetUseList, ChangeUserData, ACTIONLIST } from '@/services/api'
import { request, METHOD, removeAuthorization } from '@/utils/request'

/**
 * 登录服务
 * @param name 账户名
 * @param password 账户密码
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function login(name, password) {
  return request(LOGIN, METHOD.POST, {
    mobile: name,
    password: password
  })
}
/**
 * 退出登录
 * @param access_token 用户身份令牌
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function logoutApi() {
  return request(LOGOUT, METHOD.POST, {})
}
/**
 * 登录认证
 * @param access_token 用户身份令牌
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function loginAuth() {
  return request(LOGINAUTH, METHOD.POST, {})
}
/**
 * 注册账号
 * @param mobile 手机号
 * @param code 国别码
 * @param mobile_vc 短信验证码
 * @param password 账户密码
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function register(obj) {
  return request(REGISTER, METHOD.POST, obj)
}
/**
 * 获取验证码
 * @param verify_type 验证码类型(1:注册 2:重置密码)
 * @param ver_code 图片验证码
 * @param tem_token 临时令牌
 * @param mobile 手机号
 * @param code 国别号
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getPhoneVc(obj) {
  return request(GETPHONEVC, METHOD.POST, obj)
}
/**
 * 获取手机国家编号数据
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function countryList() {
  return request(COUNTRYLIST, METHOD.POST, {})
}
/**
 * 忘记密码
 * @param mobile 手机号
 * @param mobile_vc 短信验证码
 * @param new_password 账户密码
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function resetPassword(obj) {
  return request(RESETPASSWORD, METHOD.POST, obj)
}
/**
 * 获取用户列表
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getUseList(obj) {
  return request(GetUseList, METHOD.GET, obj)
}
/**
 * 获取用户当日工作记录
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function actionList(obj) {
  return request(ACTIONLIST, METHOD.GET, obj)
}
/**
 * 修改用户信息
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function changeUserData(obj) {
  return request(ChangeUserData, METHOD.POST, obj)
}
/**
 * 重置密码
 * @param access_token 用户身份令牌
 * @param old_password 原密码
 * @param new_password 新密码
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function changePassword(obj) {
  return request(CHANGEPASSWORD, METHOD.POST, obj)
}

/**
 * 退出登录清除操作
 */
export function logout() {
  localStorage.removeItem(process.env.VUE_APP_ROUTES_KEY)
  localStorage.removeItem(process.env.VUE_APP_PERMISSIONS_KEY)
  localStorage.removeItem(process.env.VUE_APP_ROLES_KEY)
  removeAuthorization()
}
export default {
  login,
  logout
}
