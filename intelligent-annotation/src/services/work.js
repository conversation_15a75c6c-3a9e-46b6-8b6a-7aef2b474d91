import {
  GetMyTaskList,
  GetDirList,
  GetPicList,
  GetAllPicList,
  StartClearDir,
  GetTemDirList,
  CreateTmpDir,
  SubmitDir,
  GiveUpDir,
  MovePic,
  DeleteTmpDir,
  ClearDirInvisible,
  ChangeTmpDirOrder,
  GiveUpDirNumber,
  AddTmpSku,
  UploadLogo,
  GetTmpSkuList,
  DeleteTmpSku,
  ChangeTmpSku,
  SubmitDirSku,
  CheckTmpSku,
  TagErrorType,
  GetSkuPackageList,
  AddSkuPackage,
  EditSkuPackage,
  UploadPackageLogo,
  GetPackageAuditList,
  GetUserPackageAuditList,
  AuditSkuPackage,
  SubmitFuheTaskFlow,
  GetTaskFlowInfo,
  SaveFuheCheckedProjectId,
  SortCleanImage,
  GetLeftDirSkuList,
  GetLeftDirSkuCutImages,
  SearchCanBeSort
} from '@/services/api'
import { request, METHOD } from '@/utils/request'

/**
 * 我的任务列表
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getMyTaskList(obj) {
  return request(GetMyTaskList, METHOD.GET, obj)
}

/**
 * 获取任务下文件夹列表数据
 * @task_flow_id
 * @page
 * @number
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getDirList(obj) {
  return request(GetDirList, METHOD.GET, obj)
}

/**
 * 获取文件下的图片列表数据
 * @dir_id
 * @page
 * @number
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getPicList(obj) {
  return request(GetPicList, METHOD.GET, obj)
}
/**
 * 获取文件下的图片列表数据
 * @dir_id
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getAllPicList(obj) {
  return request(GetAllPicList, METHOD.GET, obj)
}

/**
 * 开始清洗文件
 * @dir_id
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function startClearDir(dir_id) {
  return request(StartClearDir, METHOD.POST, { dir_id })
}

/**
 * 获取用户该流程下的临时文件夹列表
 * @param task_flow_id int 流程ID
 * @param dir_id 0为获取所有，非0为指定文件夹
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getTemDirList(task_flow_id, dir_id = 0) {
  return request(GetTemDirList, METHOD.GET, { task_flow_id, dir_id })
}

/**
 * 创建(临时)文件夹
 * @dir_id
 * @sku_id
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function createTmpDir(obj) {
  return request(CreateTmpDir, METHOD.POST, obj)
}

/**
 * 提交文件夹数据
 * @dir_id
 * @working_hours
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function submitDir(obj) {
  return request(SubmitDir, METHOD.POST, obj)
}

/**
 * 移动图片文件
 * @pic_id_list
 * @old_dir_id
 * @new_dir_id
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function movePic(obj) {
  return request(MovePic, METHOD.POST, obj)
}

/**
 * 放弃提交文件夹
 * @object {dir_id, reason} 包含dir_id,reason
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function giveUpDir(obj) {
  return request(GiveUpDir, METHOD.POST, obj)
}

/**
 * 删除(临时)文件夹
 * @dir_id
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function deleteTmpDir(dir_id) {
  return request(DeleteTmpDir, METHOD.POST, { dir_id })
}

/**
 * 开始清洗
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function clearDirInvisible(task_flow_id) {
  return request(ClearDirInvisible, METHOD.POST, { task_flow_id })
}

/**
 * 修改(临时)文件夹排序
 * @dir_id
 * @show_order
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function changeTmpDirOrder(obj) {
  return request(ChangeTmpDirOrder, METHOD.POST, obj)
}

/**
 * 查询放弃文件夹的剩余次数
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function giveUpDirNumber() {
  return request(GiveUpDirNumber, METHOD.GET, {})
}

/**
 * 开始sku注册审核任务
 * @dir_id
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function startSkuDir(obj) {
  return request(StartClearDir, METHOD.POST, obj)
}

/**
 * 增加临时sku接口
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function addTmpSku(obj) {
  return request(AddTmpSku, METHOD.POST, obj)
}

/**
 * 增加sku关联图片接口
 * @image
 * @tmp_sku_id
 * @logo_url
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function uploadLogo(obj) {
  return request(UploadLogo, METHOD.POST, obj)
}

/**
 * 获取当前文件夹下临时SKU接口
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getTmpSkuList(obj) {
  return request(GetTmpSkuList, METHOD.GET, obj)
}

/**
 * 删除临时sku接口
 * @tmp_sku_id
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function deleteTmpSku(tmp_sku_id) {
  return request(DeleteTmpSku, METHOD.POST, { tmp_sku_id })
}

/**
 * 修改临时sku接口
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function changeTmpSku(obj) {
  return request(ChangeTmpSku, METHOD.POST, obj)
}

/**
 * 提交临时sku接口
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function submitDirSku(obj) {
  return request(SubmitDirSku, METHOD.POST, obj)
}

/**
 * 审核临时sku接口
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function checkTmpSku(tmp_sku_id) {
  return request(CheckTmpSku, METHOD.POST, { tmp_sku_id })
}

/**
 * 标记图片错误类型
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function tagErrorType(obj) {
  return request(TagErrorType, METHOD.POST, obj)
}
/**
 * 获取sku的包装列表
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getSkuPackageList(obj) {
  return request(GetSkuPackageList, METHOD.GET, obj)
}
/**
 * 添加包装
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function addSkuPackage(obj) {
  return request(AddSkuPackage, METHOD.POST, obj)
}
/**
 * 修改sku包装接口
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function changeSkuPackage(obj) {
  return request(EditSkuPackage, METHOD.POST, obj)
}
/**
 * 增加sku包装图片接口
 * @image
 * @tmp_sku_id
 * @logo_url
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function uploadPackageLogo(obj) {
  return request(UploadPackageLogo, METHOD.POST, obj)
}
/**
 * 增加sku包装图片接口
 * @image
 * @tmp_sku_id
 * @logo_url
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getPackageList(obj) {
  return request(GetPackageAuditList, METHOD.GET, obj)
}
/**
 * 增加sku包装图片接口
 * @image
 * @tmp_sku_id
 * @logo_url
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getUserPackageList(obj) {
  return request(GetUserPackageAuditList, METHOD.GET, obj)
}
/**
 * 审核sku包装接口
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function auditSkuPackage(obj) {
  return request(AuditSkuPackage, METHOD.POST, obj)
}
/**
 * 获取复核流程任务信息
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getTaskFlowInfo(obj) {
  return request(GetTaskFlowInfo, METHOD.GET, obj)
}

/**
 * 获取复核流程任务信息
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function submitFuheTaskFlow(obj) {
  return request(SubmitFuheTaskFlow, METHOD.POST, obj)
}

/**
 * 修改复核流程参照项目
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function saveCheckedProject(obj) {
  return request(SaveFuheCheckedProjectId, METHOD.POST, obj)
}

/**
 * 重排序
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function sortCleanImage(obj) {
  return request(SortCleanImage, METHOD.POST, obj)
}

/**
 * 获取左侧单个文件夹sku列表
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getLeftDirSkuList(obj) {
  return request(GetLeftDirSkuList, METHOD.GET, obj)
}

/**
 * 获取左侧单个文件夹单个 SKU下的 图片列表
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getLeftDirSkuCutImages(obj) {
  return request(GetLeftDirSkuCutImages, METHOD.GET, obj)
}

/**
 * 查询是否可以排序
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function searchCanBeSort(obj) {
  return request(SearchCanBeSort, METHOD.GET, obj)
}
