// 跨域代理前缀
// const API_PROXY_PREFIX = '/api'
// const BASE_URL = process.env.NODE_ENV === 'production' ? process.env.VUE_APP_API_BASE_URL : API_PROXY_PREFIX
const BASE_URL = process.env.VUE_APP_API_BASE_URL
const BASE_URL2 = process.env.VUE_APP_API_BASE_URL2
const VUE_APP_API_ALPHA_URL = process.env.VUE_APP_API_ALPHA_URL
module.exports = {
  REGISTER: `${BASE_URL}/user/register/`,
  GETPHONEVC: `${BASE_URL}/user/get_phone_vc/`,
  LOGIN: `${BASE_URL}/user/login/`,
  CHANGEPASSWORD: `${BASE_URL}/user/change_password/`,
  RESETPASSWORD: `${BASE_URL}/user/reset_password/`,
  GETUSERINFO: `${BASE_URL}/user/get_user_info/`,
  COUNTRYLIST: `${BASE_URL}/country/list/`,

  LOGOUT: `${BASE_URL2}/user/logout/`,
  LOGINAUTH: `${BASE_URL2}/user/authentication/`,
  // 用户
  GetUseList: `${BASE_URL2}/user/user_list/`,
  ACTIONLIST: `${BASE_URL2}/action/action_list/`,
  ChangeUserData: `${BASE_URL2}/user/change_user_data/`,
  // 角色
  RoleList: `${BASE_URL2}/user/role_list/`,
  AddRole: `${BASE_URL2}/user/add_role/`,
  ChangeRole: `${BASE_URL2}/user/change_role/`,
  ChangeRoleJurisdiction: `${BASE_URL2}/user/change_role_jurisdiction/`,
  // 权限
  GetJurisdictionList: `${BASE_URL2}/user/jurisdiction_list/`,
  AddJurisdiction: `${BASE_URL2}/user/add_jurisdiction/`,
  ChangeJurisdiction: `${BASE_URL2}/user/change_jurisdiction/`,
  // 团队
  TeamList: `${BASE_URL2}/user/team_list/`,
  AddTeam: `${BASE_URL2}/user/add_team/`,
  ChangeTeam: `${BASE_URL2}/user/change_team/`,
  // 组
  GroupList: `${BASE_URL2}/user/group_list/`,
  AddGroup: `${BASE_URL2}/user/add_group/`,
  ChangeGroup: `${BASE_URL2}/user/change_group/`,
  // 项目
  CreateProject: `${BASE_URL2}/project/create_project/`,
  ChangeProject: `${BASE_URL2}/project/change_project/`,
  GetProjectList: `${BASE_URL2}/project/get_project_list/`,
  UploadProjectFile: `${BASE_URL2}/project/upload_project_file/`,
  ReleaseProject: `${BASE_URL2}/project/release_project/`,
  SendBackProject: `${BASE_URL2}/project/send_back_project/`,
  GetProjectInfo: `${BASE_URL2}/project/get_project_info/`,
  GetTaskFlowList: `${BASE_URL2}/project/get_task_flow_list/`,
  CreateTaskFlow: `${BASE_URL2}/project/create_task_flow/`,
  UploadTaskFlow: `${BASE_URL2}/project/change_task_flow/`,
  DeleteTaskFlow: `${BASE_URL2}/project/delete_task_flow/`,
  GetMyTaskList: `${BASE_URL2}/project/get_project_and_task_flow_list/`,
  GetDirList: `${BASE_URL2}/project/get_dir_list/`,
  GetPicList: `${BASE_URL2}/project/get_pic_list/`,
  GetAllPicList: `${BASE_URL2}/project/get_all_pic_list/`,
  StartClearDir: `${BASE_URL2}/project/start_clear_dir/`,
  GetTemDirList: `${BASE_URL2}/project/get_tem_dir_list/`,
  CreateTmpDir: `${BASE_URL2}/project/create_tmp_dir/`,
  SubmitDir: `${BASE_URL2}/project/submit_dir/`,
  GiveUpDir: `${BASE_URL2}/project/give_up_dir/`,
  MovePic: `${BASE_URL2}/project/move_pic/`,
  DeleteTmpDir: `${BASE_URL2}/project/delete_tmp_dir/`,
  DownloadProjectData: `${BASE_URL2}/project/download_project_data/`,
  ChangeTmpDirOrder: `${BASE_URL2}/project/change_tmp_dir_order/`,
  GetProjectTemplate: `${BASE_URL2}/project/get_project_template/`,
  ChangeProjectTemplate: `${BASE_URL2}/project/change_project_template/`,
  StopDir: `${BASE_URL2}/project/stop_dir/`,
  AdminGiveupDir: `${BASE_URL2}/project/admin_give_up_dir/`,
  AdminGetStopDir: `${BASE_URL2}/project/admin_get_stop_dir/`,
  // 开始任务
  ClearDirInvisible: `${BASE_URL2}/project/search_can_clear_dir/`,
  GiveUpDirNumber: `${BASE_URL2}/project/give_up_dir_number/`,
  CleanTaskFlowUser: `${BASE_URL2}/project/clean_task_flow_user/`,
  TagErrorType: `${BASE_URL2}/project/tag_error_type/`,
  SortCleanImage: `${VUE_APP_API_ALPHA_URL}/clean/openapi/task/reorder`,
  GetLeftDirSkuList: `${VUE_APP_API_ALPHA_URL}/clean/openapi/merge/list`,
  GetLeftDirSkuCutImages: `${VUE_APP_API_ALPHA_URL}/clean/openapi/merge/box/list`,
  SearchCanBeSort: `${VUE_APP_API_ALPHA_URL}/clean/openapi/confirm/task/reorder`,
  // 下拉
  SKUCOUNTRYLIST: `${BASE_URL2}/sku/get_sku_country_list/`,
  SKUPROJECTLIST: `${BASE_URL2}/sku/get_sku_project_list/`,
  SKULIST: `${BASE_URL2}/sku/get_sku_list/`,
  GetTeamUserList: `${BASE_URL2}/user/get_team_user_list/`,
  GetSkuTypeList: `${BASE_URL2}/sku/get_sku_type_list/`,
  // 多包装
  GetSkuPackageList: `${BASE_URL2}/sku/get_sku_package_list/`,
  AddSkuPackage: `${BASE_URL2}/sku/add_sku_package/`,
  EditSkuPackage: `${BASE_URL2}/sku/edit_sku_package/`,
  AuditSkuPackage: `${BASE_URL2}/sku/audit_sku_package/`,
  UploadPackageLogo: `${BASE_URL2}/sku/upload_package_logo/`,
  // 多包装审核列表
  GetPackageAuditList: `${BASE_URL2}/sku/get_package_audit_list/`,
  // 用户提交的多包装提交列表
  GetUserPackageAuditList: `${BASE_URL2}/sku/get_user_package_list/`,
  GetSkuParameterData: `${BASE_URL2}/tmp_sku/get_sku_parameter_data/`,
  // sku注册审核
  AddTmpSku: `${BASE_URL2}/tmp_sku/add_tmp_sku/`,
  UploadLogo: `${BASE_URL2}/tmp_sku/upload_logo/`,
  GetTmpSkuList: `${BASE_URL2}/tmp_sku/get_dir_tmp_sku_list/`,
  DeleteTmpSku: `${BASE_URL2}/tmp_sku/delete_tmp_sku/`,
  ChangeTmpSku: `${BASE_URL2}/tmp_sku/change_tmp_sku/`,
  SubmitDirSku: `${BASE_URL2}/tmp_sku/submit_dir/`,
  CheckTmpSku: `${BASE_URL2}/tmp_sku/check_tmp_sku/`,
  RegisterLog: `${BASE_URL2}/tmp_sku/register_log/`,
  // 绩效
  GetAchievements: `${BASE_URL2}/logger/get_achievements/`,
  DownloadPicLog: `${BASE_URL2}/logger/download_move_pic_logs/`,
  GetTodayData: `${BASE_URL2}/logger/get_today_data/`,
  GetPersonalTodayData: `${BASE_URL2}/logger/get_personal_today_data/`,
  GetDayAch: `${BASE_URL2}/logger/get_day_achievements/`,
  GetRuleName: `${BASE_URL2}/logger/get_rule_name/`,
  DownloadDayAch: `${BASE_URL2}/logger/download_day_achievements/`,
  AddRuleName: `${BASE_URL2}/logger/add_rule_name/`,
  DelRuleName: `${BASE_URL2}/logger/delete_rule_name/`,
  ChangeRuleName: `${BASE_URL2}/logger/change_rule_name/`,
  GetTaskGroupName: `${BASE_URL2}/logger/get_task_group_name_id/`,
  GetWorkAchievements: `${BASE_URL2}/logger/get_work_achievements/`,
  GetErrorLog: `${BASE_URL2}/logger/get_error_log/`,
  GetErrorCenterLog: `${BASE_URL2}/logger/get_error_center_log/`,
  ChangeErrorType: `${BASE_URL2}/logger/change_error_type/`,
  // 报表
  GetProjectAllData: `${BASE_URL2}/project/get_project_all_data/`,
  ChangeProjectAllData: `${BASE_URL2}/project/change_project_all_data/`,
  GetProjectInventoryLog: `${BASE_URL2}/project/get_project_inventory_log/`,
  // 字典 dictionary/change_switch_state/
  GetDicts: `${BASE_URL2}/dictionary/get_dictionary_data/`,
  AddDicts: `${BASE_URL2}/dictionary/add_dictionary_data/`,
  ChangeDicts: `${BASE_URL2}/dictionary/change_dictionary_data/`,
  GetSwithchState: `${BASE_URL2}/dictionary/get_switch_state/`,
  ChangeSwithchState: `${BASE_URL2}/dictionary/change_switch_state/`,
  // 等级
  AddGrade: `${BASE_URL2}/grade/add_grade_data/`,
  GetGrades: `${BASE_URL2}/grade/grade_list/`,
  ChangeGrade: `${BASE_URL2}/grade/change_grade_data/`,
  GetHisGrades: `${BASE_URL2}/grade/get_history_grade/`,
  GetTaskFlowInfo: `${BASE_URL2}/project/get_task_flow_info/`,
  SubmitFuheTaskFlow: `${BASE_URL2}/project/submit_fuhe_task_flow/`,
  SaveFuheCheckedProjectId: `${BASE_URL2}/project/save_fuhe_checked_project/`,
  // 获取用户今日工作量统计
  GetUserTodayActionAmount: `${BASE_URL2}/project/get_user_today_action_statistics`,
  // 还原暂停文件夹日志
  GetRestoreLog: `${BASE_URL2}/logger/get_restore_log/`

}
