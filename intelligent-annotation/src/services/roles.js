import {
  RoleList,
  AddRole,
  ChangeRole,
  ChangeRoleJurisdiction
} from '@/services/api'
import { request, METHOD } from '@/utils/request'

/**
 * 角色列表
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getRoleList(obj) {
  return request(RoleList, METHOD.GET, obj)
}

/**
 * 角色新增
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function addRole(obj) {
  return request(AddRole, METHOD.POST, obj)
}

/**
 * 角色修改
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function changeRole(obj) {
  return request(ChangeRole, METHOD.POST, obj)
}

/**
 * 修改角色所拥有的权限
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function changeRoleJurisdiction(obj) {
  return request(ChangeRoleJurisdiction, METHOD.POST, obj)
}
