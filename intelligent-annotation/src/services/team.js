import {
  TeamList,
  AddTeam,
  ChangeTeam,
  GroupList,
  AddGroup,
  ChangeGroup
} from '@/services/api'
import { request, METHOD } from '@/utils/request'

/**
 * 团队列表
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getTeamList(obj) {
  return request(TeamList, METHOD.GET, obj)
}
/**
 * 团队新增
 * @name String
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function addTeam(obj) {
  return request(AddTeam, METHOD.POST, obj)
}
/**
 * 团队修改
 * @team_id
 * @name String
 * @is_delete 是否删除(T or F)
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function changeTeam(obj) {
  return request(ChangeTeam, METHOD.POST, obj)
}

/**
 * 组列表
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getGroupList(obj) {
  return request(GroupList, METHOD.GET, obj)
}
/**
 * 组新增
 * @team_id Number
 * @name String
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function addGroup(obj) {
  return request(AddGroup, METHOD.POST, obj)
}

/**
 * 组修改
 * @group_id
 * @name String
 * @is_delete 是否删除(T or F)
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function changeGroup(obj) {
  return request(ChangeGroup, METHOD.POST, obj)
}
