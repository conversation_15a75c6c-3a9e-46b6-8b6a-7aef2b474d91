import {
  RoleList,
  TeamList,
  GroupList,
  GetJurisdictionList,
  SKUCOUNTRYLIST,
  SKUPROJECTLIST,
  SKULIST,
  GetTeamUserList,
  GetSkuTypeList,
  GetSkuParameterData
} from '@/services/api'
import { request, METHOD } from '@/utils/request'

/**
 * 下拉列表-角色
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getRoleList() {
  return request(RoleList, METHOD.GET, {})
}
/**
 * 下拉列表-团队
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getTeamList() {
  return request(TeamList, METHOD.GET, {})
}
/**
 * 下拉列表-组
 * @team_id
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getGroupList(team_id) {
  return request(GroupList, METHOD.GET, { team_id })
}
/**
 * 权限数据
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getJurisdictionList() {
  return request(GetJurisdictionList, METHOD.GET, {})
}
/**
 * 获取 sku国家 列表数据接口
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function skuCountryList() {
  return request(SKUCOUNTRYLIST, METHOD.GET, {})
}
/**
 * 获取 sku项目 列表数据接口
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function skuProjectList() {
  return request(SKUPROJECTLIST, METHOD.GET, {})
}
/**
 * 获取 sku 列表数据接口
 * @sku_project_id 所属sku项目id
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function skuList(sku_project_id) {
  return request(SKULIST, METHOD.GET, { sku_project_id })
}
/**
 * 获取 sku项目 列表数据接口
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getTeamUserList(team_id) {
  return request(GetTeamUserList, METHOD.GET, { team_id })
}

/**
 * 获取sku行业 列表数据接口 GetSkuParameterData
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getSkuTypeList() {
  return request(GetSkuTypeList, METHOD.GET, {})
}

/**
 * 获取sku参数数据
 * @parameter_id 参数id(0,品牌商)(1,品牌)(2,系列(子品牌))(3,口味)(4,品类)(5,包装)(6,规格)(7,外包装)(8,国家)
 * @type_id 行业id
 * @lang 语言(0,中文)(1,英文)
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function getSkuParameterData(obj) {
  return request(GetSkuParameterData, METHOD.GET, obj)
}

export default {
  getRoleList,
  getTeamList,
  getGroupList,
  getJurisdictionList,
  skuCountryList,
  skuProjectList,
  skuList,
  getTeamUserList,
  getSkuTypeList,
  getSkuParameterData
}
