module.exports = {
  messages: {
    CN: {
      router: {
        personal: {
          name: '我的工作台',
          task: {
            name: '我的任务'
          },
          error: {
            name: '我的错误'
          }
        },
        projectMag: {
          name: '项目管理'
        },
        system: {
          name: '系统管理',
          account: {
            name: '成员管理'
          },
          roles: {
            name: '角色管理'
          },
          menus: {
            name: '菜单管理'
          },
          team: {
            name: '团队管理'
          },
          config: {
            name: '配置管理'
          },
          dict: {
            name: '字典管理'
          },
          grade: {
            name: '等级管理'
          },
          reduction: {
            name: '还原管理'
          }
        },
        achievements: {
          name: '绩效管理',
          projectAch: {
            name: '项目绩效'
          },
          IndiaAch: {
            name: '印度定制'
          },
          SkuAch: {
            name: '注册绩效'
          },
          ErrorCenter: {
            name: '错误中心'
          },
          Restore: {
            name: '还原日志'
          }
        },
        report: {
          name: '报表管理',
          basicsData: {
            name: '项目基础数据'
          },
          projectSum: {
            name: '月度项目汇总'
          },
          projectCheck: {
            name: '项目盘点记录'
          },
          hourData: {
            name: '小时数据跟踪'
          }
        }
      }
    },
    US: {
      router: {
        personal: {
          name: 'My workbench',
          task: {
            name: 'My task'
          },
          error: {
            name: 'My mistake'
          }
        },
        projectMag: {
          name: 'Project manage'
        },
        system: {
          name: 'System manage',
          account: {
            name: 'Member manage'
          },
          roles: {
            name: 'Role manage'
          },
          menus: {
            name: 'Menu manage'
          },
          team: {
            name: 'Team manage'
          },
          config: {
            name: 'Config manage'
          },
          dict: {
            name: 'Dict manage'
          },
          grade: {
            name: 'Grade manage'
          },
          reduction: {
            name: 'Restore manage'
          }
        },
        achievements: {
          name: 'Performance manage',
          projectAch: {
            name: 'Project merits'
          },
          IndiaAch: {
            name: 'Customized India'
          },
          SkuAch: {
            name: 'Registration merits'
          },
          ErrorCenter: {
            name: 'Error Center'
          }
        },
        report: {
          name: 'Report manage',
          basicsData: {
            name: 'ProjectBasicData'
          },
          projectSum: {
            name: 'MonthProjectSum'
          },
          projectCheck: {
            name: 'ProjectRecord'
          },
          hourData: {
            name: 'HourDataTrack'
          }
        }
      }
    }
  }
}
