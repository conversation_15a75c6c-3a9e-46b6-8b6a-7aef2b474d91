import TabsView from '@/layouts/tabs/TabsView'
import BlankView from '@/layouts/BlankView'
// import PageView from '@/layouts/PageView'

// 路由配置
const options = {
  mode: 'hash',
  routes: [
    {
      path: '/login',
      name: '登录页',
      component: () => import('@/pages/login')
    },
    {
      path: '/register',
      name: '注册账户',
      component: () => import('@/pages/login/register')
    },
    {
      path: '/forgetPwd',
      name: '忘记密码',
      component: () => import('@/pages/login/forgetPwd')
    },
    {
      path: '*',
      name: '404',
      component: () => import('@/pages/exception/404')
    },
    {
      path: '/403',
      name: '403',
      component: () => import('@/pages/exception/403')
    },
    {
      path: '/taskHandle',
      name: 'taskHandle',
      component: () => import('@/pages/task/task-handle')
    },
    {
      path: '/skuTaskHandle',
      name: 'skuTaskHandle',
      component: () => import('@/pages/newTaskHandle/index')
    },
    {
      path: '/taskAutoCheck',
      name: 'taskAutoCheck',
      component: () => import('@/pages/autoCheck/index')
    },
    {
      path: '/',
      name: '首页',
      component: TabsView,
      redirect: '/login',
      children: [
        {
          path: 'personal',
          name: '我的工作台',
          meta: {
            icon: 'schedule'
          },
          component: BlankView,
          children: [
            {
              path: 'task',
              name: '我的任务',
              component: () => import('@/pages/task')
            },
            {
              path: 'error',
              name: '我的错误',
              component: () => import('@/pages/personal')
            }, {
              path: 'package',
              name: '我的申请',
              meta: {
                authority: {
                  permission: 'my_package'
                }
              },
              component: () => import('@/pages/approval/package/user_package')
            }
          ]
        },
        {
          path: 'approval',
          name: '流程审批',
          meta: {
            icon: 'schedule',
            authority: {
              permission: 'audit_package'
            }
          },
          component: BlankView,
          children: [
            {
              path: 'package',
              name: '多包装审批',
              meta: {
                authority: {
                  permission: 'audit_package'
                }
              },
              component: () => import('@/pages/approval/package')
            }
          ]
        },
        {
          path: 'projectMag',
          name: '项目管理',
          meta: {
            icon: 'project',
            authority: {
              permission: 2
            }
          },
          component: () => import('@/pages/projectMag')
        },
        {
          path: 'system',
          name: '系统管理',
          meta: {
            icon: 'desktop',
            authority: {
              permission: 3
            }
          },
          component: BlankView,
          children: [
            {
              path: 'account',
              name: '成员管理',
              meta: {
                authority: {
                  permission: 301
                }
              },
              component: () => import('@/pages/system/account')
            }, {
              path: 'user_detail',
              name: '成员详情',
              meta: {
                invisible: true
              },
              component: () => import('@/pages/system/account/userDetail')
            }, {
              path: 'roles',
              name: '角色管理',
              meta: {
                authority: {
                  permission: 304
                }
              },
              component: () => import('@/pages/system/roles')
            }, {
              path: 'menus',
              name: '菜单管理',
              meta: {
                authority: {
                  permission: 305
                }
              },
              component: () => import('@/pages/system/menus')
            }, {
              path: 'team',
              name: '团队管理',
              meta: {
                authority: {
                  permission: 302
                }
              },
              component: () => import('@/pages/system/team')
            }, {
              path: 'config',
              name: '配置管理',
              component: () => import('@/pages/system/config')
            }, {
              path: 'dict',
              name: '字典管理',
              component: () => import('@/pages/system/dict')
            }, {
              path: 'grade',
              name: '等级管理',
              component: () => import('@/pages/system/grade')
            }, {
              path: 'reduction',
              name: '还原管理',
              component: () => import('@/pages/system/reduction')
            }
          ]
        },
        {
          name: '绩效管理',
          path: 'achievements',
          meta: {
            icon: 'audit',
            authority: {
              permission: 4
            }
          },
          component: BlankView,
          children: [
            {
              name: '项目绩效',
              path: 'projectAch',
              component: () => import('@/pages/achievements/index')
            }, {
              name: '印度定制',
              path: 'IndiaAch',
              component: () => import('@/pages/achievements/india')
            }, {
              name: '注册绩效',
              path: 'SkuAch',
              component: () => import('@/pages/achievements/skuAch')
            }, {
              name: '错误中心',
              path: 'ErrorCenter',
              component: () => import('@/pages/achievements/errCenter')
            }, {
              name: '还原日志',
              path: 'Restore',
              component: () => import('@/pages/achievements/restore')
            }
          ]
        }, {
          name: '报表管理',
          path: 'report',
          meta: {
            icon: 'bar-chart',
            authority: {
              permission: 4
            }
          },
          component: BlankView,
          children: [
            {
              name: '项目基础数据',
              path: 'basicsData',
              component: () => import('@/pages/report/index')
            }, {
              name: '月度项目汇总',
              path: 'projectSum',
              component: () => import('@/pages/report/r-project')
            }, {
              name: '项目盘点记录',
              path: 'projectCheck',
              component: () => import('@/pages/report/c-project')
            },
            {
              name: '小时数据跟踪',
              path: 'hourData',
              component: () => import('@/pages/report/hourData')
            }
          ]
        }
      ]
    }
  ]
}

export default options
