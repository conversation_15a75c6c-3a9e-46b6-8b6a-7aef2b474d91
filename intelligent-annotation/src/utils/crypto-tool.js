import CryptoJs from 'crypto-js'

export default {
  // 加密
  encrypt(word, keyStr, ivStr) {
    keyStr = keyStr || '1234567812345678'
    ivStr = ivStr || '1234567812345678'
    const key = CryptoJs.enc.Utf8.parse(keyStr)
    const str = CryptoJs.enc.Utf8.parse(word)
    const iv = CryptoJs.enc.Utf8.parse(ivStr)
    const res = CryptoJs.AES.encrypt(str, key, {
      iv: iv,
      mode: CryptoJs.mode.CBC,
      padding: CryptoJs.pad.NoPadding
    })
    return res.toString(CryptoJs.format.Hex)
  },

  // 解密
  decrypt(word, keyStr, ivStr) {
    keyStr = keyStr || '1234567812345678'
    ivStr = ivStr || '1234567812345678'
    const key = CryptoJs.enc.Utf8.parse(keyStr)
    const iv = CryptoJs.enc.Utf8.parse(ivStr)
    const res = CryptoJs.AES.decrypt(word, key, {
      iv: iv,
      mode: CryptoJs.mode.CBC,
      padding: CryptoJs.pad.NoPadding
    })
    return res.toString(CryptoJs.enc.Utf8).replaceAll('\x00', '')
  }
}
