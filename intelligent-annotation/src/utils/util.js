import enquireJs from 'enquire.js'

export function isDef(v) {
  return v !== undefined && v !== null
}

/**
 * Remove an item from an array.
 */
export function remove(arr, item) {
  if (arr.length) {
    const index = arr.indexOf(item)
    if (index > -1) {
      return arr.splice(index, 1)
    }
  }
}

export function isRegExp(v) {
  return _toString.call(v) === '[object RegExp]'
}

export function enquireScreen(call) {
  const handler = {
    match: function() {
      call && call(true)
    },
    unmatch: function() {
      call && call(false)
    }
  }
  enquireJs.register('only screen and (max-width: 767.99px)', handler)
}

const _toString = Object.prototype.toString

// 返回list集合中某个字段的集合
export function returnListKeys(arr, key) {
  const keys = []
  arr.forEach(item => {
    keys.push(item[key])
  })
  return keys
}

// list 转 tree
export function arrayToTree(arr, parentId, idKey, parendIdKey) {
  if (!arr || arr.length === 0) return arr
  const key = idKey || 'id'
  const key2 = parendIdKey || 'parentId'
  const temp = []
  const treeArr = arr
  treeArr.forEach((item, index) => {
    if (item[key2] === parentId) {
      if (arrayToTree(treeArr, treeArr[index][key], key, key2).length > 0) {
        // 递归调用此函数
        treeArr[index].children = arrayToTree(treeArr, treeArr[index][key], key, key2)
      }
      temp.push(treeArr[index])
    }
  })
  return temp
}

export function dateUtils(dateTime) {
  let m = Math.floor(dateTime / 60)
  const h = Math.floor(m / 60)
  const s = dateTime - m * 60
  m -= h * 60
  const obj = {
    h: h,
    m: m,
    s: s
  }
  // if (dateTime === 0) {
  //   dateTimeData = h ? h + 'h' : ''
  // } else {
  //   dateTimeData = (h ? h + 'h' : '0h') + (m ? m + 'm' : '0m') + (s ? s + 's' : '0s')
  // }
  return obj
}

// 获取用户时区
export function getTimeZone() {
  return Intl.DateTimeFormat().resolvedOptions().timeZone || ''
}

// 获取本地语言, 目前可选 CN(简体)、US(英语)，也可扩展其它语言
export function getLanguage() {
  const hasLangs = ['CN', 'US']
  const chooseLanguage = localStorage.getItem('language')
  if (chooseLanguage && hasLangs.includes(chooseLanguage)) return chooseLanguage
  const language = (navigator.language || navigator.browserLanguage).toLowerCase().substr(0, 2)
  switch (language) {
    case 'zh':
      return 'CN'
    case 'en':
    default:
      return 'US'
  }
}

// 匹配字符串并返回标签包装后的html
export function matchStrToHtml(searchStr, text) {
  const reg1 = /<script[^>]*>(.|\n)*<\/script>/gi
  let result = text.replace(reg1, '')
  let num = -1
  const rStr = new RegExp(searchStr, 'gi') // 匹配传入的搜索值不区分大小写 i表示不区分大小写，g表示全局搜索
  const rHtml = new RegExp('\<.*?\>', 'ig') // 匹配html元素
  const aHtml = result.match(rHtml) // 存放html元素的数组
  let a = -1
  result = result.replace(rHtml, '{~}') // 替换html标签
  const arr = result.match(rStr)
  result = result.replace(rStr, () => {
    a++
    return `<span style="color:red;">${arr[a]}</span>`
  })
  result = result.replace(/{~}/g, () => { // 恢复html标签
    num++
    return aHtml[num]
  })
  return result
}
