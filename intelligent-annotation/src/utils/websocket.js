let ws = null
let messageCallback = null
let errorCallback = null
const wsUrl = process.env.VUE_APP_WS_URL
import { getLanguage, getTimeZone } from '@/utils/util'
// 初始化websocket
function initWebsocket(callback) {
  if (typeof (WebSocket) === 'undefined') {
    console.log('您的浏览器不支持WebSocket，无法获取数据')
    return false
  }
  ws = new WebSocket(wsUrl)
  ws.onmessage = function(e) {
    wsToMsg(e)
  }
  ws.onopen = function(e) {
    if (typeof (callback) === 'function') {
      callback()
    }
  }
  ws.onerror = function(e) {
    const res = {
      data: e,
      msg: '连接异常，请稍后重试'
    }
    errorCallback(res)
  }
  ws.onclose = function(e) {
    websocketclose()
  }
}
// 接受后台反馈消息
function wsToMsg(e) {
  const token = sessionStorage.getItem('Authorization')
  messageCallback(JSON.parse(e.data), token)
}
// 发送消息
function websocketSend(obj) {
  // 添加状态判断，当为OPEN时，发送消息
  if (ws.readyState === ws.OPEN) { // websock.OPEN = 1
    // 发给后端的数据需要字符串化
    const param = {
      ...obj,
      access_token: sessionStorage.getItem('Authorization')
    }
    ws.send(JSON.stringify(param))
  }
  if (ws.readyState === ws.CLOSED) { // websock.CLOSED = 3
    console.log('ws连接异常，请稍候重试')
    const res = {
      msg: 'ws连接异常，请稍候重试'
    }
    errorCallback(res)
  }
}

// 关闭ws连接
function websocketclose(e) {
  // e.code === 1000  表示正常关闭。 无论为何目的而创建, 该链接都已成功完成任务。
  // e.code !== 1000  表示非正常关闭。
  if (e && e.code !== 1000) {
    const res = {
      msg: 'ws连接异常，请稍候重试'
    }
    errorCallback(res)
    // // 如果需要设置异常重连则可替换为下面的代码，自行进行测试
    // if (tryTime < 10) {
    //   setTimeout(function() {
    //    websock = null
    //    tryTime++
    //    initWebSocket()
    //    console.log(`第${tryTime}次重连`)
    //  }, 3 * 1000)
    // } else {
    //  Message.error('重连失败！请稍后重试')
    // }
  }
}

/**
 * 发起websocket请求函数
 * @param {string} url ws连接地址
 * @param {Object} agentData 传给后台的参数
 * @param {function} successCallback 接收到ws数据，对数据进行处理的回调函数
 * @param {function} errCallback ws连接错误的回调函数
 */
export function sendWebsocket(agentData, successCallback, errCallback) {
  const param = {
    ...agentData,
    time_zone: getTimeZone(),
    language: getLanguage()
  }
  messageCallback = successCallback
  errorCallback = errCallback
  initWebsocket(() => {
    websocketSend(param)
  })
}

/**
 * 关闭websocket函数
 */
export function closeWebsocket() {
  if (ws) {
    ws.close() // 关闭websocket
    ws.onclose() // 关闭websocket
  }
}
