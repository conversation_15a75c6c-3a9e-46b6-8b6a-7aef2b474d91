import axios from 'axios'
// import <PERSON><PERSON> from 'js-cookie'
const CancelToken = axios.CancelToken
const pendingReqs = {}
// 不需要重复拦截的接口集合
const canRepeatReqs = ['tmp_sku/get_sku_parameter_data/', 'grade/grade_list/']
// 跨域认证信息 header 名
const xsrfHeaderName = 'Authorization'

axios.defaults.timeout = 1000 * 60 * 10
// axios.defaults.withCredentials= true
axios.defaults.xsrfHeaderName = xsrfHeaderName
axios.defaults.xsrfCookieName = xsrfHeaderName

// 认证类型
const AUTH_TYPE = {
  BEARER: 'Bearer',
  BASIC: 'basic',
  AUTH1: 'auth1',
  AUTH2: 'auth2'
}

// http method
const METHOD = {
  GET: 'get',
  POST: 'post'
}

/**
 * axios请求
 * @param url 请求地址
 * @param method {METHOD} http method
 * @param params 请求参数
 * @param header
 * @returns {Promise<AxiosResponse<T>>}
 */
async function request(url, method, params, header) {
  const h = header || {}
  switch (method) {
    case METHOD.POST:
      return new Promise((resolve, reject) => {
        axios({
          method: 'POST',
          url: url,
          data: params,
          ...h
        }).then(res => {
          resolve(res)
        }, err => { reject(err) })
      })
    case METHOD.GET:
    default:
      return new Promise((resolve, reject) => {
        axios({
          method: 'GET',
          url: url,
          params: params,
          ...h
        }).then(res => {
          resolve(res)
        }, err => { reject(err) })
      })
  }
}
/**
 * 设置认证信息
 * @param auth {Object}
 * @param authType {AUTH_TYPE} 认证类型，默认：{AUTH_TYPE.BEARER}
 */
function setAuthorization(auth, authType = AUTH_TYPE.BEARER) {
  switch (authType) {
    case AUTH_TYPE.BEARER:
      sessionStorage.setItem(xsrfHeaderName, auth.token)
      // Cookie.set(xsrfHeaderName, auth.token)
      break
    case AUTH_TYPE.BASIC:
    case AUTH_TYPE.AUTH1:
    case AUTH_TYPE.AUTH2:
    default:
      break
  }
}

/**
 * 移出认证信息
 * @param authType {AUTH_TYPE} 认证类型
 */
function removeAuthorization(authType = AUTH_TYPE.BEARER) {
  switch (authType) {
    case AUTH_TYPE.BEARER:
      sessionStorage.removeItem(xsrfHeaderName)
      // Cookie.remove(xsrfHeaderName)
      break
    case AUTH_TYPE.BASIC:
    case AUTH_TYPE.AUTH1:
    case AUTH_TYPE.AUTH2:
    default:
      break
  }
}

/**
 * 检查认证信息
 * @param authType
 * @returns {boolean}
 */
function checkAuthorization(authType = AUTH_TYPE.BEARER) {
  switch (authType) {
    case AUTH_TYPE.BEARER:
      // if (Cookie.get(xsrfHeaderName)) {
      if (sessionStorage.getItem(xsrfHeaderName)) {
        return true
      }
      break
    case AUTH_TYPE.BASIC:
    case AUTH_TYPE.AUTH1:
    case AUTH_TYPE.AUTH2:
    default:
      break
  }
  return false
}
// 检测同一接口请求
function removePending(key, isRequest = false) {
  // 同一接口重复请求
  if (pendingReqs[key] && isRequest) {
    pendingReqs[key](`Interrupt_request`)
  }
  delete pendingReqs[key]
}
function isPengingApi(url) {
  const api = url.split('/').slice(-3).join('/')
  return !canRepeatReqs.includes(api)
}
/**
 * 加载 axios 拦截器
 * @param interceptors
 * @param options
 */
function loadInterceptors(interceptors, options) {
  const { request, response } = interceptors
  // 加载请求拦截器
  request.forEach(item => {
    let { onFulfilled, onRejected } = item
    if (!onFulfilled || typeof onFulfilled !== 'function') {
      onFulfilled = config => config
    }
    if (!onRejected || typeof onRejected !== 'function') {
      onRejected = error => Promise.reject(error)
    }
    axios.interceptors.request.use(
      config => {
        if (isPengingApi(config.url)) {
          const key = config.url + '&' + config.method
          removePending(key, true)
          config.cancelToken = new CancelToken(c => {
            pendingReqs[key] = c
          })
        }
        return onFulfilled(config, options)
      },
      error => onRejected(error, options)
    )
  })
  // 加载响应拦截器
  response.forEach(item => {
    let { onFulfilled, onRejected } = item
    if (!onFulfilled || typeof onFulfilled !== 'function') {
      onFulfilled = response => response
    }
    if (!onRejected || typeof onRejected !== 'function') {
      onRejected = error => Promise.reject(error)
    }
    axios.interceptors.response.use(
      response => {
        const key = response.url + '&' + response.method
        removePending(key)
        return onFulfilled(response, options)
      },
      error => onRejected(error, options)
    )
  })
}

/**
 * 解析 url 中的参数
 * @param url
 * @returns {Object}
 */
function parseUrlParams(url) {
  const params = {}
  if (!url || url === '' || typeof url !== 'string') {
    return params
  }
  const paramsStr = url.split('?')[1]
  if (!paramsStr) {
    return params
  }
  const paramsArr = paramsStr.replace(/&|=/g, ' ').split(' ')
  for (let i = 0; i < paramsArr.length / 2; i++) {
    const value = paramsArr[i * 2 + 1]
    params[paramsArr[i * 2]] = value === 'true' ? true : (value === 'false' ? false : value)
  }
  return params
}

export {
  METHOD,
  AUTH_TYPE,
  request,
  setAuthorization,
  removeAuthorization,
  checkAuthorization,
  loadInterceptors,
  parseUrlParams
}
