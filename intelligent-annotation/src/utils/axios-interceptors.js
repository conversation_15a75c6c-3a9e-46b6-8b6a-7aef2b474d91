// import Cookie from 'js-cookie'
import { logout } from '@/services/user'
import { getTimeZone, getLanguage } from './util'
const respCommon = {
  /**
   * 响应数据之前做点什么
   * @param response 响应对象
   * @param options 应用配置 包含: {router, i18n, store, message}
   * @returns {*}
   */
  onFulfilled(response, options) {
    const { message } = options
    if (response.status === 200 && response.headers['content-type'] !== 'application/octet-stream;charset=utf-8') {
      if (response.data.errcode === 777) {
        message.warning('登录过期，请重新登录!')
        logout()
        options.router.push('/login')
        return Promise.reject()
      }
      return response.data
    }
    if (response.status === 401) {
      message.error('无此接口权限')
    }
    if (response.status === 403) {
      message.error(`请求被拒绝`)
    }
    if (response.status === 500) {
      message.error(`服务500, 请联系管理员`)
    }
    return response
  },
  /**
   * 响应出错时执行
   * @param error 错误对象
   * @param options 应用配置 包含: {router, i18n, store, message}
   * @returns {Promise<never>}
   */
  onRejected(error, options) {
    const { message } = options
    // 重复请求中断
    if (error.message !== 'Interrupt_request') {
      message.error(error.message)
    }
    return Promise.reject(error)
  }
}

const reqCommon = {
  /**
   * 发送请求之前做些什么
   * @param config axios config
   * @param options 应用配置 包含: {router, i18n, store, message}
   * @returns {*}
   */
  onFulfilled(config, options) {
    const { message } = options
    const { url, xsrfCookieName } = config
    const _t = sessionStorage.getItem(xsrfCookieName)
    const time_zone = getTimeZone()
    const language = getLanguage()
    if (noTokenCheck(url) && xsrfCookieName && !_t) {
      message.warning('登录过期，请重新登录')
      options.router.push('/login')
    }
    if (_t) { // 如果有token缓存，自动添加到接口请求参数里
      // config.headers[xsrfCookieName] = Cookie.get(xsrfCookieName)
      if (config.method === 'get') {
        config.params = {
          ...config.params,
          access_token: _t,
          time_zone,
          language
        }
      } else {
        if (config.data && config.data instanceof FormData) {
          config.data.append('access_token', _t)
          config.data.append('time_zone', time_zone)
          config.data.append('language', language)
        } else {
          config.data = {
            ...config.data,
            access_token: _t,
            time_zone,
            language
          }
        }
      }
    }
    return config
  },
  /**
   * 请求出错时做点什么
   * @param error 错误对象
   * @param options 应用配置 包含: {router, i18n, store, message}
   * @returns {Promise<never>}
   */
  onRejected(error, options) {
    const { message } = options
    message.error(error.message)
    return Promise.reject(error)
  }
}
/**
  * 匹配不需要登陆验证的接口路由
  * @param url
*/
function noTokenCheck(url) {
  const name = url.split('/').slice(3).join('/')
  const urls = ['user/login/', 'user/register/', 'country/list/', 'user/get_phone_vc/', 'user/reset_password/']
  return !urls.includes(name)
}
export default {
  request: [reqCommon], // 请求拦截
  response: [respCommon] // 响应拦截
}
