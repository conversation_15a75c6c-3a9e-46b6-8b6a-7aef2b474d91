import { encode, decode } from 'js-base64'
/**
 * 设置工作时间
 * @minute
 */
export function setHours(id, minute, name) {
  const _name = name || 'w_hours'
  const w_hours = {
    workId: id,
    value: minute
  }
  localStorage.setItem(_name, encode(JSON.stringify(w_hours)))
}

/**
 * 获取工作时间
 * @minute
 */
export function getHours(name) {
  const data = localStorage.getItem(name || 'w_hours')
  if (data) {
    return JSON.parse(decode(data))
  }
  return null
}

/**
 * 清除工作时间
 * @minute
 */
export function removeHours(name) {
  localStorage.removeItem(name || 'w_hours')
}
