<template>
  <a-layout-header :class="[headerTheme, 'admin-header']">
    <div :class="['admin-header-wide', layout, pageWidth]">
      <router-link v-if="isMobile || layout === 'head'" to="/" :class="['logo', isMobile ? null : 'pc', headerTheme]">
        <img height="32" src="@/assets/img/white-logo.png">
        <!-- <h1 v-if="!isMobile">{{systemName}}</h1> -->
      </router-link>
      <a-divider v-if="isMobile" type="vertical" />
      <!-- <a-icon v-if="layout !== 'head'" class="trigger" :type="collapsed ? 'menu-unfold' : 'menu-fold'" @click="toggleCollapse" /> -->
      <div v-if="layout !== 'side' && !isMobile" class="admin-header-menu" :style="`width: ${menuWidth};`">
        <i-menu class="head-menu" :theme="headerTheme" mode="horizontal" :options="menuData" @select="onSelect" />
      </div>
      <div :class="['admin-header-right', headerTheme]">
        <!-- <header-search class="header-item" @active="val => searchActive = val" /> -->
        <!-- <a-tooltip class="header-item" :title="help" placement="bottom" >
            <a href="https://iczer.gitee.io/vue-antd-admin-docs/" target="_blank">
              <a-icon type="question-circle-o" />
            </a>
          </a-tooltip> -->
        <!-- <header-notice class="header-item"/> -->
        <header-avatar class="header-item" @resetPwd="showRestPwdModal" @onLog="handleLog" />
        <lang-select />
      </div>
    </div>
    <a-modal v-model="visible" :title="$t('contents.resetPwd')" @ok="handleOk" @cancel="hadleCancel">
      <a-form :form="form" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item :label="$t('contents.oldPwd')">
          <a-input
            v-decorator="['old_password', { rules: [{ required: true, message: $t('rules.oldPwdReq') }] }]"
            autocomplete="off"
            type="password"
          />
        </a-form-item>
        <a-form-item :label="$t('contents.newPwd')">
          <a-input
            v-decorator="['new_password', { rules: [{ required: true, message: $t('rules.newPwdReq') }, { validator: compareToOldPassword }] }]"
            autocomplete="off"
            type="password"
          />
        </a-form-item>
        <a-form-item :label="$t('contents.surePwd')">
          <a-input
            v-decorator="['sure_password', { rules: [{ required: true, message: $t('rules.surePwdReq') }, { validator: compareToFirstPassword }] }]"
            autocomplete="off"
            type="password"
          />
        </a-form-item>
      </a-form>
    </a-modal>
    <Log ref="logCmp" />
  </a-layout-header>
</template>

<script>
// import HeaderSearch from './HeaderSearch'
// import HeaderNotice from './HeaderNotice'
import Log from '@/components/log'
import HeaderAvatar from './HeaderAvatar'
import IMenu from '@/components/menu/menu'
import LangSelect from '@/components/lang'
import { mapState } from 'vuex'
import { changePassword, logout } from '@/services/user'
const sha1 = require('js-sha1') // sha1加密
export default {
  name: 'AdminHeader',
  components: { IMenu, HeaderAvatar, LangSelect, Log },
  props: {
    collapsed: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    menuData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      searchActive: false,
      visible: false,
      form: this.$form.createForm(this),
      token: ''
    }
  },
  computed: {
    ...mapState('setting', ['theme', 'isMobile', 'layout', 'systemName', 'pageWidth']),
    headerTheme() {
      if (this.layout === 'side' && this.theme.mode === 'dark' && !this.isMobile) {
        return 'light'
      }
      return this.theme.mode
    },
    help() {
      return this.$t('help')
    },
    menuWidth() {
      const { layout, searchActive } = this
      const headWidth = layout === 'head' ? '100% - 188px' : '100%'
      const extraWidth = searchActive ? '600px' : '400px'
      return `calc(${headWidth} - ${extraWidth})`
    }
  },
  methods: {
    handleLog() {
      this.$refs.logCmp.toggleShow()
    },
    toggleCollapse() {
      this.$emit('toggleCollapse')
    },
    onSelect(obj) {
      this.$emit('menuSelect', obj)
    },
    // 判断新老密码不一样
    compareToOldPassword(rule, value, callback) {
      const form = this.form
      if (value && value === form.getFieldValue('old_password')) {
        callback('原密码和新密码一样!')
      } else {
        callback()
      }
    },
    // 在此确认密码校验
    compareToFirstPassword(rule, value, callback) {
      const form = this.form
      if (value && value !== form.getFieldValue('new_password')) {
        callback('两次输入密码不一致!')
      } else {
        callback()
      }
    },
    // 展示修改密码框
    showRestPwdModal(t) {
      this.visible = true
      this.token = t
    },
    // 关闭修改密码框
    hadleCancel() {
      this.form.resetFields()
    },
    // 修改密码
    handleOk() {
      this.form.validateFields((err, values) => {
        if (!err) {
          const param = {
            access_token: this.token,
            old_password: sha1(values.old_password),
            new_password: sha1(values.new_password)
          }
          changePassword(param).then(res => {
            if (res.errcode === 0) {
              this.visible = true
              this.$message.success(res.errmsg)
              setTimeout(() => {
                logout()
                this.$router.push('/login')
              }, 1000)
            } else {
              this.$message.error(res.errmsg)
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
@import "index";
</style>
