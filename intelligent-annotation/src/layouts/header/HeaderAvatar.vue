<template>
  <div class="header-avatar">
    <div class="level_img">
      <level-icon :grade="user.grade" />
    </div>
    <a-dropdown :placement="'bottomRight'">
      <div class=" ant-dropdown-link" style="cursor: pointer">
        <!-- <a-avatar class="avatar" size="small" shape="circle" :src="user.avatar"/> -->
        <span class="name">{{ user.name }}</span>
      </div>
      <a-menu slot="overlay">
        <a-menu-item @click="myLevel">
          <a-icon type="crown" />
          <span>{{ $t('contents.myLevel') }}</span>
        </a-menu-item>
        <a-menu-divider />
        <a-menu-item @click="resetPwdFun">
          <a-icon type="setting" />
          <span>{{ $t('contents.resetPwd') }}</span>
        </a-menu-item>
        <a-menu-divider />
        <!-- <a-menu-item @click="goLog">
        <a-icon type="bug" />
        <span>{{ $t('log') }}</span>
      </a-menu-item>
      <a-menu-divider /> -->
        <a-menu-item @click="logout">
          <a-icon type="poweroff" />
          <span>{{ $t('logout') }}</span>
        </a-menu-item>
      </a-menu>
    </a-dropdown>
    <my-level-modal ref="myLevelModal" :visible="visible" :grade="user.grade" :level-list="levelList" :history-level="historyLevel" :lang="lang" @onHideModal="closeModal" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { logoutApi, logout } from '@/services/user'
import { getGrades, getHisGrades } from '@/services/sys'
import MyLevelModal from '@/components/myLevelModal'
import LevelIcon from '@/components/levelIcon'
export default {
  name: 'HeaderAvatar',
  components: { MyLevelModal, LevelIcon },
  data() {
    return {
      visible: false,
      levelList: [],
      historyLevel: []
    }
  },
  computed: {
    ...mapGetters('account', ['user']),
    ...mapGetters('setting', ['lang'])
  },
  mounted() {
    this.getList()
    this.getHisList()
  },
  methods: {
    getList() {
      getGrades({}).then(res => {
        if (res.errcode === 0) {
          this.levelList = [{
            grade: '',
            grade_id: null,
            name: '暂无等级',
            name_en: 'No grade'
          }, ...res.data]
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    getHisList() {
      getHisGrades({}).then(res => {
        if (res.errcode === 0) {
          this.historyLevel = res.data
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    logout() {
      logoutApi().then(res => {
        if (res.errcode === 0) {
          logout()
          this.$router.push('/login')
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    goLog() {
      this.$emit('onLog')
    },
    resetPwdFun() {
      this.$emit('resetPwd', this.user.access_token)
    },
    // 我的等级
    myLevel() {
      this.visible = true
    },
    closeModal() {
      this.visible = false
    }
  }
}
</script>
<style lang="less">
  .header-avatar{
    display: flex;
    align-items: center;
    .level_img {
      margin-right: 15px;
    }
    .avatar, .name{
      align-self: center;
    }
    .avatar{
      margin-right: 8px;
    }
    .name{
      font-weight: 500;
    }
  }
</style>
