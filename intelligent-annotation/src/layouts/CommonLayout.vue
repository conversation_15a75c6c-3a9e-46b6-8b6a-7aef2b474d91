<template>
  <div class="common-layout">
    <div class="my_top_title">
      <div class="left_text">{{ $t('logo.text2') }}</div>
      <div class="changeLang">
        <lang-select />
      </div>
    </div>
    <div class="content beauty-scroll"><slot /></div>
    <!-- <page-footer :link-list="footerLinks" :copyright="copyright" /> -->
  </div>
</template>

<script>
import langSelect from '@/components/lang'
// import PageFooter from '@/layouts/footer/PageFooter'
import { mapState } from 'vuex'

export default {
  name: 'CommonLayout',
  components: { langSelect },
  computed: {
    ...mapState('setting', ['footerLinks', 'copyright']),
    systemName() {
      return this.$store.state.setting.systemName
    }
  }
}
</script>

<style scoped lang="less">
.common-layout{
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: auto;
  background-color: @layout-body-background;
  background-image: url('../assets/img/loginBg.jpeg');
  background-repeat: no-repeat;
  background-size: 100%;
  .my_top_title {
    position: absolute;
    top: 0; left: 0;
    width: 100%;
    display: flex;
    padding: 20px 25px 0;
    justify-content: space-between;
    z-index: 5;
    .left_text {
      color: #333333;
      font-size: 18px;
      font-weight: bold;
    }
    .changeLang {}
  }
  .content{
    padding: 60px 0;
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: auto;
    // @media (min-width: 768px){

    //   padding: 100px 0 24px;
    // }
  }
}
</style>
