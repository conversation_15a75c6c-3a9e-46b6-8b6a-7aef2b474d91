<template>
  <div class="footer">
    <div class="links">
      <a v-for="(item, index) in linkList" :key="index" target="_blank" :href="item.link ? item.link : 'javascript: void(0)'">
        <a-icon v-if="item.icon" :type="item.icon" />{{ item.name }}
      </a>
    </div>
    <div class="copyright">
      Copyright <a-icon type="copyright" /> {{ copyright }} {{ $t('logo.text') }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'PageFooter',
  props: {
    copyright: {
      type: String,
      default: ''
    },
    linkList: {
      type: Array,
      default: () => {
        return []
      }
    }
  }
}
</script>

<style lang="less" scoped>
  .footer{
    padding: 48px 16px 24px;
    /*margin: 48px 0 24px;*/
    text-align: center;
    .copyright{
      color: @text-color-second;
      font-size: 14px;
    }
    .links{
      margin-bottom: 8px;
      a:not(:last-child) {
        margin-right: 40px;
      }
      a{
        color: @text-color-second;
        -webkit-transition: all .3s;
        transition: all .3s;
      }
    }
  }
</style>
