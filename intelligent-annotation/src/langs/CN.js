module.exports = {
  home: { name: '首页' },
  logo: {
    text: '智标智能',
    text2: '智标数据管理中心'
  },
  content: '演示页面',
  description: '这是一个演示页面',
  help: '帮助文档',
  personalCenter: '个人中心',
  setting: '设置',
  logout: '退出登录',
  log: '更新日志',
  contents: {
    register: '注册账户',
    forgetPwd: '忘记密码',
    getCaptcha: '获取验证码',
    loginStyle: '账户密码登录',
    loginStyle2: '手机号登录',
    resetPwd: '修改密码',
    newPwd: '新密码',
    oldPwd: '原密码',
    surePwd: '确认密码',
    goLogin: '去登陆',
    catTotal: '总分类数',
    curCutNum: '当前页切图数',
    ySelect: '您选中了',
    tClean: '待清洗',
    clean: '清洗中',
    tCheck: '待检查',
    checked: '检查中',
    tSum: '待汇总',
    suming: '汇总中',
    eSum: '已汇总',
    pausing: '暂停中',
    end: '已经到底了',
    load: '正在加载中',
    picNum: '图片数量',
    key: '快捷键',
    help: '上传原图路径、切图坐标和分类名等数据',
    loading: '加载中...',
    fileUpLoading: '文件上传中...',
    serchTip: '输入内容后，点击Enter键搜索',
    tips: '点击右下角开始获取任务',
    dayWork: '今日工作记录',
    order: '序号',
    exportFolder: '导出文件夹',
    myLevel: '我的等级',
    noLevel: '暂无等级',
    not: '无',
    mLevel: '本月等级',
    hLevel: '历史等级',
    grade1: '上个月战绩：无',
    grade2: '上个月战绩：日均清洗{a}张图片，错误率{b}%，月处理{c}张图片',
    grade3: '日产量：{a}，错误率：{b}，月处理量：{c}，三者必须同时满足，不满足时按照单项对应的最低级别进行级别划分',
    grade: '评级说明',
    explain: '1、评级频次: 每月一次，每月1号根据上月数据更新等级（根据实际需求，可做动态调整）;<br/>2、当月等级，由上月数据决定;<br/>3、根据月度累计图片处理量，进行称谓及等级的评定;<br/>4、取日处理量、错误率两个维度中的较低值，确定对应的等级;',
    taskType: {
      clean: '清洗',
      check: '检查',
      check2: '检查2',
      spot_check: '抽检',
      audit: '审核',
      summary: '汇总',
      register: '注册'
    },
    multiPackaging: 'SKU多包装',
    completedTasksToday: '今日数据',
    completedTasksTodayTip: '今日完成数据：已完成文件夹数/已完成切图数量'
  },
  buttons: {
    login: '登录',
    register: '注册',
    submit: '提交',
    search: '查询',
    delete: '删除',
    edit: '编辑',
    define: '确定',
    cancel: '取消',
    close: '关闭',
    add: '新增',
    addTo: '添加',
    auth: '授权',
    addteam: '新增团队',
    editTeam: '编辑团队',
    addGroup: '新增班',
    editGroup: '编辑班',
    release: '发布',
    enable: '启用',
    prohibit: '禁止',
    creatP: '新建项目',
    task: '配置任务',
    exportPro: '导出项目数据',
    endProject: '完成项目',
    more: '更多',
    exportLog: '导出日志',
    return: '返回',
    start: '开始',
    giveUp: '放弃',
    save: '保存',
    upJson: '请选择json文件上传',
    reduction: '还原',
    download: '下载',
    details: '详情',
    unOffline: '下线',
    temp: '使用模板',
    pass: '通过',
    pause: '暂停',
    cancelPause: '取消暂停',
    accessControl: '访问控制',
    audit: '审批',
    task_rule: '任务规则',
    detail: '详情',
    reordering: '重排序'

  },
  drawer: {
    user: '用户编辑',
    role: '角色编辑',
    roleAuth: '角色权限',
    menu: '菜单编辑',
    add: '新增{name}',
    edit: '编辑{name}',
    dict: '字典',
    addPackage: '新增包装',
    auditPackage: '审核包装'
  },
  modal: {
    title: '弹框',
    addPro: '新增项目',
    bigPic: '查看大图'
  },
  confirm: {
    del: '确定要删除吗?',
    title: '提示',
    release: '确定发布此项目，发布后任务流程不可更改！',
    endProject: '确定完成此项目,将清洗结果回传？',
    noCheckData: '暂无check数据',
    cnt: '你有清洗任务还未提交，确定要离开此页面吗?',
    cnt2: '请先选择要操作的文件夹！',
    cnt3: '确定开始清洗操作?',
    cnt4: '确定放弃此次清洗操作?',
    cnt5: '确定提交此次清洗操作?',
    cnt6: '您今天的剩余放弃次数还有',
    cnt7: '次',
    cnt8: '您今天的放弃次数已用完',
    cnt81: '确定提交此次注册操作?',
    cnt9: '确定提交此次审核操作?',
    cnt11: '确定提交此次检查操作?',
    cnt12: '确定提交此次汇总操作?',
    workHours: '截至目前为止，您今天的有效工作时长为',
    start: '请先点击开始按钮',
    folder: '请先选择文件',
    sku: '此SKU已创建',
    projectId: '请输入项目ID!',
    bigData: '数据量比较多，请耐心等待！',
    unOffline: '确定强制下线已选中的人员？',
    noPeople: '请先选择要操作的人员！',
    sukSure: '确定提交此次数据操作?',
    useTemp: '模板应用后，之前模板的流程信息会被清除，请确认应用模板？',
    noWork: '您还没有开始任务!',
    editSure: '确定修改',
    packing: '的包装图片',
    noSum: '队列项目不支持汇总操作',
    reduction: '确认对操作人进行还原操作？',
    giveUpReasonTip: '请选择或输入放弃原因',
    maxAttemptsExceeded: '已达最大可添加次数'
  },
  label: {
    mobile: '手机号',
    code: '随机码',
    mobileCode: '验证码',
    password: '密码',
    fullname: '姓名',
    name: '名称',
    time: '时间',
    month: '月份',
    level: '等级',
    account: '账号名',
    role: '角色',
    team: '所属团队',
    group: '所属班',
    sTime: '选择时间',
    projectName: '项目名称',
    auth: '权限',
    parentMenu: '上级菜单',
    apiName: '接口名称',
    proName: '项目名称',
    teamOrId: '项目名称或ID',
    proType: '项目大类',
    taskType: '任务类型',
    proId: '项目ID',
    teamVal: '团队',
    main: '配置人员',
    ass: '分配人员',
    taskFlow: '任务流程',
    upJson: '上传项目数据',
    parentDict: '上级字典',
    isErrType: '是否全部进行错误分类',
    isFixSkuProject: 'SKU项目是否固定',
    id: '编号ID',
    sku_name: 'SKU名称',
    audit_result: '审批结果',
    skuId: 'SKU ID'
  },
  rules: {
    mobile: '请输入手机号',
    account: '请输入账户名',
    password: '请输入密码',
    code: '请输入随机码',
    mobileCode: '请输入验证码',
    name: '请输入名称',
    fullname: '请输入姓名',
    checkTeam: '请选择团队',
    role: '请选择角色',
    level: '请选择等级',
    proName: '请输入项目名称',
    newPwdReq: '新密码必填',
    oldPwdReq: '老密码必填',
    surePwdReq: '确认密码必填',
    surePwdErr: '两次输入密码不一致',
    minPwd: '密码最小长度为6',
    intNum: '只能输入1~20的正整数',
    noSave: '有修改的数据未保存',
    skuProject: '请选择SKU行业',
    skuPic: '包装图片不能为空',
    notNull: '{name}不能为空',
    maxLength: '支持最大输入长度{number}',
    skuReg: '只支持中文,英文,数字,空格,下划线,+,%和开头结尾不能有空格',
    required: '此项不能为空',
    has2Space: '内容不能有连续空格',
    noChinese: '英文SKU名称不能包含中文',
    numG1: '起始值或结束值不能为空',
    numG2: '结束值不能小于起始值'
  },
  placeholder: {
    mobile: '手机号',
    password: '密码'
  },
  table: {
    id: '序号',
    mobile: '手机号',
    name: '姓名',
    role: '角色',
    menuName: '菜单名称',
    roleName: '角色名',
    team: '所属团队',
    group: '所属班',
    teamName: '团队名',
    groupName: '班名',
    isEnable: '是否启用',
    creatTime: '创建时间',
    updateTime: '更新时间',
    action: '操作',
    task: '任务',
    hours: '有效工作时长(h)',
    hours2: '实际工作时长(h)',
    restTime: '休息时长(h)',
    picNum: '处理图片数',
    speed: '速度(个/小时)',
    coverPicNum: '被处理图片数',
    errNum: '错误数',
    errRate: '错误率',
    total: '共',
    strip: '条',
    project: '项目名称',
    proTotal: '项目切图总数',
    state: '状态',
    isClean: '是否清空提交',
    isFun: '是否运行',
    reTime: '发布时间',
    endTime: '结束时间',
    creater: '创建人',
    completed: '已完成',
    incomplete: '未完成',
    expTotal: '已导出总数',
    agTotal: '汇总总数',
    people: '人数',
    taskType: '任务类型',
    taskId: '任务ID',
    toolType: '工具类型',
    rTeam: '负责团队',
    taskName: '任务名称',
    sort: '排序',
    dirInvisible: '是否显示所有文件夹',
    skuProject: 'SKU项目',
    skuIndustry: 'SKU行业',
    invalidData: '无效数据',
    invalidPro: '无效占比',
    validData: '有效数据',
    validPro: '有效占比',
    projectCat: '项目大类',
    projectId: '项目ID',
    time: '时间',
    planDate: '计划日期',
    overdue: '是否逾期',
    cleanErrorRate: '清洗错误率',
    cleanErrorNum: '清洗错误数',
    checkErrorRate: '检查错误率',
    checkErrorNum: '检查错误数',
    check2ErrorNum: '检查2错误数',
    check2ErrorRate: '检查2错误率',
    spotCheckErrorNum: '抽检错误数',
    cleanTime: '清洗耗时',
    cleanSpeed: '清洗时速',
    checkTime: '检查耗时',
    checkSpeed: '检查时速',
    check2time: '检查2耗时',
    check2Speed: '检查2时速',
    spotTime: '抽检耗时',
    sumTime: '汇总耗时',
    sumSpeed: '汇总时速',
    cleanReduce: '清洗减错误数',
    checkReduce: '检查减错误数',
    difCoe: '难度系数',
    conAmo: '换算后的量',
    headI: '头部标识',
    midI: '中间标识',
    projectC: '项目内容',
    projectT: '项目分类',
    projectG: '项目组',
    fullC: '全品类',
    cleanSurplus: '清洗剩余量',
    checkSurplus: '检查剩余量',
    sumSurplus: '抽检剩余量',
    rdDate: '要求交付日期',
    adDate: '交付日期',
    dec: '备注',
    pGroup: '人员所属班',
    iGroup: '项目所属班',
    projectType: '项目类型',
    projectTaskRule: '任务规则',
    flowerTem: '流程模板',
    sku_r: '申请数',
    sku_b: '被审核数',
    sku_err: '错误数',
    sku_b_del: '被删除数',
    sku_l: '漏提数',
    sku_s: '审核数',
    sku_x: '修改数',
    sku_del: '删除数',
    totalN: '总计',
    sum: '汇总',
    pic: '图片',
    pic_link: '图片链接',
    err_sku: '错误SKU',
    oper_time: '操作时间',
    oper_people: '操作人',
    modify_sku: '修正后SKU',
    modify_people: '修改人',
    modify_time: '修改时间',
    flow_name: '流程名称',
    cn_name: '中文名称',
    en_name: '英文名称',
    err_type: '错误类型',
    grade_name: '等级名称',
    iden: '标识',
    day_action: '日均产量(张)',
    month_all: '月处理量(张)',
    rc: '达成条件',
    icon: '图标',
    date: '日期',
    dirName: '文件夹名称',
    dir_id: '文件夹编号',
    pauseTime: '暂停时间',
    oPic: '原图',
    sku_name: 'SKU名称',
    audit_user: '审核人',
    audit_time: '审核时间',
    audit_state: '审核结果',
    restore_reason: '还原原因',
    restore_user: '还原操作人',
    restore_time: '还原时间',
    start_time: '开始时间',
    stop_time: '暂停时间',
    stop_number: '暂停时数量',
    dir_number: '文件夹切图数'
  },
  dic: {
    yes: '是',
    no: '否',
    done: '已完成',
    rAccept: '待验收',
    ongoing: '进行中',
    rStart: '待开始',
    unreleased: '未发布',
    function: '运行',
    suspend: '暂停',
    clean: '清洗任务',
    check: '检查任务',
    sum: '汇总任务',
    delivered: '已交付',
    undelivered: '未交付',
    lAsc: '字母升序',
    qDesc: '数量降序',
    register_t: '注册任务',
    audit: '审核任务',
    cn: '中文',
    en: '英文',
    registered: '注册中',
    register: '待注册',
    reviewed: '审核中',
    review: '待审核',
    confidence: '置信度',
    distance: '距离',
    applicant: '申请人',
    audit_pass: '审批通过',
    audit_unpassed: '驳回',
    audit_to_audit: '待审核',
    sku_name: 'SKU名称',
    previous: '上一个',
    next: '下一个'
  },
  india: {
    hourly: '统计',
    today: '个人平均清洗量',
    hour: '平均清洗量',
    man: '平均工时',
    min: '平均清洗量',
    interval: '间隔',
    type: '任务类型',
    received: '图片总数',
    total: '待处理总数',
    completed: '已完成数',
    cHour: '本小时完成数',
    mHour: '工时(HC)',
    cHours: '清洗数/小时',
    cMin: '清洗数/分钟',
    username: '员工姓名',
    pname: '项目名称',
    tasktype: '任务类型',
    startTime: '开始时间',
    endTime: '结束时间',
    spentTime: '花费时间',
    ptotal: '图像总数',
    remaining: '剩余图像数',
    wtotalimg: '文件夹数',
    wtotalf: '已完成数',
    whours: '实际工作时长',
    whours2: '有效工作时长',
    whours3: '实际花费时间',
    whours4: '有效花费时间',
    restTime: '休息时间',
    pstas: '个人统计',
    time: '时间',
    getMyErr: '查询我的错误',
    allow: '允许',
    refuse: '拒绝'
  },
  skuForm: {
    name: '产品名称',
    type: '类型',
    sLanguage: '选择语言',
    brand: '品牌',
    dealer: '品牌商',
    brandChild: '系列',
    flavor: '口味',
    category: '品类',
    packing: '包装',
    specs: '规格',
    specsUnit: '规格单位',
    salesUnit: '销售单位',
    outerPacking: '外包装',
    aFiles: '额外字段',
    sRegion: '选择地区',
    packingPic: '包装图片',
    updatePic: '上传图片',
    updatePicMsg: '图片最大3M，支持格式PNG,JPG,JPEG',
    masterPackage: '设为主包装'
  },
  skuPackagesForm: {
    audit_result: '审核结果',
    audit_remark: '审核意见',
    masterPackage: '主包装'
  },
  skuInfo: {
    other_brands: '其他品牌',
    sku_not_found: '未找到SKU',
    to_be_deleted: '待删除',
    check_out_to_be_repaired: '待修复检出',
    new_sku: 'NEW SKU'
  },
  stopRestoreReason: {
    uncertaintiesAwaitingClarification: '待明确的不确定因素',
    ProjectHoldDueToSequenceChange: '因(交付)序列更改而搁置项目',
    shiftEnds: '下班',
    movingInspectorsBackToTheInspection: '将质检员调回检查'
  }

}
