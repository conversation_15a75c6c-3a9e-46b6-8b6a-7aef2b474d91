module.exports = {
  home: { name: 'home' },
  logo: {
    text: 'Zhibiao Intelligence',
    text2: 'Zhibiao data management center'
  },
  content: 'Demo Page',
  description: 'This is a demo page',
  help: 'Help documentation',
  personalCenter: 'Personal Center',
  setting: 'Set Up',
  logout: 'log out',
  log: 'Update log',
  contents: {
    register: 'Registered account',
    forgetPwd: 'Forget password',
    getCaptcha: 'Get captcha',
    loginStyle: 'Account password login',
    loginStyle2: 'Mobile phone number login',
    resetPwd: 'Reset password',
    newPwd: 'New password',
    oldPwd: 'Old password',
    surePwd: 'Sure password',
    goLogin: 'Go Login',
    catTotal: 'Categories total',
    curCutNum: 'Current cuts number',
    ySelect: 'You have chosen',
    tClean: 'To be cleaned',
    clean: 'Cleaning',
    tCheck: 'To be checked',
    checked: 'Under inspection',
    tSum: 'To be summarized',
    suming: 'In summary',
    eSum: 'Summarized',
    pausing: 'Pausing',
    end: 'To then end',
    load: 'Loading',
    picNum: 'Number',
    key: 'Shortcut key',
    help: 'Upload the original path, cut coordinates and classification name and other data',
    loading: 'Loading...',
    fileUpLoading: 'File uploading...',
    serchTip: 'After entering the content, click enter to search',
    tips: 'Click on the lower right corner to get the task',
    dayWork: "Today's work record",
    order: 'Order',
    exportFolder: 'Export folder',
    myLevel: 'My level',
    noLevel: 'No grade',
    not: 'Null',
    mLevel: 'Month Level',
    hLevel: 'Historical Level',
    grade1: 'Last month record: None',
    grade2: 'Last month record：Average daily cleaning {a} pictures, error rate {b}%, monthly processing {c} pictures',
    grade3: 'Daily output: {a}, error rate: {b}, monthly processing capacity: {c}. The three must be met at the same time. If not, the level shall be divided according to the lowest level corresponding to a single item',
    grade: 'Rating description',
    explain: '1 Rating frequency: Update on the first of every month (dynamic adjustment can be made according to actual demand).<br/>2 The tier of the current month is determined by the data of the previous month.<br/>3 The tier is evaluated according to the monthly output (monthly cumulative crop processing quantity).<br/>4 The tier is chosen according to the lower value of daily output or higher error rate.',
    taskType: {
      clean: 'Clean',
      check: 'Check',
      check2: 'Check2',
      spot_check: 'Spot check',
      audit: 'Audit',
      summary: 'Summary',
      register: 'Register'
    },
    multiPackaging: 'Multi Packaging',
    completedTasksToday: 'TOD Task Count',
    completedTasksTodayTip: 'Today\'s completed data: Number of completed folders/Number of completed image cuts'
  },
  buttons: {
    login: 'Login',
    register: 'Register',
    submit: 'Submit',
    search: 'Search',
    delete: 'Delete',
    edit: 'Edit',
    define: 'Define',
    cancel: 'Cancel',
    close: 'Close',
    add: 'Add',
    addTo: 'Add To',
    auth: 'Authorize',
    addteam: 'Add Team',
    editTeam: 'Edit Team',
    addGroup: 'Add Group',
    editGroup: 'Edit Group',
    release: 'Release',
    enable: 'Enable',
    prohibit: 'Prohibit',
    creatP: 'Create Project',
    task: 'Configuration task',
    exportPro: 'Export project data',
    endProject: 'End project',
    more: 'More',
    exportLog: 'Export Log',
    return: 'Return',
    start: 'Start',
    giveUp: 'Give Up',
    save: 'Save',
    upJson: 'Please select JSON file to upload',
    reduction: 'Restore',
    download: 'Download',
    details: 'Details',
    unOffline: 'Offline',
    temp: 'Use template',
    pass: 'Pass',
    pause: 'Pause',
    cancelPause: 'Cancel pause',
    accessControl: 'Access control',
    audit: 'Audit',
    task_rule: 'Task rules',
    detail: 'Detail',
    reordering: 'Reordering'
  },
  drawer: {
    user: 'User Edit',
    role: 'Role Edit',
    roleAuth: 'Role Author',
    menu: 'Menu Edit',
    add: 'Add {name}',
    edit: 'Edit {name}',
    dict: 'dict',
    addPackage: 'Add SKU Package',
    auditPackage: 'Audit SKU Package'
  },
  modal: {
    title: 'Spring frame',
    addPro: 'Add Project',
    bigPic: 'View big picture'
  },
  confirm: {
    del: 'Sure to delete?',
    title: 'Tips',
    release: 'Confirm to publish this project. The task flow cannot be changed after publishing!',
    endProject: 'Confirm to end this project. Send back the cleaning result？',
    noCheckData: 'No checked Data',
    cnt: 'You have a cleaning task that has not been submitted. Are you sure you want to leave this page?',
    cnt2: 'Please select the folder to operate first!',
    cnt3: 'Are you sure to start the cleaning operation?',
    cnt4: 'Are you sure you want to abandon this cleaning operation?',
    cnt5: 'Are you sure to submit this cleaning operation?',
    cnt11: 'Are you sure to submit this checking operation?',
    cnt6: 'You have',
    cnt7: 'times left to give up today',
    cnt8: 'You have run out of waivers today',
    cnt81: 'Are you sure to submit this register operation?',
    cnt9: 'Are you sure to submit this audit operation?',
    cnt12: 'Are you sure to submit this summary operation?',
    sukSure: 'Are you sure to submit this data operation?',
    workHours: 'So far, your effective working hours today are',
    start: 'Please click the start button first',
    folder: 'Please select a folder first',
    sku: 'This SKU has been created',
    projectId: 'Please enter the project ID',
    bigData: 'There is a large amount of data, please wait patiently',
    unOffline: 'Are you sure you want to force the selected person to offline?',
    noPeople: 'Please select the person to operate first!',
    useTemp: 'After the template is applied, the process information of the previous template will be cleared. Are you sure to apply the template?',
    noWork: "You haven't started the task yet!",
    editSure: 'Confirm modification',
    packing: 'packaging picture',
    noSum: 'Summary operation is not supported for queue items',
    reduction: 'Are you sure to restore the operator?',
    giveUpReasonTip: 'Please select or enter the reason for abandonment',
    maxAttemptsExceeded: 'Reached the maximum number of attempts'
  },
  label: {
    mobile: 'Mobile',
    code: 'Random code',
    mobileCode: 'Verification code',
    password: 'Password',
    name: 'Name',
    fullname: 'Full Name',
    time: 'Time',
    month: 'Month',
    level: 'Level',
    account: 'Account',
    role: 'Role',
    team: 'Team',
    group: 'Group',
    sTime: 'Select Time',
    projectName: 'Project Name',
    auth: 'Authority',
    parentMenu: 'Parent Menu',
    apiName: 'Api Name',
    proName: 'Project Name',
    teamOrId: 'Project Name Or ID',
    taskType: 'Task type',
    proId: 'Project Id',
    proType: 'Project category',
    teamVal: 'Team',
    main: 'Main people',
    ass: 'Assign people',
    taskFlow: 'Task flow',
    upJson: 'Upload data',
    parentDict: 'Parent Dict',
    isErrType: 'Are all misclassified',
    isFixSkuProject: 'Is the SKU project fixed',
    id: 'ID',
    sku_name: 'SKU Name',
    audit_result: 'Audit Result',
    skuId: 'SKU ID'
  },
  rules: {
    mobile: 'Please enter mobile code',
    account: 'Please enter account name',
    password: 'Please enter password',
    code: 'Please enter random code',
    mobileCode: 'Please enter verification code',
    name: 'Please enter name',
    fullname: 'Please enter full name',
    checkTeam: 'Please select a team',
    role: 'Please select a role',
    level: 'Please select a level',
    proName: 'Please enter project name',
    newPwdReq: 'New password required',
    oldPwdReq: 'Old password required',
    surePwdReq: 'Sure password required',
    surePwdErr: 'The two passwords are inconsistent',
    minPwd: 'The minimum password length is 6',
    intNum: 'You can only enter positive integers from 1 to 20',
    noSave: 'Some modified data is not saved',
    skuProject: 'Please select sku industry',
    skuPic: 'Packaging picture cannot be empty',
    notNull: '{name} is required',
    maxLength: 'Maximum length {number}',
    skuReg: 'Only Chinese, English, numbers, spaces, underscores, +,% and beginning and end are supported. No spaces are allowed',
    required: 'This is required',
    has2Space: 'The content cannot have consecutive spaces',
    noChinese: 'English SKU name cannot contain Chinese',
    numG1: 'The start value or end value cannot be empty',
    numG2: 'The end value cannot be less than the start value'
  },
  placeholder: {
    mobile: 'Mobile Number',
    password: 'Password'
  },
  table: {
    id: 'ID',
    mobile: 'Mobile',
    name: 'Name',
    role: 'Role',
    menuName: 'Menu Name',
    roleName: 'Role Name',
    team: 'Team',
    group: 'Group',
    teamName: 'Team Name',
    groupName: 'Group Name',
    isEnable: 'Enable',
    creatTime: 'Creat Time',
    updateTime: 'Update Time',
    action: 'Action',
    task: 'Task',
    hours: 'Valid work hours(h)',
    hours2: 'Actual work hours(h)',
    restTime: 'Rest time(h)',
    picNum: 'Handle picture number',
    speed: 'Speed(one/h)',
    coverPicNum: 'Cover handle picture number',
    errNum: 'Error number',
    errRate: 'Error rate',
    total: 'Joint',
    strip: 'Strip',
    project: 'Entry Name',
    proTotal: 'Project Cuts Number',
    state: 'State',
    isClean: 'Clear Submission',
    isFun: 'Running',
    reTime: 'Release Time',
    endTime: 'End Time',
    creater: 'creater',
    completed: 'Completed',
    incomplete: 'Incomplete',
    expTotal: 'Total exported',
    agTotal: 'Aggregate total',
    people: 'People Number',
    taskType: 'Task Type',
    toolType: 'Tool Type',
    rTeam: 'Responsible Team',
    taskName: 'Task Name',
    taskId: 'Task ID',
    sort: 'Sort',
    dirInvisible: 'Show all folders',
    skuProject: 'Sku Project',
    skuIndustry: 'Sku Industry',
    invalidData: 'Invalid data',
    invalidPro: 'Invalid proportion',
    validData: 'Valid data',
    validPro: 'Effective proportion',
    projectCat: 'Project category',
    projectId: 'Project Id',
    time: 'Time',
    planDate: 'Plan date',
    overdue: 'Overdue',
    cleanErrorRate: 'Clean error rate',
    cleanErrorNum: 'Clean error number',
    checkErrorRate: 'Check error rate',
    checkErrorNum: 'Check error number',
    check2ErrorNum: 'Check 2 error number',
    check2ErrorRate: 'Check 2 error rate',
    spotCheckErrorNum: 'Spot error number',
    cleanTime: 'Clean time',
    cleanSpeed: 'Clean speed',
    checkTime: 'Check time',
    checkSpeed: 'Check speed',
    check2time: 'Check 2 time',
    check2Speed: 'Check 2 speed',
    spotTime: 'Spot time',
    sumTime: 'Summary time',
    sumSpeed: 'Summary speed',
    cleanReduce: 'Clean minus errors',
    checkReduce: 'Check minus errors',
    difCoe: 'Degree of Difficulty',
    conAmo: 'Converted quantity',
    headI: 'Head identification',
    midI: 'Intermediate identification',
    projectC: 'Project content',
    projectT: 'Project item',
    projectG: 'Project team',
    fullC: 'Full category',
    cleanSurplus: 'Clean surplus',
    checkSurplus: 'check surplus',
    sumSurplus: 'Spot check surplus',
    rdDate: 'Required delivery date',
    adDate: 'Delivery date',
    dec: 'Remarks',
    pGroup: 'People group',
    iGroup: 'Project group',
    projectType: 'Project type',
    projectTaskRule: 'Task rules',
    flowerTem: 'Process template',
    sku_r: 'Apply',
    sku_b: 'Audited',
    sku_err: 'Error',
    sku_b_del: 'Deleted number',
    sku_l: 'Missed extraction',
    sku_s: 'Examine',
    sku_x: 'Modify',
    sku_del: 'Delete',
    totalN: 'Total',
    sum: 'Summary',
    pic: 'Pictures',
    pic_link: 'Pictures linking',
    err_sku: 'Error sku',
    oper_time: 'Operation time',
    oper_people: 'Operation people',
    modify_sku: 'Correct SKU',
    modify_people: 'Modified by',
    modify_time: 'Modification time',
    flow_name: 'Flow name',
    cn_name: 'Chinese name',
    en_name: 'English name',
    err_type: 'Error type',
    grade_name: 'Grade name',
    iden: 'Identification',
    day_action: 'Average daily output (piece)',
    month_all: 'Monthly processing capacity (piece)',
    rc: 'Reach conditions',
    icon: 'Icon',
    date: 'Date',
    dirName: 'Folder name',
    dir_id: 'Folder ID',
    pauseTime: 'Pause time',
    oPic: 'Original Picture',
    sku_name: 'SKU Name',
    audit_user: 'Auditor',
    audit_time: 'Audit time',
    audit_state: 'Audit result',
    restore_reason: 'Restore Reason',
    restore_user: 'Restore Operator',
    restore_time: 'Restore Time',
    start_time: 'Start Time',
    stop_time: 'Stop Time',
    stop_number: 'Remaining Image Count of the Folder at Pause',
    dir_number: 'Image Count of the Folder'
  },
  dic: {
    yes: 'Yes',
    no: 'No',
    done: 'Done',
    rAccept: 'To be accepted',
    ongoing: 'Have in hand',
    rStart: 'To begin',
    unreleased: 'Unpublished',
    function: 'Function',
    suspend: 'Suspend',
    clean: 'Cleaning task',
    check: 'Checking task',
    sum: 'Summary task',
    delivered: 'Delivered',
    undelivered: 'Undelivered',
    lAsc: 'Letter ASC',
    qDesc: 'Quantity DESC',
    register_t: 'Register task',
    audit: 'Audit task',
    cn: 'Chinese',
    en: 'English',
    registered: 'Registration',
    register: 'To be registered',
    reviewed: 'Under review',
    review: 'To be reviewed',
    confidence: 'Confidence',
    distance: 'Distance',
    applicant: 'Applicant',
    audit_pass: 'Passed',
    audit_unpassed: 'Unpassed',
    audit_to_audit: 'To audit',
    sku_name: 'SKU Name',
    previous: 'Previous',
    next: 'Next'
  },
  india: {
    hourly: 'Hourly stats',
    today: 'Average personal cleaning volume',
    hour: 'Average Cleaned Images',
    man: 'AverAge Man Hours',
    min: 'Average Cleaned Images',
    interval: 'Interval',
    type: 'Type of Task',
    received: 'Image Received',
    total: 'Total Pending',
    completed: 'Completed',
    cHour: 'Completed this hour',
    mHour: 'Man Hours(HC)',
    cHours: 'Images Cleaned/Hr/Head',
    cMin: 'Images Cleaned/Min/Head',
    username: 'Employees Name',
    pname: 'Project Name',
    tasktype: 'Task type',
    startTime: 'Folder started Time',
    endTime: 'Folder end Time',
    spentTime: 'Time spent',
    ptotal: 'Total images in the folder',
    remaining: 'Remaining images to process',
    wtotalimg: 'Total folders worked',
    wtotalf: 'Total images worked',
    whours: 'Actual working hours',
    whours2: 'Valid working hours',
    whours3: 'Actual time spent',
    whours4: 'Valid time spent',
    restTime: 'Rest time',
    pstas: 'Personal Stats',
    time: 'Time',
    getMyErr: 'Query my error',
    allow: 'Allow',
    refuse: 'Refuse'
  },
  skuForm: {
    name: 'Product name',
    type: 'Type',
    sLanguage: 'Select Language',
    brand: 'Brand',
    dealer: 'Brand dealer',
    brandChild: 'Series',
    flavor: 'Flavor',
    category: 'Category',
    packing: 'Packing',
    specs: 'Specifications',
    specsUnit: 'Specification unit',
    salesUnit: 'Sales unit',
    outerPacking: 'Outer packaging',
    aFiles: 'Additional fields',
    sRegion: 'Select region',
    packingPic: 'Packaging picture',
    updatePic: 'Upload pictures',
    updatePicMsg: 'Picture Max 3M, supporting PNG, JPG and JPEG formats',
    masterPackage: 'Master Packaging'
  },
  skuPackagesForm: {
    audit_result: 'Audit result',
    audit_remark: 'Audit opinion',
    masterPackage: 'Master Packaging'
  },
  skuInfo: {
    other_brands: 'Other brands',
    sku_not_found: 'Sku not found',
    to_be_deleted: 'To be deleted',
    check_out_to_be_repaired: 'Check out to be repaired',
    new_sku: 'NEW SKU'
  },
  stopRestoreReason: {
    uncertaintiesAwaitingClarification: 'Uncertainties Awaiting Clarification',
    ProjectHoldDueToSequenceChange: 'Project Hold due to Sequence Change',
    shiftEnds: 'Shift Ends',
    movingInspectorsBackToTheInspection: 'Moving Inspectors Back to the Inspection'
  }
}
