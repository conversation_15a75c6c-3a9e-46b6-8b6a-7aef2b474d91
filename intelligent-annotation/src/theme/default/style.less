.week-mode{
  overflow: hidden;
  filter: invert(80%);
}
.beauty-scroll{
  scrollbar-color: @primary-color @primary-2;
  scrollbar-width: thin;
  -ms-overflow-style:none;
  position: relative;
  &::-webkit-scrollbar{
    width: 3px;
    height: 1px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: @primary-color;
  }
  &::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 1px rgba(0,0,0,0);
    border-radius: 3px;
    background: @primary-3;
  }
}
.split-right{
  &:not(:last-child) {
    border-right: 1px solid rgba(98, 98, 98, 0.2);
  }
}
.disabled{
  cursor: not-allowed;
  color: @disabled-color;
  pointer-events: none;
}

.input_no_radius {
  .ant-input {
    border-radius: 0;
  }
  .ant-input, .ant-select-selection {
    border-radius: 0;
  }
}

.page_fixed_load {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, .3);
}