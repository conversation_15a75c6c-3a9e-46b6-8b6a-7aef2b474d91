
.ant-table-thead{
  tr{
    th{
      &.ant-table-column-has-actions{
        &.ant-table-column-has-sorters:hover{
          background-color: @background-color-base;
        }
        &.ant-table-column-has-filters{
          &:hover{
            .anticon-filter, .anticon-filter:hover{
              background-color: @background-color-base;
            }
          }
          .anticon-filter.ant-table-filter-open{
            background-color: @background-color-base;
          }
        }
      }
    }
  }
}
.ant-table {
  tr {
    td, th { word-break: break-all; }
  }
}
.my_scroll_table {
  .ant-table {
    td, th { white-space: nowrap; }
  } 
}