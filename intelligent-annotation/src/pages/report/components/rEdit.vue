<template>
  <form-drawer :title="$t('buttons.edit')" :visible="openDraw" @closeDrawer="openDraw = false">
    <a-form :form="form" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item :label="$t('table.project')">
            {{ formData && formData.name ? formData.name : '-' }}
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item :label="$t('table.projectCat')">
            <a-select
              v-decorator="[
                'project_category_id',
                {
                  rules: [], initialValue: formData && formData.project_category_id ? Number(formData.project_category_id) : ''
                },
              ]"
              show-search
              :filter-option="filterOption"
            >
              <a-select-option v-for="item in ruleList" :key="item.id" :value="item.id">{{ item.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('table.projectC')">
            <a-input
              v-decorator="[
                'content',
                {
                  rules: [], initialValue: formData ? formData.content : ''
                },
              ]"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item :label="$t('table.projectG')">
            <a-select
              v-decorator="[
                'group_name',
                {
                  rules: [], initialValue: formData ? formData.group : ''
                },
              ]"
            >
              <a-select-option v-for="item in groupList" :key="item.name" :value="item.name">{{ item.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('table.planDate')">
            <a-date-picker v-decorator="['plan_date', { rules: [], initialValue: formData && formData.plan_date ? formData.plan_date : null } ]" :show-today="false" format="YYYY-MM-DD" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item :label="$t('table.projectT')">
            <a-select
              v-decorator="[
                'classification',
                {
                  rules: [], initialValue: formData ? formData.classification : ''
                },
              ]"
            >
              <a-select-option v-for="item in projectTList" :key="item.id" :value="item.id">{{ item.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('table.overdue')">
            <a-radio-group
              v-decorator="[
                'is_overtime',
                {
                  rules: [],
                  initialValue: formData && formData.is_overtime === 'T' ? 'T' : 'F',
                },
              ]"
            >
              <a-radio value="T">{{ $t('dic.yes') }}</a-radio>
              <a-radio value="F">{{ $t('dic.no') }}</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item :label="$t('table.fullC')">
            <a-radio-group
              v-decorator="[
                'is_full_category',
                {
                  rules: [],
                  initialValue: formData && formData.is_full_category === 'T' ? 'T' : 'F',
                },
              ]"
            >
              <a-radio value="T">{{ $t('dic.yes') }}</a-radio>
              <a-radio value="F">{{ $t('dic.no') }}</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('table.validData')">
            <a-input-number v-decorator="['effective_data', { rules: [], initialValue: formData ? formData.effective_data : '' } ]" :min="0" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item :label="$t('table.spotCheckErrorNum')">
            <a-input-number v-decorator="['spot_check_error_number', { rules: [], initialValue: formData ? formData.spot_check_error_number : '' } ]" :min="0" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item :label="$t('table.cleanReduce')">
            <a-input-number v-decorator="['clean_error_number_change', { rules: [], initialValue: formData ? formData.clean_error_number_change : '' } ]" :min="0" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item :label="$t('table.checkReduce')">
            <a-input-number v-decorator="['check_error_number_change', { rules: [], initialValue: formData ? formData.check_error_number_change : '' } ]" :min="0" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1,
      }"
    >
      <a-button :style="{ marginRight: '8px' }" @click="openDraw = false">
        {{ $t('buttons.cancel') }}
      </a-button>
      <a-button type="primary" @click="onSubmit">
        {{ $t('buttons.define') }}
      </a-button>
    </div>
  </form-drawer>
</template>

<script>
import FormDrawer from '@/components/formDrawer'
import { changeProjectAllData } from '@/services/kpi'
import moment from 'moment'
export default {
  name: 'REdit',
  components: { FormDrawer },
  props: {
    groupList: {
      type: Array,
      default: () => {
        return []
      }
    },
    ruleList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      form: this.$form.createForm(this),
      openDraw: false,
      formData: null,
      projectTList: [
        { id: 'sku', name: 'sku' },
        { id: 'others', name: 'others' }
      ]
    }
  },
  methods: {
    // 项目大类搜索
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    },
    onSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          const param = Object.assign({ project_id: this.formData.project_id }, values)
          if (values.plan_date) param.plan_date = moment(param.plan_date).format('YYYY-MM-DD')
          changeProjectAllData(param).then(res => {
            if (res.errcode === 0) {
              this.$message.success(res.errmsg)
              this.openDraw = false
              this.$emit('submitSuccess')
            } else {
              this.$message.error(res.errmsg)
            }
          })
        }
      })
    }
  }
}
</script>

<style>

</style>
