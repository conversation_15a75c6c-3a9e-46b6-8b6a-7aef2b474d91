<template>
  <form-drawer :title="$t('buttons.edit')" :visible="openDraw" @closeDrawer="openDraw = false">
    <a-form :form="form" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item :label="$t('table.project')">
            {{ formData && formData.project_name ? formData.project_name : '-' }}
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item :label="$t('table.dec')">
            <a-textarea
              v-decorator="[
                'remarks',
                {
                  rules: [], initialValue: formData ? formData.remarks : ''
                },
              ]"
              :auto-size="{ minRows: 3, maxRows: 6 }"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-item :label="$t('table.planDate')">
            <a-date-picker v-decorator="['plan_date', { rules: [], initialValue: formData && formData.plan_date ? formData.plan_date : null } ]" :show-today="false" format="YYYY-MM-DD" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1,
      }"
    >
      <a-button :style="{ marginRight: '8px' }" @click="openDraw = false">
        {{ $t('buttons.cancel') }}
      </a-button>
      <a-button type="primary" @click="onSubmit">
        {{ $t('buttons.define') }}
      </a-button>
    </div>
  </form-drawer>
</template>

<script>
import FormDrawer from '@/components/formDrawer'
import { changeProjectAllData } from '@/services/kpi'
import moment from 'moment'
export default {
  name: 'CEdit',
  components: { FormDrawer },
  data() {
    return {
      form: this.$form.createForm(this),
      openDraw: false,
      formData: null
    }
  },
  methods: {
    onSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          const param = Object.assign({ project_id: this.formData.project_id }, values)
          if (values.plan_date) param.plan_date = moment(param.plan_date).format('YYYY-MM-DD')
          changeProjectAllData(param).then(res => {
            if (res.errcode === 0) {
              this.$message.success(res.errmsg)
              this.openDraw = false
              this.$emit('submitSuccess')
            } else {
              this.$message.error(res.errmsg)
            }
          })
        }
      })
    }
  }
}
</script>

<style>

</style>
