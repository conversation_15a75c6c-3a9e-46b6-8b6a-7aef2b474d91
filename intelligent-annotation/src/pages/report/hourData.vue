<template>
  <div class="hour_data_container">
    <a-card>
      <div style="margin-bottom: 20px">
        <a-form-model
          layout="inline"
          :model="formInline"
          @submit="handleSubmit(true)"
          @submit.native.prevent
        >
          <a-form-model-item required :label="$t('label.time')">
            <a-date-picker
              :default-value="moment().subtract(1, 'days')"
              format="YYYY-MM-DD"
              type="date"
              :allow-clear="false"
              :show-today="false"
              @change="onDateChange"
            />
          </a-form-model-item>
          <a-form-model-item required :label="$t('label.taskType')">
            <a-input v-model="formInline.task_flow_type" @change="handleTaskChange" />
          </a-form-model-item>
          <a-form-model-item required :label="$t('label.group')">
            <a-select style="width: 150px" :value="formInline.group_id" @change="handleGroupChange">
              <a-select-option v-for="it in groupList" :key="it.group_id" :value="it.group_id">
                {{ it.group_name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item>
            <a-button type="primary" :disabled="!formInline.group_id" html-type="submit">{{ $t("buttons.search") }}</a-button>
          </a-form-model-item>
          <a-form-model-item>
            <a-button type="primary" :disabled="data.length === 0" @click="downAllData">{{ $t("buttons.download") }}</a-button>
          </a-form-model-item>
        </a-form-model>
      </div>
      <div v-if="exportLoading" class="export_loading">
        <a-spin size="large" :tip="$t('confirm.bigData')" />
      </div>
      <a-table
        size="middle"
        :columns="columns"
        :data-source="data"
        :loading="tableLoading"
        bordered
        :row-key="(record, index) => `${index}_${Math.random()*10000}`"
        :pagination="false"
        :scroll="{ y: table_height }"
      >
        <template slot="footer">
          <div class="sum_tr">
            <table>
              <thead class="ant-table-thead">
                <tr>
                  <th>{{ $t('table.sum') }}</th>
                  <th>&nbsp;</th>
                  <th>&nbsp;</th>
                  <th>&nbsp;</th>
                  <th>&nbsp;</th>
                  <th>{{ footer_data.n8 || '' }}</th>
                  <th>{{ footer_data.n9 || '' }}</th>
                  <th>{{ footer_data.n10 || '' }}</th>
                  <th>{{ footer_data.n11 || '' }}</th>
                  <th>{{ footer_data.n13 || '' }}</th>
                  <th>{{ footer_data.n14 || '' }}</th>
                  <th>{{ footer_data.n15 || '' }}</th>
                  <th>{{ footer_data.n16 || '' }}</th>
                  <th>{{ footer_data.n17 || '' }}</th>
                  <th>{{ footer_data.n18 || '' }}</th>
                  <th>{{ footer_data.n8_13 || '' }}</th>
                  <th>{{ footer_data.n13_19 || '' }}</th>
                  <th>{{ footer_data.n19_8 || '' }}</th>
                  <th>{{ footer_data.n8_8 || '' }}</th>
                </tr>
              </thead>
            </table>
          </div>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script>
import moment from 'moment'
import { sendWebsocket, closeWebsocket } from '@/utils/websocket'
import { getTaskGroupName, getWorkAchievements } from '@/services/kpi'
export default {
  name: 'HourData',
  data() {
    return {
      formInline: {
        time: moment().subtract(1, 'days').format('YYYY-MM-DD'),
        task_flow_type: '清洗',
        group_id: null
      },
      groupList: [],
      data: [],
      footer_data: {},
      exportLoading: false,
      tableLoading: false,
      downLoadParam: {},
      table_height: 520
    }
  },
  computed: {
    columns() {
      const data = [
        { title: this.$t('table.group'), dataIndex: 'group_name', align: 'center' },
        { title: this.$t('table.name'), dataIndex: 'user', align: 'center' },
        { title: this.$t('table.projectId'), dataIndex: 'project_id', align: 'center' },
        { title: this.$t('table.projectType'), dataIndex: 'project_category', align: 'center' },
        { title: this.$t('table.taskType'), dataIndex: 'task_flow', align: 'center' },
        { title: '8', dataIndex: 'n8', sorter: (a, b) => a.n8 - b.n8, align: 'center' },
        { title: '9', dataIndex: 'n9', sorter: (a, b) => a.n9 - b.n9, align: 'center' },
        { title: '10', dataIndex: 'n10', sorter: (a, b) => a.n10 - b.n10, align: 'center' },
        { title: '11', dataIndex: 'n11', sorter: (a, b) => a.n11 - b.n11, align: 'center' },
        { title: '13', dataIndex: 'n13', sorter: (a, b) => a.n13 - b.n13, align: 'center' },
        { title: '14', dataIndex: 'n14', sorter: (a, b) => a.n14 - b.n14, align: 'center' },
        { title: '15', dataIndex: 'n15', sorter: (a, b) => a.n15 - b.n15, align: 'center' },
        { title: '16', dataIndex: 'n16', sorter: (a, b) => a.n16 - b.n16, align: 'center' },
        { title: '17', dataIndex: 'n17', sorter: (a, b) => a.n17 - b.n17, align: 'center' },
        { title: '18', dataIndex: 'n18', sorter: (a, b) => a.n18 - b.n18, align: 'center' },
        { title: '8~13', dataIndex: 'n8_13', sorter: (a, b) => a.n8_13 - b.n8_13, align: 'center' },
        { title: '13~19', dataIndex: 'n13_19', sorter: (a, b) => a.n13_19 - b.n13_19, align: 'center' },
        { title: '19~8', dataIndex: 'n19_8', sorter: (a, b) => a.n19_8 - b.n19_8, align: 'center' },
        { title: this.$t('table.totalN'), dataIndex: 'n8_8', align: 'center' }
      ]
      return data
    }
  },
  mounted() {
    // 获取有数据的班级
    this.getTaskGroupNameFun()
    const h = window.innerHeight - 350
    this.table_height = h > 250 ? h : 250
    window.addEventListener('resize', this.resizeTableH, true)
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeTableH, true)
  },
  methods: {
    moment,
    // 重新计算表格最大高度
    resizeTableH() {
      const h = window.innerHeight - 350
      this.table_height = h > 250 ? h : 250
    },
    // 选择时间
    onDateChange(date, dataStr) {
      this.formInline.time = dataStr
      this.formInline.group_id = null
      this.getTaskGroupNameFun()
    },
    // 任务类型
    handleTaskChange(e) {
      const value = e.target.value
      this.formInline.task_flow_type = value
      this.formInline.group_id = null
      if (value) {
        this.getTaskGroupNameFun()
      } else {
        this.groupList = []
      }
    },
    // 选择组
    handleGroupChange(value) {
      this.formInline.group_id = value
    },
    handleSubmit() {
      this.downLoadParam = {
        task_flow_name: this.formInline.task_flow_type,
        group_id: this.formInline.group_id,
        date_time: this.formInline.time
      }
      this.tableLoading = true
      getWorkAchievements(this.downLoadParam).then(res => {
        this.tableLoading = false
        if (res.errcode === 0) {
          const data = res.data.data
          this.data = data.filter((item, index) => index > 0)
          this.footer_data = data[0] ? data[0] : {}
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 获取可选班级
    getTaskGroupNameFun() {
      const param = {
        task_flow_name: this.formInline.task_flow_type,
        date_time: this.formInline.time
      }
      getTaskGroupName(param).then(res => {
        if (res.errcode === 0) {
          this.groupList = res.data
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 长连接接受后台信息
    wsMessage(res, t) {
      this.exportLoading = false
      if (res.errcode === 0) {
        const _url = `${process.env.VUE_APP_API_BASE_URL2}/project/download_file/?file_name=${res.data.file_name}&access_token=${t}`
        const link = document.createElement('a')
        link.setAttribute('download', res.data.file_name)
        link.setAttribute('href', _url)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } else {
        this.$message.error(res.errmsg)
      }
      closeWebsocket()
    },
    wsError(msg) {
      this.exportLoading = false
      this.$message.error(msg.errmsg)
    },
    // 下载全部数据
    downAllData() {
      this.exportLoading = true
      const param = {
        ...this.downLoadParam,
        url: 'download_work_achievements'
      }
      sendWebsocket(param, this.wsMessage, this.wsError)
    }
  }
}
</script>

<style lang="less" scoped>
.export_loading {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.4);
  z-index: 10;
}
.hour_data_container {
  .sum_tr {
    margin: -16px 0 -16px -16px;
    tr {
      th {
        text-align: center;
        &:last-child {
          border-right-color: transparent;
        }
      }
    }
  }
}
</style>
