<template>
  <a-card>
    <div style="margin-bottom: 20px">
      <a-form-model
        layout="inline"
        :model="formInline"
        @submit="handleSubmit(true)"
        @submit.native.prevent
      >
        <a-form-model-item :label="$t('label.fullname')">
          <a-input v-model="formInline.user_name" />
        </a-form-model-item>
        <a-form-model-item :label="$t('label.proId')">
          <a-input v-model="formInline.project_id" />
        </a-form-model-item>
        <a-form-model-item :label="$t('label.proType')">
          <a-select
            style="width: 150px"
            allow-clear
            :value="formInline.project_category"
            show-search
            :filter-option="filterOption"
            @change="handleTypeChange"
          >
            <a-select-option v-for="item in ruleList" :key="item.id" :value="item.id">{{ item.name }}</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item :label="$t('label.taskType')">
          <a-select style="width: 150px" allow-clear :value="formInline.task_flow_type" @change="handleTaskChange">
            <a-select-option v-for="it in taskList" :key="it.value" :value="it.value">
              {{ it.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item :label="$t('label.team')">
          <a-select style="width: 150px" allow-clear :value="formInline.team_id" @change="handleTeamChange">
            <a-select-option v-for="it in teamList" :key="it.team_id" :value="it.team_id">
              {{ it.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item :label="$t('table.pGroup')">
          <a-select style="width: 150px" allow-clear :value="formInline.group_id" @change="handleGroupChange">
            <a-select-option v-for="it in groupList" :key="it.group_id" :value="it.group_id">
              {{ it.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item :label="$t('label.time')">
          <a-range-picker
            :allow-clear="false"
            format="YYYY-MM-DD HH:mm"
            :disabled-date="disabledDate"
            :default-value="[moment().startOf('day'), moment().endOf('day')]"
            :show-time="{ format: 'HH:mm', defaultValue:[moment('01:00', 'HH:mm'), moment('23:59', 'HH:mm')] }"
            @change="onTimeChange"
            @ok="handleSubmit(true)"
          />
        </a-form-model-item>
        <a-form-model-item>
          <a-button type="primary" html-type="submit">{{ $t("buttons.search") }}</a-button>
        </a-form-model-item>
        <a-form-model-item>
          <a-button type="primary" :disabled="data.length === 0" @click="downAllData">{{ $t("buttons.download") }}</a-button>
        </a-form-model-item>
      </a-form-model>
    </div>
    <a-table
      class="my_scroll_table"
      :scroll="{ x: true }"
      :columns="columns"
      :data-source="data"
      :loading="tableLoading"
      bordered
      :row-key="(record, index) => index"
      :pagination="paginations"
      @change="handleTableChange"
    >
      <template slot="project_id" slot-scope="text, record">
        <span v-if="record.is_queue && record.is_queue === 'T'" style="color: #ff0000;">
          <a-icon type="bulb" theme="filled" style="vertical-align: baseline;" /> {{ record.project_id }}
        </span>
        <span v-else> {{ record.project_id }} </span>
      </template>
      <span slot="workTime" slot-scope="text">
        {{ text | hourFormat }}
      </span>
    </a-table>
    <div v-if="exportLoading" class="export_loading">
      <a-spin size="large" />
    </div>
  </a-card>
</template>

<script>
import moment from 'moment'
import { apiCommonServer } from '@/services'
import { getDayAch } from '@/services/kpi'
import { getRuleName } from '@/services/sys'
import { sendWebsocket, closeWebsocket } from '@/utils/websocket'
export default {
  name: 'Report',
  filters: {
    hourFormat: (value) => {
      return value ? parseFloat((value / 3600).toFixed(3)) : 0
    }
  },
  data() {
    return {
      formInline: {
        user_name: null,
        project_id: null,
        project_category: null,
        task_flow_type: null,
        team_id: null,
        group_id: null,
        start_time: moment().startOf('day').format('YYYY-MM-DD HH:mm'),
        end_time: moment().endOf('day').format('YYYY-MM-DD HH:mm')
      },
      tableLoading: false,
      paginations: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30', '50'],
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => {
          return this.$t('table.total') + total + this.$t('table.strip')
        },
        total: 0
      },
      data: [],
      teamList: [],
      groupList: [],
      exportLoading: false,
      ruleList: []
    }
  },
  computed: {
    taskList() {
      const data = [
        { name: this.$t('dic.clean'), value: 10 },
        { name: this.$t('dic.check'), value: 11 }
        // { name: this.$t('dic.sum'), value: 12 }
      ]
      return data
    },
    columns() {
      const data = [
        { title: this.$t('table.time'), dataIndex: 'time', align: 'center' },
        { title: this.$t('table.projectId'), key: 'project_id', align: 'center', scopedSlots: { customRender: 'project_id' }},
        { title: this.$t('table.projectCat'), dataIndex: 'project_category', align: 'center' },
        { title: this.$t('table.iGroup'), dataIndex: 'project_group_name', align: 'center' },
        { title: this.$t('table.name'), dataIndex: 'user', align: 'center' },
        { title: this.$t('table.taskType'), dataIndex: 'task_flow', align: 'center' },
        { title: this.$t('table.pGroup'), dataIndex: 'group', align: 'center' },
        { title: this.$t('table.hours'), dataIndex: 'valid_working_hours', align: 'center', scopedSlots: { customRender: 'workTime' }},
        { title: this.$t('table.hours2'), dataIndex: 'actual_working_hours', align: 'center', scopedSlots: { customRender: 'workTime' }},
        { title: this.$t('table.restTime'), dataIndex: 'rest_hours', align: 'center', scopedSlots: { customRender: 'workTime' }},
        { title: this.$t('table.picNum'), dataIndex: 'handle_pic_number', align: 'center' },
        { title: this.$t('table.speed'), dataIndex: 'speed', align: 'center' },
        { title: this.$t('table.errNum'), dataIndex: 'error_number', align: 'center' },
        { title: this.$t('table.errRate'), dataIndex: 'error_rate', align: 'center' }
      ]
      return data
    }
  },
  mounted() {
    this.getTeamList()
    this.getRuleNameFun()
  },
  methods: {
    moment,
    // 不可选日期区间
    disabledDate(current) {
      return current && current > moment().endOf('day')
    },
    // 时间选择
    onTimeChange(date, dataStr) {
      this.formInline.start_time = dataStr[0]
      this.formInline.end_time = dataStr[1]
    },
    // 任务类型
    handleTaskChange(value) {
      this.formInline.task_flow_type = value
    },
    // 选择队
    handleTeamChange(value) {
      this.formInline.team_id = value
      this.formInline.group_id = null
      if (value) {
        this.getGroupList()
      } else {
        this.groupList = []
      }
    },
    // 选择组
    handleGroupChange(value) {
      this.formInline.group_id = value
    },
    // 数据搜索
    handleSubmit(first) {
      if (first) this.paginations.current = 1
      this.tableLoading = true
      const param = {
        ...this.formInline,
        page: this.paginations.current,
        number: this.paginations.pageSize
      }
      getDayAch(param).then(res => {
        this.tableLoading = false
        if (res.errcode === 0) {
          this.data = res.data.data_list
          this.paginations.total = res.data.total
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 分页改变
    handleTableChange(obj) {
      this.paginations.current = obj.current
      if (obj.showSizeChanger) {
        this.paginations.pageSize = obj.pageSize
      }
      this.handleSubmit()
    },
    // 获取团队
    getTeamList() {
      apiCommonServer.getTeamList().then(res => {
        if (res.errcode === 0) {
          this.teamList = res.data.team_list
        }
      })
    },
    // 获取组
    getGroupList() {
      apiCommonServer.getGroupList(this.formInline.team_id).then(res => {
        if (res.errcode === 0) {
          this.groupList = res.data.group_list
        }
      })
    },
    // 项目大类改变
    handleTypeChange(value) {
      this.formInline.project_category = value
    },
    // 项目大类搜索
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    },
    // 获取项目大类
    getRuleNameFun() {
      getRuleName({ is_all: 'T' }).then(res => {
        if (res.errcode === 0) {
          this.ruleList = res.data.data_list
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 长连接接受后台信息
    wsMessage(res, t) {
      this.exportLoading = false
      if (res.errcode === 0) {
        const _url = `${process.env.VUE_APP_API_BASE_URL2}/project/download_file/?file_name=${res.data.file_name}&access_token=${t}`
        const link = document.createElement('a')
        link.setAttribute('download', res.data.file_name)
        link.setAttribute('href', _url)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } else {
        this.$message.error(res.errmsg)
      }
      closeWebsocket()
    },
    wsError(msg) {
      this.exportLoading = false
      this.$message.error(msg.errmsg)
    },
    // 下载全部数据
    downAllData() {
      this.exportLoading = true
      const param = {
        ...this.formInline,
        url: 'download_day_achievements'
      }
      sendWebsocket(param, this.wsMessage, this.wsError)
    }
  }
}
</script>

<style scoped lang="less">
.export_loading {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.4);
  z-index: 10;
}
</style>
