<template>
  <a-card>
    <div style="margin-bottom: 20px">
      <a-form-model
        layout="inline"
        :model="formInline"
        @submit="handleSubmit(true)"
        @submit.native.prevent
      >
        <a-form-model-item :label="$t('label.group')">
          <a-select style="width: 150px" allow-clear :value="formInline.group_name" @change="handleGroupChange">
            <a-select-option v-for="it in groupList" :key="it.group_id" :value="it.name">
              {{ it.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item :label="$t('label.proId')">
          <a-input v-model="formInline.project_id" />
        </a-form-model-item>
        <a-form-model-item :label="$t('label.proType')">
          <a-select
            style="width: 150px"
            allow-clear
            :value="formInline.project_category_id"
            show-search
            :filter-option="filterOption"
            @change="handleTypeChange"
          >
            <a-select-option v-for="item in ruleList" :key="item.name" :value="item.id">{{ item.name }}</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item :label="$t('table.state')">
          <a-select
            style="width: 150px"
            allow-clear
            :value="formInline.is_delivery"
            @change="handleStateChange"
          >
            <a-select-option value="T">{{ $t('dic.delivered') }}</a-select-option>
            <a-select-option value="F">{{ $t('dic.undelivered') }}</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item :label="$t('table.adDate')">
          <a-range-picker
            format="YYYY-MM-DD"
            :default-picker-value="[moment().subtract(1, 'months'), moment()]"
            @change="onTimeChange"
          />
        </a-form-model-item>
        <a-form-model-item :label="$t('table.planDate')">
          <a-range-picker
            format="YYYY-MM-DD"
            :default-picker-value="[moment().subtract(1, 'months'), moment()]"
            @change="onPlanTimeChange"
          />
        </a-form-model-item>
        <a-form-model-item>
          <a-button type="primary" html-type="submit">{{ $t("buttons.search") }}</a-button>
        </a-form-model-item>
        <a-form-model-item>
          <a-button type="primary" :disabled="data.length === 0" @click="downAllData">{{ $t("buttons.download") }}</a-button>
        </a-form-model-item>
      </a-form-model>
    </div>
    <a-table
      class="my_scroll_table"
      :scroll="{ x: true }"
      :columns="columns"
      :data-source="data"
      :loading="tableLoading"
      bordered
      :row-key="(record, index) => index"
      :pagination="paginations"
      @change="handleTableChange"
    >
      <span slot="action" slot-scope="text, record">
        <a @click="showEdit(record)">{{ $t('buttons.edit') }}</a>
      </span>
    </a-table>
    <CEdit ref="ceditRef" @submitSuccess="handleSubmit" />
    <div v-if="exportLoading" class="export_loading">
      <a-spin size="large" />
    </div>
  </a-card>
</template>

<script>
import moment from 'moment'
import { apiCommonServer } from '@/services'
import { getProjectInventoryLog } from '@/services/kpi'
import { getRuleName } from '@/services/sys'
import { sendWebsocket, closeWebsocket } from '@/utils/websocket'
import CEdit from './components/cEdit'
export default {
  name: 'CProject',
  components: { CEdit },
  data() {
    return {
      formInline: {
        project_id: null,
        project_category_id: null,
        group_name: null,
        start_time: null,
        end_time: null,
        plan_date_start: null,
        plan_date_end: null,
        delivery_date_start: null,
        delivery_date_end: null,
        is_delivery: null
      },
      tableLoading: false,
      paginations: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30', '50'],
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => {
          return this.$t('table.total') + total + this.$t('table.strip')
        },
        total: 0
      },
      data: [],
      groupList: [],
      exportLoading: false,
      ruleList: []
    }
  },
  computed: {
    columns() {
      const data = [
        { title: this.$t('table.group'), dataIndex: 'group_name', align: 'center' },
        { title: this.$t('table.projectId'), dataIndex: 'project_id', align: 'center' },
        { title: this.$t('table.projectCat'), dataIndex: 'project_category', align: 'center' },
        { title: this.$t('table.project'), dataIndex: 'project_name', align: 'center' },
        { title: this.$t('table.proTotal'), dataIndex: 'file_total', align: 'center' },
        { title: this.$t('table.cleanSurplus'), dataIndex: 'clean_surplus_number', align: 'center' },
        { title: this.$t('table.checkSurplus'), dataIndex: 'check_surplus_number', align: 'center' },
        { title: this.$t('table.sumSurplus'), dataIndex: 'spot_check_surplus_number', align: 'center' },
        { title: this.$t('table.reTime'), dataIndex: 'release_time', align: 'center' },
        { title: this.$t('table.planDate'), dataIndex: 'plan_date', align: 'center' },
        { title: this.$t('table.adDate'), dataIndex: 'delivery_date', align: 'center' },
        { title: this.$t('table.dec'), dataIndex: 'remarks', align: 'center' },
        {
          title: this.$t('table.action'),
          key: 'action',
          fixed: 'right',
          align: 'center',
          width: 85,
          scopedSlots: { customRender: 'action' }
        }
      ]
      return data
    }
  },
  mounted() {
    this.getGroupList()
    this.getRuleNameFun()
  },
  methods: {
    moment,
    // 不可选日期区间
    disabledDate(current) {
      return current && current > moment().endOf('day') || current < moment().subtract(31, 'days')
    },
    // 交付时间选择
    onTimeChange(date, dataStr) {
      this.formInline.start_time = dataStr[0] ? `${dataStr[0]} 00:00:00` : null
      this.formInline.end_time = dataStr[1] ? `${dataStr[1]} 23:59:59` : null
    },
    // 计划时间选择
    onPlanTimeChange(date, dataStr) {
      this.formInline.plan_date_start = dataStr[0] || null
      this.formInline.plan_date_end = dataStr[1] || null
    },
    // 选择组
    handleGroupChange(value) {
      this.formInline.group_name = value
    },
    // 状态改变
    handleStateChange(value) {
      this.formInline.is_delivery = value
    },
    // 项目大类改变
    handleTypeChange(value) {
      this.formInline.project_category_id = value
    },
    // 项目大类搜索
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    },
    // 获取项目大类
    getRuleNameFun() {
      getRuleName({ is_all: 'T' }).then(res => {
        if (res.errcode === 0) {
          this.ruleList = res.data.data_list
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 数据搜索
    handleSubmit(first) {
      if (first) { this.paginations.current = 1 }
      this.tableLoading = true
      const param = {
        ...this.formInline,
        page: this.paginations.current,
        number: this.paginations.pageSize
      }
      getProjectInventoryLog(param).then(res => {
        this.tableLoading = false
        if (res.errcode === 0) {
          this.data = res.data.data_list
          this.paginations.total = res.data.total
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 分页改变
    handleTableChange(obj) {
      this.paginations.current = obj.current
      if (obj.showSizeChanger) {
        this.paginations.pageSize = obj.pageSize
      }
      this.handleSubmit()
    },
    // 获取组
    getGroupList() {
      apiCommonServer.getGroupList(1).then(res => {
        if (res.errcode === 0) {
          this.groupList = res.data.group_list
        }
      })
    },
    // 编辑
    showEdit(item) {
      this.$refs.ceditRef.formData = item
      this.$refs.ceditRef.openDraw = true
    },
    // 长连接接受后台信息
    wsMessage(res, t) {
      this.exportLoading = false
      if (res.errcode === 0) {
        const _url = `${process.env.VUE_APP_API_BASE_URL2}/project/download_file/?file_name=${res.data.file_name}&access_token=${t}`
        const link = document.createElement('a')
        link.setAttribute('download', res.data.file_name)
        link.setAttribute('href', _url)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } else {
        this.$message.error(res.errmsg)
      }
      closeWebsocket()
    },
    wsError(msg) {
      this.exportLoading = false
      this.$message.error(msg.errmsg)
    },
    // 下载全部数据
    downAllData() {
      this.exportLoading = true
      const param = {
        ...this.formInline,
        url: 'download_project_inventory_log'
      }
      sendWebsocket(param, this.wsMessage, this.wsError)
    }
  }
}
</script>

<style scoped lang="less">
.export_loading {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.4);
  z-index: 10;
}
</style>
