<template>
  <a-card>
    <div style="margin-bottom: 20px">
      <a-form-model
        layout="inline"
        :model="formInline"
        @submit="handleSubmit(true)"
        @submit.native.prevent
      >
        <a-form-model-item :label="$t('label.proId')">
          <a-input v-model="formInline.project_id" allow-clear />
        </a-form-model-item>
        <a-form-model-item :label="$t('table.projectG')">
          <a-select style="width: 150px" allow-clear :value="formInline.group_name" @change="handleGroupChange">
            <a-select-option v-for="it in groupList" :key="it.name" :value="it.name">
              {{ it.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item :label="$t('label.time')">
          <a-range-picker
            :allow-clear="false"
            :show-time="{ format: 'HH:mm' }"
            :default-value="[moment().startOf('month'), moment().endOf('month')]"
            format="YYYY-MM-DD HH:mm"
            @change="handleMonthChange"
          />
          <!-- <a-month-picker :allow-clear="false" :disabled-date="disabledDate" :default-value="moment()" format="YYYY-MM"/> -->
        </a-form-model-item>
        <a-form-model-item>
          <a-button type="primary" html-type="submit">{{ $t("buttons.search") }}</a-button>
        </a-form-model-item>
        <a-form-model-item>
          <a-button type="primary" :disabled="data.length === 0" @click="downAllData">{{ $t("buttons.download") }}</a-button>
        </a-form-model-item>
      </a-form-model>
    </div>
    <a-table
      class="relative_td"
      :columns="columns"
      :data-source="data"
      :loading="tableLoading"
      bordered
      :row-key="(record, index) => index"
      :pagination="paginations"
      @change="handleTableChange"
    >
      <span slot="is_overtime" slot-scope="text, record">
        {{ record.is_overtime === 'T' ? $t('dic.yes') : $t('dic.no') }}
      </span>
      <span slot="is_full_category" slot-scope="text, record">
        {{ record.is_full_category === 'T' ? $t('dic.yes') : $t('dic.no') }}
      </span>
      <span slot="action" slot-scope="text, record">
        <a @click="showEdit(record)">{{ $t('buttons.edit') }}</a>
      </span>
      <div slot="expandedRowRender" slot-scope="record" class="relative_expanded">
        <a-table
          size="middle"
          :row-key="(record, index) => 'ex' + index"
          :columns="innerColumns"
          :data-source="[record]"
          :pagination="false"
        />
      </div>
    </a-table>
    <REdit ref="rEditCmp" :group-list="groupList" :rule-list="ruleList" @submitSuccess="handleSubmit" />
    <div v-if="exportLoading" class="export_loading">
      <a-spin size="large" />
    </div>
  </a-card>
</template>

<script>
import moment from 'moment'
import { apiCommonServer } from '@/services'
import { getProjectAllData } from '@/services/kpi'
import { mapState } from 'vuex'
import REdit from './components/rEdit'
import { sendWebsocket, closeWebsocket } from '@/utils/websocket'
import { getRuleName } from '@/services/sys'
export default {
  name: 'RProject',
  components: { REdit },
  data() {
    return {
      formInline: {
        project_id: null,
        group_name: null,
        start_time: moment().startOf('month').format('YYYY-MM-DD HH:mm'),
        end_time: moment().endOf('month').format('YYYY-MM-DD HH:mm')
      },
      tableLoading: false,
      paginations: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30', '50'],
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => {
          return this.$t('table.total') + total + this.$t('table.strip')
        },
        total: 0
      },
      data: [],
      groupList: [],
      exportLoading: false,
      ruleList: [] // 项目大类
    }
  },
  computed: {
    ...mapState('setting', ['lang']),
    columns() {
      const data = [
        { title: this.$t('table.projectId'), dataIndex: 'project_id', align: 'center' },
        { title: this.$t('table.projectCat'), dataIndex: 'project_category', align: 'center' },
        { title: this.$t('table.project'), dataIndex: 'name', align: 'center' },
        { title: this.$t('table.projectG'), dataIndex: 'group', align: 'center' },
        { title: this.$t('table.projectC'), dataIndex: 'content', align: 'center' },
        { title: this.$t('table.projectT'), dataIndex: 'classification', align: 'center' },
        { title: this.$t('table.proTotal'), dataIndex: 'file_total', align: 'center' },
        { title: this.$t('table.reTime'), dataIndex: 'release_time', align: 'center' },
        { title: this.$t('table.endTime'), dataIndex: 'finish_time', align: 'center' },
        { title: this.$t('table.planDate'), dataIndex: 'plan_date', align: 'center' },
        { title: this.$t('table.overdue'), key: 'is_overtime', scopedSlots: { customRender: 'is_overtime' }, align: 'center' },
        { title: this.$t('table.fullC'), key: 'is_full_category', scopedSlots: { customRender: 'is_full_category' }, align: 'center' },
        { title: this.$t('table.state'), dataIndex: 'state', align: 'center' },
        { title: this.$t('table.validData'), dataIndex: 'effective_data', align: 'center' },
        { title: this.$t('table.validPro'), dataIndex: 'effective_proportion', align: 'center' },
        {
          title: this.$t('table.action'),
          key: 'action',
          // fixed: 'right',
          align: 'center',
          width: 85,
          scopedSlots: { customRender: 'action' }
        }
      ]
      return data
    },
    innerColumns() {
      const data = [
        { title: this.$t('table.cleanErrorRate'), dataIndex: 'clean_error_rate', align: 'center' },
        { title: this.$t('table.cleanErrorNum'), dataIndex: 'clean_error_number', align: 'center' },
        { title: this.$t('table.checkErrorRate'), dataIndex: 'check_error_rate', align: 'center' },
        { title: this.$t('table.checkErrorNum'), dataIndex: 'check_error_number', align: 'center' },
        { title: this.$t('table.check2ErrorNum'), dataIndex: 'check_two_error_number', align: 'center' },
        { title: this.$t('table.check2ErrorRate'), dataIndex: 'check_two_error_rate', align: 'center' },
        { title: this.$t('table.spotCheckErrorNum'), dataIndex: 'spot_check_error_number', align: 'center' },
        { title: this.$t('table.cleanTime'), dataIndex: 'clean_time', align: 'center' },
        { title: this.$t('table.cleanSpeed'), dataIndex: 'clean_speed', align: 'center' },
        { title: this.$t('table.checkTime'), dataIndex: 'check_time', align: 'center' },
        { title: this.$t('table.checkSpeed'), dataIndex: 'check_speed', align: 'center' },
        { title: this.$t('table.check2time'), dataIndex: 'check_two_time', align: 'center' },
        { title: this.$t('table.check2Speed'), dataIndex: 'check_two_speed', align: 'center' },
        { title: this.$t('table.spotTime'), dataIndex: 'spot_check_time', align: 'center' },
        { title: this.$t('table.cleanReduce'), dataIndex: 'clean_error_number_change', align: 'center' },
        { title: this.$t('table.checkReduce'), dataIndex: 'check_error_number_change', align: 'center' },
        { title: this.$t('table.difCoe'), dataIndex: 'degree_of_difficulty', align: 'center' },
        { title: this.$t('table.conAmo'), dataIndex: 'converted_quantity', align: 'center' }
      ]
      return data
    }
  },
  mounted() {
    this.getGroupList()
    this.getRuleNameFun()
  },
  methods: {
    moment,
    // 不可选日期区间
    disabledDate(current) {
      return current && current > moment().endOf('month') || current < moment(new Date()).subtract(6, 'months').endOf('month')
    },
    // 数据搜索
    handleSubmit(first) {
      if (first) this.paginations.current = 1
      const param = {
        ...this.formInline,
        page: this.paginations.current,
        number: this.paginations.pageSize
      }
      getProjectAllData(param).then(res => {
        if (res.errcode === 0) {
          this.data = res.data.data_list
          this.paginations.total = res.data.total
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 分页改变
    handleTableChange(obj) {
      this.paginations.current = obj.current
      if (obj.showSizeChanger) {
        this.paginations.pageSize = obj.pageSize
      }
      this.handleSubmit()
    },
    // 选择组
    handleGroupChange(value) {
      this.formInline.group_name = value
    },
    // 获取组
    getGroupList() {
      apiCommonServer.getGroupList(1).then(res => {
        if (res.errcode === 0) {
          this.groupList = res.data.group_list
        }
      })
    },
    // 选择月
    handleMonthChange(dates, dateStrings) {
      this.formInline.start_time = dateStrings[0]
      this.formInline.end_time = dateStrings[1]
    },
    // 获取项目大类
    getRuleNameFun() {
      getRuleName({ is_all: 'T' }).then(res => {
        if (res.errcode === 0) {
          this.ruleList = res.data.data_list
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 展示编辑页
    showEdit(item) {
      if (item.plan_date) item.plan_date = moment(item.plan_date)
      this.$refs.rEditCmp.formData = item
      this.$refs.rEditCmp.openDraw = true
    },
    // 长连接接受后台信息
    wsMessage(res, t) {
      this.exportLoading = false
      if (res.errcode === 0) {
        const _url = `${process.env.VUE_APP_API_BASE_URL2}/project/download_file/?file_name=${res.data.file_name}&access_token=${t}`
        const link = document.createElement('a')
        link.setAttribute('download', res.data.file_name)
        link.setAttribute('href', _url)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } else {
        this.$message.error(res.errmsg)
      }
      closeWebsocket()
    },
    wsError(msg) {
      this.exportLoading = false
      this.$message.error(msg.errmsg)
    },
    // 下载全部数据
    downAllData() {
      this.exportLoading = true
      const param = {
        ...this.formInline,
        url: 'download_project_all_data'
      }
      sendWebsocket(param, this.wsMessage, this.wsError)
    }
  }
}
</script>
<style>
  .relative_td .ant-table-expanded-row td {
    position: relative;
    overflow: hidden;
    padding: 0;
  }
</style>
<style scoped lang="less">
  .export_loading {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.4);
    z-index: 10;
  }
</style>
