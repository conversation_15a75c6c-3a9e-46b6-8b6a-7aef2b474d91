<template>
  <form-drawer :title="drawerTitle" :visible="openDraw" @closeDrawer="openDraw = false">
    <a-form :form="form" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item :label="$t('table.projectCat')">
            <a-input
              v-decorator="[
                'name',
                {
                  rules: [{ required: true }], initialValue: formData ? formData.name : ''
                },
              ]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('table.headI')">
            <a-input
              v-decorator="[
                'name_begin',
                {
                  rules: [], initialValue: formData ? formData.name_begin : ''
                },
              ]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('table.midI')">
            <a-input
              v-decorator="[
                'name_content',
                {
                  rules: [], initialValue: formData ? formData.name_content : ''
                },
              ]"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1,
      }"
    >
      <a-button :style="{ marginRight: '8px' }" @click="openDraw = false">
        {{ $t('buttons.cancel') }}
      </a-button>
      <a-button type="primary" @click="onSubmit">
        {{ $t('buttons.define') }}
      </a-button>
    </div>
  </form-drawer>
</template>

<script>
import FormDrawer from '@/components/formDrawer'
import { addRuleName, changeRuleName } from '@/services/sys'
export default {
  name: 'ConfigForm',
  components: { FormDrawer },
  data() {
    return {
      form: this.$form.createForm(this),
      drawerTitle: '',
      openDraw: false,
      formData: null
    }
  },
  methods: {
    // 提交
    onSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          if (this.formData && this.formData.id) {
            const param = Object.assign({ change_rule_id: this.formData.id }, values)
            changeRuleName(param).then(res => {
              if (res.errcode === 0) {
                this.$message.success(res.errmsg)
                this.openDraw = false
                this.$emit('submitSuccess', false)
              } else {
                this.$message.error(res.errmsg)
              }
            })
            return
          }
          addRuleName(values).then(res => {
            if (res.errcode === 0) {
              this.$message.success(res.errmsg)
              this.openDraw = false
              this.$emit('submitSuccess', true)
            } else {
              this.$message.error(res.errmsg)
            }
          })
        }
      })
    }
  }
}
</script>

<style>

</style>
