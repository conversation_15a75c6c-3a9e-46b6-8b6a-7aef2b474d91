<template>
  <div class="config_mag">
    <a-card>
      <div style="margin-bottom: 20px">
        <a-form-model
          layout="inline"
          :model="formInline"
          @submit="handleSubmit(true)"
          @submit.native.prevent
        >
          <a-form-model-item :label="$t('table.projectCat')">
            <a-input v-model="formInline.name" />
          </a-form-model-item>
          <a-form-model-item :label="$t('table.headI')">
            <a-input v-model="formInline.name_begin" />
          </a-form-model-item>
          <a-form-model-item>
            <a-button type="primary" html-type="submit">{{ $t('buttons.search') }} </a-button>
          </a-form-model-item>
          <a-form-model-item>
            <a-button type="primary" @click="AddData">{{ $t("buttons.add") }}</a-button>
          </a-form-model-item>
        </a-form-model>
      </div>
      <a-table
        class="my_scroll_table"
        :scroll="{ x: true }"
        :columns="columns"
        :data-source="data"
        :loading="tableLoading"
        bordered
        row-key="id"
        :pagination="paginations"
        @change="handleTableChange"
      >
        <span slot="action" slot-scope="text, record">
          <a @click="editDataFun(record)">{{ $t('buttons.edit') }}</a>
          <a-divider type="vertical" />
          <a-popconfirm
            :title="$t('confirm.del')"
            @confirm="() => delDataFun(record)"
          >
            <a href="javascript:;">{{ $t('buttons.delete') }}</a>
          </a-popconfirm>
        </span>
      </a-table>
    </a-card>
    <ConfigForm ref="configFormRef" @submitSuccess="handleSuccess" />
  </div>
</template>

<script>
import { getRuleName, delRuleName } from '@/services/sys'
import ConfigForm from './components/configForm'
export default {
  name: 'ConfigMag',
  components: { ConfigForm },
  data() {
    return {
      formInline: {
        name: '',
        name_begin: ''
      },
      tableLoading: false,
      data: [],
      paginations: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30', '50'],
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => {
          return this.$t('table.total') + total + this.$t('table.strip')
        },
        total: 0
      }
    }
  },
  computed: {
    columns() {
      return [
        { title: this.$t('table.id'), dataIndex: 'id', align: 'center', width: 85 },
        { title: this.$t('table.projectCat'), dataIndex: 'name', align: 'center' },
        { title: this.$t('table.headI'), dataIndex: 'name_begin', align: 'center' },
        { title: this.$t('table.midI'), dataIndex: 'name_content', align: 'center' },
        {
          title: this.$t('table.action'),
          key: 'action',
          fixed: 'right',
          width: 155,
          scopedSlots: { customRender: 'action' }
        }
      ]
    }
  },
  mounted() {
    this.handleSubmit(true)
  },
  methods: {
    // 搜索
    handleSubmit(first) {
      if (first) this.paginations.current = 1
      this.tableLoading = true
      const param = {
        ...this.formInline,
        page: this.paginations.current,
        number: this.paginations.pageSize
      }
      getRuleName(param).then(res => {
        this.tableLoading = false
        if (res.errcode === 0) {
          this.data = res.data.data_list
          this.paginations.total = res.data.total
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 分页改变
    handleTableChange(obj) {
      this.paginations.current = obj.current
      if (obj.showSizeChanger) {
        this.paginations.pageSize = obj.pageSize
      }
      this.handleSubmit()
    },
    // 新增
    AddData() {
      this.$refs.configFormRef.drawerTitle = this.$t('buttons.add')
      this.$refs.configFormRef.formData = null
      this.$refs.configFormRef.openDraw = true
    },
    // 编辑
    editDataFun(obj) {
      this.$refs.configFormRef.drawerTitle = this.$t('buttons.edit')
      this.$refs.configFormRef.formData = obj
      this.$refs.configFormRef.openDraw = true
    },
    // 删除
    delDataFun(obj) {
      delRuleName({ change_rule_id: obj.id }).then(res => {
        if (res.errcode === 0) {
          this.$message.success(res.errmsg)
          this.handleSubmit(true)
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 新增或编辑成功
    handleSuccess(flag) {
      this.handleSubmit(true)
    }
  }
}
</script>

<style>

</style>
