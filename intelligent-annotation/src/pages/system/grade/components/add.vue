<template>
  <form-drawer :title="title" :visible="openDraw" @closeDrawer="handleDrawerClose">
    <a-form :form="form" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item :label="$t('skuForm.type')">
            <a-select
              v-decorator="[
                'grade_type',
                {
                  rules: [{ required: true, message: $t('rules.required') }], initialValue: formData && formData.grade_type ? formData.grade_type : 1
                },
              ]"
              :disabled="true"
            >
              <a-select-option v-for="item in dictList" :key="item.id" :value="item.id">{{ item.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item :label="$t('table.cn_name')">
            <a-input
              v-decorator="[
                'name',
                {
                  rules: [{ required: true, message: $t('rules.required') }, { max: 100, trigger: 'change', message: $t('rules.maxLength', { number: 100 }) }],
                  initialValue: formData ? formData.name : ''
                },
              ]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item :label="$t('table.en_name')">
            <a-input
              v-decorator="[
                'name_en',
                {
                  rules: [{ required: true, message: $t('rules.required') }, { max: 100, trigger: 'change', message: $t('rules.maxLength', { number: 100 }) }],
                  initialValue: formData ? formData.name_en : ''
                },
              ]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item :label="$t('label.level')">
            <a-input
              v-decorator="[
                'grade',
                {
                  rules: [{ required: true, message: $t('rules.required')}],
                  initialValue: formData ? formData.grade : ''
                },
              ]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('table.sort')">
            <a-input-number
              v-decorator="[
                'sort',
                {
                  rules: [{ required: true, message: $t('rules.required') }],
                  initialValue: formData ? formData.sort : ''
                },
              ]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('table.day_action')">
            <number-group
              v-decorator="[
                'days',
                {
                  rules: [
                    { required: true, message: $t('rules.required') },
                    { trigger: 'change', validator: validatorErrNum }
                  ],
                  initialValue: formData ? formData.days : null
                }
              ]"
              :min="0"
              :precision="0"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('table.errRate')">
            <number-group
              v-decorator="[
                'errs',
                {
                  rules: [
                    { required: true, message: $t('rules.required') },
                    { trigger: 'change', validator: validatorErrNum }
                  ],
                  initialValue: formData ? formData.errs : null
                }
              ]"
              :min="0"
              :max="100"
              :precision="2"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('table.month_all')">
            <number-group
              v-decorator="[
                'months',
                {
                  rules: [
                    { required: true, message: $t('rules.required') },
                    { trigger: 'change', validator: validatorErrNum }
                  ],
                  initialValue: formData ? formData.months : null
                }
              ]"
              :min="0"
              :precision="0"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1,
      }"
    >
      <a-button :style="{ marginRight: '8px' }" @click="openDraw = false">
        {{ $t('buttons.cancel') }}
      </a-button>
      <a-button type="primary" @click="onSubmit">
        {{ $t('buttons.define') }}
      </a-button>
    </div>
  </form-drawer>
</template>

<script>
import FormDrawer from '@/components/formDrawer'
import NumberGroup from './number-group.vue'
import { changeGrade, addGrade } from '@/services/sys'
export default {
  name: 'GradeAdd',
  components: { FormDrawer, NumberGroup },
  props: {
    dictList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      formData: null,
      openDraw: false,
      form: this.$form.createForm(this)
    }
  },
  computed: {
    title() {
      return this.formData && this.formData.dictionary_id ? this.$t('drawer.edit', { name: this.$t('label.level') }) : this.$t('drawer.add', { name: this.$t('label.level') })
    }
  },
  methods: {
    validatorErrNum(rule, value, callback) {
      if (value.start === null || value.end === null) {
        callback(this.$t('rules.numG1'))
      }
      if (value.end < value.start) {
        callback(this.$t('rules.numG2'))
      }
      callback()
    },
    // 提交数据
    onSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          if (this.formData && this.formData.grade_id) {
            const param = {
              ...values,
              grade_id: this.formData.grade_id,
              avg_min: values.days.start,
              avg_max: values.days.end,
              error_rate_min: values.errs.start,
              error_rate_max: values.errs.end,
              number_min: values.months.start,
              number_max: values.months.end
            }

            delete param.days
            delete param.months
            delete param.errs
            changeGrade(param).then(res => {
              this.apiResFun(res)
            })
          } else {
            const param = {
              ...values,
              avg_min: values.days.start,
              avg_max: values.days.end,
              error_rate_min: values.errs.start,
              error_rate_max: values.errs.end,
              number_min: values.months.start,
              number_max: values.months.end
            }

            delete param.days
            delete param.months
            delete param.errs
            addGrade(param).then(res => {
              this.apiResFun(res)
            })
          }
          this.handleDrawerClose()
        }
      })
    },
    apiResFun(res) {
      if (res.errcode === 0) {
        this.$message.success(res.errmsg)
        this.handleDrawerClose()
        this.$emit('submitSuccess')
      } else {
        this.$message.error(res.errmsg)
      }
    },
    // 直接关闭侧拉框
    handleDrawerClose() {
      this.formData = null
      this.openDraw = false
    }
  }
}
</script>

<style>

</style>
