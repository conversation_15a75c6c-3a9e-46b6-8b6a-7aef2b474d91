<template>
  <div class="num_group">
    <a-input-number :value="start" :max="max" :min="min" :precision="precision" @change="onChange($event, 'start')" />
    ~
    <a-input-number :value="end" :max="max" :min="min" :precision="precision" @change="onChange($event, 'end')" />
  </div>
</template>

<script>
export default {
  name: 'NumberGroup',
  props: {
    value: {
      type: Object,
      default: () => {
        return {}
      }
    },
    max: {
      type: Number,
      default: 10000 * 10000
    },
    min: {
      type: Number,
      default: null
    },
    precision: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      start: null,
      end: null
    }
  },
  mounted() {
    this.start = this.value ? this.value.start : null
    this.end = this.value ? this.value.end : null
  },
  methods: {
    onChange(value, type) {
      if (type === 'start') {
        this.start = value
      } else {
        this.end = value
      }
      this.$emit('change', {
        start: this.start,
        end: this.end
      })
    }
  }
}
</script>

<style lang="less" scoped>

</style>
