<template>
  <div class="new-page">
    <a-card>
      <div style="margin-bottom: 20px">
        <a-button type="primary" @click="addDataFun()">{{ $t('buttons.add') }} </a-button>
      </div>
      <a-table
        :columns="columns"
        :data-source="data"
        :loading="tableLoading"
        bordered
        row-key="grade_id"
        :pagination="false"
      >
        <span slot="num" slot-scope="text, record, index">
          {{ index + 1 }}
        </span>
        <span slot="gradeType" slot-scope="text, record">{{ fDictList(record.grade_type) }}</span>
        <span slot="avg" slot-scope="text, record">{{ `${record.avg_min}-${record.avg_max}` }}</span>
        <span slot="errRat" slot-scope="text, record">{{ `${record.error_rate_min}-${record.error_rate_max}` }}</span>
        <span slot="number" slot-scope="text, record">{{ `${record.number_min}-${record.number_max}` }}</span>
        <span slot="action" slot-scope="text, record">
          <a @click="addDataFun(record)">{{ $t('buttons.edit') }}</a>
          <a-divider type="vertical" />
          <a-popconfirm
            :title="$t('confirm.del')"
            @confirm="() => delDataFun(record.grade_id)"
          >
            <a href="javascript:;">{{ $t('buttons.delete') }}</a>
          </a-popconfirm>
        </span>
      </a-table>
      <add-cmp ref="addCmp" :dict-list="dictList" @submitSuccess="getData" />
    </a-card>
  </div>
</template>

<script>
import { getGrades, changeGrade } from '@/services/sys'
import AddCmp from './components/add.vue'
export default {
  name: 'Grade',
  components: { AddCmp },
  data() {
    return {
      tableLoading: true,
      data: [],
      dictList: [{
        id: 1,
        name: '清洗'
      }]
    }
  },
  computed: {
    columns() {
      const data = [
        { title: this.$t('table.id'), key: 'grade_id', align: 'center', width: 85, scopedSlots: { customRender: 'num' }},
        { title: this.$t('skuForm.type'), key: 'gradeType', scopedSlots: { customRender: 'gradeType' }},
        { title: this.$t('table.cn_name'), dataIndex: 'name' },
        { title: this.$t('table.en_name'), dataIndex: 'name_en' },
        { title: this.$t('label.level'), dataIndex: 'grade', align: 'center' },
        { title: this.$t('table.sort'), dataIndex: 'sort', align: 'center' },
        { title: this.$t('table.day_action'), key: 'avg', align: 'center', scopedSlots: { customRender: 'avg' }},
        { title: this.$t('table.errRate'), key: 'errRat', align: 'center', width: 100,
          scopedSlots: { customRender: 'errRat' }
        },
        { title: this.$t('table.month_all'), key: 'number', align: 'center', scopedSlots: { customRender: 'number' }},
        {
          title: this.$t('table.action'),
          key: 'action',
          fixed: 'right',
          width: 155,
          scopedSlots: { customRender: 'action' },
          align: 'center'
        }
      ]
      return data
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    fDictList(id) {
      const it = this.dictList.find(item => item.id === id)
      return it ? it.name : ''
    },
    getData() {
      this.tableLoading = true
      getGrades({}).then(res => {
        this.tableLoading = false
        if (res.errcode === 0) {
          this.data = res.data
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    addDataFun(item) {
      if (item) {
        const it = {
          ...item,
          days: {
            start: item.avg_min,
            end: item.avg_max
          },
          errs: {
            start: item.error_rate_min,
            end: item.error_rate_max
          },
          months: {
            start: item.number_min,
            end: item.number_max
          }
        }
        this.$refs.addCmp.formData = it
      }
      this.$refs.addCmp.openDraw = true
    },
    // 删除
    delDataFun(id) {
      changeGrade({ grade_id: id, is_delete: 'T' }).then(res => {
        if (res.errcode === 0) {
          this.$message.success(res.errmsg)
          this.getData()
        } else {
          this.$message.error(res.errmsg)
        }
      })
    }
  }
}
</script>

<style>

</style>
