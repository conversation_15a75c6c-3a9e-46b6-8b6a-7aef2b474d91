<template>
  <form-drawer :title="drawerTitle" :visible="openDraw" @closeDrawer="handleDrawerClose">
    <a-form :form="form" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item :label="$t('label.fullname')">
            <a-input
              v-decorator="[
                'full_name',
                {
                  rules: [{ required: true, message: $t('rules.name') }], initialValue: formData ? formData.full_name : ''
                },
              ]"
              placeholder="Please enter user name"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('label.role')">
            <a-select
              v-decorator="[
                'role_id',
                {
                  rules: [{ required: true, message: $t('rules.role') }], initialValue: formData ? formData.role_id : ''
                },
              ]"
            >
              <a-select-option v-for="item in roleList" :key="item.role_id" :value="item.role_id">{{ item.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('label.team')">
            <a-select
              v-decorator="[
                'team_id',
                {
                  rules: [], initialValue: formData ? formData.team_id : ''
                },
              ]"
              @change="teamChange"
            >
              <a-select-option v-for="item in teamList" :key="item.team_id" :value="item.team_id">{{ item.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('label.group')">
            <a-select
              v-decorator="[
                'group_id',
                {
                  rules: [], initialValue: formData ? formData.group_id : ''
                },
              ]"
            >
              <a-select-option v-for="item in groupList" :key="item.group_id" :value="item.group_id">{{ item.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1,
      }"
    >
      <a-button :style="{ marginRight: '8px' }" @click="openDraw = false">
        {{ $t('buttons.cancel') }}
      </a-button>
      <a-button type="primary" @click="onSubmit">
        {{ $t('buttons.define') }}
      </a-button>
    </div>
  </form-drawer>
</template>

<script>
import { changeUserData } from '@/services/user'
import { apiCommonServer } from '@/services'
import FormDrawer from '@/components/formDrawer'
export default {
  name: 'AccountEdit',
  components: { FormDrawer },
  props: {
    drawerTitle: {
      type: String,
      default: ''
    },
    roleList: {
      type: Array,
      default: () => {
        return []
      }
    },
    teamList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      formData: null,
      openDraw: false,
      form: this.$form.createForm(this),
      groupList: []
    }
  },
  watch: {
    openDraw(val) {
      if (val && this.formData.team_id) {
        this.getGroupListFun(this.formData.team_id)
      } else {
        this.groupList = []
      }
    }
  },
  methods: {
    // 提交数据
    onSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          const param = Object.assign({ user_id: this.formData.user_id }, values)
          changeUserData(param).then(res => {
            if (res.errcode === 0) {
              this.$message.success(res.errmsg)
              this.openDraw = false
              this.$emit('submitSuccess')
            } else {
              this.$message.error(res.errmsg)
            }
          })
        }
      })
    },
    // 直接关闭侧拉框
    handleDrawerClose() {
      this.openDraw = false
    },
    // 团队下拉改变时
    teamChange(value) {
      this.form.setFieldsValue({ group_id: '' })
      this.getGroupListFun(value)
    },
    // 组列表
    getGroupListFun(teamId) {
      apiCommonServer.getGroupList(teamId).then(res => {
        if (res.errcode === 0) {
          this.groupList = res.data.group_list
        }
      })
    }
  }
}
</script>

<style>

</style>
