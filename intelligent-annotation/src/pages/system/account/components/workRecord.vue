<template>
  <form-drawer :title="cardTitle" :visible="openDraw" @closeDrawer="handleDrawerClose">
    <div class="work_record">
      <a-timeline>
        <a-timeline-item v-for="(it, i) in list" :key="i" :color="it.list.length > 0 ? 'green' : 'gray'">
          <a-icon slot="dot" type="clock-circle-o" style="font-size: 16px;" />
          <p class="time_title">{{ it.time }}</p>
          <div v-for="(it2, i2) in it.list" :key="i2" class="content_list">
            <div class="content">
              <span v-if="it2.dir_name" class="title">{{ it2.dir_name }}</span>
              <span class="action">{{ it2.action_type }}</span>
              <span class="text">{{ it2.created_at }}</span>
            </div>
            <div v-if="it2.description" class="description">{{ it2.description }}</div>
          </div>
        </a-timeline-item>
      </a-timeline>
    </div>
    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1,
      }"
    >
      <a-button @click="openDraw = false">
        {{ $t('buttons.close') }}
      </a-button>
    </div>
  </form-drawer>
</template>

<script>
import FormDrawer from '@/components/formDrawer'
import { actionList } from '@/services/user'
export default {
  name: 'WorkRecord',
  components: { FormDrawer },
  props: {
    userName: {
      type: String,
      default: ''
    },
    userId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      openDraw: false,
      list: []
    }
  },
  computed: {
    cardTitle() {
      return `${this.$t('contents.dayWork')}-${this.userName}`
    }
  },
  watch: {
    userId(val) {
      if (val) {
        this.getData(val)
      } else {
        this.list = []
      }
    }
  },
  methods: {
    // 关闭侧拉框
    handleDrawerClose() {
      this.openDraw = false
    },
    // 获取用户当天工作记录
    getData(userId) {
      actionList({ user_id: userId }).then(res => {
        if (res.errcode === 0) {
          this.list = res.data
        } else {
          this.$message.error(res.errmsg)
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
  .work_record {
    .time_title {
      color: #333333;
      font-weight: bold;
    }
    .content_list {
      padding: 10px 15px;
      cursor: pointer;
      &:hover {
        background: #f4f4;
      }

      flex-wrap: wrap;
      .content {
        color: #333333;
        display: flex;
        justify-content: space-between;
        .title {
          flex: 1;
          padding-right: 15px;
        }
        .action {
          padding-right: 15px;
        }

      }

      .description {
        flex: 1;
        display: block;
        padding-top: 10px;
        color: #acacac;
      }
    }
  }
</style>
