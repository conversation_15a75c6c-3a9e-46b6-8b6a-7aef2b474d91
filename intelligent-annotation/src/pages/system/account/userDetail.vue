<template>
  <div class="user_detail">
    <a-card :title="cardTitle">
      <a-timeline mode="alternate">
        <a-timeline-item v-for="(it, i) in list" :key="i" :color="it.list.length > 0 ? 'green' : 'gray'">
          <a-icon slot="dot" type="clock-circle-o" style="font-size: 16px;" />
          <p class="time_title">{{ it.time }}</p>
          <p v-for="(it2, i2) in it.list" :key="i2" class="content_list">
            <span v-if="it2.dir_name" class="title">{{ it2.dir_name }}</span>
            <span class="title">{{ it2.action_type }}</span>
            <span class="text">{{ it2.created_at }}</span>
          </p>
        </a-timeline-item>
      </a-timeline>
    </a-card>
  </div>
</template>

<script>
import { actionList } from '@/services/user'
export default {
  name: 'UserDetail',
  data() {
    return {
      username: '',
      list: []
    }
  },
  computed: {
    cardTitle() {
      return `${this.$t('contents.dayWork')}-${this.username}`
    }
  },
  mounted() {
    const userID = this.$route.query.user_id
    this.username = this.$route.query.full_name
    if (userID) {
      this.getData(userID)
    }
  },
  methods: {
    // 获取用户当天工作记录
    getData(userId) {
      actionList({ user_id: userId }).then(res => {
        if (res.errcode === 0) {
          this.list = res.data
        } else {
          this.$message.error(res.errmsg)
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
  .user_detail {
    .time_title {
      color: #333333;
      font-weight: bold;
    }
    .content_list {
      .title {
        margin-right: 20px;
      }
    }
  }
</style>
