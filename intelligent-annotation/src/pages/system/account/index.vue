<template>
  <div class="new-page">
    <a-card>
      <div style="margin-bottom: 20px">
        <a-form-model
          layout="inline"
          :model="formInline"
          @submit="handleSubmit(true)"
          @submit.native.prevent
        >
          <a-form-model-item :label="$t('label.mobile')">
            <a-input v-model="formInline.mobile" />
          </a-form-model-item>
          <a-form-model-item :label="$t('table.name')">
            <a-input v-model="formInline.full_name" />
          </a-form-model-item>
          <a-form-model-item :label="$t('label.role')">
            <a-select style="width: 150px" :value="formInline.role_id" allow-clear @change="handleRoleChange">
              <a-select-option v-for="it in roleList" :key="it.role_id" :value="it.role_id">
                {{ it.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item :label="$t('label.team')">
            <a-select style="width: 150px" :value="formInline.team_id" allow-clear @change="handleTeamChange">
              <a-select-option v-for="it in teamList" :key="it.team_id" :value="it.team_id">
                {{ it.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item :label="$t('label.group')">
            <a-select style="width: 150px" :value="formInline.group_id" allow-clear @change="handleGroupChange">
              <a-select-option v-for="it in groupList" :key="it.group_id" :value="it.group_id">
                {{ it.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item :label="$t('table.isEnable')">
            <a-select style="width: 150px" :value="formInline.is_enable" allow-clear @change="handleEnableChange">
              <a-select-option key="T" value="T">{{ $t('dic.yes') }}</a-select-option>
              <a-select-option key="F" value="F">{{ $t('dic.no') }}</a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item>
            <a-button type="primary" html-type="submit">{{ $t('buttons.search') }} </a-button>
          </a-form-model-item>
        </a-form-model>
      </div>
      <a-table
        class="my_scroll_table"
        :scroll="{ x: true }"
        :columns="columns"
        :data-source="data"
        :loading="tableLoading"
        bordered
        row-key="user_id"
        :pagination="paginations"
        @change="handleTableChange"
      >
        <template slot="fullName" slot-scope="text, record">
          <a href="javascript:void(0);" @click="goDetail(record)">{{ record.full_name }}</a>
        </template>
        <template slot="grade" slot-scope="text, record">
          <level-icon style="margin: -15px 0 -15px;" :grade="record.grade" />
        </template>
        <template slot="enable" slot-scope="text, record">
          <a-switch :checked-children="$t('buttons.enable')" :un-checked-children="$t('buttons.prohibit')" :checked="record.is_enable" @click="userEnableChange(record)" />
        </template>
        <span slot="action" slot-scope="text, record">
          <a @click="editDataFun(record)">{{ $t('buttons.edit') }}</a>
          <a-divider type="vertical" />
          <!-- <a @click="delDataFun(record)">{{ $t('buttons.delete') }}</a> -->
          <a-popconfirm
            :title="$t('confirm.del')"
            @confirm="() => delDataFun(record)"
          >
            <a href="javascript:;">{{ $t('buttons.delete') }}</a>
          </a-popconfirm>
        </span>
      </a-table>
    </a-card>
    <edit-drawer ref="editDrawer" :role-list="roleList" :team-list="teamList" :drawer-title="drawerTitle" @submitSuccess="handleSubmit" />
    <work-record ref="workRecord" :user-id="activeUserId" :user-name="activeUserName" />
  </div>
</template>

<script>
import { getUseList, changeUserData } from '@/services/user'
import { apiCommonServer } from '@/services'
import EditDrawer from './components/editDrawer'
import WorkRecord from './components/workRecord'
import LevelIcon from '@/components/levelIcon'
export default {
  name: 'Account',
  components: { EditDrawer, WorkRecord, LevelIcon },
  data() {
    return {
      drawerTitle: '',
      formInline: {
        mobile: null,
        full_name: null,
        role_id: null,
        team_id: null,
        group_id: null,
        is_enable: null
      },
      tableLoading: true,
      paginations: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30', '50'],
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => {
          return this.$t('table.total') + total + this.$t('table.strip')
        },
        total: 0
      },
      data: [],
      roleList: [],
      teamList: [],
      groupList: [],
      activeUserId: null,
      activeUserName: ''
    }
  },
  computed: {
    columns() {
      const data = [
        { title: this.$t('table.id'), dataIndex: 'user_id', align: 'center', width: 85 },
        { title: this.$t('table.mobile'), dataIndex: 'mobile', align: 'right' },
        { title: this.$t('table.name'), key: 'full_name', scopedSlots: { customRender: 'fullName' }},
        { title: this.$t('table.roleName'), dataIndex: 'role' },
        { title: this.$t('table.team'), dataIndex: 'team' },
        { title: this.$t('table.group'), dataIndex: 'group' },
        { title: this.$t('label.level'), key: 'grade', scopedSlots: { customRender: 'grade' }, align: 'center' },
        { title: this.$t('table.isEnable'), key: 'is_enable', scopedSlots: { customRender: 'enable' }, align: 'center' },
        { title: this.$t('table.creatTime'), dataIndex: 'joined_since', align: 'center', width: 185 },
        {
          title: this.$t('table.action'),
          key: 'action',
          fixed: 'right',
          width: 155,
          scopedSlots: { customRender: 'action' }
        }
      ]
      return data
    }
  },
  created() {
    this.handleSubmit()
    this.getRoleListFun()
    this.getTeamListFun()
  },
  methods: {
    // 角色列表
    getRoleListFun() {
      apiCommonServer.getRoleList().then(res => {
        if (res.errcode === 0) {
          this.roleList = res.data.role_list
        }
      })
    },
    // 团队列表
    getTeamListFun() {
      apiCommonServer.getTeamList().then(res => {
        if (res.errcode === 0) {
          this.teamList = res.data.team_list
        }
      })
    },
    // 组列表
    getGroupListFun() {
      apiCommonServer.getGroupList(this.formInline.team_id).then(res => {
        if (res.errcode === 0) {
          this.groupList = res.data.group_list
        }
      })
    },
    // 选择角色
    handleRoleChange(value) {
      this.formInline.role_id = value
    },
    // 选择团队
    handleTeamChange(value) {
      this.formInline.team_id = value
      this.formInline.group_id = null
      if (value) {
        this.getGroupListFun()
      } else {
        this.groupList = []
      }
    },
    // 选择组
    handleGroupChange(value) {
      this.formInline.group_id = value
    },
    handleEnableChange(value) {
      this.formInline.is_enable = value
    },
    // 搜索
    handleSubmit(first) {
      if (first) this.paginations.current = 1
      this.tableLoading = true
      const param = {
        ...this.formInline,
        page: this.paginations.current,
        number: this.paginations.pageSize
      }
      getUseList(param).then((res) => {
        this.tableLoading = false
        if (res.errcode === 0) {
          this.data = res.data.data_list
          this.paginations.total = res.data.total
        }
      })
    },
    // 分页改变
    handleTableChange(obj) {
      this.paginations.current = obj.current
      if (obj.showSizeChanger) {
        this.paginations.pageSize = obj.pageSize
      }
      this.handleSubmit()
    },
    // 启用状态修改
    userEnableChange(item) {
      const param = {
        user_id: item.user_id,
        is_enable: item.is_enable ? 'F' : 'T'
      }
      changeUserData(param).then(res => {
        if (res.errcode === 0) {
          this.$message.success(res.errmsg)
          this.handleSubmit()
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 编辑
    editDataFun(item) {
      this.drawerTitle = this.$t('drawer.user')
      this.$refs.editDrawer.formData = item
      this.$refs.editDrawer.openDraw = true
    },
    // 删除
    delDataFun(item) {
      const param = {
        user_id: item.user_id,
        is_delete: 'T'
      }
      changeUserData(param).then(res => {
        if (res.errcode === 0) {
          this.$message.success(res.errmsg)
          this.handleSubmit()
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 跳转详情
    goDetail(item) {
      this.activeUserId = item.user_id
      this.activeUserName = item.full_name
      this.$refs.workRecord.openDraw = true
      // this.$router.push({
      //   path: 'user_detail',
      //   query: {
      //     user_id: item.user_id,
      //     full_name: item.full_name
      //   }
      // })
    }
  }
}
</script>

<style scoped lang="less"></style>
