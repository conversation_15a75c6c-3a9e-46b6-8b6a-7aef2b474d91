<template>
  <form-drawer :title="formData ? $t('buttons.editTeam') : $t('buttons.addteam')" :visible="openDraw" @closeDrawer="handleDrawerClose">
    <a-form :form="form" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item :label="$t('label.name')">
            <a-input
              v-decorator="[
                'name',
                {
                  rules: [{ required: true, message: $t('rules.name') }], initialValue: formData ? formData.name : ''
                },
              ]"
              :placeholder="$t('rules.name')"
              autocomplete="off"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1,
      }"
    >
      <a-button :style="{ marginRight: '8px' }" @click="handleDrawerClose">
        {{ $t('buttons.cancel') }}
      </a-button>
      <a-button type="primary" @click="onSubmit">
        {{ $t('buttons.define') }}
      </a-button>
    </div>
  </form-drawer>
</template>

<script>
import FormDrawer from '@/components/formDrawer'
import { addTeam, changeTeam } from '@/services/team'
export default {
  name: 'AddTeam',
  components: { FormDrawer },
  data() {
    return {
      openDraw: false,
      formData: null,
      form: this.$form.createForm(this)
    }
  },
  methods: {
    handleDrawerClose() {
      this.formData = null
      this.openDraw = false
    },
    onSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          if (this.formData) {
            const param = Object.assign({ team_id: this.formData.team_id }, values)
            changeTeam(param).then(res => {
              this.apiResFun(res)
            })
          } else {
            addTeam(values).then(res => {
              this.apiResFun(res)
            })
          }
        }
      })
    },
    apiResFun(res) {
      if (res.errcode === 0) {
        this.$message.success(res.errmsg)
        this.handleDrawerClose()
        this.$emit('refreshData')
      } else {
        this.$message.error(res.errmsg)
      }
    }
  }
}
</script>

<style>

</style>
