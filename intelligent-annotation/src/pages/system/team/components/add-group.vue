<template>
  <form-drawer :title="formData ? $t('buttons.editGroup') : $t('buttons.addGroup')" :visible="openDraw" @closeDrawer="handleDrawerClose">
    <a-form :form="form" layout="vertical">
      <a-row :gutter="16">
        <a-col v-if="teamList.length > 0" :span="24">
          <a-form-item :label="$t('table.team')">
            <a-select
              v-decorator="[
                'team_id',
                {
                  rules: [{ required: true, message: $t('rules.checkTeam') }], initialValue: formData ? formData.team_id : ''
                },
              ]"
            >
              <a-select-option v-for="item in teamList" :key="item.team_id" :value="item.team_id">{{ item.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item :label="$t('label.name')">
            <a-input
              v-decorator="[
                'name',
                {
                  rules: [{ required: true, message: $t('rules.name') }], initialValue: formData ? formData.name : ''
                },
              ]"
              :placeholder="$t('rules.name')"
              autocomplete="off"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1,
      }"
    >
      <a-button :style="{ marginRight: '8px' }" @click="handleDrawerClose">
        {{ $t('buttons.cancel') }}
      </a-button>
      <a-button type="primary" @click="onSubmit">
        {{ $t('buttons.define') }}
      </a-button>
    </div>
  </form-drawer>
</template>

<script>
import FormDrawer from '@/components/formDrawer'
import { addGroup, changeGroup } from '@/services/team'
export default {
  name: 'AddGroup',
  components: { FormDrawer },
  data() {
    return {
      openDraw: false,
      formData: null,
      teamList: [],
      form: this.$form.createForm(this)
    }
  },
  methods: {
    handleDrawerClose() {
      this.formData = null
      this.teamList = []
      this.openDraw = false
    },
    onSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          if (this.formData) {
            const param = Object.assign({ group_id: this.formData.group_id }, values)
            changeGroup(param).then(res => {
              this.apiResFun(res)
            })
          } else {
            addGroup(values).then(res => {
              this.apiResFun(res)
            })
          }
        }
      })
    },
    apiResFun(res) {
      if (res.errcode === 0) {
        this.$message.success(res.errmsg)
        this.handleDrawerClose()
        this.$emit('refreshData')
      } else {
        this.$message.error(res.errmsg)
      }
    }
  }
}
</script>

<style>

</style>
