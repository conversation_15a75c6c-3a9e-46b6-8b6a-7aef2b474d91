<template>
  <div class="new-page">
    <a-card>
      <div style="margin-bottom: 20px">
        <a-button type="primary" @click="addTeamFun">{{ $t('buttons.addteam') }} </a-button>
        <a-button style="margin-left: 15px;" type="primary" @click="addGroupFun">{{ $t('buttons.addGroup') }} </a-button>
      </div>
      <a-table
        :columns="columns"
        :data-source="data"
        :loading="tableLoading"
        bordered
        row-key="team_id"
        :pagination="false"
        :expanded-row-keys="expandedRowKeys"
        @expand="handleExpand"
      >
        <span slot="action" slot-scope="text, record">
          <a @click="editDataFun(record)">{{ $t('buttons.edit') }}</a>
          <a-divider type="vertical" />
          <a-popconfirm
            :title="$t('confirm.del')"
            @confirm="() => delDataFun(record)"
          >
            <a href="javascript:;">{{ $t('buttons.delete') }}</a>
          </a-popconfirm>
        </span>
        <a-table
          slot="expandedRowRender"
          :columns="innerColumns"
          :data-source="innerData"
          row-key="group_id"
          :pagination="false"
        >
          <span slot="innerAction" slot-scope="text, record">
            <a @click="editDataFun(record)">{{ $t('buttons.edit') }}</a>
            <a-divider type="vertical" />
            <a-popconfirm
              :title="$t('confirm.del')"
              @confirm="() => delDataFun(record)"
            >
              <a href="javascript:;">{{ $t('buttons.delete') }}</a>
            </a-popconfirm>
          </span>
        </a-table>
      </a-table>
    </a-card>
    <add-team ref="addteamCmp" @refreshData="handleSubmit" />
    <add-group ref="addGroupCmp" @refreshData="handleSubmit" />
  </div>
</template>

<script>
import { getTeamList, getGroupList, changeTeam, changeGroup } from '@/services/team'
import AddTeam from './components/add-team'
import AddGroup from './components/add-group'
export default {
  name: 'Team',
  components: { AddTeam, AddGroup },
  data() {
    return {
      tableLoading: true,
      paginations: {
        current: 1,
        pageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => {
          return this.$t('table.total') + total + this.$t('table.strip')
        },
        total: 0
      },
      data: [],
      expandedRowKeys: [],
      innerData: []
    }
  },
  computed: {
    columns() {
      const data = [
        { title: this.$t('table.id'), dataIndex: 'team_id', align: 'center', width: 85 },
        { title: this.$t('table.teamName'), dataIndex: 'name' },
        { title: this.$t('table.updateTime'), dataIndex: 'updated_at', align: 'center', width: 185 },
        { title: this.$t('table.creatTime'), dataIndex: 'created_at', align: 'center', width: 185 },
        {
          title: this.$t('table.action'),
          key: 'action',
          width: 155,
          scopedSlots: { customRender: 'action' }
        }
      ]
      return data
    },
    innerColumns() {
      const data = [
        // { title: this.$t('table.id'), dataIndex: 'group_id' },
        { title: this.$t('table.groupName'), dataIndex: 'name' },
        { title: this.$t('table.updateTime'), dataIndex: 'updated_at', align: 'center', width: 185 },
        { title: this.$t('table.creatTime'), dataIndex: 'created_at', align: 'center', width: 185 },
        {
          title: this.$t('table.action'),
          key: 'innerAction',
          width: 155,
          scopedSlots: { customRender: 'innerAction' }
        }
      ]
      return data
    }
  },
  created() {
    this.handleSubmit()
  },
  methods: {
    // 搜索
    handleSubmit() {
      this.tableLoading = true
      getTeamList({}).then((res) => {
        this.tableLoading = false
        if (res.errcode === 0) {
          this.data = res.data.team_list
          if (this.expandedRowKeys.length > 0) {
            this.handleExpand(true, { team_id: this.expandedRowKeys[0] })
          }
        }
      })
    },
    handleExpand(expanded, record) {
      if (expanded) {
        getGroupList({ team_id: record.team_id }).then((res) => {
          if (res.errcode === 0) {
            this.expandedRowKeys = [record.team_id]
            this.innerData = res.data.group_list
          }
        })
      } else {
        this.expandedRowKeys = []
      }
    },
    // 新增团队
    addTeamFun() {
      this.$refs.addteamCmp.openDraw = true
    },
    // 新增组
    addGroupFun() {
      this.$refs.addGroupCmp.teamList = this.data
      this.$refs.addGroupCmp.openDraw = true
    },
    // 编辑团队或组
    editDataFun(item) {
      console.log(item)
      if (item.team_id) {
        this.$refs.addteamCmp.formData = item
        this.$refs.addteamCmp.openDraw = true
      } else {
        this.$refs.addGroupCmp.formData = item
        this.$refs.addGroupCmp.openDraw = true
      }
    },
    // 删除团队或组
    delDataFun(item) {
      if (item.team_id) {
        changeTeam({ team_id: item.team_id, is_delete: 'T' }).then(res => {
          if (res.errcode === 0) {
            this.$message.success(res.errmsg)
            this.handleSubmit()
          } else {
            this.$message.error(res.errmsg)
          }
        })
      } else {
        changeGroup({ group_id: item.group_id, is_delete: 'T' }).then(res => {
          if (res.errcode === 0) {
            this.$message.success(res.errmsg)
            this.handleSubmit()
          } else {
            this.$message.error(res.errmsg)
          }
        })
      }
    }
  }
}
</script>

<style scoped lang="less"></style>
