<template>
  <a-card>
    <a-form-model
      layout="inline"
      :model="formInline"
      @submit="handleSubmit(true)"
      @submit.native.prevent
    >
      <a-form-model-item :label="$t('label.projectName')">
        <a-input v-model="formInline.project_name" />
      </a-form-model-item>
      <a-form-model-item :label="$t('label.proId')">
        <a-input v-model="formInline.project_id" />
      </a-form-model-item>
      <a-form-model-item :label="$t('label.taskType')">
        <a-select style="width: 150px" allow-clear :value="formInline.task_flow_type" @change="handleTaskChange">
          <a-select-option v-for="it in taskList" :key="it.value" :value="it.value">
            {{ it.name }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item :label="$t('table.oper_people')">
        <a-input v-model="formInline.operator_name" />
      </a-form-model-item>
      <a-form-model-item :label="$t('label.team')">
        <a-select style="width: 150px" :value="formInline.team_id" allow-clear @change="handleTeamChange">
          <a-select-option v-for="it in teamList" :key="it.team_id" :value="it.team_id">
            {{ it.name }}
          </a-select-option>
        </a-select>
      </a-form-model-item>
      <a-form-model-item>
        <a-button type="primary" html-type="submit">{{ $t('buttons.search') }} </a-button>
      </a-form-model-item>
    </a-form-model>
    <div style="margin-top: 24px;">
      <a-table
        class="my_scroll_table"
        :scroll="{ x: true }"
        :columns="columns"
        :data-source="data"
        :loading="tableLoading"
        bordered
        row-key="dir_id"
        :pagination="false"
      >
        <span slot="taskType" slot-scope="text, record">{{ getTaskTypeName(record.task_flow_type) }}</span>
        <span slot="action" slot-scope="text, record">
          <a @click="redDataFun(record)">{{ $t('buttons.reduction') }}</a>
        </span>
      </a-table>
    </div>
    <input-select-pop-confirm
      :give-up-visible.sync="giveUpVisible"
      :title="$t('confirm.reduction')"
      @ok="handleGiveUp"
      @cancel="handleGiveUpCancel"
    />
  </a-card>
</template>

<script>
import { apiCommonServer } from '@/services'
import { adminGetStopDir, adminGiveupDir } from '@/services/project'
import InputSelectPopConfirm from '@/components/popConfirm/InputSelectPopConfirm.vue'
export default {
  name: 'Reduction',
  components: { InputSelectPopConfirm },
  data() {
    return {
      formInline: {
        project_name: '',
        project_id: '',
        task_flow_type: '',
        operator_name: '',
        team_id: ''
      },
      teamList: [],
      data: [],
      tableLoading: true,
      optionRowData: null,
      giveUpVisible: false,
      giveUpNumber: 0, // 剩余放弃的次数
      reasonRequireError: false,
      giveUpReasonOption: '' // 用户选择的选项
    }
  },
  computed: {
    taskList() {
      return [
        { name: this.$t('dic.clean'), value: 10 },
        { name: this.$t('dic.check'), value: 11 }
        // { name: this.$t('dic.sum'), value: 12 }
      ]
    },
    columns() {
      return [
        { title: this.$t('table.dirName'), dataIndex: 'dir_name', align: 'center' },
        { title: this.$t('table.project'), dataIndex: 'project_name', align: 'center' },
        { title: this.$t('table.projectId'), dataIndex: 'project_id', align: 'center' },
        { title: this.$t('table.taskType'), dataIndex: 'task_flow_type', align: 'center', scopedSlots: { customRender: 'taskType' }},
        { title: this.$t('table.oper_people'), dataIndex: 'operator_name', align: 'center' },
        { title: this.$t('table.team'), dataIndex: 'team', align: 'center' },
        { title: this.$t('table.pauseTime'), dataIndex: 'stop_time', align: 'center' },
        {
          title: this.$t('table.action'),
          key: 'action',
          fixed: 'right',
          width: 155,
          scopedSlots: { customRender: 'action' }
        }
      ]
    }
  },
  watch: {
    giveUpVisible(val) {
      console.log('val:giveUpVisible', val)
    }
  },
  mounted() {
    this.getTeamListFun()
    this.handleSubmit()
  },
  methods: {
    getTaskTypeName(id) {
      const it = this.taskList.find(item => item.value === id)
      return it ? it.name : ''
    },
    getTeamListFun() {
      apiCommonServer.getTeamList().then(res => {
        if (res.errcode === 0) {
          this.teamList = res.data.team_list
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    handleTeamChange(value) {
      this.formInline.team_id = value
    },
    handleTaskChange(value) {
      this.formInline.task_flow_type = value
    },
    handleSubmit() {
      this.tableLoading = true
      adminGetStopDir(this.formInline).then(res => {
        this.tableLoading = false
        if (res.errcode === 0) {
          this.data = res.data.dir_list
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    redDataFun(item) {
      console.log('还原', item)
      // 显示还原确认对话框
      this.giveUpVisible = true
      this.optionRowData = item // 选中的数据
    },
    handleGiveUp(reason) {
      // 确认还原
      adminGiveupDir({ dir_id: this.optionRowData.dir_id, reason }).then(res => {
        if (res.errcode === 0) {
          this.handleSubmit()
          this.giveUpVisible = false
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    handleGiveUpCancel() {
      console.log('关闭model')
      // 取消还原
      this.giveUpVisible = false
    }
  }
}
</script>
