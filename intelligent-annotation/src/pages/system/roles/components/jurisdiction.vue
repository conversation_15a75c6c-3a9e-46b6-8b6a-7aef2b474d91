<template>
  <form-drawer :title="$t('drawer.roleAuth')" :visible="openDraw" @closeDrawer="handleDrawerClose">
    <a-form :form="form" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item :label="$t('label.auth')">
            <a-tree
              v-model="checkedKeys"
              checkable
              default-expand-all
              check-strictly
              :tree-data="treeData"
              :replace-fields="replaceFields"
              @check="handleCheck"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1,
      }"
    >
      <a-button :style="{ marginRight: '8px' }" @click="handleDrawerClose">
        {{ $t('buttons.cancel') }}
      </a-button>
      <a-button type="primary" @click="onSubmit">
        {{ $t('buttons.define') }}
      </a-button>
    </div>
  </form-drawer>
</template>

<script>
import FormDrawer from '@/components/formDrawer'
import { apiCommonServer } from '@/services'
import { changeRoleJurisdiction } from '@/services/roles'
import { returnListKeys, arrayToTree } from '@/utils/util'
export default {
  name: 'Jurisdiction',
  components: { FormDrawer },
  data() {
    return {
      openDraw: false,
      formData: null,
      form: this.$form.createForm(this),
      replaceFields: {
        children: 'children',
        title: 'name',
        key: 'jurisdiction_id'
      },
      treeData: [],
      checkedKeys: [],
      hasCheck: false // 是否做了修改动作
    }
  },
  watch: {
    openDraw(val) {
      if (val && this.formData) {
        this.checkedKeys = returnListKeys(this.formData.jurisdiction, 'jurisdiction_id')
      }
    }
  },
  created() {
    this.getData()
  },
  methods: {
    handleDrawerClose() {
      this.hasCheck = false
      this.openDraw = false
    },
    // 获取菜单列表
    getData() {
      apiCommonServer.getJurisdictionList().then(res => {
        if (res.errcode === 0) {
          const _data = res.data.jurisdiction_list
          this.treeData = arrayToTree(_data, '', 'jurisdiction_id', 'parent_id')
        }
      })
    },
    onSubmit() {
      if (this.hasCheck) {
        changeRoleJurisdiction({ role_id: this.formData.role_id, jurisdiction_list: this.checkedKeys.checked }).then(res => {
          if (res.errcode === 0) {
            this.$message.success(res.errmsg)
            this.handleDrawerClose()
            this.$emit('submitSuccess')
          } else {
            this.$message.error(res.errmsg)
          }
        })
      } else {
        this.handleDrawerClose()
      }
    },
    // 点击复选框
    handleCheck() {
      this.hasCheck = true
    }
  }
}
</script>

<style>

</style>
