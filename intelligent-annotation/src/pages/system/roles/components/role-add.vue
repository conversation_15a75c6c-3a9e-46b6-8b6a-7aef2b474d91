<template>
  <form-drawer :title="$t('drawer.role')" :visible="openDraw" @closeDrawer="handleDrawerClose">
    <a-form :form="form" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item :label="$t('label.name')">
            <a-input
              v-decorator="[
                'name',
                {
                  rules: [{ required: true, message: $t('rules.name') }], initialValue: formData ? formData.name : ''
                },
              ]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item :label="$t('label.level')">
            <a-select
              v-decorator="[
                'level',
                {
                  rules: [{ required: true, message: $t('rules.level') }], initialValue: formData ? formData.level : '111'
                },
              ]"
            >
              <a-select-option v-for="item in levelList" :key="item.id" :value="item.id">{{ item.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1,
      }"
    >
      <a-button :style="{ marginRight: '8px' }" @click="openDraw = false">
        {{ $t('buttons.cancel') }}
      </a-button>
      <a-button type="primary" @click="onSubmit">
        {{ $t('buttons.define') }}
      </a-button>
    </div>
  </form-drawer>
</template>

<script>
import { addRole, changeRole } from '@/services/roles'
import FormDrawer from '@/components/formDrawer'
export default {
  name: 'RoleAdd',
  components: { FormDrawer },
  data() {
    return {
      formData: null,
      openDraw: false,
      form: this.$form.createForm(this),
      levelList: [
        { id: 111, name: '标注员' },
        { id: 222, name: '项目管理员' },
        { id: 333, name: '管理员' },
        { id: 999, name: '超级用户' }
      ]
    }
  },
  methods: {
    // 提交数据
    onSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          if (this.formData && this.formData.role_id) {
            const param = Object.assign({ role_id: this.formData.role_id }, values)
            changeRole(param).then(res => {
              this.apiResFun(res)
            })
          } else {
            addRole(values).then(res => {
              this.apiResFun(res)
            })
          }
        }
      })
    },
    apiResFun(res) {
      if (res.errcode === 0) {
        this.$message.success(res.errmsg)
        this.handleDrawerClose()
        this.$emit('submitSuccess')
      } else {
        this.$message.error(res.errmsg)
      }
    },
    // 直接关闭侧拉框
    handleDrawerClose() {
      this.formData = null
      this.openDraw = false
    }
  }
}
</script>

<style>

</style>
