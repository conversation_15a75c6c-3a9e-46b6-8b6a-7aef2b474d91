<template>
  <div class="new-page">
    <a-card>
      <div style="margin-bottom: 20px">
        <a-button type="primary" @click="addRoleFun">{{ $t('buttons.add') }} </a-button>
      </div>
      <a-table
        :columns="columns"
        :data-source="data"
        :loading="tableLoading"
        bordered
        row-key="role_id"
        :pagination="false"
      >
        <span slot="num" slot-scope="text, record, index">
          {{ index + 1 }}
        </span>
        <span slot="action" slot-scope="text, record">
          <a @click="authDataFun(record)">{{ $t('buttons.auth') }}</a>
          <a-divider type="vertical" />
          <a @click="addRoleFun(record)">{{ $t('buttons.edit') }}</a>
          <a-divider type="vertical" />
          <a-popconfirm
            :title="$t('confirm.del')"
            @confirm="() => delDataFun(record)"
          >
            <a href="javascript:;">{{ $t('buttons.delete') }}</a>
          </a-popconfirm>
        </span>
      </a-table>
    </a-card>
    <jurisdiction ref="jdnDrawer" @submitSuccess="handleSubmit" />
    <role-add ref="roleAddCmp" @submitSuccess="handleSubmit" />
  </div>
</template>

<script>
import { getRoleList, changeRole } from '@/services/roles'
import Jurisdiction from './components/jurisdiction'
import RoleAdd from './components/role-add'
export default {
  name: 'Roles',
  components: { Jurisdiction, RoleAdd },
  data() {
    return {
      tableLoading: true,
      paginations: {
        current: 1,
        pageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => {
          return this.$t('table.total') + total + this.$t('table.strip')
        },
        total: 0
      },
      data: []
    }
  },
  computed: {
    columns() {
      const data = [
        { title: this.$t('table.id'), align: 'center', width: 85, scopedSlots: { customRender: 'num' }},
        // { title: this.$t('table.id'), dataIndex: 'role_id', align: 'center', width: 85 },
        { title: this.$t('table.roleName'), dataIndex: 'name' },
        { title: this.$t('table.updateTime'), dataIndex: 'updated_at', align: 'center', width: 185 },
        { title: this.$t('table.creatTime'), dataIndex: 'created_at', align: 'center', width: 185 },
        {
          title: this.$t('table.action'),
          key: 'action',
          fixed: 'right',
          align: 'center',
          width: 200,
          scopedSlots: { customRender: 'action' }
        }
      ]
      return data
    }
  },
  created() {
    this.handleSubmit()
  },
  methods: {
    // 搜索
    handleSubmit() {
      this.tableLoading = true
      getRoleList({}).then((res) => {
        this.tableLoading = false
        if (res.errcode === 0) {
          this.data = res.data.role_list
        }
      })
    },
    // 授权
    authDataFun(item) {
      this.$refs.jdnDrawer.formData = item
      this.$refs.jdnDrawer.openDraw = true
    },
    // 新增and编辑
    addRoleFun(item) {
      if (item) {
        this.$refs.roleAddCmp.formData = item
      }
      this.$refs.roleAddCmp.openDraw = true
    },
    // 删除
    delDataFun(item) {
      changeRole({ role_id: item.role_id, is_delete: 'T' }).then(res => {
        if (res.errcode === 0) {
          this.$message.success(res.errmsg)
          this.handleSubmit()
        } else {
          this.$message.error(res.errmsg)
        }
      })
    }
  }
}
</script>

<style scoped lang="less"></style>
