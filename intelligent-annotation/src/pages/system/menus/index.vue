<template>
  <div class="new-page">
    <a-card>
      <div style="margin-bottom: 20px">
        <a-button type="primary" @click="addDataFun">{{ $t('buttons.add') }} </a-button>
      </div>
      <a-table
        :columns="columns"
        :data-source="data"
        :loading="tableLoading"
        bordered
        row-key="jurisdiction_id"
        :pagination="false"
      >

        <span slot="action" slot-scope="text, record">
          <a @click="addDataFun(record)">{{ $t('buttons.edit') }}</a>
          <a-divider type="vertical" />
          <a-popconfirm
            :title="$t('confirm.del')"
            @confirm="() => delDataFun(record)"
          >
            <a href="javascript:;">{{ $t('buttons.delete') }}</a>
          </a-popconfirm>
        </span>
      </a-table>
    </a-card>
    <add-draw ref="addComp" :menu-list="data" @submitSuccess="handleSubmit" />
  </div>
</template>

<script>
import { getJurisdictionList, changeJurisdiction } from '@/services/jurisdiction'
import AddDraw from './components/add_draw'
import { arrayToTree } from '@/utils/util'
export default {
  name: 'Menus',
  components: { AddDraw },
  data() {
    return {
      tableLoading: true,
      paginations: {
        current: 1,
        pageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => {
          return this.$t('table.total') + total + this.$t('table.strip')
        },
        total: 0
      },
      data: []
    }
  },
  computed: {
    columns() {
      const data = [
        // { title: this.$t('table.id'), dataIndex: 'jurisdiction_id' },
        { title: this.$t('table.menuName'), dataIndex: 'name' },
        { title: this.$t('table.updateTime'), dataIndex: 'updated_at', align: 'center', width: 185 },
        { title: this.$t('table.creatTime'), dataIndex: 'created_at', align: 'center', width: 185 },
        {
          title: this.$t('table.action'),
          key: 'action',
          fixed: 'right',
          width: 155,
          scopedSlots: { customRender: 'action' }
        }
      ]
      return data
    }
  },
  created() {
    this.handleSubmit()
  },
  methods: {
    // 搜索
    handleSubmit() {
      this.tableLoading = true
      getJurisdictionList({}).then((res) => {
        this.tableLoading = false
        if (res.errcode === 0) {
          const _data = res.data.jurisdiction_list
          if (_data && _data.length > 0) {
            this.data = arrayToTree(_data, '', 'jurisdiction_id', 'parent_id')
          }
        }
      })
    },
    // 新增and编辑
    addDataFun(item) {
      if (item) {
        this.$refs.addComp.formData = item
      }
      this.$refs.addComp.openDraw = true
    },
    // 删除
    delDataFun(item) {
      changeJurisdiction({ jurisdiction_id: item.jurisdiction_id, is_delete: 'T' }).then(res => {
        if (res.errcode === 0) {
          this.$message.success(res.errmsg)
          this.handleSubmit()
        } else {
          this.$message.error(res.errmsg)
        }
      })
    }
  }
}
</script>

<style scoped lang="less"></style>
