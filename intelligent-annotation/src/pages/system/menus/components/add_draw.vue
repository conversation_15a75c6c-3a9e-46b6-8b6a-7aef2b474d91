<template>
  <form-drawer :title="$t('drawer.menu')" :visible="openDraw" @closeDrawer="handleDrawerClose">
    <a-form :form="form" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item :label="$t('label.name')">
            <a-input
              v-decorator="[
                'name',
                {
                  rules: [{ required: true, message: $t('rules.name') }], initialValue: formData ? formData.name : ''
                },
              ]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('label.parentMenu')">
            <a-tree-select
              v-decorator="[
                'parent_id',
                {
                  rules: [], initialValue: formData ? formData.parent_id : ''
                },
              ]"
              style="width: 100%"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              :tree-data="menuList"
              allow-clear
              :replace-fields="replaceFields"
              tree-default-expand-all
              :get-popup-container="
                triggerNode => {
                  return triggerNode.parentNode || document.body;
                }
              "
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('label.apiName')">
            <a-input
              v-decorator="[
                'url_name',
                {
                  rules: [], initialValue: formData ? formData.url_name : ''
                },
              ]"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1,
      }"
    >
      <a-button :style="{ marginRight: '8px' }" @click="openDraw = false">
        {{ $t('buttons.cancel') }}
      </a-button>
      <a-button type="primary" @click="onSubmit">
        {{ $t('buttons.define') }}
      </a-button>
    </div>
  </form-drawer>
</template>

<script>
import { addJurisdiction, changeJurisdiction } from '@/services/jurisdiction'
import FormDrawer from '@/components/formDrawer'
export default {
  name: 'MenuAdd',
  components: { FormDrawer },
  props: {
    menuList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      formData: null,
      openDraw: false,
      form: this.$form.createForm(this),
      replaceFields: {
        children: 'children',
        title: 'name',
        key: 'jurisdiction_id',
        value: 'jurisdiction_id'
      }
    }
  },
  methods: {
    // 提交数据
    onSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          if (this.formData && this.formData.jurisdiction_id) {
            const param = Object.assign({ jurisdiction_id: this.formData.jurisdiction_id }, values)
            changeJurisdiction(param).then(res => {
              this.apiResFun(res)
            })
          } else {
            addJurisdiction(values).then(res => {
              this.apiResFun(res)
            })
          }
        }
      })
    },
    apiResFun(res) {
      if (res.errcode === 0) {
        this.$message.success(res.errmsg)
        this.handleDrawerClose()
        this.$emit('submitSuccess')
      } else {
        this.$message.error(res.errmsg)
      }
    },
    // 直接关闭侧拉框
    handleDrawerClose() {
      this.formData = null
      this.openDraw = false
    }
  }
}
</script>

<style>

</style>
