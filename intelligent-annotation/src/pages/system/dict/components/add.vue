<template>
  <form-drawer :title="title" :visible="openDraw" @closeDrawer="handleDrawerClose">
    <a-form :form="form" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item :label="$t('label.parentDict')">
            <a-select
              v-decorator="[
                'parent_id',
                {
                  rules: [{ required: true, message: $t('rules.required') }], initialValue: formData && formData.parent_id ? formData.parent_id : 0
                },
              ]"
              :disabled="formData && formData.parent_id === 0"
            >
              <a-select-option v-for="item in dictList" :key="item.dictionary_id" :value="item.dictionary_id">{{ lang === 'CN' ? item.name : item.name_en }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item :label="$t('table.cn_name')">
            <a-input
              v-decorator="[
                'name',
                {
                  rules: [{ required: true, message: $t('rules.required') }, { max: 100, trigger: 'change', message: $t('rules.maxLength', { number: 100 }) }], initialValue: formData ? formData.name : ''
                },
              ]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item :label="$t('table.en_name')">
            <a-input
              v-decorator="[
                'name_en',
                {
                  rules: [{ required: true, message: $t('rules.required') }, { max: 200, trigger: 'change', message: $t('rules.maxLength', { number: 200 }) }], initialValue: formData ? formData.name_en : ''
                },
              ]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item :label="$t('table.sort')">
            <a-input-number
              v-decorator="[
                'sort',
                {
                  rules: [{ required: true, message: $t('rules.required') }], initialValue: formData ? formData.sort : 1
                },
              ]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item :label="$t('table.dec')">
            <a-textarea
              v-decorator="[
                'remark',
                {
                  rules: [{ max: 200, trigger: 'change', message: $t('rules.maxLength', { number: 200 }) }], initialValue: formData ? formData.remark : ''
                },
              ]"
              :rows="3"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1,
      }"
    >
      <a-button :style="{ marginRight: '8px' }" @click="openDraw = false">
        {{ $t('buttons.cancel') }}
      </a-button>
      <a-button type="primary" @click="onSubmit">
        {{ $t('buttons.define') }}
      </a-button>
    </div>
  </form-drawer>
</template>

<script>
import { addDicts, changeDicts } from '@/services/sys'
import { mapState } from 'vuex'
import FormDrawer from '@/components/formDrawer'
export default {
  name: 'DictAdd',
  components: { FormDrawer },
  props: {
    dictList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      formData: null,
      openDraw: false,
      form: this.$form.createForm(this)
    }
  },
  computed: {
    ...mapState('setting', ['lang']),
    title() {
      return this.formData && this.formData.dictionary_id ? this.$t('drawer.edit', { name: this.$t('drawer.dict') }) : this.$t('drawer.add', { name: this.$t('drawer.dict') })
    }
  },
  methods: {
    // 提交数据
    onSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          if (this.formData && this.formData.dictionary_id) {
            const param = Object.assign({ dictionary_id: this.formData.dictionary_id }, values)
            changeDicts(param).then(res => {
              this.apiResFun(res)
            })
          } else {
            addDicts(values).then(res => {
              this.apiResFun(res)
            })
          }
        }
      })
    },
    apiResFun(res) {
      if (res.errcode === 0) {
        this.$message.success(res.errmsg)
        this.handleDrawerClose()
        this.$emit('submitSuccess')
      } else {
        this.$message.error(res.errmsg)
      }
    },
    // 直接关闭侧拉框
    handleDrawerClose() {
      this.formData = null
      this.openDraw = false
    }
  }
}
</script>

<style>

</style>
