<template>
  <div class="new-page">
    <a-card>
      <div style="margin-bottom: 20px">
        <a-button type="primary" @click="addDataFun">{{ $t('buttons.add') }} </a-button>
      </div>
      <a-table
        :columns="columns"
        :data-source="data"
        :loading="tableLoading"
        bordered
        row-key="dictionary_id"
        :pagination="false"
      >
        <template slot="isEnable" slot-scope="text, record">
          <a-switch v-if="record.parent_id !== 0" :checked-children="$t('buttons.enable')" :un-checked-children="$t('buttons.prohibit')" :checked="record.is_enable" @click="enableChange(record)" />
        </template>
        <span slot="action" slot-scope="text, record">
          <a @click="addDataFun(record)">{{ $t('buttons.edit') }}</a>
          <a-divider type="vertical" />
          <a-popconfirm
            :title="$t('confirm.del')"
            @confirm="() => delDataFun(record.dictionary_id)"
          >
            <a href="javascript:;">{{ $t('buttons.delete') }}</a>
          </a-popconfirm>
        </span>
      </a-table>
      <add-cmp ref="addCmp" :dict-list="dictList" @submitSuccess="getData" />
    </a-card>
  </div>
</template>

<script>
import { getDicts, changeDicts } from '@/services/sys'
import AddCmp from './components/add.vue'
export default {
  name: 'Dict',
  components: { AddCmp },
  data() {
    return {
      tableLoading: true,
      data: [],
      dictList: []
    }
  },
  computed: {
    columns() {
      const data = [
        // { title: this.$t('table.id'), dataIndex: 'jurisdiction_id' },
        { title: this.$t('table.cn_name'), dataIndex: 'name' },
        { title: this.$t('table.en_name'), dataIndex: 'name_en', align: 'center' },
        { title: this.$t('table.sort'), dataIndex: 'sort', align: 'center' },
        { title: this.$t('table.dec'), dataIndex: 'remark', align: 'center' },
        { title: this.$t('table.isEnable'), dataIndex: 'is_enable', align: 'center', width: 100,
          scopedSlots: { customRender: 'isEnable' }
        },
        {
          title: this.$t('table.action'),
          key: 'action',
          fixed: 'right',
          width: 155,
          scopedSlots: { customRender: 'action' },
          align: 'center'
        }
      ]
      return data
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      this.tableLoading = true
      getDicts({}).then(res => {
        this.tableLoading = false
        if (res.errcode === 0) {
          this.data = res.data.data_list
          this.getFistDict(res.data.data_list)
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    getFistDict(arr) {
      const list = [{ dictionary_id: 0, name: '一级字典', name_en: 'one-level dict' }]
      arr.forEach(element => {
        const item = { ...element }
        delete item.children
        list.push(item)
      })
      this.dictList = list
    },
    addDataFun(item) {
      if (item) {
        this.$refs.addCmp.formData = item
      }
      this.$refs.addCmp.openDraw = true
    },
    // 删除
    delDataFun(id) {
      changeDicts({ dictionary_id: id, is_delete: 'T' }).then(res => {
        if (res.errcode === 0) {
          this.$message.success(res.errmsg)
          this.getData()
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 启用禁用
    enableChange(item) {
      changeDicts({ dictionary_id: item.dictionary_id, is_enable: item.is_enable ? 'F' : 'T' }).then(res => {
        if (res.errcode === 0) {
          this.$message.success(res.errmsg)
          this.getData()
        } else {
          this.$message.error(res.errmsg)
        }
      })
    }
  }
}
</script>

<style>

</style>
