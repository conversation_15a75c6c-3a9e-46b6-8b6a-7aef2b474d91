<template>
  <div class="new-page">
    <a-card>
      <div style="margin-bottom: 20px">
        <a-form-model
          layout="inline"
          :model="formInline"
          @submit="handleSubmit(true)"
          @submit.native.prevent
        >
          <a-form-model-item :label="$t('label.proId')">
            <a-input v-model="formInline.project_id" />
          </a-form-model-item>
          <a-form-model-item :label="$t('label.taskType')">
            <a-select style="width: 150px" allow-clear @change="handleTaskChange">
              <a-select-option v-for="it in taskList" :key="it.value" :value="it.value">
                {{ it.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item :label="$t('label.team')">
            <a-select allow-clear style="width: 150px" :value="formInline.team_id" @change="handleTeamChange">
              <a-select-option v-for="it in teamList" :key="it.team_id" :value="it.team_id">
                {{ it.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item :label="$t('table.oper_people')">
            <a-input v-model="formInline.operator_id" />
          </a-form-model-item>
          <a-form-model-item :label="$t('table.restore_user')">
            <a-input v-model="formInline.user_name" />
          </a-form-model-item>
          <a-form-model-item :label="$t('table.oper_time')">
            <a-range-picker
              :allow-clear="false"
              format="YYYY-MM-DD HH:mm"
              :default-value="[moment().startOf('day'), moment().endOf('day')]"
              :show-time="{ format: 'HH:mm', defaultValue:[moment('01:00', 'HH:mm'), moment('23:59', 'HH:mm')] }"
              @change="onTimeChange"
              @ok="handleSubmit(true)"
            />
          </a-form-model-item>
          <a-form-model-item>
            <a-button type="primary" html-type="submit">{{ $t("buttons.search") }}</a-button>
          </a-form-model-item>
          <a-form-model-item>
            <a-button type="primary" :disabled="data.length === 0" @click="downAllData">{{ $t("buttons.download") }}</a-button>
          </a-form-model-item>
        </a-form-model>
      </div>
      <a-table
        class="my_scroll_table"
        :scroll="{ x: true }"
        :columns="columns"
        :data-source="data"
        :loading="tableLoading"
        bordered
        size="small"
        :row-key="(record, index) => index"
        :pagination="paginations"
        @change="handleTableChange"
      >
        <template slot="img" slot-scope="text">
          <div
            class="hove_img_box"
            @mouseenter="showHoverImg($event, text)"
            @mouseleave="hideHoverImg"
            @click="showBigImg(text)"
          >
            <a-avatar :size="25" shape="square" icon="picture" :src="text" />
          </div>
        </template>
        <template slot="imgBig" slot-scope="text, record">
          <div
            class="hove_img_box"
            @mouseenter="showHoverImg($event, subUrl(record))"
            @mouseleave="hideHoverImg"
            @click="showBigImg(subUrl(record))"
          >
            <a-avatar :size="25" shape="square" icon="picture" :src="subUrl(record)" />
          </div>
        </template>
        <div slot="errType" slot-scope="text, record">
          <EditableCell :text="lang === 'CN' ? record.error_type : record.error_type_en" :select-value="record.error_type_id" :lang="lang" :list="dictList" @change="onCellChange(record, 'error_type_id', $event)" />
        </div>
        <!-- <span slot="action" slot-scope="text, record">
          <a @click="editFun(record)">{{ $t('buttons.edit') }}</a>
        </span> -->
      </a-table>
    </a-card>
    <div v-show="hoverImg" class="hove_img" :style="{ top: hoverImgY + 'px', left: hoverImgX + 'px' }">
      <img alt="example" :src="hoverImgUrl">
    </div>
    <a-modal :visible="previewVisible" :footer="null" @cancel="closeBigImg">
      <img alt="example" style="width: 100%;max-height: 500px;object-fit: contain;" :src="previewImage">
    </a-modal>
  </div>
</template>

<script>
import { apiCommonServer } from '@/services'
import { getRestoreLog, changeErrorType } from '@/services/kpi'
import { mapState } from 'vuex'
import moment from 'moment'
import EditableCell from './components/editableCell'
import { sendWebsocket, closeWebsocket } from '@/utils/websocket'

export default {
  name: 'Restore',
  components: { EditableCell },
  data() {
    return {
      formInline: {
        project_id: null,
        task_flow_type: [],
        team_id: null,
        operator_id: '',
        user_name: '',
        start_time: moment().startOf('day').format('YYYY-MM-DD HH:mm'),
        end_time: moment().endOf('day').format('YYYY-MM-DD HH:mm')
      },
      tableLoading: false,
      paginations: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30', '50'],
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => {
          return this.$t('table.total') + total + this.$t('table.strip')
        },
        total: 0
      },
      data: [],
      dictList: [],
      teamList: [],
      previewVisible: false,
      previewImage: '',
      hoverImg: false,
      hoverImgUrl: '',
      hoverImgX: 0,
      hoverImgY: 0
    }
  },
  computed: {
    ...mapState('setting', ['lang']),
    taskList() {
      const data = [
        { name: this.$t('dic.clean'), value: 10 },
        { name: this.$t('dic.check'), value: 11 }
      ]
      return data
    },
    columns() {
      const data = [
        { title: this.$t('table.date'), dataIndex: 'restore_date' },
        { title: this.$t('table.taskId'), dataIndex: 'project_id' },
        { title: this.$t('table.name'), dataIndex: 'operator_name' },
        { title: this.$t('table.dirName'), dataIndex: 'dir_name' },
        { title: this.$t('table.taskType'), dataIndex: 'task_flow_name', align: 'center' },
        { title: this.$t('table.teamName'), dataIndex: 'team_name' },
        { title: this.$t('table.dir_number'), dataIndex: 'number' },
        { title: this.$t('table.stop_number'), dataIndex: 'stop_number' },
        { title: this.$t('table.start_time'), dataIndex: 'start_time', align: 'center' },
        { title: this.$t('table.stop_time'), dataIndex: 'stop_time', align: 'center' },
        { title: this.$t('table.restore_user'), dataIndex: 'user_name', align: 'center' },
        { title: this.$t('table.restore_reason'), dataIndex: 'description' },
        { title: this.$t('table.restore_time'), dataIndex: 'create_at', align: 'center' }
        // {
        //   title: this.$t('table.action'),
        //   key: 'action',
        //   fixed: 'right',
        //   width: 155,
        //   scopedSlots: { customRender: 'action' },
        //   align: 'center'
        // }
      ]
      return data
    }
  },
  mounted() {
    this.getTeamList()
  },
  methods: {
    moment,
    // 返回大图url
    subUrl(item) {
      const url = this.lang === 'CN' ? item.box_url : item.box_url_en
      if (url) {
        return url.slice(0, url.indexOf('?'))
      }
      return url
    },
    // 获取团队
    getTeamList() {
      apiCommonServer.getTeamList().then(res => {
        if (res.errcode === 0) {
          this.teamList = res.data.team_list
        }
      })
    },
    // 团队改变
    handleTeamChange(value) {
      this.formInline.team_id = value
    },
    // 任务类型
    handleTaskChange(value) {
      this.formInline.task_flow_type = value
    },
    // 时间选择
    onTimeChange(date, dataStr) {
      this.formInline.start_time = dataStr[0]
      this.formInline.end_time = dataStr[1]
    },
    handleSubmit(first) {
      this.tableLoading = true
      if (first) this.paginations.current = 1
      const param = {
        ...this.formInline,
        page: this.paginations.current,
        number: this.paginations.pageSize
      }
      getRestoreLog(param).then(res => {
        this.tableLoading = false
        if (res.errcode === 0) {
          this.data = res.data.data_list
          this.paginations.total = res.data.total
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 分页改变
    handleTableChange(obj) {
      this.paginations.current = obj.current
      if (obj.showSizeChanger) {
        this.paginations.pageSize = obj.pageSize
      }
      this.handleSubmit()
    },
    // 长连接接受后台信息
    wsMessage(res, t) {
      this.exportLoading = false
      if (res.errcode === 0) {
        const _url = `${process.env.VUE_APP_API_BASE_URL2}/project/download_file/?file_name=${res.data.file_name}&access_token=${t}`
        const link = document.createElement('a')
        link.setAttribute('download', res.data.file_name)
        link.setAttribute('href', _url)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } else {
        this.$message.error(res.errmsg)
      }
      closeWebsocket()
    },
    wsError(msg) {
      this.exportLoading = false
      this.$message.error(msg.errmsg)
    },
    // 下载全部数据
    downAllData() {
      this.exportLoading = false
      const param = {
        ...this.formInline,
        url: 'download_restore_log'
      }
      sendWebsocket(param, this.wsMessage, this.wsError)
    },
    showBigImg(url) {
      if (url) {
        this.previewImage = url
        this.previewVisible = true
      }
    },
    closeBigImg() {
      this.previewVisible = false
    },
    showHoverImg(e, url) {
      const obj = e.target.getBoundingClientRect()
      this.hoverImgX = obj.left - 250
      this.hoverImgY = obj.top - 225
      if (url) {
        this.hoverImgUrl = url
        this.hoverImg = true
      }
    },
    hideHoverImg() {
      this.hoverImg = false
    },
    onCellChange(record, name, value) {
      if (record[name] === value) {
        return
      }
      this.tableLoading = true
      const param = {
        error_log_id: record.error_log_id,
        error_type_id: value
      }
      changeErrorType(param).then(res => {
        if (res.errcode === 0) {
          this.$message.success(res.errmsg)
          this.handleSubmit()
        } else {
          this.tableLoading = false
          this.$message.error(res.errmsg)
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.hove_img_box {
  cursor: pointer;
}
.hove_img {
  position: fixed;
  left: 0;
  top: 0;
  width: 250px;
  height: 250px;
  background-color: #ffffff;
  z-index: 999;
  border-radius: 3px;
  border: 1px solid #ebeef5;
  -webkit-box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
.prew_img[lazy=loading]{
  background: url('../../assets/img/loading.svg') center no-repeat;
}
.prew_img[lazy=error]{
  background: url('../../assets/img/error.svg') center no-repeat;
}
</style>
