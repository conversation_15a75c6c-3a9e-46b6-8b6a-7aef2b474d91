<template>
  <div class="new-page">
    <a-card>
      <div style="margin-bottom: 20px">
        <a-form-model
          layout="inline"
          :model="formInline"
          @submit="handleSubmit(true)"
          @submit.native.prevent
        >
          <a-form-model-item :label="$t('label.fullname')">
            <a-input v-model="formInline.user_name" />
          </a-form-model-item>
          <a-form-model-item :label="$t('label.proId')">
            <a-input v-model="formInline.project_id" />
          </a-form-model-item>
          <a-form-model-item :label="$t('label.taskType')">
            <a-select style="width: 150px" allow-clear @change="handleTaskChange">
              <a-select-option v-for="it in taskList" :key="it.value" :value="it.value">
                {{ it.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item :label="$t('label.team')">
            <a-select style="width: 150px" allow-clear :value="formInline.team_id" @change="handleTeamChange">
              <a-select-option v-for="it in teamList" :key="it.team_id" :value="it.team_id">
                {{ it.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item :label="$t('label.group')">
            <a-select style="width: 150px" allow-clear :value="formInline.group_id" @change="handleGroupChange">
              <a-select-option v-for="it in groupList" :key="it.group_id" :value="it.group_id">
                {{ it.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item :label="$t('label.time')">
            <a-range-picker
              format="YYYY-MM-DD HH:mm"
              :default-picker-value="[moment().subtract(1, 'months'), moment()]"
              :show-time="{ format: 'HH:mm', defaultValue:[moment('00:00', 'HH:mm'), moment('23:59', 'HH:mm')] }"
              @change="onTimeChange"
              @ok="handleSubmit(true)"
            />
          </a-form-model-item>
          <a-form-model-item>
            <a-button type="primary" html-type="submit">{{ $t("buttons.search") }}</a-button>
          </a-form-model-item>
          <a-form-model-item>
            <a-button type="primary" :disabled="data.length === 0" @click="downAllData">{{ $t("buttons.download") }}</a-button>
          </a-form-model-item>
        </a-form-model>
      </div>
      <a-table
        class="my_scroll_table"
        :scroll="{ x: true }"
        :columns="columns"
        :data-source="data"
        :loading="tableLoading"
        bordered
        :row-key="(record, index) => index"
        :pagination="paginations"
        @change="handleTableChange"
      >
        <span slot="workTime" slot-scope="text">
          {{ text | hourFormat }}
        </span>
      </a-table>
    </a-card>
    <div v-if="exportLoading" class="export_loading">
      <a-spin size="large" />
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import { apiCommonServer } from '@/services'
import { registerLog } from '@/services/kpi'
import { sendWebsocket, closeWebsocket } from '@/utils/websocket'
export default {
  name: 'Achievements',
  filters: {
    hourFormat: (value) => {
      if (typeof value === 'string') {
        return value
      }
      return value ? parseFloat((value / 3600).toFixed(3)) : 0
    }
  },
  data() {
    return {
      formInline: {
        user_name: null,
        task_flow_type: null,
        project_id: null,
        team_id: null,
        group_id: null,
        start_time: null,
        end_time: null
      },
      tableLoading: false,
      paginations: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30', '50'],
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => {
          return this.$t('table.total') + total + this.$t('table.strip')
        },
        total: 0
      },
      data: [],
      teamList: [],
      groupList: [],
      exportLoading: false
    }
  },
  computed: {
    taskList() {
      const data = [
        { name: this.$t('dic.register_t'), value: 8 },
        { name: this.$t('dic.audit'), value: 9 }
      ]
      return data
    },
    columns() {
      const data = [
        { title: this.$t('table.name'), dataIndex: 'user_name', align: 'center' },
        { title: this.$t('table.team'), dataIndex: 'team', align: 'center' },
        { title: this.$t('table.group'), dataIndex: 'group', align: 'center' },
        { title: this.$t('label.proId'), dataIndex: 'project_id', align: 'center' },
        { title: this.$t('table.task'), dataIndex: 'task_flow', align: 'center' },
        { title: this.$t('table.hours'), dataIndex: 'working_hours', align: 'center', scopedSlots: { customRender: 'workTime' }},
        { title: this.$t('table.picNum'), dataIndex: 'handle_pic', align: 'center' },
        { title: this.$t('table.sku_r'), dataIndex: 'add_number', align: 'center' },
        { title: this.$t('table.sku_b'), dataIndex: 'checked_number', align: 'center' },
        { title: this.$t('table.sku_err'), dataIndex: 'err_number', align: 'center' },
        { title: this.$t('table.sku_b_del'), dataIndex: 'deleted_number', align: 'center' },
        { title: this.$t('table.sku_l'), dataIndex: 'lack_number', align: 'center' },
        { title: this.$t('table.errRate'), dataIndex: 'error_rate', align: 'center' },
        { title: this.$t('table.sku_s'), dataIndex: 'check_number', align: 'center' },
        { title: this.$t('table.sku_x'), dataIndex: 'change_number', align: 'center' },
        { title: this.$t('table.sku_del'), dataIndex: 'delete_number', align: 'center' }
      ]
      return data
    }
  },
  created() {
    this.getTeamList()
  },
  methods: {
    moment,
    // 不可选日期区间
    disabledDate(current) {
      return current && current > moment().endOf('day') || current < moment().subtract(31, 'days')
    },
    // 时间选择
    onTimeChange(date, dataStr) {
      this.formInline.start_time = dataStr[0]
      this.formInline.end_time = dataStr[1]
    },
    // 任务类型
    handleTaskChange(value) {
      this.formInline.task_flow_type = value
    },
    // 选择团队
    handleTeamChange(value) {
      this.formInline.team_id = value
      this.formInline.group_id = null
      if (value) {
        this.getGroupList()
      } else {
        this.groupList = []
      }
    },
    // 选择组
    handleGroupChange(value) {
      this.formInline.group_id = value
    },
    // 获取团队
    getTeamList() {
      apiCommonServer.getTeamList().then(res => {
        if (res.errcode === 0) {
          this.teamList = res.data.team_list
          if (this.teamList && this.teamList.length === 1) {
            this.formInline.team_id = this.teamList[0].team_id
            this.getGroupList()
          }
        }
      })
    },
    // 获取组
    getGroupList() {
      apiCommonServer.getGroupList(this.formInline.team_id).then(res => {
        if (res.errcode === 0) {
          this.groupList = res.data.group_list
        }
      })
    },
    // 搜索
    handleSubmit(first) {
      if (first) this.paginations.current = 1
      this.tableLoading = true
      const param = {
        ...this.formInline,
        page: this.paginations.current,
        number: this.paginations.pageSize
      }
      registerLog(param).then(res => {
        this.tableLoading = false
        if (res.errcode === 0) {
          this.data = res.data.data_list
          this.paginations.total = res.data.total
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 分页改变
    handleTableChange(obj) {
      this.paginations.current = obj.current
      if (obj.showSizeChanger) {
        this.paginations.pageSize = obj.pageSize
      }
      this.handleSubmit()
    },
    // 长连接接受后台信息
    wsMessage(res, t) {
      this.exportLoading = false
      if (res.errcode === 0) {
        const _url = `${process.env.VUE_APP_API_BASE_URL2}/project/download_file/?file_name=${res.data.file_name}&access_token=${t}`
        const link = document.createElement('a')
        link.setAttribute('download', res.data.file_name)
        link.setAttribute('href', _url)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } else {
        this.$message.error(res.errmsg)
      }
      closeWebsocket()
    },
    wsError(msg) {
      this.exportLoading = false
      this.$message.error(msg.errmsg)
    },
    // 下载全部数据
    downAllData() {
      this.exportLoading = false
      const param = {
        ...this.formInline,
        url: 'download_register_log'
      }
      sendWebsocket(param, this.wsMessage, this.wsError)
    }
  }
}
</script>

<style scoped lang="less">
.export_loading {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.4);
  z-index: 10;
}
</style>
