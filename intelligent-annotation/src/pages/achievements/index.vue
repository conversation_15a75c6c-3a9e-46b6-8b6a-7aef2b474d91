<template>
  <div class="new-page">
    <a-card>
      <div style="margin-bottom: 20px">
        <a-form-model
          layout="inline"
          :model="formInline"
          @submit="handleSubmit(true)"
          @submit.native.prevent
        >
          <a-form-model-item :label="$t('label.fullname')">
            <a-input v-model="formInline.user_name" />
          </a-form-model-item>
          <a-form-model-item :label="$t('label.proId')">
            <a-input v-model="formInline.project_id" />
          </a-form-model-item>
          <a-form-model-item :label="$t('label.taskType')">
            <a-select style="width: 150px" allow-clear @change="handleTaskChange">
              <a-select-option v-for="it in taskList" :key="it.value" :value="it.value">
                {{ it.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item :label="$t('label.team')">
            <a-select style="width: 150px" :value="formInline.team_id" @change="handleTeamChange">
              <a-select-option v-for="it in teamList" :key="it.team_id" :value="it.team_id">
                {{ it.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item :label="$t('label.group')">
            <a-select style="width: 150px" :value="formInline.group_id" @change="handleGroupChange">
              <a-select-option v-for="it in groupList" :key="it.group_id" :value="it.group_id">
                {{ it.name }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
          <a-form-model-item :label="$t('label.time')">
            <a-range-picker
              format="YYYY-MM-DD HH:mm"
              :disabled-date="disabledDate"
              :default-picker-value="[moment().subtract(1, 'months'), moment()]"
              :show-time="{ format: 'HH:mm', defaultValue:[moment('00:00', 'HH:mm'), moment('23:59', 'HH:mm')] }"
              @change="onTimeChange"
              @openChange="onOpenChange"
              @calendarChange="onCalendarChange"
              @ok="handleSubmit(true)"
            />
          </a-form-model-item>
          <a-form-model-item>
            <a-button type="primary" html-type="submit">{{ $t("buttons.search") }}</a-button>
          </a-form-model-item>
          <a-form-model-item>
            <a-button type="primary" :disabled="data.length === 0" @click="downAllData">{{ $t("buttons.download") }}</a-button>
          </a-form-model-item>
          <a-form-model-item>
            <a-button type="primary" @click="openExportMode">{{ $t("buttons.exportLog") }}</a-button>
          </a-form-model-item>
        </a-form-model>
      </div>
      <a-table
        class="my_scroll_table"
        :scroll="{ x: true }"
        :columns="columns"
        :data-source="data"
        :loading="tableLoading"
        bordered
        :row-key="(record, index) => index"
        :pagination="paginations"
        @change="handleTableChange"
      >
        <template slot="project_id" slot-scope="text, record">
          <span v-if="record.is_queue && record.is_queue === 'T'" style="color: #ff0000;">
            <a-icon type="bulb" theme="filled" style="vertical-align: baseline;" /> {{ record.project_id }}
          </span>
          <span v-else> {{ record.project_id }} </span>
        </template>
        <span slot="hours" slot-scope="text, record">
          {{ record.working_hours | hourFormat }}
        </span>
        <span slot="hours2" slot-scope="text, record">
          {{ record.use_hours | hourFormat }}
        </span>
        <span slot="hours3" slot-scope="text, record">
          {{ record.rest_hours | hourFormat }}
        </span>
        <span slot="speed" slot-scope="text, record">
          {{ record.handle_pic_number | speedFormat(record.working_hours) }}
        </span>
        <span slot="errRate" slot-scope="text, record">
          {{ record.error_number | errRateFormat(record.handle_pic_number) }}
        </span>
        <span slot="action" slot-scope="text, record">
          <a :disabled="exportLoading" @click="exportDataFun(record)">{{ $t('buttons.exportLog') }}</a>
        </span>
      </a-table>
    </a-card>
    <div v-if="exportLoading" class="export_loading">
      <a-spin size="large" :tip="$t('confirm.bigData')" />
    </div>
    <a-modal v-model="visible" :title="$t('buttons.exportLog')" @ok="handleOk">
      <a-row>
        <a-col :span="24">
          <div class="exprt_log">
            <div class="title">{{ $t('label.proId') }}:</div>
            <div class="input_e">
              <a-input v-model="projectId" />
            </div>
          </div>
        </a-col>
      </a-row>
    </a-modal>
  </div>
</template>

<script>
import moment from 'moment'
import { apiCommonServer } from '@/services'
import { getAchievements } from '@/services/kpi'
// import { dateUtils } from '@/utils/util'
import { sendWebsocket, closeWebsocket } from '@/utils/websocket'
export default {
  name: 'Achievements',
  filters: {
    hourFormat: (value) => {
      // const obj = dateUtils(value)
      // const str = (obj.h ? obj.h + 'h' : '0h') + (obj.m ? obj.m + 'm' : '0m') + (obj.s ? obj.s + 's' : '0s')
      return value ? parseFloat((value / 3600).toFixed(3)) : 0
    },
    speedFormat: (total, time) => {
      const h = (time / 60) / 60
      const s = h ? parseFloat((total / h).toFixed(2)) : 0
      return s
    },
    errRateFormat: (value, total) => {
      if (value) {
        const v = parseFloat((value / total) * 100).toFixed(2)
        return parseFloat(v) + '%'
      } else {
        return 0
      }
    }
  },
  data() {
    return {
      formInline: {
        user_name: null,
        task_flow_type: null,
        project_id: null,
        team_id: null,
        group_id: null,
        start_time: null,
        end_time: null
      },
      tableLoading: false,
      paginations: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30', '50'],
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => {
          return this.$t('table.total') + total + this.$t('table.strip')
        },
        total: 0
      },
      data: [],
      teamList: [],
      groupList: [],
      exportLoading: false,
      projectId: null,
      visible: false,
      dates: []
    }
  },
  computed: {
    taskList() {
      const data = [
        { name: this.$t('dic.clean'), value: 10 },
        { name: this.$t('dic.check'), value: 11 }
        // { name: this.$t('dic.sum'), value: 12 }
      ]
      return data
    },
    columns() {
      const data = [
        { title: this.$t('table.name'), dataIndex: 'user_name', align: 'center' },
        { title: this.$t('table.team'), dataIndex: 'team', align: 'center' },
        { title: this.$t('table.group'), dataIndex: 'group', align: 'center' },
        { title: this.$t('label.proId'), key: 'project_id', align: 'center', scopedSlots: { customRender: 'project_id' }},
        { title: this.$t('table.project'), dataIndex: 'project_name', align: 'center' },
        { title: this.$t('table.taskType'), dataIndex: 'task_flow_type', align: 'center' },
        { title: this.$t('table.task'), dataIndex: 'task_flow', align: 'center' },
        { title: this.$t('table.hours'), key: 'working_hours', scopedSlots: { customRender: 'hours' }, align: 'center' },
        { title: this.$t('table.hours2'), key: 'use_hours', scopedSlots: { customRender: 'hours2' }, align: 'center' },
        { title: this.$t('table.restTime'), key: 'rest_hours', scopedSlots: { customRender: 'hours3' }, align: 'center' },
        { title: this.$t('table.picNum'), dataIndex: 'handle_pic_number', align: 'center' },
        { title: this.$t('table.speed'), key: 'speed', scopedSlots: { customRender: 'speed' }, align: 'center' },
        { title: this.$t('table.coverPicNum'), dataIndex: 'others_handle_pic_number', align: 'center' },
        { title: this.$t('table.errNum'), dataIndex: 'error_number', align: 'center' },
        { title: this.$t('table.errRate'), key: 'errRate', scopedSlots: { customRender: 'errRate' }, align: 'center' },
        {
          title: this.$t('table.action'),
          key: 'action',
          fixed: 'right',
          align: 'center',
          width: 125,
          scopedSlots: { customRender: 'action' }
        }
      ]
      return data
    }
  },
  created() {
    this.getTeamList()
  },
  methods: {
    moment,
    // 不可选日期区间
    disabledDate(current) {
      const long = current && current > moment().endOf('day') || current < moment().subtract(185, 'days')
      const tooLate = current && this.dates[0] && current.diff(this.dates[0], 'days') > 31
      const tooEarly = current && this.dates[1] && this.dates[1].diff(current, 'days') > 31
      return long || tooLate || tooEarly
    },
    // 时间选择
    onTimeChange(date, dataStr) {
      this.dates = date
      this.formInline.start_time = dataStr[0]
      this.formInline.end_time = dataStr[1]
    },
    onOpenChange(open) {
      if (open) {
        this.dates = []
      }
    },
    onCalendarChange(val) {
      this.dates = val
    },
    // 任务类型
    handleTaskChange(value) {
      this.formInline.task_flow_type = value
    },
    // 选择团队
    handleTeamChange(value) {
      this.formInline.team_id = value
      this.formInline.group_id = null
      if (value) {
        this.getGroupList()
      } else {
        this.groupList = []
      }
    },
    // 选择组
    handleGroupChange(value) {
      this.formInline.group_id = value
    },
    // 获取团队
    getTeamList() {
      apiCommonServer.getTeamList().then(res => {
        if (res.errcode === 0) {
          this.teamList = res.data.team_list
          if (this.teamList && this.teamList.length === 1) {
            this.formInline.team_id = this.teamList[0].team_id
            this.getGroupList()
          }
        }
      })
    },
    // 获取组
    getGroupList() {
      apiCommonServer.getGroupList(this.formInline.team_id).then(res => {
        if (res.errcode === 0) {
          this.groupList = res.data.group_list
        }
      })
    },
    // 搜索
    handleSubmit(first) {
      if (first) this.paginations.current = 1
      this.tableLoading = true
      const param = {
        ...this.formInline,
        page: this.paginations.current,
        number: this.paginations.pageSize
      }
      getAchievements(param).then(res => {
        this.tableLoading = false
        if (res.errcode === 0) {
          this.data = res.data.data_list
          this.paginations.total = res.data.total
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 分页改变
    handleTableChange(obj) {
      this.paginations.current = obj.current
      if (obj.showSizeChanger) {
        this.paginations.pageSize = obj.pageSize
      }
      this.handleSubmit()
    },
    // 长连接接受后台信息
    wsMessage(res, t) {
      this.exportLoading = false
      if (res.errcode === 0) {
        const _url = `${process.env.VUE_APP_API_BASE_URL2}/project/download_file/?file_name=${res.data.file_name}&access_token=${t}`
        const link = document.createElement('a')
        link.setAttribute('download', res.data.file_name)
        link.setAttribute('href', _url)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } else {
        this.$message.error(res.errmsg)
      }
      closeWebsocket()
    },
    wsError(msg) {
      this.exportLoading = false
      this.$message.error(msg.errmsg)
    },
    // 导出日志
    exportDataFun(item) {
      this.exportLoading = true
      const param = {
        user_id: item.user_id,
        task_flow_id: item.task_flow_id,
        start_time: this.formInline.start_time,
        end_time: this.formInline.end_time,
        url: 'download_move_pic_logs'
      }
      sendWebsocket(param, this.wsMessage, this.wsError)
    },
    // 下载全部数据
    downAllData() {
      this.exportLoading = true
      const param = {
        ...this.formInline,
        url: 'download_achievements'
      }
      sendWebsocket(param, this.wsMessage, this.wsError)
    },
    // 导出项目操作日志
    openExportMode() {
      this.visible = true
    },
    handleOk() {
      this.visible = false
      if (!this.projectId) {
        this.$message.error(this.$t('confirm.projectId'))
        return
      }
      this.exportLoading = true
      const param = {
        project_id: this.projectId,
        url: 'download_operation_logs'
      }
      sendWebsocket(param, this.wsMessage, this.wsError)
    }
  }
}
</script>

<style scoped lang="less">
.export_loading {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.4);
  z-index: 10;
}
.exprt_log {
  display: flex;
  align-items: center;
  .title {
    margin-right: 5px;
  }
  .input_e {
    flex: 1;
  }
}
</style>
