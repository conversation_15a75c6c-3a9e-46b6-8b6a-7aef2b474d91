<template>
  <div class="india_ach">
    <div class="total_index">
      <div class="item">
        <div class="title">
          <span>{{ $t('india.hourly') }}</span>
          <span class="color1">Hour</span>
        </div>
        <div class="num">
          <a-icon class="color1" type="code-sandbox-circle" theme="filled" />
          {{ indexData.hourly_stats || '-' }}
        </div>
      </div>
      <div class="item">
        <div class="title">
          <span>{{ $t('india.today') }}</span>
          <span class="color2">Day</span>
        </div>
        <div class="num">
          <a-icon class="color2" type="clock-circle" theme="filled" />
          {{ indexData.total || '0' }}
        </div>
      </div>
      <div class="item">
        <div class="title">
          <span>{{ $t('india.man') }}</span>
          <span class="color3">HC</span>
        </div>
        <div class="num">
          <a-icon class="color3" type="slack-circle" theme="filled" />
          {{ indexData.hours || '0' }}
        </div>
      </div>
      <div class="item">
        <div class="title">
          <span>{{ $t('india.hour') }}</span>
          <span class="color4">Hour</span>
        </div>
        <div class="num">
          <a-icon class="color4" type="dropbox-circle" theme="filled" />
          {{ indexData.c_hour || '0' }}
        </div>
      </div>
      <div class="item">
        <div class="title">
          <span>{{ $t('india.min') }}</span>
          <span class="color5">Min</span>
        </div>
        <div class="num">
          <a-icon class="color5" type="codepen-circle" theme="filled" />
          {{ indexData.c_min || '0' }}
        </div>
      </div>
    </div>
    <a-card>
      <div style="margin-bottom: 20px;">
        <a-form-model
          layout="inline"
          :model="formDay"
          @submit="getData()"
          @submit.native.prevent
        >
          <a-form-model-item :label="$t('label.time')">
            <a-date-picker
              :default-value="moment()"
              type="date"
              :allow-clear="false"
              :show-today="false"
              @change="onDateChange"
            />
          </a-form-model-item>
        </a-form-model>
      </div>
      <a-table
        class="my_scroll_table"
        bordered
        :scroll="{ x: true }"
        :loading="tableDayLoading"
        :columns="columns"
        :data-source="data"
        :row-key="(record, index) => index"
        :pagination="false"
      />
      <div class="personal">
        <div class="main_title">{{ $t('india.pstas') }}</div>
        <div style="margin-bottom: 20px;">
          <a-form-model
            layout="inline"
            :model="formInline"
            @submit="handleSubmit(true)"
            @submit.native.prevent
          >
            <a-form-model-item :label="$t('label.fullname')">
              <a-input v-model="formInline.user_name" />
            </a-form-model-item>
            <a-form-model-item :label="$t('label.proId')">
              <a-input v-model="formInline.project_id" />
            </a-form-model-item>
            <a-form-model-item :label="$t('label.taskType')">
              <a-select style="width: 150px" allow-clear @change="handleTaskChange">
                <a-select-option v-for="it in taskList" :key="it.value" :value="it.value">
                  {{ it.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item :label="$t('label.time')">
              <a-range-picker
                format="YYYY-MM-DD HH:mm"
                :default-picker-value="[moment().subtract(1, 'months'), moment()]"
                :disabled-date="disabledDate"
                :show-time="{ format: 'HH:mm', defaultValue:[moment().startOf('day'), moment().endOf('day')] }"
                @change="onTimeChange"
                @ok="handleSubmit(true)"
              />
            </a-form-model-item>
            <a-form-model-item>
              <a-button type="primary" html-type="submit">{{ $t("buttons.search") }}</a-button>
            </a-form-model-item>
            <a-form-model-item>
              <a-button type="primary" :disabled="data2.length === 0" @click="downAllData">{{ $t("buttons.download") }}</a-button>
            </a-form-model-item>
            <a-form-model-item>
              <a-button type="primary" @click="getAccess">{{ $t("buttons.accessControl") }}</a-button>
            </a-form-model-item>
          </a-form-model>
        </div>
        <a-table
          class="my_scroll_table"
          bordered
          :scroll="{ x: true }"
          :columns="columns2"
          :data-source="data2"
          :loading="tableLoading"
          :row-key="(record, index) => index"
          :pagination="paginations"
          @change="handleTableChange"
        />
      </div>
    </a-card>
    <div v-if="exportLoading" class="export_loading">
      <a-spin size="large" />
    </div>
    <a-modal v-model="visible" centered :title="$t('buttons.accessControl')" :confirm-loading="confirmLoading" @ok="accessControl">
      <div class="access-controll">
        <div class="label">{{ $t('india.getMyErr') }}:</div>
        <div class="radios">
          <a-radio-group v-model="myError" @change="onAccessControl">
            <a-radio value="T">{{ $t('india.allow') }}</a-radio>
            <a-radio value="F">{{ $t('india.refuse') }}</a-radio>
          </a-radio-group>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { getTodayData, getPersonalTodayData } from '@/services/kpi'
import { getSwithchState, changeSwithchState } from '@/services/sys'
import moment from 'moment'
import { sendWebsocket, closeWebsocket } from '@/utils/websocket'
export default {
  name: 'IndiaAch',
  data() {
    return {
      indexData: {},
      data: [],
      data2: [],
      formDay: {
        time: moment().format('YYYY-MM-DD')
      },
      formInline: {
        user_name: null,
        project_id: null,
        start_time: null,
        end_time: null,
        task_flow_type: null
      },
      paginations: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30', '50'],
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => {
          return this.$t('table.total') + total + this.$t('table.strip')
        },
        total: 0
      },
      tableDayLoading: true,
      tableLoading: true,
      exportLoading: false,
      visible: false, // 访问控制弹框
      myError: 'T', // 是否可以查看我的错误
      confirmLoading: false
    }
  },
  computed: {
    taskList() {
      const data = [
        { name: this.$t('dic.clean'), value: 10 },
        { name: this.$t('dic.check'), value: 11 }
        // { name: this.$t('dic.sum'), value: 12 }
      ]
      return data
    },
    columns() {
      const data = [
        { title: this.$t('india.interval'), dataIndex: 'interval', align: 'center' },
        { title: this.$t('india.type'), dataIndex: 'type_of_task', align: 'center' },
        { title: this.$t('india.received'), dataIndex: 'image_received', align: 'center' },
        { title: this.$t('india.total'), dataIndex: 'total_pending', align: 'center' },
        { title: this.$t('india.completed'), dataIndex: 'completed', align: 'center' },
        { title: this.$t('india.cHour'), dataIndex: 'completed_this_hour', align: 'center' },
        { title: this.$t('india.mHour'), dataIndex: 'man_hours', align: 'center' },
        { title: this.$t('india.cHours'), dataIndex: 'c_hour', align: 'center' },
        { title: this.$t('india.cMin'), dataIndex: 'c_min', align: 'center' }
      ]
      return data
    },
    columns2() {
      const data = [
        { title: this.$t('table.date'), dataIndex: 'end_date', align: 'center' },
        { title: this.$t('india.username'), dataIndex: 'employees_name', align: 'center' },
        { title: this.$t('table.projectId'), dataIndex: 'folder_id', align: 'center' },
        { title: this.$t('india.pname'), dataIndex: 'folder_name', align: 'center' },
        { title: this.$t('india.tasktype'), dataIndex: 'task_type', align: 'center' },
        { title: this.$t('india.whours3'), dataIndex: 'actual_time_spent', align: 'center' },
        { title: this.$t('india.whours4'), dataIndex: 'valid_time_spent', align: 'center' },
        { title: this.$t('india.startTime'), dataIndex: 'start_time', align: 'center' },
        { title: this.$t('india.endTime'), dataIndex: 'end_time', align: 'center' },
        { title: this.$t('india.restTime'), dataIndex: 'rest_hours', align: 'center' },
        { title: this.$t('india.ptotal'), dataIndex: 'total_images_folder', align: 'center' },
        // { title: this.$t('india.remaining'), dataIndex: 'remaining', align: 'center' },
        { title: this.$t('india.wtotalimg'), dataIndex: 'total_folders', align: 'center' },
        { title: this.$t('india.wtotalf'), dataIndex: 'total_images_worked', align: 'center' },
        { title: this.$t('india.whours'), dataIndex: 'actual_working_hours', align: 'center' },
        { title: this.$t('india.whours2'), dataIndex: 'valid_working_hours', align: 'center' }
      ]
      return data
    }
  },
  mounted() {
    this.getData()
    this.handleSubmit(true)
  },
  methods: {
    moment,
    // 不可选日期区间
    disabledDate(current) {
      return current && current > moment().endOf('day') || current < moment().subtract(31, 'days')
    },
    // 时间选择
    onTimeChange(date, dataStr) {
      this.formInline.start_time = dataStr[0]
      this.formInline.end_time = dataStr[1]
    },
    // 任务类型
    handleTaskChange(value) {
      this.formInline.task_flow_type = value
      this.handleSubmit(true)
    },
    onDateChange(date, dataStr) {
      this.formDay.time = dataStr
      this.getData()
    },
    getData() {
      this.tableDayLoading = true
      getTodayData({ date_time: this.formDay.time }).then(res => {
        this.tableDayLoading = false
        if (res.errcode === 0) {
          this.indexData = res.data
          this.data = res.data.list
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    handleSubmit(first) {
      if (first) this.paginations.current = 1
      this.tableLoading = true
      const param = {
        ...this.formInline,
        page: this.paginations.current,
        number: this.paginations.pageSize
      }
      getPersonalTodayData(param).then(res => {
        this.tableLoading = false
        if (res.errcode === 0) {
          this.data2 = res.data.data_list
          this.paginations.total = res.data.total
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 分页改变
    handleTableChange(obj) {
      this.paginations.current = obj.current
      if (obj.showSizeChanger) {
        this.paginations.pageSize = obj.pageSize
        this.paginations.total = 1
      }
      this.handleSubmit()
    },
    // 下载全部数据
    downAllData() {
      this.exportLoading = false
      const param = {
        ...this.formInline,
        url: 'download_personal_today_data'
      }
      sendWebsocket(param, this.wsMessage, this.wsError)
    },
    // 长连接接受后台信息
    wsMessage(res, t) {
      this.exportLoading = false
      if (res.errcode === 0) {
        const _url = `${process.env.VUE_APP_API_BASE_URL2}/project/download_file/?file_name=${res.data.file_name}&access_token=${t}`
        const link = document.createElement('a')
        link.setAttribute('download', res.data.file_name)
        link.setAttribute('href', _url)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } else {
        this.$message.error(res.errmsg)
      }
      closeWebsocket()
    },
    wsError(msg) {
      this.exportLoading = false
      this.$message.error(msg.errmsg)
    },
    // 获取访问状态
    getAccess() {
      this.visible = true
      getSwithchState({}).then(res => {
        if (res.errcode === 0) {
          this.myError = res.data.state
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 访问控制状态切换
    onAccessControl(e) {
      this.myError = e.target.value
    },
    // 访问控制
    accessControl() {
      this.confirmLoading = true
      changeSwithchState({ state: this.myError }).then(res => {
        this.confirmLoading = false
        if (res.errcode === 0) {
          this.$message.success(res.errmsg)
          this.visible = false
        } else {
          this.$message.error(res.errmsg)
          this.myError = this.myError === 'T' ? 'F' : 'T'
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.india_ach {
  .total_index {
    display: flex;
    justify-content: space-between;
    .item {
      width: 19%;
      margin-bottom: 25px;
      padding: 10px 15px;
      color: @text-color;
      background-color: @body-background;
      border-radius: 6px;
      font-size: 1.15rem;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .title {
        word-break: break-all;
        margin-bottom: 15px;
        display: flex;
        justify-content: space-between;
        span:last-child {
          margin-left: 15px;
          white-space: nowrap;
        }
      }
      .num {
        font-size: 1.5em;
        font-weight: bold;
      }
      .color1 { color: #EC3870; }
      .color2 { color: #2ACDE6; }
      .color3 { color: #7E22DB; }
      .color4 { color: #ED5C36; }
      .color5 { color: #315CE9; }
    }
  }
  .personal {
    .main_title {
      padding: 15px 0;
      color: #333;
      font-size: 1.25rem;
      font-weight: bold;
    }
  }
}
.export_loading {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.4);
  z-index: 10;
}
.access-controll {
  display: flex;
  align-items: center;
  .label {
    margin-right: 10px;
  }
}
</style>
