<template>
  <div class="editable-cell">
    <div v-if="editable" class="editable-cell-input-wrapper">
      <!-- <a-input :value="value" @change="handleChange" @pressEnter="check" /> -->
      <a-select style="width: 100%" :value="value" @change="handleChange">
        <a-select-option v-for="it in list" :key="it.dictionary_id" :value="it.dictionary_id">
          {{ lang === 'CN' ? it.name : it.name_en }}
        </a-select-option>
      </a-select>
      <a-icon
        type="check"
        class="editable-cell-icon-check"
        @click="check"
      />
    </div>
    <div v-else class="editable-cell-text-wrapper">
      {{ text || ' ' }}
      <a-icon type="edit" class="editable-cell-icon" @click="edit" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'EditableCell',
  props: {
    text: {
      type: String,
      default: ''
    },
    selectValue: {
      type: Number,
      default: null
    },
    list: {
      type: Array,
      default: () => {
        return []
      }
    },
    lang: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      value: this.selectValue,
      editable: false
    }
  },
  methods: {
    handleChange(value, option) {
      this.value = value
    },
    check() {
      this.editable = false
      this.$emit('change', this.value)
    },
    edit() {
      this.editable = true
    }
  }
}
</script>

<style lang="less" scoped>
.editable-cell {
  position: relative;
}

.editable-cell-input-wrapper,
.editable-cell-text-wrapper {
  padding-right: 24px;
}

.editable-cell-text-wrapper {
  padding: 5px 24px 5px 5px;
}

.editable-cell-icon,
.editable-cell-icon-check {
  position: absolute;
  right: 0;
  width: 20px;
  cursor: pointer;
}

.editable-cell-icon {
  line-height: 18px;
  display: none;
}

.editable-cell-icon-check {
  line-height: 28px;
}

.editable-cell:hover .editable-cell-icon {
  display: inline-block;
}

.editable-cell-icon:hover,
.editable-cell-icon-check:hover {
  color: #108ee9;
}

.editable-add-btn {
  margin-bottom: 8px;
}
</style>
