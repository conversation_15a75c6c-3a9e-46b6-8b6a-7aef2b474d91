<template>
  <div class="page_container">
    <div class="contents">
      <a-row>
        <a-form layout="inline">

          <a-form-item label="参照项目ID" :help="taskInfo.refer_to_project_id?'已保存':'请输入对照的的项目编号'">
            <a-input
              v-model="checkedProjectId"
              :disabled="!!taskInfo.refer_to_project_id"
            />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" class="btn_back" :disabled="!!taskInfo.refer_to_project_id" @click="saveProject">保存</a-button>
          </a-form-item>
        </a-form>
      </a-row>
      <a-row>
        <a-form :form="form">

          <a-form-item label="提交文件夹数量">
            <a-input-number
              v-decorator="[
                'submit_dir_num',
                { rules: [], initialValue: formData.submit_dir_num ? formData.submit_dir_num : 1 },
              ]"
              :min="1"
            />
          </a-form-item>

          <a-form-item label="显示提交人姓名">
            <a-select
              v-decorator="[
                'user_id',
                {
                  rules: [{required: true}], initialValue: formData.user_id ? formData.user_id : '',
                },
              ]"
            >
              <a-select-option v-for="item in userList" :key="item.id" :value="item.id">
                {{ item.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-form>
      </a-row>

    </div>
    <div class="page_bot">
      <div class="lt">
        <span>{{ $t('table.project') }}：{{ project_name }}</span>
        <span style="margin-left: 15px;">总分类数：{{ taskInfo.dir_count }}</span>
        <span style="margin-left: 15px;">已提交分类：{{ taskInfo.dir_count - taskInfo.un_finish_dir_count }}</span>
      </div>
      <div class="rt">
        <a-button type="primary" class="btn_back" @click="goBack">{{ $t('buttons.return') }}</a-button>
        <a-button type="danger" class="btn_submit" @click="submitTask">{{ $t('buttons.submit') }}</a-button>
      </div>
    </div>
    <div v-if="pageLoading" class="page_fixed_load">
      <a-spin />
    </div>
  </div>
</template>

<script>
import { getTaskFlowInfo, saveCheckedProject, submitFuheTaskFlow } from '@/services/work'
export default {
  name: 'NewTaskHandle',
  components: { },
  data() {
    return {
      taskId: this.$route.query.task_flow_id,
      projectId: this.$route.query.project_id,
      taskFlowType: this.$route.query.task_flow_type, // 流程类型 8注册 9审核
      // form_check: this.$form.createForm(this),
      form: this.$form.createForm(this),
      checkedProjectId: '',
      taskInfo: {},
      formData: {
        submit_dir_num: '',
        user_id: ''
      },
      userList: [],
      project_name: '',
      tasking: false, // 是否开始了任务
      pageLoading: false
    }
  },
  mounted() {
    this.project_name = this.$route.query.name
    this.taskFlowType = this.$route.query.task_flow_type
    this.customWatchEvent()
    this.getTaskInfo()
  },

  destroyed() {
    this.removeCustomWatchEvent()
  },
  methods: {
    customWatchEvent() {
      window.addEventListener('resize', this.windowResizeFun, true)
    },
    removeCustomWatchEvent() {
      window.removeEventListener('resize', this.windowResizeFun, true)
    },
    getTaskInfo() { // 任务
      getTaskFlowInfo({ task_id: this.taskId }).then(res => {
        if (res.errcode === 0) {
          this.taskInfo = res.data
          if (res.data.refer_to_project_id) {
            this.checkedProjectId = res.data.refer_to_project_id
          }
          if (res.data.user_list && res.data.user_list.length > 0) {
            this.userList = res.data.user_list
          }
        }
      })
    },

    submitTask() { // 提交任务
      const _this = this
      this.$confirm({
        title: this.$t('confirm.title'),
        content: this.$t('confirm.sukSure'),
        onOk() {
          _this.submitTaskApi()
        }
      })
    },
    submitTaskApi() {
      this.form.validateFields((err, values) => {
        if (!err) {
          const param = {
            task_id: this.taskId,
            user_id: values.user_id,
            submit_dir_num: values.submit_dir_num,
            project_id: this.projectId,
            checked_project_id: this.checkedProjectId
          }
          this.pageLoading = true
          submitFuheTaskFlow(param).then(res => {
            this.pageLoading = false
            if (res.errcode === 0) {
              // 刷新 todo
              this.tasking = false
              this.getTaskInfo()
            } else {
              this.$message.error(res.errmsg)
            }
          })
        }
      })
    },
    saveProject() {
      const param = {
        checked_project_id: this.checkedProjectId,
        project_id: this.projectId }
      saveCheckedProject(param).then(res => {
        this.pageLoading = false
        if (res.errcode === 0) {
          // 刷新 todo
          this.tasking = false
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    goBack() { // 返回我的任务页
      if (!this.tasking) {
        this.$router.push({ path: '/personal/task' })
        return
      }
      const _this = this
      this.$confirm({
        title: this.$t('confirm.title'),
        content: this.$t('confirm.cnt'),
        onOk() {
          _this.$router.push({ path: '/personal/task' })
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.page_container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding-bottom: 56px;
  position: relative;
  .contents {
    margin:30px auto;
    width: 80%;
    height: 100%;

  }
  .page_bot {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 56px;
    padding: 0 25px;
    background-color: #282828;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .lt {
      user-select: none;
    }
    .mid {
      font-size: 22px;
      .scale_num {
        margin: 0 10px;
        user-select: none;
      }
    }
    .rt {
      .ant-btn {
        margin-left: 15px;
      }
      .btn_back {
        background-color: #5daf34;
        border-color: #5daf34;
      }
    }
  }
}
</style>
