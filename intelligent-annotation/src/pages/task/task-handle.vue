<template>
  <div class="handle_container">
    <div class="left_cnt">
      <div class="canvas_400">
        <left-cnavas-cmp ref="canvasCmp" />
      </div>
      <div class="folder_list">
        <left-bot-cmp
          ref="leftBotCmp"
          :current-id="midDataDirId"
          :clean-data="cleanLeftBotData"
          :show-all-dir="showTotalDir"
          :flow-id="taskFlowId"
          @dirClick="handleDirClick"
          @createTemDir="createTmpDirFun"
          @getDirTotal="e => dirTotal = e"
        />
      </div>
    </div>
    <div id="mid_cnt_cmp" class="mid_cnt">
      <mid-cmp
        ref="midCmp"
        :tool-task-id="tool_task_id"
        :clean-state="cleaning"
        :item-num="scaleSize"
        :item-margin="midItemMargin"
        :show-hover-img="!showCurrentImgList"
        :dir-id="midDataDirId"
        :current-ids="currentPicIds"
        :show-right-mouse="showRightMouse"
        :inputing="inputing"
        :mid-data-dir-name="midDataDirName"
        @numberKeyCodeEnd="handleMidNumKey"
        @setCurrentPidIds="handleSetPicIds"
        @onItemClick="hanldMidItemClick"
        @midPicTotal="e => thisPicTotal = e"
        @reductionData="handleReductionData"
        @pageLoading="handlePageLoading"
        @copyToSortContainer="copyToSortContainer"
      />
      <SkuPackages />
    </div>
    <div class="right_cnt">
      <div v-if="canBeSort" class="sort_seed_container">
        <a-badge :count="sortSeedList.length" class="sort_button" @click.native="sortFn">
          <a-button type="primary" :disabled="!sortSeedList.length || showRightMouse || sorting" size="small" @click.native="sortFn">
            {{ $t('buttons.reordering') }}
          </a-button>
        </a-badge>
        <div id="sortSeedContainer" class="sort_seed_image_contain">
          <div v-for="seed in sortSeedList" :id="`own_seed_image_${seed.pic_id}`" :key="seed.pic_id" class="sort_seed_image" @dblclick.stop="deleteSeedImage(seed)">
            <img :draggable="false" :src="DeleteIcon" alt="" style="cursor:pointer;width:16px;height:16px;position: absolute;right:-4px;top:-4px;" @click.stop="deleteSeedImage(seed)">
            <img v-lazy="(lang === 'CN' ? seed.box_url : seed.box_url_en) + '/format/webp>'" :draggable="false" alt="" style="width:100%;height:100%;object-fit: contain;">
          </div>
        </div>
      </div>
      <right-cmp
        v-if="project.project_id"
        :can-be-sort="canBeSort"
        :current-id="midDataDirId"
        :clean-state="cleaning"
        :clean-dir-id="cleanDirId"
        :drag-sku-list="rightTemDirList"
        :project="project"
        @temDirList="handleGetTemDir"
        @setTemDir="handleSetTemDir"
        @inputIng="e => inputing = e"
      />
    </div>
    <div class="fix_bot">
      <div class="lt">
        <!-- <a-button type="primary">下一个任务</a-button> -->
        <div class="dec">
          <span>{{ $t('table.project') }}：{{ projectName | ellipsisName }}</span>
          <!--          标注员不显示分类总数-->
          <span v-if="user.role_id !== 1">{{ $t('contents.catTotal') }}：{{ dirTotal }}</span>
          <span>{{ $t('contents.curCutNum') }}：{{ thisPicTotal }}</span>
          <span :title="$t('contents.completedTasksTodayTip')">{{ $t('contents.completedTasksToday') }}：{{ todayDirTotal }} / {{ todayPicTotal }}</span>
        </div>
      </div>
      <div class="md">
        <a-icon type="minus-circle" @click="minusNum" />
        <span class="scale_num">{{ percentNum }}%</span>
        <a-icon type="plus-circle" @click="plusNum" />
      </div>
      <div class="rt">
        <!--        打开链接 -->
        <a-button v-if="project.task_rule" type="link" @click="openTaskRule">{{ $t('buttons.task_rule') }}</a-button>
        <a-button type="primary" class="bg_color_1" @click="pageback">{{ $t('buttons.return') }}</a-button>
        <a-button v-if="cleaning" type="danger" :loading="giveUpLoading" @click="giveUpDirNumberFun">{{ $t('buttons.giveUp') }}</a-button>
        <a-button v-else type="primary" :loading="start_loading" @click="startClean">{{ $t('buttons.start') }}</a-button>
        <a-button v-if="pausing" type="danger" @click="startClean">{{ $t('buttons.cancelPause') }}</a-button>
        <a-button v-if="cleaning && !pausing" type="danger" @click="stopDirFun">{{ $t('buttons.pause') }}</a-button>
        <a-button v-if="cleaning" type="danger" style="margin-right: 0;" @click="submitDirFun">{{ $t('buttons.submit') }}</a-button>
      </div>
    </div>
    <div
      v-show="showCurrentImgList"
      class="current_img_list"
      :style="{'top': dragShandowY + 'px', 'left': dragShandowX + 'px'}"
    >
      {{ $t('contents.ySelect') }} {{ currentPicIds.length }} {{ $t('table.strip') }}
    </div>
    <div v-if="submitLoading" class="submit_load">
      <a-spin />
    </div>

    <a-modal
      v-model="giveUpVisible"
      ok-text="确认"
      cancel-text="取消"
      :title="$t('confirm.title') + '：' + $t('confirm.cnt4')"
      @ok="giveUpHandleOk"
      @cancel="giveUpHandleCancel"
    >
      <div>
        <span>{{ $t('confirm.cnt6') }}</span>
        <span :style="{color: 'red', 'padding': '0 5px'}">{{ giveUpNumber }}</span>
        <span>{{ $t('confirm.cnt7') }}</span>

      </div>
      <p />
      <p :style="reasonRequireError?{'color':'red'}:null">{{ $t('confirm.giveUpReasonTip') }}:</p>
      <a-select
        v-model="giveUpReasonOption"
        :placeholder="$t('confirm.giveUpReasonTip')"
        show-arrow
        allow-clear
        autofocus
        :filter-option="false"
        mode="combobox"
        style="width:100%"
        :options="giveUpReasonOptions"
        @change="giveUpReasonHandleChange"
      />
    </a-modal>
  </div>
</template>

<script>
import DeleteIcon from '@/assets/img/<EMAIL>'
import { mapGetters, mapState } from 'vuex'
import LeftCnavasCmp from './components/left-top-cmp'
import LeftBotCmp from './components/left-bot-cmp'
import MidCmp from './components/mid-cmp'
import RightCmp from './components/right-cmp'
import SkuPackages from './components/sku-packages'
import { getProjectInfo, getUserTodayActionAmount, stopDir } from '@/services/project'
import { startClearDir, submitDir, giveUpDir, movePic, getTemDirList, createTmpDir, clearDirInvisible, giveUpDirNumber, sortCleanImage } from '@/services/work'
import { setHours, getHours, removeHours } from '@/utils/workHours.js'
import { dateUtils } from '@/utils/util'
import { searchCanBeSort } from '../../services/work'
const sha1 = require('js-sha1')
export default {
  name: 'TaskHandle',
  components: { LeftCnavasCmp, LeftBotCmp, MidCmp, RightCmp, SkuPackages },
  filters: {
    // 项目名显示12位，超过12位显示...
    ellipsisName: function(value) {
      if (value && value.length > 40) {
        return value.slice(0, 40) + '...'
      }
      return value
    }
  },
  data() {
    return {
      midDataDirName: '',
      canBeSort: false,
      tool_task_id: null,
      projectId: null, // 项目id
      project: {
        project_id: null,
        name: null,
        task_rule: null
      }, // 项目详情
      taskFlowId: null, // 任务id
      taskFlowType: null, // 任务类型 10 清洗 11 审核 12汇总
      projectName: '', // 项目名称
      dirTotal: 0, // 总分类数
      thisPicTotal: 0, // 当前页切图数
      midItemMargin: 10,
      scaleSize: 150,
      percentNum: 100,
      start_loading: false, // 开始接口Loading
      cleaning: false, // 清洗状态
      pausing: false, // 暂停中
      cleanDirId: null, // 选中的文件夹ID
      currentPicIds: [], // 选中的图片id集合
      showCurrentImgList: false, // 展示拖动阴影
      midDataDirId: null, // 中间展示的图片所属文件夹Id
      rightTemDirList: [], // 右侧可拖动目标临时文件夹集合,
      submitLoading: false,
      dragShandowX: 0,
      dragShandowY: 0,
      dragShandowStartX: 0,
      dragShandowStartY: 0,
      timer: null, // 工作事件统计
      count: 0, // 操作计时，非零时表示多长事件没有操作
      workingHours: 0, // 今天已提交的有效工时
      isCleanTime: false, // 是否清空本地保存的工作时
      showRightMouse: false,
      inputing: false, // 搜索中
      cleanLeftBotData: [],
      showTotalDir: true,
      hasMoveTarget: false,
      giveUpLoading: false, // 放弃按钮状态
      todayDirTotal: 0,
      todayPicTotal: 0,
      giveUpVisible: false,
      giveUpNumber: 0,
      reasonRequireError: false,
      giveUpReasonOption: '', // 用户选择的选项
      sortSeedList: [],
      DeleteIcon,
      sorting: false
    }
  },
  computed: {
    ...mapState('setting', ['lang']),
    ...mapGetters('account', ['user']),
    ...mapState('taskHandle', ['sku', 'skuPackages', 'showPackage']),
    giveUpReasonOptions() {
      if (this.user.team_id === 3) { // 印度放弃原因
        return [
          {
            value: 'Uncertainties Awaiting Clarification',
            label: 'Uncertainties Awaiting Clarification'
          },
          {
            value: 'Project Hold due to Sequence Change',
            label: 'Project Hold due to Sequence Change'
          },
          {
            value: 'Shift Ends',
            label: 'Shift Ends',
            disabled: false
          },
          {
            value: 'Moving Inspectors Back to the Inspection',
            label: 'Moving Inspectors Back to the Inspection'
          }
        ]
      }
      return [
        {
          value: '待明确的不确定因素',
          label: '待明确的不确定因素'
        },
        {
          value: '因(交付)序列更改而搁置项目',
          label: '因(交付)序列更改而搁置项目'
        },
        {
          value: '下班',
          label: '下班',
          disabled: false
        },
        {
          value: '将质检员调回检查',
          label: '将质检员调回检查'
        }
      ]
    }
  },
  created() {
    this.projectId = this.$route.query.id
    this.getProjectDetail()
  },
  mounted() {
    this.taskFlowId = parseFloat(this.$route.query.task_flow_id)
    this.projectName = this.$route.query.name
    const isInvisible = this.$route.query.is_invisible
    this.taskFlowType = parseFloat(this.$route.query.task_flow_type)
    const roleId = parseFloat(this.$route.query.role_id)
    const teamId = parseFloat(this.user.team_id)
    if (roleId === 1 && (this.taskFlowType === 10 || (teamId === 3 && this.taskFlowType === 11)) && isInvisible === 'F') { // 当登录人角色是标注员，且流程为第一步清洗，且项目为不可查看全部文件夹时
      // 印度检查流程 随机检查目录 2022-09-27 18:35:18
      this.showTotalDir = false
    }
    // if (this.taskFlowId) {
    //   this.handleGetTemDir()
    // }
    this.getScaleSize()
    // 事件监听
    this.watchDragEvent()
    this.getUserTodayActionAmount()
  },
  destroyed() {
    this.removeDragEvent()
    // 结束工作计时
    this.cleanTimer()
  },
  methods: {
    copyToSortContainer(item) {
      if (!this.canBeSort || !this.cleaning) return
      // 先从 sortSeedList 中 判断是否存在，如果存在，则不添加，否则添加
      const findItem = this.sortSeedList.find(v => v.pic_id === item.pic_id)
      if (findItem) {
        const dom = document.getElementById(`own_seed_image_${findItem.pic_id}`)
        if (dom) {
          dom.scrollIntoView({ block: 'end', behavior: 'smooth' })
        }
        return
      }
      this.sortSeedList.push(item)
      this.$nextTick(() => {
        const div = document.getElementById('sortSeedContainer') // 替换为你的div的ID
        div.scrollTo({ left: div.scrollWidth - div.clientWidth, behavior: 'smooth' }) // 滚动到最右侧
        // div.scrollLeft = div.scrollWidth - div.clientWidth; // 滚动到最右侧
      })
    },
    sortFn() {
      if (this.sorting) {
        return
      }
      if (!this.sortSeedList.length) {
        return
      }
      if (this.showRightMouse) {
        return
      }
      let totalBoxList = []
      if (this.$refs.midCmp) {
        totalBoxList = this.$refs.midCmp.data
      }
      this.submitLoading = true
      this.sorting = true
      const formData = {
        tool_task_id: this.tool_task_id,
        seed_box_ids: this.sortSeedList.map(v => v.box_id),
        order_box_ids: totalBoxList.map(v => v.box_id)
      }
      sortCleanImage(formData).then((res) => {
        if (res.code === 200) {
          if (Array.isArray(res.data) && res.data.length) {
            const boxIds = res.data.map(v => Number(v.box_id))
            const sortedImageList = this.$refs.midCmp.data.sort((a, b) => boxIds.indexOf(Number(a.box_id)) - boxIds.indexOf(Number(b.box_id)))
            this.$refs.midCmp.data = sortedImageList
            setTimeout(() => {
              this.$refs.midCmp.updateScrollTop()
            }, 200)
          }
          // this.sortSeedList = []
        } else {
          this.$message.error(res.message)
        }
      }).finally(() => {
        setTimeout(() => {
          this.sorting = false
          this.submitLoading = false
        }, 200)
      })
    },
    deleteSeedImage(seedImage) {
      const findIndex = this.sortSeedList.findIndex(v => v.pic_id === seedImage.pic_id)
      if (findIndex > -1) {
        this.sortSeedList.splice(findIndex, 1)
      }
    },
    // 监听拖动事件
    watchDragEvent() {
      window.addEventListener('beforeunload', this.beforeunloadFn, true)
      window.addEventListener('resize', this.getScaleSize, true)
      if (window.history && window.history.pushState) {
        history.pushState(null, null, document.URL)
        window.addEventListener('popstate', this.pageback, false)
      }
      // 监听鼠标
      document.addEventListener('mousedown', this.eMouseDown, false)
      document.addEventListener('mousemove', this.eMouseMove, false)
      document.addEventListener('mouseup', this.eMouseUp, false)
    },
    removeDragEvent() {
      window.removeEventListener('beforeunload', this.beforeunloadFn, true)
      window.removeEventListener('popstate', this.pageback, false)
      window.removeEventListener('resize', this.getScaleSize, true)

      document.removeEventListener('mousedown', this.eMouseDown, false)
      document.removeEventListener('mousemove', this.eMouseMove, false)
      document.removeEventListener('mouseup', this.eMouseUp, false)
    },
    // 鼠标按下
    eMouseDown(event) {
      if (event.target.className === 'drag_mask move') {
        this.hasMoveTarget = true
      }
    },
    // 鼠标移动
    eMouseMove(event) {
      if (this.count !== 0) { // 初始化页面无操作时间
        this.count = 0
      }
      if (this.hasMoveTarget) {
        this.dragShandowX = event.clientX + 15
        this.dragShandowY = event.clientY + 15
        this.showCurrentImgList = true
      }
    },
    // 鼠标抬起
    eMouseUp(event) {
      if (this.hasMoveTarget) {
        this.hasMoveTarget = false
        this.showCurrentImgList = false
        const dataClass = event.target.getAttribute('drop-class') || event.target.parentNode.getAttribute('drop-class')
        if (dataClass && dataClass === 'dropTarget') {
          if (this.currentPicIds.length === 0) {
            return
          }
          const newId = event.target.getAttribute('data-id') || event.target.parentNode.getAttribute('data-id')
          if (!newId) {
            console.log(event)
            return
          }
          const param = {
            pic_id_list: this.currentPicIds,
            old_dir_id: this.midDataDirId,
            new_dir_id: parseInt(newId)
          }
          if (!this.submitLoading) {
            this.movePicApi(param)
          }
        }
      }
    },
    // 移动图片接口
    movePicApi(param) {
      if (param.old_dir_id === param.new_dir_id) {
        this.submitLoading = false
        return
      }
      this.submitLoading = true
      movePic(param).then(res => {
        this.submitLoading = false
        if (res.errcode === 0|| res.errcode === 3) {
          this.restMidPic()
        } else {
          this.$message.error({
            content: h => {
              return h('span', { attrs: {
                style: 'font-size: 24px;vertical-align:middle;line-height:1;display:inline-block;margin-top:-5px;'
              }}, res.errmsg)
            },
            duration: 10
          })
        }
      })
    },
    // 刷新中间图片数据
    restMidPic() {
      if (this.$refs.midCmp) {
        this.$refs.midCmp.moveSuccess(true)
      } else {
        this.$nextTick(() => {
          if (this.$refs.midCmp) {
            this.$refs.midCmp.moveSuccess(true)
          } else {
            this.$message.error('The component is not found')
          }
        })
      }
    },
    // 鼠标右键还原
    handleReductionData() {
      const param = {
        pic_id_list: this.currentPicIds,
        old_dir_id: this.midDataDirId,
        new_dir_id: this.cleanDirId
      }
      this.movePicApi(param)
    },
    // 监听页面返回
    pageback() {
      if (!this.cleaning) {
        this.$router.push({ path: '/personal/task' })
        return
      }
      const _this = this
      this.$confirm({
        title: this.$t('confirm.title'),
        content: this.$t('confirm.cnt'),
        onOk() {
          _this.$router.push({ path: '/personal/task' })
        }
      })
    },
    // 监听页面刷新
    beforeunloadFn() {
      const e = window.event
      if (e && this.cleaning) {
        e.returnValue = this.$t('buttons.close') + this.$t('confirm.title')
      }
      return this.$t('buttons.close')
    },
    // 开始清洗
    startClean() {
      if (this.showTotalDir) {
        if (!this.cleanDirId) {
          this.$message.error(this.$t('confirm.cnt2'))
          return
        }
        this.startClearDirApi()
      } else {
        this.newStartClean()
      }
    },
    // 暂停文件夹
    stopDirFun() {
      if (!this.cleanDirId) {
        this.$message.error(this.$t('confirm.cnt2'))
        return
      }
      stopDir({ dir_id: this.cleanDirId }).then(res => {
        if (res.errcode === 0) {
          this.pausing = true
          this.refreshLeftBotCmpData('pause')
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 开始清洗，并获取需要清洗的任务文件
    newStartClean() {
      this.start_loading = true
      clearDirInvisible(this.taskFlowId).then(res => {
        this.start_loading = false
        if (res.errcode === 0) {
          if (res.data) {
            this.pausing = false
            this.workingHours = res.data.working_hours || 0
            this.isCleanTime = res.data.is_clean && res.data.is_clean === 'T'
            this.cleanLeftBotData = [res.data]
            this.handleDirClick(this.cleanLeftBotData[0])
            this.cleaning = true
            this.startTimer()
            this.handleGetTemDir()
          } else {
            this.$message.error(res.errmsg)
          }
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 开始清洗接口
    startClearDirApi() {
      this.start_loading = true
      startClearDir(this.cleanDirId).then(res => {
        this.start_loading = false
        if (res.errcode === 0) {
          this.pausing = false
          const data = res.data
          this.workingHours = data && data.working_hours ? data.working_hours : 0
          this.isCleanTime = data.is_clean && data.is_clean === 'T'
          this.refreshLeftBotCmpData('start')
          this.handleGetTemDir()
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 放弃清洗
    giveUpDirFun(number) {
      if (number && number === '0') {
        this.$message.error(this.$t('confirm.cnt8'), 5)
        this.giveUpLoading = false
        return
      }
      // const _this = this
      this.giveUpVisible = true
      this.giveUpNumber = number
      // this.$confirm({
      //   title: _this.$t('confirm.title'),
      //   content: h => {
      //     return h('div', {}, [
      //       h('span', _this.$t('confirm.cnt6')),
      //       h('span', { attrs: { style: 'color: red;padding: 0 5px;' }}, number),
      //       h('span', _this.$t('confirm.cnt7') + '，'),
      //       h('div', _this.$t('confirm.cnt4'))
      //     ])
      //   },
      //   onOk() {
      //     _this.givUpDirApi()
      //   },
      //   onCancel() {
      //     _this.giveUpLoading = false
      //   }
      // })
    },
    giveUpHandleOk() {
      // 检查是否已经选择了一个选项 印度必填
      if (!this.giveUpReasonOption && this.user.team_id === 3) {
        this.reasonRequireError = true
        return
      }
      // 提交后台请求，这需要你自己来完成
      // this.$http.post('/api/submitOption', {option: this.selectedOption});
      this.givUpDirApi()
      // 关闭对话框
      this.giveUpVisible = false
      this.giveUpReasonOption = ''
    },
    giveUpHandleCancel() {
      // 提交后台请求，这需要你自己来完成
      // this.$http.post('/api/submitOption', {option: this.selectedOption});
      // this.givUpDirApi()
      // 关闭对话框
      this.giveUpVisible = false
      this.giveUpLoading = false
      this.giveUpReasonOption = ''
    },
    giveUpReasonHandleChange(value) {
      if (value) {
        this.reasonRequireError = false
      }
      console.log(`selected ${value}`)
    },
    givUpDirApi() {
      giveUpDir({ dir_id: this.cleanDirId, reason: this.giveUpReasonOption }).then(res => {
        this.giveUpLoading = false
        if (res.errcode === 0) {
          this.refreshLeftBotCmpData('giveup')
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 获取剩余放弃次数
    giveUpDirNumberFun() {
      this.giveUpLoading = true
      giveUpDirNumber().then(res => {
        if (res.errcode === 0) {
          this.giveUpDirFun(res.data)
        } else {
          this.giveUpLoading = false
          this.$message.error(res.errmsg)
        }
      })
    },
    // 开始工作计时(初始化5s)
    startTimer() {
      const t = getHours()
      if (!t) { // 当本地没有保存的工时时
        setHours(this.cleanDirId, 5)
      }
      if (t && t.workId && t.workId !== this.cleanDirId) { // 当开始的文件夹id不相同时，重新计时
        setHours(this.cleanDirId, 5)
      }
      if (this.isCleanTime) { // 当开始接口后端返回的是否重新计时为T，重新计时
        setHours(this.cleanDirId, 5)
      }
      clearInterval(this.timer)
      this.timer = setInterval(() => {
        if (this.count <= 60 && !this.pausing) {
          const _time = getHours()
          if (_time) {
            const _t = parseInt(_time.value) + 5
            setHours(this.cleanDirId, _t)
            this.showWorkHours(_t)
          } else {
            setHours(this.cleanDirId, 0)
          }
        }
        // 印度团队无操作2分钟后，自动暂停文件夹清洗
        if (this.user.team_id === 3 && this.count >= 120 && !this.pausing) {
          this.stopDirFun()
        }
        this.count += 5
      }, 5000)
    },
    // 结束计时
    cleanTimer() {
      clearInterval(this.timer)
    },
    // 弹出工时提示
    showWorkHours(t) {
      const has_work = dateUtils(this.workingHours + t)
      const now_time = this.$moment().format('lll')
      if (has_work.h !== 0 && has_work.m === 0 && has_work.s === 0) {
        this.$notification.info({
          message: this.$t('confirm.title'),
          description: (h) => {
            return h('div', null, [
              h('span', this.$t('confirm.workHours')),
              h('span', { style: 'color: red;font-weight: bold;' }, ` ${has_work.h}H`),
              h('div', { style: 'text-align: right;margin-top: 15px;' }, `${now_time}`)
            ])
          },
          duration: null,
          placement: 'topLeft'
        })
      }
    },
    // 提交
    submitDirFun() {
      const _t = parseInt(getHours().value)
      const _str = _t + 'TD'
      const param = {
        dir_id: this.cleanDirId,
        sha_str: sha1(_str),
        working_hours: _t
      }
      const is_clean_empty = this.$route.query.is_clean_empty
      const task_flow_type = parseFloat(this.$route.query.task_flow_type)
      if ((task_flow_type === 10 || task_flow_type === 11) && is_clean_empty === 'F') {
        const _this = this
        this.$confirm({
          title: _this.$t('confirm.title'),
          content: task_flow_type === 10 ? this.$t('confirm.cnt5')  : this.$t('confirm.cnt11') ,
          onOk() {
            _this.submitDirApi(param)
          }
        })
      } else if (task_flow_type === 12 && is_clean_empty === 'F') {
        const _this = this
        this.$confirm({
          title: this.$t('confirm.title'),
          content: this.$t('confirm.cnt12'),
          onOk() {
            _this.submitDirApi(param)
          }
        })
      } else {
        this.submitDirApi(param)
      }
    },
    submitDirApi(param) {
      this.submitLoading = true
      submitDir(param).then(res => {
        this.submitLoading = false
        if (res.errcode === 0) {
          this.$message.success(res.errmsg)
          this.refreshLeftBotCmpData('submit')
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 修改选中的图片id集合
    handleSetPicIds(ids) {
      if (this.count !== 0) { // 初始化页面无操作时间
        this.count = 0
      }
      this.currentPicIds = ids
    },
    // 中间小图点击事件反馈
    hanldMidItemClick(url) {
      if (url) {
        const str = url.split('?')
        const picUrl = str[0] + '?imageMogr2/format/webp'
        const coordinate = str[1].split('/')[2].split('x')
        this.$refs.canvasCmp.doDraw(picUrl, coordinate)
      }
    },
    // 清洗中点击临时文件夹
    handleSetTemDir(id) {
      this.showRightMouse = true
      this.midDataDirId = id
    },
    // 数字快捷键拖动图片
    handleMidNumKey(index) {
      if (!this.cleaning) return
      const newDirId = this.rightTemDirList.filter(item => item.webCode === index)[0]
      if (this.currentPicIds.length > 0 && newDirId && !this.submitLoading) {
        const param = {
          pic_id_list: this.currentPicIds,
          old_dir_id: this.midDataDirId,
          new_dir_id: newDirId.dir_id
        }
        this.movePicApi(param)
      }
    },
    // 判断此sku_id有没有已经创建了临时文件夹
    hasThisTemSku(skuId) {
      const hasThisSku = this.rightTemDirList.some(item => {
        if (item.sku_id === skuId) {
          return true
        }
      })
      // this.rightTemDirList.forEach(item => {
      //   if (item.sku_id === skuId) { hasThisSku = true }
      // })
      return hasThisSku
    },
    // 左侧文件夹点击事件
    handleDirClick(item) {
      if (this.cleaning && item.dir_id !== this.cleanDirId) {
        return
      }
      this.midDataDirName = item.name
      if (this.midDataDirId === item.dir_id) {
        this.$refs.midCmp.getData(true)
      }
      this.midDataDirId = item.dir_id
      this.cleanDirId = item.dir_id
      this.showRightMouse = false
      if (item.operator_id) {
        // 当有人操作时，获取右侧临时文件夹集合
        this.handleGetTemDir()
      } else {
        this.rightTemDirList = [] // 清空右侧临时文件夹集合
      }
    },
    // 添加项目下临时文件夹
    createTmpDirFun(item) {
      // 判断此sku_id有没有已经创建了临时文件夹
      const hasThisSku = this.rightTemDirList.some(it => it.name === item.name)
      if (!this.cleaning || !this.cleanDirId || !item.sku_id || hasThisSku) {
        return
      }
      const param = {
        dir_id: this.cleanDirId,
        sku_id: item.sku_id
      }
      createTmpDir(param).then(res => {
        if (res.errcode === 0) {
          this.handleGetTemDir()
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 获取右侧临时文件夹集合
    handleGetTemDir() {
      getTemDirList(this.taskFlowId, this.cleanDirId).then(res => {
        if (res.errcode === 0) {
          const _newArr = res.data.dir_list
          const _rArr = _newArr.map((item, index) => {
            const rev = _newArr.length - index
            if (rev <= 9) {
              return {
                ...item,
                webCode: rev
              }
            } else {
              return item
            }
          })
          this.rightTemDirList = _rArr
        }
      })
    },
    // 刷新左下角文件夹数据
    refreshLeftBotCmpData(type) {
      // if (this.taskFlowType !== 12) { // 当不是汇总流程时，刷新左下角文件就 }
      switch (type) {
        case 'start':
          this.cleaning = true
          this.startTimer()
          this.$refs.leftBotCmp.getDirListFun(true)
          break
        case 'giveup':
          this.sortSeedList = []
          this.restPageParam()
          this.$refs.leftBotCmp.getDirListFun(true)
          break
        case 'submit':
          this.sortSeedList = []
          this.restPageParam()
          this.$refs.leftBotCmp.getDirListFun(true)
          this.getUserTodayActionAmount()
          break
        case 'pause':
          this.$refs.leftBotCmp.getDirListFun(true)
          break
        default:
          break
      }
    },
    // 提交或放弃成功后，重置页面状态变量
    restPageParam() {
      this.showRightMouse = false
      this.pausing = false
      this.cleanDirId = null
      this.cleaning = false
      this.midDataDirId = null
      this.midDataDirName = ''
      this.cleanLeftBotData = []
      this.currentPicIds = []
      // 结束工作计时
      this.cleanTimer()
      removeHours()
    },
    // 增加每行小图展示个数
    plusNum() {
      const dom = document.getElementById('mid_cnt_cmp').getElementsByClassName('mid_content')[0].firstChild
      if (this.scaleSize + 35 >= parseFloat(dom.offsetWidth / 2)) {
        this.$message.info('已到最大尺寸')
        return
      }
      this.percentNum += 10
      this.getScaleSize()
    },
    // 减少每行小图展示个数
    minusNum() {
      if (this.scaleSize < 100) {
        this.$message.info('已到最小尺寸')
        return
      }
      this.percentNum -= 10
      this.getScaleSize()
    },
    // 获取中间区域大小计算每个图片所占宽度
    getScaleSize() {
      const dom = document.getElementById('mid_cnt_cmp').getElementsByClassName('mid_content')[0].firstChild
      const g = (100 - this.percentNum) / 10 + 5
      const size = parseFloat((dom.offsetWidth - g * this.midItemMargin * 2) / g)
      this.scaleSize = size
    },
    // 页面loading,状态
    handlePageLoading(f) {
      this.submitLoading = f
    },
    getProjectDetail() { // 获取项目信息
      getProjectInfo(this.projectId).then(res => {
        if (res.errcode === 0) {
          this.project = res.data
          // this.project.task_rule = 'https://www.baidu.com'
          this.projectName = res.data.name
          this.tool_task_id = res.data.external_task_id
          if (this.projectName) {
            // todo 判断是否可以排序 项目名称包含 _ALPHA_ 才能实现排序逻辑
            if (this.projectName.includes('_ALPHA_')) {
              searchCanBeSort({ task_id: this.tool_task_id }).then((res) => {
                if (res.code === 200) {
                  const { is_reorder } = res.data
                  this.canBeSort = !!is_reorder
                }
              })
            }
          }
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    openTaskRule() {
      window.open(this.project.task_rule, '_blank')
    },
    getUserTodayActionAmount() {
      getUserTodayActionAmount(this.user.user_id).then(res => {
        if (res.errcode === 0) {
          this.todayDirTotal = res?.data?.dir_count ?? 0
          this.todayPicTotal = res?.data?.image_count ?? 0
        } else {
          this.$message.error(res.errmsg)
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.handle_container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  padding-bottom: 56px;
  box-sizing: border-box;
  position: relative;
  .left_cnt, .mid_cnt, .right_cnt {
    height: 100%;
  }
  .left_cnt {
    width: 530px;
    display: flex;
    flex-direction: column;
    .canvas_400 {
      height: 400px;
    }
    .folder_list {
      flex: 1;
      position: relative;
    }
  }
  .mid_cnt {
    flex: 1;
    position: relative;
    background-color: #fff;
    border-left: 1px solid #f2f2f2;
    border-right: 1px solid #f2f2f2;
  }
  .right_cnt {
    width: 280px;
  }
  .fix_bot {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    padding: 10px 20px;
    background-color: #282828;
    color: #ffffff;
    display: flex;
    align-items: center;
    .lt {
      min-width: 600px;
      .dec {
        font-size: 16px;
        span {
          margin-right: 10px;
        }
      }
    }
    .ant-btn {
      margin-right: 15px;
    }
    .bg_color_1 {
      background-color: #5daf34;
      border-color: #5daf34;
    }
    .md {
      flex: 1;
      text-align: center;
      font-size: 1.75em;
      cursor: pointer;
      .scale_num {
        padding: 0 20px;
        user-select: none;
      }
    }
  }
}
.current_img_list {
  position: fixed;
  top: 0;
  left: 600px;
  padding: 5px 10px;
  border-radius: 5px;
  border: 1px solid #cccccc;
  background-color: #ffffff;
  color: #333333;
  .current_img_item {
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0.8;
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
}
.submit_load {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, .1);
}
.sort_seed_container{
  position: relative;
  .sort_button{
    cursor: pointer;
    position: absolute;
    bottom:22px;
    right:25px;
    z-index:3;
  }
}
.sort_seed_image_contain{
  //border:1px solid #e6e6e6;
  height:140px;
  overflow-y: hidden;
  overflow-x: scroll;
  padding:10px;
  // 禁止换行
  white-space: nowrap;
  position: relative;
}
.sort_seed_image{
  cursor: pointer;
  width:80px;
  height:100px;
  background-color: #f5f7fa;
  display: inline-block;
  margin-right:10px;
  position: relative;
}
.target_sku_scroll_box{
  height: calc(100% - 32px);
  overflow-y: auto;
}
.target_sku_list{
  height:calc(100% - 250px - 140px); // 250 + 140
}
</style>
<style lang="less">
.sort_button{
  .ant-btn {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3); /* 明显一些的阴影效果 */
  }
}

</style>
