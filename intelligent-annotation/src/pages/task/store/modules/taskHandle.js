export default {
  namespaced: true,
  state: {
    showPackage: false,
    skuPackages: [],
    sku: {},
    projectId: null
  },
  getters: {
    skuPackages: state => {
      return state.skuPackages
    },
    sku: state => {
      return state.sku
    },
    projectId: state => {
      return state.projectId
    },
    showPackage: state => {
      return state.showPackage
    }
  },
  mutations: {
    setSkuPackages(state, skuPackages) {
      state.skuPackages = skuPackages
    },
    setSku(state, sku) {
      state.sku = sku
    },
    setProjectId(state, projectId) {
      state.projectId = projectId
    },
    setShowPackage(state, showPackage) {
      state.showPackage = showPackage
    }

  }
}
