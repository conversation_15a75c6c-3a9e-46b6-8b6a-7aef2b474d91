<template>
  <form-drawer title="项目详情" :visible="openDraw" @closeDrawer="handleDrawerClose">
    <a-card :bordered="false" :body-style="{padding: 0}">
      <div v-html="dec" />
    </a-card>
    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1,
      }"
    >
      <a-button :style="{ marginRight: '8px' }" @click="handleDrawerClose">
        {{ $t('buttons.cancel') }}
      </a-button>
    </div>
  </form-drawer>
</template>

<script>
import FormDrawer from '@/components/formDrawer'
export default {
  name: 'TaskDetail',
  components: { FormDrawer },
  data() {
    return {
      openDraw: false,
      dec: '项目描述'
    }
  },
  methods: {
    handleDrawerClose() {
      this.openDraw = false
    }
  }
}
</script>

<style>

</style>
