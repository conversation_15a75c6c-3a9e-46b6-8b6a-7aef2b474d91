<template>
  <div class="folder_list_container">
    <a-input v-if="cleanData.length === 0 && showAllDir" v-model="serVal" class="input_no_radius" size="large" allow-clear @change="getDirListFun(true)">
      <a-icon slot="prefix" type="search" />
      <template slot="suffix">
        <div class="my_input_after">
          <div class="icon_i letter" :class="{'active': order === 2 || order === 3}" @click="handleChangeOrder('letter')">
            <span class="title">字母</span>
            <div class="icon_box">
              <a-icon :class="{'current_icon': order === 2 }" class="icon" type="caret-up" />
              <a-icon :class="{'current_icon': order === 3 }" class="icon" type="caret-down" />
            </div>
          </div>
          <div class="icon_i number" :class="{'active': order === 0 || order === 1}" @click="handleChangeOrder('number')">
            <span class="title">数量</span>
            <div class="icon_box">
              <a-icon :class="{'current_icon': order === 1 }" class="icon" type="caret-up" />
              <a-icon :class="{'current_icon': order === 0 }" class="icon" type="caret-down" />
            </div>
          </div>
        </div>
      </template>
    </a-input>
    <!--滚动加载部分-->
    <div class="folder_list_scroll">
      <FolderList
        ref="folderListCmp"
        :lists="showDataList"
        :current-id="currentId"
        :loading="loading"
        :show-end="dataEnd"
        :show-start="!showAllDir"
        @onScrollEnd="onscroll"
        @onFolderClick="handleClick"
        @onAvatarClick="createTmpDirFun"
      />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getDirList } from '@/services/work'
import FolderList from './folder-list.vue'
export default {
  name: 'LeftBotCmp',
  components: { FolderList },
  props: {
    currentId: {
      type: Number,
      default: null
    },
    cleanData: {
      type: Array,
      default: () => {
        return []
      }
    },
    showAllDir: {
      type: Boolean,
      default: () => {
        return true
      }
    },
    flowId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      serVal: '',
      order: 2, // 排序方式 2字母正序3字母倒叙 0数量倒叙1数量正序
      loading: false, // 数据加载中状态
      dataEnd: false, // 数据加载完
      data: [], // 展示的数据
      page: 1,
      num: 100,
      total: 0
    }
  },
  computed: {
    ...mapGetters('account', ['user']),
    orderList() {
      return [
        { name: this.$t('dic.lAsc'), value: 1 },
        { name: this.$t('dic.qDesc'), value: 0 }
      ]
    },
    showDataList() {
      const data = this.showAllDir ? this.data : this.cleanData
      return data
    }
  },
  methods: {
    handleChangeOrder(type) { // order 排序改变
      switch (type) {
        case 'letter':
          this.order = this.order === 2 ? 3 : 2
          break
        case 'number':
          this.order = this.order === 0 ? 1 : 0
          break
        default:
          break
      }
      this.getDirListFun(true)
    },
    // 获取项目下的文件夹
    getDirListFun(first) {
      this.loading = true
      if (first) { this.page = 1 }
      const param = {
        dir_name: this.serVal,
        order: this.order,
        task_flow_id: this.flowId,
        page: this.page,
        number: this.num
      }
      getDirList(param).then(res => {
        this.loading = false
        if (res.errcode === 0) {
          this.total = res.data.total
          const _list = res.data.data_list
          if (this.page === 1) {
            this.data = _list
            this.$nextTick(() => {
              this.$emit('getDirTotal', this.total)
              const cmp = this.$refs.folderListCmp
              if (cmp) {
                const folder_index = this.data.findIndex(it => it.operator_id === this.user.user_id && it.state === 2)
                if (folder_index !== -1) { // 如果有任务文件夹操作人是登陆人且状态值为2时，直接定位到对应位置
                  cmp.scrollToTop(folder_index)
                  this.$emit('dirClick', this.data[folder_index])
                  return
                }
                cmp.scrollToTop()
              }
            })
          } else {
            this.data = this.data.concat(_list)
          }
          // 是否是最后一页
          this.dataEnd = this.data.length === this.total
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 文件夹点击事件
    handleClick(item) {
      this.$emit('dirClick', item)
    },
    // 创建临时文件夹
    createTmpDirFun(item) {
      this.$emit('createTemDir', item)
    },
    // 滚动条滚动到底部
    onscroll() {
      if (this.dataEnd) return
      if (this.showAllDir && this.data.length === 0) {
        this.getDirListFun(true)
        return
      }
      if (this.showAllDir && this.data.length > 0) {
        this.page += 1
        this.getDirListFun()
      }
    }
  }
}
</script>
<style scoped lang="less">
.my_input_after {
  display: flex;
  .icon_i {
    display: flex;
    align-items: center;
    cursor: pointer;
    &:first-child {
      margin-right: 10px;
    }
    .title { margin-right: 2.5px; }
    .icon_box {
      display: flex;
      flex-direction: column;
      align-items: center;
      .icon:first-child {
        margin-bottom: -2.5px;
      }
      .icon:last-child {
        margin-top: -2.5px;
      }
    }
    &.letter.active {
      .title, .current_icon {
        color: #2ABCE6;
      }
    }
    &.number.active {
      .title, .current_icon {
        color: #ED5C36;
      }
    }
  }
}
.folder_list_container {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  .folder_list_scroll {
    width: 100%;
    height: ~"calc(100% - 40px)";
    overflow-y: auto;
  }
}
</style>
