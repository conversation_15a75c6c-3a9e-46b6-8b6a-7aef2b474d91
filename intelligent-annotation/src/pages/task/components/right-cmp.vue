<template>
  <div class="right_cmp_container" :class="{'has_sort_height':canBeSort}">
    <div class="search_item">
      <a-select ref="skuProjectSelect" class="input_no_radius" style="width: 100%" :value="defaultValue" show-search :filter-option="filterOption" @change="handleChange">
        <a-select-option v-for="item in mList" :key="item.sku_project_id" :value="item.sku_project_id">{{ item.name }}</a-select-option>
      </a-select>
      <a-input class="input_no_radius" allow-clear :value="searchVal" @change="onChange" @focus="onFouce" @blur="onBlur" />
    </div>
    <div style="flex: 1;position: relative;">
      <!-- 拖拽目标的文件夹list -->
      <div id="drag_box" class="move_box drag_sku_list" :style="{'left': !isShowSku ? 0 : '100%'}">
        <div
          v-for="item in dragSkuList"
          :key="item.dir_id"
          :class="{'item': true, 'active': currentId === item.dir_id, 'twinkle': item.dir_id === twinkle_id}"
          drop-class="dropTarget"
          :data-id="item.dir_id"
          @click.stop="handleClick(item)"
        >
          <div v-if="item.webCode" class="code_num">{{ $t('contents.key') }}: {{ item.webCode }}</div>
          <div class="img">
            <template v-if="!item.sku_id">
              <a-avatar
                shape="square"
                :style="{color: item.name === '待删除' ? 'rgb(255, 52, 99)' : 'rgb(18, 150, 219)' }"
                :icon="getIcon(item.name)"
                :size="135"
                :src="item.sku_logo"
              />
            </template>
            <template v-else>
              <a-avatar
                shape="square"
                :style="{color: item.icon && item.icon === 'delete' ? 'rgb(255, 52, 99)' : 'rgb(18, 150, 219)' }"
                icon="picture"
                :size="135"
                :src="item.sku_logo"
              />
            </template>
            <div class="dropTarget" drop-class="dropTarget" :data-id="item.dir_id" />
          </div>
          <div class="name"> {{ item.sku_id ? item.name : transInvalidSku(item.name) }}</div>
          <div class="control_box">
            <div class="order">
              <a-popconfirm @visibleChange="orderInput" @confirm="changeOrder(item)">
                <a slot="icon" />
                <div slot="title" style="margin-left: -11px;">
                  <a-input-number v-model="orderNumber" placeholder="-999~999" :min="-999" :max="999" :step="1" />
                </div>
                <span @click.stop>{{ $t('contents.order') }}: {{ item.show_order || 0 }}</span>
              </a-popconfirm>
            </div>
            <div class="del" @click.stop="delTemSku(item)">
              <a-icon type="delete" />
            </div>
            <div v-if="item.sku_id" class="multiPackage">
              <a-badge :count="item.package_count>1?item.package_count:0" :number-style="{height:'12px',padding:'0 4px',lineHeight: '14px',minWidth: '12px',fontSize:'10px',backgroundColor:'#999'}">
                <a-icon :title="$t( 'contents.multiPackaging')" :class="item.package_count>1 ? 'has-package':'no-package'" type="picture" @click.stop="openPackages(item)" />
              </a-badge>
            </div>
          </div>
        </div>
      </div>
      <!-- sku文件夹list集合 -->
      <div class="move_box" :style="{'left': isShowSku ? 0 : '100%'}">
        <SkuList
          :search-str="searchVal"
          :lists="data"
          :fold-list="dragSkuList"
          :loading="loading"
          @onShowView="showViewerFun"
          @onSkuClick="skuItemClick"
        />
      </div>
    </div>
    <image-viewer v-if="showViewer" :on-close="closeViewer" :url-list="srcList" />
  </div>
</template>

<script>
import ImageViewer from '@/components/imgPreview'
import { apiCommonServer } from '@/services'
import { changeTmpDirOrder, createTmpDir, deleteTmpDir } from '@/services/work'
import { mapActions, mapGetters, mapMutations, mapState } from 'vuex'
import SkuList from './sku-list.vue'

export default {
  name: 'RightCmp',
  components: { SkuList, ImageViewer },
  props: {
    canBeSort: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    cleanState: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    cleanDirId: {
      type: [Number, String],
      default: ''
    },
    currentId: {
      type: [Number, String],
      default: ''
    },
    dragSkuList: {
      type: Array,
      default: () => {
        return []
      }
    },
    project: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      showViewer: false,
      srcList: [],
      defaultValue: null, // 行业目录下拉值
      searchVal: '', // 模糊搜索值
      mList: [], // 行业目录数据集合
      cacheData: [], // 所有sku数据集合
      skuListCacheTable: this.$forage.createInstance({
        name: 'zhibiao',
        storeName: 'sku_list'
      }), // 所有sku数据集合
      data: [], // 展示出来的sku集合
      loading: true,
      isShowSku: false,
      fixedSkuList: [ // 前台固定的三个Sku <a-icon type="dash" />
        {
          icon: 'dash',
          master_url: '',
          master_url_en: '',
          name: '其他品牌',
          sku_id: -1
        },
        {
          icon: 'file-excel',
          master_url: '',
          master_url_en: '',
          name: '未找到SKU',
          sku_id: -2
        }, {
          icon: 'delete',
          master_url: '',
          master_url_en: '',
          name: '待删除',
          sku_id: -3
        }, {
          icon: 'file-exclamation',
          master_url: '',
          master_url_en: '',
          name: '待修复检出',
          sku_id: -4
        }, {
          icon: 'file-add',
          master_url: '',
          master_url_en: '',
          name: 'NEW SKU',
          sku_id: -5
        }
      ],
      twinkle_id: null, // 闪烁的临时文件夹ID
      orderNumber: null,
      usedRandomNumbers: new Set() // 新增：用于存储已使用的随机数
    }
  },
  computed: {
    ...mapGetters('account', ['user']),
    ...mapState('setting', ['lang']),
    ...mapState('taskHandle', ['showPackage'])
  },
  watch: {
    dragSkuList: {
      handler(val, oldVal) {
        const box = document.getElementById('drag_box')
        box.scrollTop = 0
      },
      deep: true // true 深度监听
    },
    // 多包装变化，触发是否显示sku-list
    showPackage(val) {
      this.onBlur()
    }

  },
  created() {
    this.getMList()
  },
  methods: {
    ...mapMutations('taskHandle', ['setSkuProject', 'setShowPackage', 'setSku', 'setSkuPackages', 'setProjectId']),
    ...mapActions('taskHandle', ['getSkuPackege', 'showSkuPackage']),
    // 关闭查看大图
    closeViewer() {
      this.showViewer = false
    },
    showViewerFun(url) {
      if (url) {
        this.srcList = [url]
        this.showViewer = true
      }
    },
    // 行业目录搜索
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    },
    getIcon(item) {
      // 优先匹配固定 SKU 的图标
      const fixedItem = this.fixedSkuList.find(it => it.name === item);
      if (fixedItem) {
          return fixedItem.icon;
      } else {
          // 新增逻辑：如果是 "NEW SKU" 开头的 name，也返回对应的图标
        if (item.startsWith('NEW SKU')) {
          return 'file-add'; // 对应 NEW SKU 的图标
        }
        return null; // 默认无图标
      }
    },
    // 获取行业目录
    getMList() {
      apiCommonServer.skuProjectList().then(res => {
        if (res.errcode === 0) {
          if (res.data.sku_project_list && res.data.sku_project_list.length > 0) {
            const localSkuProject = localStorage.getItem('mySetSkuProjectId')
            if (this.project && this.project.is_fix_sku_project === 'T') {
              // 获取项目设置的sku项目
              this.defaultValue = this.project.sku_project_id
              if (this.user.role_id === 1) { // 对标注员角色，禁用切换行业目录
                this.$refs.skuProjectSelect.disabled = true
              }
            } else if (localSkuProject) {
              this.defaultValue = parseInt(localSkuProject)
            } else {
              this.defaultValue = res.data.sku_project_list[0].sku_project_id
            }
            this.mList = res.data.sku_project_list
            this.handleChange(this.defaultValue)
          }
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 下拉行业目录下的所有sku数据
    handleChange(value) {
      this.loading = true
      this.data = []
      this.defaultValue = value
      this.setSkuProject(this.mList.find(it => it.sku_project_id === value))
      localStorage.setItem('mySetSkuProjectId', value)
      // console.log(this.skuListCacheTable)
      // 本地缓存sku，有些sku有几兆
      this.skuListCacheTable.getItem(value).then((val) => {
        console.log('getItem', value, val)
        this.defaultValue = value
        if (val) {
          this.loading = false
          this.cacheData = val
          this.data = [...this.fixedSkuList, ...val]
        }
      }).catch(err => {
        this.$message.error(err)
      })
      // 无论本地有无缓存，都在后台请求更新
      apiCommonServer.skuList(value).then(res => {
        this.loading = false
        if (res.errcode === 0) {
          // if (this.data.length === 0) {
          this.cacheData = res.data.sku_list
          this.data = [...this.fixedSkuList, ...res.data.sku_list]
          // }
          this.skuListCacheTable.setItem(value, res.data.sku_list).then((val) => {
            // console.log('setItem', value, val)
          }).catch(err => {
            console.log(err)
            this.$message.error('sku缓存写入失败')
          })
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 模糊搜索
    onChange(e) {
      if (e) this.searchVal = e.target.value
      if (this.searchVal) {
        const arr = this.searchVal.split(' ')
        const filterData = this.cacheData.filter(item => this.inspectData(item.name, arr))
        // 过滤分页
        this.data = this.fixedSkuList.concat(filterData)
      } else {
        this.data = [...this.fixedSkuList, ...this.cacheData]
      }
    },
    inspectData(str, arr) {
      if (arr.length === 1) {
        return str.toLowerCase().includes(arr[0].toLowerCase())
      }
      let hasStr = true
      for (let i = 0; i < arr.length; i++) {
        if (!str.toLowerCase().includes(arr[i].toLowerCase())) {
          hasStr = false
          break
        }
      }
      return hasStr
    },
    // 搜索框获取焦点
    onFouce() {
      this.$emit('inputIng', true)
      if (this.data.length === 0) {
        this.data = [...this.cacheData]
      }
      this.isShowSku = true
      this.fixedSkuList.map(item => {
        if(item.sku_id ===-5) item.name = 'NEW SKU'
      })
    },
    // 搜索框失去焦点
    onBlur() {
      this.$emit('inputIng', false)
      setTimeout(() => {
        if (this.searchVal && !this.cleanState || this.showPackage) { // 当输入框有值，且不在清洗中时不隐藏sku列表
          return
        }
        this.isShowSku = false
      }, 500)
    },
    // sku点击事件（添加项目下临时文件夹）
    skuItemClick(item) {
      if (item.sku_id === -5&&this.cleanState) {
        let randomNum = Math.floor(Math.random() * 10) + 1;
        const maxAttempts = 10; // 最大尝试次数
        let attempts = 0;

        // 确保随机数不重复，并且不超过最大尝试次数
        while (this.usedRandomNumbers.has(randomNum)) {
          if (attempts >= maxAttempts) {
            // 超出尝试次数，提示用户
            this.$message.warning(this.$t('confirm.maxAttemptsExceeded'));
            break;
          }
          randomNum = Math.floor(Math.random() * 10) + 1;
          attempts++;
        }

        // 如果成功找到唯一的随机数，则加入集合
        if (!this.usedRandomNumbers.has(randomNum)) {
          this.usedRandomNumbers.add(randomNum);
        }

        item.name = `NEW SKU ${randomNum}`
        this.searchVal = item.name;
      } else {
        this.searchVal = item.name;
      }
      if (!this.cleanDirId) {
        this.$message.error(this.$t('confirm.folder'))
        return
      }
      if (!this.cleanState) {
        this.$message.error(this.$t('confirm.start'))
        return
      }
      let param = null
      if (this.fixedSkuList.some(it => it.sku_id === item.sku_id)) { // 当时固定sku时id传0，sku_name必传
        param = {
          dir_id: this.cleanDirId,
          sku_id: 0,
          sku_name: item.name
        }
      } else {
        param = {
          dir_id: this.cleanDirId,
          sku_id: item.sku_id
        }
      }
      // 是否已经创建了临时文件夹
      const hasCreate = this.dragSkuList.find(it => it.name === item.name)
      if (hasCreate) {
        // this.$message.error(this.$t('confirm.sku'))
        this.searchVal = ''
        this.isShowSku = false
        this.scrollFolder(hasCreate)
        return
      }
      createTmpDir(param).then(res => {
        if (res.errcode === 0) {
          this.isShowSku = false
          this.searchVal = ''
          this.$emit('temDirList')
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 滚动到对应的临时文件夹
    scrollFolder(obj) {
      this.$nextTick(() => {
        const box = document.getElementById('drag_box')
        const items = box.children
        for (let index = 0; index < items.length; index++) {
          const element = items[index]
          if (Number(element.getAttribute('data-id')) === obj.dir_id) {
            box.scrollTop = element.offsetTop
            this.twinkle_id = obj.dir_id
            setTimeout(() => {
              this.twinkle_id = null
            }, 3000)
            break
          }
        }
      })
    },
    // 删除临时文件夹
    delTemSku(item) {
      deleteTmpDir(item.dir_id).then(res => {
        if (res.errcode === 0) {
          this.$emit('temDirList')
          if (item.name.startsWith('NEW SKU')) {
            item.name = 'NEW SKU'
          }
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 点击临时文件夹
    handleClick(item) {
      this.$emit('setTemDir', item.dir_id)
    },
    // 排序输入框输入
    orderInput(open) {
      this.$emit('inputIng', open)
    },
    // 修改临时文件夹序号
    changeOrder(item) {
      if (!this.orderNumber && this.orderNumber !== 0) {
        this.$message.error('请输入-999~999的数字！')
        return
      }
      const param = {
        dir_id: item.dir_id,
        show_order: this.orderNumber
      }
      changeTmpDirOrder(param).then(res => {
        if (res.errcode === 0) {
          this.$emit('temDirList')
          this.orderNumber = null
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    transInvalidSku(name) {
      const invalidSku = {
        '其他品牌': 'other_brands',
        '未找到SKU': 'sku_not_found',
        '待删除': 'to_be_deleted',
        '待修复检出': 'check_out_to_be_repaired',
        'NEW SKU': 'new_sku'
      }
      if (invalidSku[name]) {
        return this.$t('skuInfo.' + invalidSku[name])
      }
      return name
    },
    // 打开多包装
    openPackages(sku) {
      this.showSkuPackage({ sku: sku, showPackage: true })
    }
  }
}
</script>
<style>
  .ant-avatar > img {
    object-fit: contain;
  }
</style>
<style scoped lang="less">
  @keyframes twinkling{
    0%{
      background-color: #fff3a9;
      border-bottom-color: #67c23a;
    }
    100%{
      background-color: transparent;
      border-bottom-color: rgb(217, 217, 217);
    }
  }
  .right_cmp_container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    &.has_sort_height{
      height: calc(100% - 140px);
    }
    .ant-list-item-meta {
      align-items: center;
    }
    .ant-list-item {
        padding-left: 15px;
        // background-color: #ffffff;
    }
    .ant-list-item-meta-title{
      font-size: 12px;
      line-height: 1.5;
      margin: 0;
    }
    .move_box {
      position: absolute;
      top:0;
      width:100%;
      height: 100%;
      overflow-y: auto;
      translate: left 1s linear;
    }
    .drag_sku_list {
      .item {
        position: relative;
        width: 100%;
        padding: 20px 10px 10px;
        // background-color: #ffffff;
        box-shadow: 0 2px 10px 0 rgba(0, 0, 0, .1);
        border-bottom: 1px solid rgb(217, 217, 217);
        display: flex;
        flex-direction: column;
        cursor: pointer;
        &.active {
          background-color: #fff3a9;
        }
        &:hover {
          border-bottom-color: #67c23a;
          .name {
            color:  #67c23a;
          }
        }
        &.twinkle {
          animation: twinkling 1s infinite ease-in-out;
        }
        .img {
          flex: 1;
          text-align: center;
          position: relative;
          .dropTarget {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
          }
        }
        .name {
          padding-top: 10px;
          font-size: 12px;
          color: #333333;
        }
        .code_num {
          position: absolute;
          left: 5px;
          top: 0;
          color: #333333;
        }
        .control_box {
          position: absolute;
          right: 10px;
          top: 0;
          display: flex;
          align-items: center;
          .del, .order {
            background-color: rgb(119, 117, 117, .4);
            color: #ffffff;
            cursor: pointer;
            padding: 3px 8px;
            &:hover {
              background-color: #1890ff;
            }
          }
          .order {
            margin-right: 5px;
          }
        }
        .multiPackage {
          position: absolute;
          right: 10px;
          top: 30px;
          color: #e3e3e3;
          cursor: pointer;
          padding: 3px 8px;

          &:hover {
            //background-color: #1890ff;
            color: #0b4bd7;
          }

          //color: #e3e3e3;
          .has-package {
            font-size: 1.2em;
            color: #1fb481;
            //background-color: rgb(119, 117, 117);
            &:hover {
              font-size: 1.3em;
            }
          }

          .no-package {
            font-size: 1.2em;

            &:hover {
              font-size: 1.3em;
            }
          }

        }
      }
    }
  }
  .right_hover_img {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 10;
    background-color: #ffffff;
    border-radius: 3px;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    width: 200px;
    height: 200px;
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
</style>
