<template>
  <div v-if="showPackage" ref="skuPackage" class="">

    <a-drawer
      class="sku-package"
      :title="$t('contents.multiPackaging')+': '+ sku.name"
      placement="bottom"
      :closable="true"
      :visible="showPackage"
      get-container=".mid_cnt"
      :style="{ position: 'absolute',}"
      :header-style="{ padding:'6px 12px' }"
      :body-style="{ padding:'10px' }"
      height="250"
      :z-index="50"
      :after-visible-change="afterVisibleChange"
      @close="onClose"
    >
      <div class="sku-package-panel">
        <div id="sku-package-list" ref="skuPackageList" class="sku-package-list">
          <div v-for="item in skuPackages" :key="item.id" class="pack-item">
            <div class="logo">
              <img
                :src="lang === 'CN' ? item.before_url : item.master_url_en"
                alt=""
                @mouseenter="handleEnter"
                @mouseleave="handleLeave"
              >

            </div>
            <div class="title">{{ item.created_date }}</div>
          </div>
        </div>
        <div v-if="canAddPackage" class="sku-package-add">
          <div class="add-icon">
            <a-icon type="plus-square" @click="addPackagePanel" />
          </div>
          <div style="margin-top: 10px">{{ $t('drawer.addPackage') }}</div>
        </div>
      </div>

    </a-drawer>
    <AddSkuPackageForm ref="addSkuCmp" :working="tasking" :sku-id="sku.sku_id" @onSuccess="handleSuccess" />
    <transition name="loading-fade">
      <div v-show="hoverImgSrc" id="package_hover_img" class="package_hover_img">
        <img :src="hoverImgSrc" alt="">
      </div>
    </transition>
  </div>
</template>

<script>
// import { getSkuPackageList } from '@/services/work'
import { mapGetters, mapMutations, mapActions } from 'vuex'
import AddSkuPackageForm from '@/pages/task/components/add-sku-package-form'

export default {
  name: 'SkuPackages',
  components: { AddSkuPackageForm },
  props: {
    lists: {
      type: Array,
      default: () => {
        return []
      }
    },
    loading: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    searchStr: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      documentObj: null,
      hoverImgSrc: '',
      tasking: true, // 是否开始了任务
      title: this.$t('drawer.add', { name: 'SKU' }),
      form: this.$form.createForm(this)
    }
  },
  computed: {
    ...mapGetters('taskHandle', ['showPackage', 'skuPackages', 'sku', 'skuProject']),
    ...mapGetters('account', ['user']),
    ...mapGetters('setting', ['lang']),
    // 权限判断 郑州/上海 todo 后端处理权限逻辑
    canAddPackage() {
      return [1, 2].indexOf(parseInt(this.user.team_id)) > -1
    }
  },
  watch: {
    list: {
      handler(newName, oldName) {

      },
      immediate: true,
      deep: true

    }
  },
  mounted() {
    console.log('mounted sku-packages')
  },
  created() {
  },
  // 销毁事件
  beforeDestroy() {
    if (!this.documentObj) return
    this.documentObj.removeEventListener('DOMMouseScroll', this.handlerMouserScroll)
    this.documentObj.removeEventListener('mousewheel', this.handlerMouserScroll)
  },
  methods: {
    ...mapMutations('taskHandle', ['setSkuPackages', 'setSku', 'setShowPackage']),
    ...mapActions('taskHandle', ['getSkuPackege']),
    afterVisibleChange(bool) {
      console.log('滚动事件1')
      this.$nextTick(() => {
        console.log('滚动事件')
        this.setScroolFun()
      })
    },
    showViewerFun(url) {
      this.$emit('onShowView', url)
    },
    onClose(e) {
      this.setShowPackage(false)
    },
    addPackagePanel() {
      this.$refs.addSkuCmp.openDrawer()
    },
    handleOk(e) {
      console.log(e)
      this.visible = false
    },
    handleSuccess(data) { // 注册或修改sku成功反馈
      this.getSkuPackege(this.sku)
    },
    // 鼠标移入
    handleEnter(e) {
      if (e.target.src) {
        const domBody = document.body
        const dom = document.getElementById('package_hover_img')
        if (domBody.offsetHeight < e.y + 856) {
          dom.style.top = parseInt(domBody.offsetHeight - 856) + 'px'
        } else {
          dom.style.top = e.y + 'px'
        }
        dom.style.left = parseInt(e.x - 550) + 'px'
        this.hoverImgSrc = e.target.src
      }
    },
    // 鼠标移出
    handleLeave() {
      this.hoverImgSrc = ''
    },
    /* 容器绑定鼠标滚轮事件*/
    setScroolFun() {
      // 绑定的容器
      this.documentObj = document.getElementById('sku-package-list')// 获取DOM元素节点
      console.log(this.documentObj)
      // 添加监听事件（不同浏览器，事件方法不一样，所以可以作判断，也可以如下偷懒）
      this.documentObj.addEventListener('DOMMouseScroll', this.handlerMouserScroll, false)// 滚轮事件
      this.documentObj.addEventListener('mousewheel', this.handlerMouserScroll, false)// 滚轮事件
    },
    handlerMouserScroll(event) {
      // 获取滚轮跨距，兼容获取方式
      const detail = event.wheelDelta || event.detail || event.wheelDeltaY
      /* 反向*/
      const moveForwardStep = -1
      /* 正向*/
      const moveBackStep = 1
      let step = 0
      // 如果跨步大于0，表明正向跨步，将跨步放大100倍，改变滑动速度，如果跨步小于0，表明反向跨步，将跨步放大500倍，改变滑动速度
      step = detail > 0 ? moveForwardStep * 10 : moveBackStep * 10
      /* 覆盖当前滚动条的位置,单位是像素，叠增或剃减*/
      this.documentObj.scrollLeft = this.documentObj.scrollLeft + step

      // 平滑值(越小越慢，不能小于等于0)
      const slipNum = 0.8
      // 末尾值（越小，则越平稳，越大越仓促）
      const endNum = 5
      /* 递减步伐值*/
      let decreasingPaceNum = step
      /* 速度*/
      const paceNum = 60

      /* 效果一*/
      const t = setInterval(() => {
        if (Math.abs(decreasingPaceNum) < endNum) {
          clearInterval(t)
          return
        }
        decreasingPaceNum = decreasingPaceNum * slipNum
        this.documentObj.scrollLeft = this.documentObj.scrollLeft + decreasingPaceNum
      }, paceNum)
    }
  }
}
</script>

<style lang="less" scoped>

.sku-package {
  position: relative;

  /deep/ .ant-drawer-close {
    width: 35px;
    height: 35px;
    line-height: 35px;
  }

  //.ant-drawer-body {
  //  padding: 10px;
  //}
  .sku-package-panel {

    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 100%;
    display: flex;

    .sku-package-list {
      display: flex;
      //flex-direction:row;
      //overflow:auto;
      overflow-x: scroll;
      overflow-y: hidden;

      .pack-item {
        flex-flow: row;
        child-align: middle;
        display: table-cell;
        vertical-align: middle;
        text-align: center;

        .logo {
          width: 100px;
          box-sizing: border-box;
          border: 1px solid #e0e0e0;
          height: 120px;
          margin-right: 4px;

          img {
            max-width: 100%;
            max-height: 100%;
            display: block;
            margin: auto;
          }
        }

        .title {
          margin-top: 4px;
          line-height: 1em;
        }
      }
    }

    .sku-package-add {
      width: 100px;
      //height: 200px;
      text-align: center;

      .add-icon {
        font-size: 5rem;
      }
    }
  }

  .ant-drawer-close {
    width: 30px;
    height: 30px;
  }
}

.package_hover_img {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  background-color: #ffffff;
  border-radius: 3px;
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
  width: 500px;
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    max-width: 100%;
    max-height: 100%;
    min-width: 300px;
    min-height: 300px;
    object-fit: contain;
  }
}

</style>
