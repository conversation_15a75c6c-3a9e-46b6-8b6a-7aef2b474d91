<template>
  <FormDrawer :title="title" :visible="openDraw" @closeDrawer="handleDrawerClose">
    <a-form :form="form" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <a-row :gutter="16">
        <a-divider style="margin: 20px 0;" />
        <!-- 行业 -->
        <a-col :span="24">
          <a-form-item :label="$t('label.proName')" :label-col="{ span: 3 }">
            <a-select
              v-decorator="[
                'sku_project_id',
                {initialValue: skuProject ? skuProject.sku_project_id : ''
                },
              ]"
              disabled
            >
              <a-select-option :value="skuProject.sku_project_id">{{ skuProject.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item
            :label="$t('skuForm.packingPic')"
            required
            :label-col="{ span: 3 }"
            :wrapper-col="{ span: 21 }"
            :extra="$t('skuForm.updatePicMsg')"
            :help="'请依次上传改包装的，前后左右图，第一张将作为改包装主图'"
          >
            <div class="pics_container">
              <div class="control">
                <a-upload
                  :file-list="beforeFileList"
                  accept=".jpg, .jpeg, .png"
                  :multiple="false"
                  :show-upload-list="true"
                  list-type="picture-card"
                  :before-upload="(file, fileList)=>handleBeforeUpload(file, fileList, 'before')"
                  class="upload-list-inline"
                  :remove="(file)=>delPic(file,'before')"
                  @preview="handlePreview"
                >
                  <div v-if="beforeFileList.length<1">
                    <span class="extra">{{ $t('skuForm.updatePicMsg') }}</span>
                  </div>
                </a-upload>
                <a-upload
                  :file-list="afterFileList"
                  accept=".jpg, .jpeg, .png"
                  :multiple="false"
                  :show-upload-list="true"
                  list-type="picture-card"
                  :before-upload="(file, fileList)=>handleBeforeUpload(file, fileList, 'after')"
                  class="upload-list-inline"

                  :remove="(file)=>delPic(file,'after')"
                  @preview="handlePreview"
                >
                  <div v-if="afterFileList.length<1">
                    <span class="extra">{{ $t('skuForm.updatePicMsg') }}</span>
                  </div>
                </a-upload>
                <a-upload
                  :file-list="leftFileList"
                  accept=".jpg, .jpeg, .png"
                  :multiple="false"
                  :show-upload-list="true"
                  list-type="picture-card"
                  :before-upload="(file, fileList)=>handleBeforeUpload(file, fileList, 'left')"
                  class="upload-list-inline"

                  :remove="(file)=>delPic(file,'left')"
                  @preview="handlePreview"
                >
                  <div v-if="leftFileList.length<1">
                    <span class="extra">{{ $t('skuForm.updatePicMsg') }}</span>
                  </div>
                </a-upload>
                <a-upload
                  :file-list="rightFileList"
                  accept=".jpg, .jpeg, .png"
                  :multiple="false"
                  :show-upload-list="true"
                  list-type="picture-card"
                  :before-upload="(file, fileList)=>handleBeforeUpload(file, fileList, 'right')"
                  class="upload-list-inline"

                  :remove="(file)=>delPic(file,'right')"
                  :preview="handlePreview"
                >
                  <div v-if="rightFileList.length<1">
                    <span class="extra">{{ $t('skuForm.updatePicMsg') }}</span>
                  </div>
                </a-upload>
                <a-modal :visible="previewVisible" :title="previewTitle" :footer="null" @cancel="handleCancel">
                  <img alt="example" style="max-width: 300px;max-height: 300px" :src="previewImage">
                </a-modal>
              </div>
            </div>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item
            :label="$t('skuForm.masterPackage')"
            required
            :label-col="{ span: 3 }"
            :wrapper-col="{ span: 21 }"
            :help="form.getFieldValue('is_master')?'该包装将作为sku的主包装':'是否将该包装作为sku的主包装'"
          >
            <a-switch
              v-decorator="[
                'is_master',
                {
                  valuePropName: 'checked',
                  initialValue: formData ? formData.is_master===1 : false
                }
              ]"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1,
      }"
    >
      <a-button :style="{ marginRight: '8px' }" @click="handleDrawerClose">
        {{ $t('buttons.cancel') }}
      </a-button>
      <a-button type="primary" :disabled="!working" :loading="btnLoad" @click="onSubmit">
        {{ $t('buttons.define') }}
      </a-button>
    </div>
  </FormDrawer>
</template>

<script>
import FormDrawer from '@/components/formDrawer'
import { checkTmpSku, addSkuPackage, changeSkuPackage, uploadPackageLogo } from '@/services/work'
import { mapGetters } from 'vuex'

function getBase64(img, callback) {
  const reader = new FileReader()
  reader.addEventListener('load', () => callback(reader.result))
  reader.readAsDataURL(img)
}

export default {
  name: 'AddSkuPackageForm',
  components: { FormDrawer },
  props: {
    skuId: { // 任务文件夹id
      type: Number,
      default: null
    },

    working: {
      type: Boolean,
      default: () => {
        return true
      }
    }
  },
  data() {
    return {
      title: this.$t('drawer.addPackage'),
      form: this.$form.createForm(this),
      openDraw: false,
      selectSearchVal: '',
      taskFlowType: null, // 流程类型
      skuTypeId: null, // sku行业ID
      skuProjectId: null, // sku项目ID
      formData: {},
      is_master: false,
      lang: 0,
      fileList: new Map(),
      beforeFileList: [],
      afterFileList: [],
      leftFileList: [],
      rightFileList: [],
      btnLoad: false, // 提交按钮提交中状态
      previewVisible: false, // 是否展示预览大图
      previewImage: '',
      previewTitle: ''
    }
  },
  computed: {
    ...mapGetters('taskHandle', ['sku', 'skuProject'])
  },
  mounted() {
    const query = this.$route.query
    this.taskFlowType = query.task_flow_type
    this.skuTypeId = 2// parseFloat(query.sku_type_id)
    this.skuProjectId = parseFloat(query.sku_project_id)
  },
  methods: {
    openDrawer(param = {}, type = 'add') {
      this.formData = {
        is_master: 0,
        sku_package_id: 0
      }
      this.title = this.$t(type === 'add' ? 'drawer.add' : 'drawer.edit', { name: this.$t('contents.multiPackaging') })
      this.openDraw = true
    },
    onClose() {
      this.openDraw = false
    },
    afterVisibleChange(val) {
      console.log('visible', val)
    },
    handleCancel() {
      this.previewVisible = false
      this.previewTitle = ''
    },

    handlePreview(file) {
      console.log(file)
      if (!file.url && !file.preview) {
        getBase64(file.originFileObj, imageUrl => {
          this.previewImage = imageUrl
        })
      }

      this.previewImage = file.url || file.preview
      this.previewVisible = true
      this.previewTitle = file.name || file.url.substring(file.url.lastIndexOf('/') + 1)
    },
    handleBeforeUpload(file, fileList, index) { // 附件上传
      const isLt2M = file.size / 1024 / 1024 < 3
      if (!isLt2M) {
        this.$message.error(this.$t('skuForm.updatePicMsg'))
        return false
      }
      getBase64(file, imageUrl => {
        file.preview = imageUrl
        file.url = imageUrl
        const field = index + 'FileList'
        this[field] = [...this[field], file]
        this.fileList.set(index, file)
      })

      // false 则 手动上传
      return false
    },
    delPic(file, index) { // 图片删除
      this.previewVisible = false
      const field = index + 'FileList'
      const fileIndex = this[field].indexOf(file)
      const newFileList = this[field].slice()
      newFileList.splice(fileIndex, 1)
      this[field] = newFileList
      this.fileList.delete(index)
    },
    handleDrawerClose() { // 关闭侧拉
      this.openDraw = false
      this.fileList.clear()
      this.beforeFileList = []
      this.afterFileList = []
      this.leftFileList = []
      this.rightFileList = []
      this.previewVisible = false
    },
    onSubmit() { // 提交
      this.form.validateFields((err, values) => {
        if (!err) {
          this.btnLoad = true
          const _values = Object.assign(this.formData, values)
          let upload = false
          const param = new FormData()
          this.fileList.forEach((file, index, map) => {
            if (file.status === undefined || file.status === 'removed') {
              param.append(index, file)
              upload = true
            }
          })
          if (upload) { // 有新图片上传
            if (this.formData.sku_package_id) {
              param.append('sku_package_id', this.formData.sku_package_id)
            }
            uploadPackageLogo(param).then(res => {
              if (res.errcode === 0) { // logo_key logo_url
                Object.keys(res.data).forEach((key) => {
                  _values[key + '_path'] = res.data[key].logo_key
                  _values[key + '_url'] = res.data[key].logo_url
                })
                const param = {
                  ..._values,
                  sku_id: this.skuId
                }
                if (this.formData.sku_package_id) {
                  param['sku_package_id'] = this.formData.sku_package_id
                  this.changeSkuPackageFun(param)
                } else {
                  this.addSkuPackageFun(param)
                }
              } else {
                this.btnLoad = false
                this.$message.error(res.errmsg)
              }
            }).catch(err => {
              console.log('错误', err)
              this.btnLoad = false
              this.$message.error('上传错误：' + err)
            })
          } else { // 没有新图片
            const param = { ..._values, dir_id: this.taskFolderId }
            if (this.formData.sku_package_id) {
              param['sku_package_id'] = this.formData.sku_package_id
              this.changeSkuPackageFun(param)
            } else {
              this.$message.error(this.$t('rules.skuPic'))
              this.btnLoad = false
            }
          }
        }
      })
    },
    // 行业和项目目录搜索
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    },
    addSkuPackageFun(param) { // 新增
      addSkuPackage(param).then(res => {
        this.btnLoad = false
        if (res.errcode === 0) {
          this.$message.success(res.errmsg)
          this.$emit('onSuccess')
          this.handleDrawerClose()
        } else {
          this.$message.error(res.errmsg)
        }
      }).catch(err => {
        console.log('错误', err)
        this.btnLoad = false
        this.$message.error('上传错误：' + err)
      })
    },
    changeSkuPackageFun(param) { // 修改
      changeSkuPackage(param).then(res => {
        this.btnLoad = false
        if (res.errcode === 0) {
          this.$message.success(res.errmsg)
          this.$emit('onSuccess')
          this.handleDrawerClose()
        } else {
          this.$message.error(res.errmsg)
        }
      }).catch(err => {
        console.log('错误', err)
        this.btnLoad = false
        this.$message.error('上传错误：' + err)
      })
    },
    onPass() { // 通过
      if (this.formData.sku_package_id) {
        checkTmpSku(this.formData.sku_package_id).then(res => {
          if (res.errcode === 0) {
            this.$message.success(res.errmsg)
            this.$emit('onSuccess')
            this.handleDrawerClose()
          } else {
            this.$message.error(res.errmsg)
          }
        })
      }
    }

  }
}
</script>
<style lang="less" scoped>
.pics_container {
  .control {
    .extra {
      font-size: 12px;
      color: #999;
      margin-left: 15px;
    }

    //margin-bottom: 15px;
  }

  .lists {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .item {
      padding: 8px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      position: relative;
      cursor: pointer;
      overflow: hidden;

      &:hover {
        .cover {
          display: block;
        }
      }

      .cover {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, .4);
        display: none;

        .contorl {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;

          .icon {
            color: hsla(0, 0%, 100%, .85);
            font-size: 18px;

            &:first-child {
              margin-right: 15px;
            }

            &:hover {
              color: #ffffff;
            }
          }
        }
      }
    }
  }
}

.img_box {
  width: 100%;
  height: 100%;
  text-align: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    user-select: none;
  }
}

/* tile uploaded pictures */
.upload-list-inline {
  float: left;
  width: auto;

  .ant-upload-list-item {
    float: left;
    width: 200px;
    margin-right: 8px;
  }
}

.upload-list-inline [class*='-upload-list-rtl'] {
  .ant-upload-list-item {
    float: right;;
  }
}
</style>
