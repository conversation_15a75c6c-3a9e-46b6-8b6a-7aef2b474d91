<template>
  <div class="left_canvas_cmp" @contextmenu.prevent @mousedown="mStart = true" @mousemove="hanldMouseMove" @mouseup="mStart = false">
    <div class="canvas_content" @mousewheel.prevent="handleMouseWheel">
      <!-- 放大缩小的容器 -->
      <div class="transfrom_dom" :style="canvasStyle">
        <canvas id="myCanvas" />
        <div class="rect_dom" :style="rectDomStyle" />
      </div>
      <!-- 撑滚动条的容器 -->
      <div class="scroll_dom" :style="scrollDomStyle" />
    </div>
    <div class="canvas_control">
      <a-button shape="circle" icon="scan" @click.stop="showViewerFun" />
      <a-button shape="circle" icon="zoom-out" @click.stop="handleScale('minus')" />
      <a-button shape="circle" icon="zoom-in" @click.stop="handleScale('plus')" />
    </div>
    <image-viewer v-if="showViewer" :on-close="closeViewer" :url-list="srcList" />
  </div>
</template>

<script>
import ImageViewer from '@/components/imgPreview'
export default {
  name: 'LeftCnavasCmp',
  components: { ImageViewer },
  data() {
    return {
      srcList: [],
      showViewer: false,
      scale: 1,
      stageX: 0,
      stageY: 0,
      mStart: false,
      coordinate: null, // 原生坐标
      canvasWidth: 0,
      canvasHeight: 0,
      rectDomStyleData: [0, 0, 0, 0]
    }
  },
  computed: {
    canvasStyle() {
      const _val = 'matrix(' + this.scale + ', 0, 0, ' + this.scale + ', 0, 0)'
      const _x = this.stageX
      const _y = this.stageY
      return {
        transform: _val,
        left: _x + 'px',
        top: _y + 'px'
      }
    },
    scrollDomStyle() {
      return {
        width: this.canvasWidth * this.scale,
        height: this.canvasHeight * this.scale
      }
    },
    rectDomStyle() {
      const _x = this.rectDomStyleData[0]
      const _y = this.rectDomStyleData[1]
      const _width = this.rectDomStyleData[2]
      const _height = this.rectDomStyleData[3]
      return {
        left: _x + 'px',
        top: _y + 'px',
        width: _width + 'px',
        height: _height + 'px'
      }
    }
  },
  methods: {
    // 关闭查看大图
    closeViewer() {
      this.showViewer = false
    },
    showViewerFun() {
      if (this.srcList.length > 0) {
        this.showViewer = true
      }
    },
    // 点击放大缩小
    handleScale(type) {
      if (!this.coordinate) return
      if (type === 'minus') {
        if (this.scale <= 0.1) return
        this.scale = parseFloat(this.scale * 1 - 0.1).toFixed(1)
        this.overViewFun()
      } else {
        if (this.scale >= 2) return
        this.scale = parseFloat(this.scale * 1 + 0.1).toFixed(1)
        this.overViewFun()
      }
    },
    // 鼠标拖动
    hanldMouseMove(e) {
      if (!this.coordinate) return
      if (this.mStart) {
        this.stageX += e.movementX
        this.stageY += e.movementY
      }
    },
    // 鼠标滚轮
    handleMouseWheel(e) {
      if (!this.coordinate) return
      if (e.deltaY > 0) {
        if (this.scale <= 0.1) return
        this.scale = parseFloat(this.scale * 1 - 0.1).toFixed(1)
        this.overViewFun()
      } else {
        if (this.scale >= 2) {
          return
        }
        this.scale = parseFloat(this.scale * 1 + 0.1).toFixed(1)
        this.overViewFun()
      }
    },
    // 计算SKU框偏移量
    overViewFun() {
      const delta = this.scale
      const dom = document.getElementsByClassName('left_canvas_cmp')[0]
      const outerWidth = dom.offsetWidth
      const outerHeight = dom.offsetHeight
      const canvesSku = this.coordinate.map(v => {
        return v * delta
      })
      const x = canvesSku[0]
      const y = canvesSku[1]
      const width = canvesSku[2]
      const height = canvesSku[3]
      const ruleWidth = (outerWidth - width) / 2
      const ruleHeight = (outerHeight - height) / 2
      this.stageX = ruleWidth - x
      this.stageY = ruleHeight - y
    },
    // 绘制标注图
    doDraw(imageUrl, coordinate) {
      const canvas = document.getElementById('myCanvas')
      const context = canvas.getContext('2d')
      const img = new Image()
      img.src = imageUrl
      img.onload = () => {
        if (img.complete) {
          this.canvasWidth = img.width
          this.canvasHeight = img.height
          canvas.setAttribute('width', this.canvasWidth)
          canvas.setAttribute('height', this.canvasHeight)
          context.drawImage(img, 0, 0, this.canvasWidth, this.canvasHeight)
          context.strokeStyle = '#f00'
          context.lineWidth = 4
          const x = coordinate[2]
          const y = coordinate[3]
          const width = coordinate[0]
          const height = coordinate[1]
          this.coordinate = [x, y, width, height]
          this.rectDomStyleData = [x, y, width, height]
          this.srcList = [imageUrl]
          this.scale = 0.5
          this.overViewFun()
        }
      }
    }
  }
}
</script>

<style scoped lang="less">
.left_canvas_cmp {
    width: 100%;
    height: 100%;
    position: relative;
    .canvas_control {
        position: absolute;
        right: 15px;
        bottom: 20px;
        .ant-btn {
            margin-right: 5px;
            line-height: 1.5;
        }
    }
    .canvas_content {
        width: 100%;
        height: 100%;
        overflow: scroll;
        cursor: move;
        position: relative;
    }
}
.transfrom_dom {
  transform-origin: 0 0;
  position: absolute;
  .rect_dom {
    position: absolute;
    border: 4px solid #f00;
  }
}
</style>
