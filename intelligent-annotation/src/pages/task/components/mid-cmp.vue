<template>
  <div id="mid_container" class="mid_container" @contextmenu.prevent="onContextmenu">
    <!-- <div v-if="showRightMouse && taskFlowType === 11" class="controls">
      <div class="btn confidence" @click="changeOrder('confidence')">
        <span>{{ $t('dic.confidence') }}</span>
        <a-icon class="up" :class="{'current': order === 0}" type="swap-right" />
        <a-icon class="down" :class="{'current': order === 1}" type="swap-right" />
      </div>
      <div class="btn distance" @click="changeOrder('distance')">
        <span>{{ $t('dic.distance') }}</span>
        <a-icon class="up" :class="{'current': order === 2}" type="swap-right" />
        <a-icon class="down" :class="{'current': order === 3}" type="swap-right" />
      </div>
      <a-select :value="err_type_value" class="my_err_type" @change="changeErrType">
        <a-select-option :key="-1" :value="-1">{{ lang === 'CN' ? '待分类' : 'To be classified' }}</a-select-option>
        <a-select-option v-for="item in errTypeList" :key="item.dictionary_id" :value="item.dictionary_id">{{ lang === 'CN' ? item.name : item.name_en }}</a-select-option>
      </a-select>
    </div> -->
    <div
      class="mid_content"
    >
      <a-list :data-source="data">
        <a-list-item slot="renderItem" slot-scope="item" :style="{width: `${itemNum}px`, height: `${itemNum}px`, margin: `${itemMargin}px`}" @click="itemClick(item)">
          <div
            :id="`item_${item.pic_id}`"
            class="my_item"
            @mouseover="handleEnter($event, item)"
            @mousemove="handleMove($event, item)"
            @mouseleave="handleLeave"
            @dblclick="copyToSortContainer(item)"
          >
            <div
              class="img_border"
              :class="[currentIds.indexOf(item.pic_id) !== -1 ? 'bordered' : item.error_type_id ? 'redBordered' : '']"
              :style="{width: `${itemNum - 30}px`, height: `${itemNum - 30}px`}"
            >
              <img v-lazy="(lang === 'CN' ? item.box_url : item.box_url_en) + '/format/webp>'" class="prew_img" alt="">
              <div :class="{'drag_mask': true, 'move': cleanState}" />
            </div>
            <h5>{{ item.name.split('_')[0]+'-'+parseFloat(item.confidence) }}</h5>
            <h5 v-if="showRightMouse && taskFlowType === 11 && item.error_type_id" style="margin: 0;color:#f56c6c;">{{ lang === 'CN' ? item.error_type : item.error_type_en }}</h5>
          </div>
        </a-list-item>
      </a-list>
    </div>
    <transition name="loading-fade">
      <div v-show="showImgUrl && showHoverImg" class="show_hover_img" :style="{left: img_left + 'px', top: img_top + 'px'}">
        <div class="img">
          <img :src="showImgUrl" alt="">
        </div>
        <div v-if="hoverImgDec" class="dec"> {{ hoverImgDec }} </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { getAllPicList, tagErrorType, getLeftDirSkuList } from '@/services/work'
import { getDicts } from '@/services/sys'
import { mapState } from 'vuex'
import AES from '@/utils/crypto-tool.js'
import { getLeftDirSkuCutImages } from '../../../services/work'
export default {
  name: 'MidCmp',
  props: {
    toolTaskId: {
      type: [String, Number],
      default: () => {
        return ''
      }
    },
    midDataDirName: {
      type: String,
      default: ''
    },
    itemNum: {
      type: [String, Number],
      default: () => {
        return 1
      }
    },
    itemMargin: {
      type: [String, Number],
      default: () => {
        return 10
      }
    },
    cleanState: { // 清洗状态
      type: Boolean,
      default: () => {
        return false
      }
    },
    showHoverImg: { // 是否展示hover大图
      type: Boolean,
      default: () => {
        return true
      }
    },
    currentIds: { // 选中的数据Id
      type: Array,
      default: () => {
        return []
      }
    },
    dirId: { // 文件夹id
      type: [String, Number],
      default: ''
    },
    showRightMouse: { // 是否展示鼠标右键还原
      type: Boolean,
      default: () => {
        return false
      }
    },
    inputing: { // 是否正在输入
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  data() {
    return {
      selectStyle: {
        width: '100%'
      },
      filterValue: '',
      filterSkuList: [],
      data: [],
      showImgUrl: null, // 展示大图的图片Url
      hoverImgDec: '', // 展示大图的图片描述
      img_left: 0, // 展示大图的x坐标
      img_top: 0, // 展示大图的Y坐标
      drawId: null, // 画布所画的数据id
      shiftCodeDown: false, // shift 是否被按下
      ctrlCodeDown: false, // ctrl 是否被按下
      draggedData: null,
      numberKeyCodeList: [49, 50, 51, 52, 53, 54, 55, 56, 57],
      upMoveLastId: null, // 根据上次拖拽计算出最后点击图片的下一张id
      order: 0, // 排序 0置信度倒叙 1置信度正序 2距离倒叙 3距离正序
      errTypeList: [],
      taskFlowType: null,
      mouseMoveTimeOut: null,
      totalData: []
    }
  },
  computed: {
    ...mapState('setting', ['lang']),
    ...mapState('account', ['user']),
    hasPrev() {
      const index = this.filterSkuList.findIndex(item => item.value === this.filterValue)
      if (index < 1) {
        return false
      } else {
        return true
      }
    },
    hasNext() {
      const index = this.filterSkuList.findIndex(item => item.value === this.filterValue)
      if (index >= this.filterSkuList.length - 1) {
        return false
      } else {
        return true
      }
    },
    canSearch() {
      // 判断是否为 Cluster + 数字的格式
      return /^cluster\d+$/.test(this.midDataDirName)
    }
  },
  watch: {
    dirId(val) {
      if (val) {
        this.getData(true)
      } else {
        this.showImgUrl = null
        this.data = []
      }
    },
    midDataDirName(val) {
      if (val) {
        this.filterValue = ''
        if (this.canSearch) {
          this.updateSelectWidth()
          this.getFilterSkuList()
        }
      }
    }
  },
  mounted() {
    window.addEventListener('resize', this.updateSelectWidth)
    this.taskFlowType = parseFloat(this.$route.query.task_flow_type)
    this.watchKeyEvent()
    if (this.taskFlowType === 11) {
      this.getDictsData()
    }
  },
  destroyed() {
    // 清除键盘监听事件
    document.onkeydown = null
    document.onkeyup = null
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateSelectWidth)
  },
  methods: {
    onDropdownVisibleChange(visible) {
      if (visible) {
        // 清除键盘监听事件
        document.onkeydown = null
        document.onkeyup = null
      } else {
        this.watchKeyEvent()
      }
    },
    updateSelectWidth() {
      this.$nextTick(() => {
        const filterContainer = document.querySelector('.filter-container')
        if (filterContainer) {
          // 获取容器宽度的60%作为select的宽度
          const containerWidth = filterContainer.offsetWidth
          if (containerWidth - 320 > 50) {
            this.selectStyle.width = `${containerWidth - (this.lang === 'CN' ? 310 : 324)}px`
          } else {
            this.selectStyle.width = '50px'
          }
        }
      })
    },
    getFilterSkuList() {
      const formData = {
        task_id: this.toolTaskId,
        cluster_name: this.midDataDirName
      }
      getLeftDirSkuList(formData).then((res) => {
        if (res.code === 200) {
          if (Array.isArray(res.data) && res.data.length) {
            this.filterSkuList = res.data.map((item) => {
              return {
                value: item.sku_id,
                label: item.sku_name,
                box_ids: [],
                loaded: false
              }
            })
          }
        } else {
          this.$message.error(res.message)
        }
      }).catch(() => {
        this.filterSkuList = []
      })
    },
    filterOption(input, option) {
      if (!input) return true
      const optionText = option.componentOptions.children[0].text.toLowerCase()
      const searchTerms = input.toLowerCase().split(/\s+/).filter(term => term)
      return searchTerms.every(term => optionText.includes(term))
    },
    // ... existing code ...
    handleFilterChange() {
      if (this.$refs.selectRef && this.$refs.selectRef.blur) {
        this.$refs.selectRef.blur()
      }
      // 处理筛选变化 todo
      if (!this.filterValue) {
        this.data = JSON.parse(JSON.stringify(this.totalData))
        if (this.data.length > 0) {
          this.updateScrollTop()
        }
      } else {
        const item = this.filterSkuList.find(v => v.value === this.filterValue)
        if (item.loaded) {
          this.data = this.totalData.filter(v => item.box_ids.includes(Number(v.box_id)))
          if (this.data.length > 0) {
            this.updateScrollTop()
          }
        } else {
          this.$emit('pageLoading', true)
          getLeftDirSkuCutImages({
            task_id: this.toolTaskId,
            sku_id: this.filterValue
          }).then((res) => {
            if (res.code === 200) {
              item.loaded = true
              if (Array.isArray(res.data) && res.data.length) {
                this.data = this.totalData.filter(v => res.data.includes(Number(v.box_id)))
                item.box_ids = res.data
                if (this.data.length > 0) {
                  this.updateScrollTop()
                }
              } else {
                this.data = []
              }
            } else {
              this.data = []
              this.$message.error(res.message)
            }
          }).catch(() => {
            this.data = []
          }).finally(() => {
            this.$emit('pageLoading', false)
          })
        }
      }
    },
    handlePrev() {
      if (this.hasPrev) {
        const index = this.filterSkuList.findIndex(item => item.value === this.filterValue)
        this.filterValue = this.filterSkuList[index - 1].value
        this.handleFilterChange()
      }
    },
    handleNext() {
      if (this.hasNext) {
        const index = this.filterSkuList.findIndex(item => item.value === this.filterValue)
        this.filterValue = this.filterSkuList[index + 1].value
        this.handleFilterChange()
      }
    },
    copyToSortContainer(item) {
      if (this.showRightMouse) return
      this.$emit('copyToSortContainer', item)
    },
    // 获取错误类型下拉值
    getDictsData() {
      getDicts({ dictionary_id: 1 }).then(res => {
        if (res.errcode === 0) {
          this.errTypeList = res.data.data_list
        }
      })
    },
    watchKeyEvent() {
      document.onkeydown = (e) => {
        if (e && e.keyCode === 38 || e.keyCode === 40) {
          e.preventDefault()
        }
        if (!this.inputing) { // 搜索框是否在触发状态
          this.setKeyStatusDown(e.keyCode)
        }
      }
      document.onkeyup = (e) => {
        if (e && e.keyCode === 38 || e.keyCode === 40) {
          e.preventDefault()
        }
        if (!this.inputing) { // 搜索框是否在触发状态
          this.setKeyStatusUp(e.keyCode)
        }
      }
    },
    // 鼠标事件监听
    setKeyStatusDown(keyCode) {
      switch (keyCode) {
        case 16: // shift 键值
          this.shiftCodeDown = true
          break
        case 17: // ctrl 键值
          this.ctrlCodeDown = true
          break
        case 65: // A
          if (this.shiftCodeDown && this.data.length > 0) {
            // 全选当前页数据
            const newArr = this.data.map(it => it.pic_id)
            this.$emit('setCurrentPidIds', newArr)
          }
          break
        default:
          break
      }
    },
    setKeyStatusUp(keyCode) {
      switch (keyCode) {
        case 16: // shift 键值
          this.shiftCodeDown = false
          break
        case 17: // ctrl 键值
          this.ctrlCodeDown = false
          break
        case 37: // 方向左键值
          this.arrowKeyFun('left')
          break
        case 38: // 方向上键值
          this.arrowKeyFun('up')
          break
        case 39: // 方向右键值
          this.arrowKeyFun('right')
          break
        case 40: // 方向下键值
          this.arrowKeyFun('down')
          break
        default:
          // eslint-disable-next-line no-case-declarations
          const idx = this.numberKeyCodeList.indexOf(keyCode)
          if (idx !== -1) {
            this.$emit('numberKeyCodeEnd', idx + 1)
          }
          break
      }
    },
    // 获取图片数据
    getData(moveObj) {
      this.showImgUrl = null
      this.$emit('pageLoading', true)
      const param = {
        dir_id: this.dirId,
        order_method: this.order
      }
      getAllPicList(param).then(res => {
        this.$emit('pageLoading', false)
        if (res.errcode === 0) {
          const result = JSON.parse(AES.decrypt(res.data))
          this.totalData = JSON.parse(JSON.stringify(result.data))
          this.data = JSON.parse(JSON.stringify(result.data))
          this.$emit('midPicTotal', result.total)
          if (this.data.length > 0) {
            if (moveObj && moveObj.type === 'errType') {
              document.getElementsByClassName('mid_content')[0].scrollTop = moveObj.moveTop
              this.$emit('setCurrentPidIds', [])
              return
            }
            this.updateScrollTop()
          } else {
            this.$emit('setCurrentPidIds', [])
          }
        } else {
          this.totalData = []
          this.data = []
          this.$emit('midPicTotal', 0)
          this.$message.error(res.errmsg)
        }
      }).catch(() => {
        this.totalData = []
        this.data = []
        this.$emit('midPicTotal', 0)
      }).finally(() => {
        this.$emit('pageLoading', false)
      })
    },
    updateScrollTop() {
      const it = this.data.find(item => item.pic_id === this.upMoveLastId)
      if (this.upMoveLastId && it) {
        this.$emit('onItemClick', this.lang === 'CN' ? it.box_url : it.box_url_en)
        this.$emit('setCurrentPidIds', [this.upMoveLastId])
        this.$nextTick(() => {
          const domId0 = `item_${this.upMoveLastId}`
          const dom0 = document.getElementById(domId0)
          if (dom0 && dom0.offsetTop) {
            const top = dom0.offsetTop - 20 < 0 ? 0 : dom0.offsetTop - 20
            document.getElementsByClassName('mid_content')[0].scrollTop = top
          } else {
            document.getElementsByClassName('mid_content')[0].scrollTop = 0
          }
          // 定位成功后重置定位数据id
          this.upMoveLastId = null
        })
      } else {
        this.$emit('onItemClick', this.lang === 'CN' ? this.data[0].box_url : this.data[0].box_url_en)
        this.$emit('setCurrentPidIds', [this.data[0].pic_id])
        this.$nextTick(() => {
          document.getElementsByClassName('mid_content')[0].scrollTop = 0
        })
      }
    },
    // 拖动图片成功后的回调
    // 拖动图片成功后，默认选中拖动图片最后点击的下一张，如果没有下一张就默认选中上一张
    moveSuccess() {
      // 将totalData数组中存在于了currentIds里面的图片移除掉
      this.totalData = this.totalData.filter(item => !this.currentIds.includes(item.pic_id))
      this.showImgUrl = null
      const _curId = this.currentIds[this.currentIds.length - 1]
      const _curIndex = this.data.findIndex(it => it.pic_id === _curId)
      const after_item = this.createIndexFinder(1, _curIndex)(this.data, item => this.currentIds.indexOf(item.pic_id) === -1)
      if (after_item) {
        this.upMoveLastId = after_item.pic_id
      } else {
        const before_item = this.createIndexFinder(-1, _curIndex)(this.data, item => this.currentIds.indexOf(item.pic_id) === -1)
        if (before_item) {
          this.upMoveLastId = before_item.pic_id
        }
      }
      const filters = this.data.filter(item => this.currentIds.indexOf(item.pic_id) === -1)
      this.data = filters
      this.$emit('midPicTotal', this.totalData.length)
      if (this.data.length > 0) {
        this.updateScrollTop()
      } else {
        this.$emit('setCurrentPidIds', [])
        this.upMoveLastId = null
      }
    },
    // 从某个索引位置向前或向后查找符合条件的项
    createIndexFinder(dir, idx) {
      return function(array, cb, context) {
        // 条件： 在数组范围内；
        // 递增或递减：递加 1 或者 -1； 妙啊~
        for (let index = idx; index >= 0 && index < array.length; index += dir) {
          if (cb.call(context, array[index], index)) return array[index]
        }
        return null
      }
    },
    // 鼠标右键
    onContextmenu(event) {
      if (!this.showRightMouse || this.currentIds.length === 0) {
        return false
      }
      const items = [{
        label: this.$t('buttons.reduction'),
        divided: this.errTypeList.length > 0,
        onClick: () => {
          this.$emit('reductionData')
        }
      }]
      this.errTypeList.forEach((item, index) => {
        items.push({
          label: this.lang === 'CN' ? item.name : item.name_en,
          divided: index + 1 < this.errTypeList.length,
          onClick: () => { this.onMenuChildrenClick(item.dictionary_id) }
        })
      })
      this.$contextmenu({
        items,
        event,
        zIndex: 1990,
        minWidth: 80
      })
      return false
    },
    // 图片标注错误类型
    onMenuChildrenClick(id) {
      tagErrorType({ error_type_id: id, pic_id_list: this.currentIds }).then(res => {
        if (res.errcode === 0) {
          this.$message.success(res.errmsg)
          const dom = document.getElementsByClassName('mid_content')[0]
          const obj = {
            type: 'errType',
            moveTop: dom.scrollTop
          }
          this.getData(obj)
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },

    // 鼠标进入item,展示大图
    handleEnter(e, item) {
      clearTimeout(this.mouseMoveTimeOut)
      this.mouseMoveTimeOut = setTimeout(() => {
        // 执行鼠标停留后的操作
        this.showImgUrl = (this.lang === 'CN' ? item.box_url : item.box_url_en) + '/format/webp'
        // 印度检查流程 仅对标注员1，隐藏清洗人姓名 2022-11-03 23:09:31
        if (this.user.team_id === 3 && this.user.role_id === 1 && this.taskFlowType === 11) {
          this.hoverImgDec = item.record.replace(/ -- .* --> /, ' --> ')
        } else {
          this.hoverImgDec = item.record
        }
      }, 200)
    },
    // 鼠标进入item,展示大图
    handleMove(e, item) {
      if (!this.showHoverImg) { // 拖动过程中不触发
        return
      }
      const dom = document.body
      const _h = document.getElementsByClassName('show_hover_img')[0].offsetHeight
      if (_h + e.clientY > dom.offsetHeight) {
        this.img_top = dom.offsetHeight - _h
      } else {
        this.img_top = e.clientY
      }
      this.img_left = e.clientX - 350
    },
    handleLeave(e) {
      clearTimeout(this.mouseMoveTimeOut)
      this.showImgUrl = null
    },
    // 点击item
    itemClick(item) {
      if (this.drawId !== item.pic_id) {
        this.$emit('onItemClick', this.lang === 'CN' ? item.box_url : item.box_url_en) // 出发canvas画图
      }
      if (this.shiftCodeDown && this.currentIds.length > 0) { // 当shift按键被按下时
        const startIdx = this.getIdxById(this.currentIds[this.currentIds.length - 1])
        const endIdx = this.getIdxById(item.pic_id)
        if (startIdx > endIdx) {
          this.addCurrentItem(endIdx, startIdx)
        } else {
          this.addCurrentItem(startIdx, endIdx)
        }
        return
      }
      if (this.ctrlCodeDown) { // 当crtl被按下时
        const index = this.currentIds.indexOf(item.pic_id)
        if (index !== -1) {
          const _data = JSON.parse(JSON.stringify(this.currentIds))
          _data.splice(index, 1)
          this.$emit('setCurrentPidIds', _data)
        } else {
          const _data = JSON.parse(JSON.stringify(this.currentIds))
          _data.push(item.pic_id)
          this.$emit('setCurrentPidIds', _data)
        }
        return
      }
      this.$emit('setCurrentPidIds', [item.pic_id])
    },
    // 方向键事件
    arrowKeyFun(arrow) {
      if (this.currentIds.length !== 1) { // 当是多选时，禁用方向键
        return
      }
      const lens = this.data.length // 总条数
      const _idx = this.getIdxById(this.currentIds[0]) // 上次选中的数据下标
      let _current = 0
      if (arrow === 'right') {
        _current = (_idx + 1) >= lens ? _idx : _idx + 1
      }
      if (arrow === 'left') {
        _current = _idx === 0 ? 0 : _idx - 1
      }
      if (arrow === 'up') {
        const w = document.getElementById('mid_container').offsetWidth
        const n = parseInt(w / (this.itemNum + 20)) // 每行多少个
        if (_idx - n < 0) {
          return
        }
        _current = _idx - n
      }
      if (arrow === 'down') {
        const w = document.getElementById('mid_container').offsetWidth
        const n = parseInt(w / (this.itemNum + 20)) // 每行多少个
        if (_idx + n > lens - 1) {
          return
        }
        _current = _idx + n
      }
      const domId0 = `item_${this.data[_idx].pic_id}`
      const dom0 = document.getElementById(domId0)
      const domId = `item_${this.data[_current].pic_id}`
      const dom = document.getElementById(domId)
      const dom1 = document.getElementById('mid_container')
      const dom2 = document.getElementsByClassName('mid_content')[0]
      if (dom0.offsetTop !== dom.offsetTop) {
        if (dom.offsetTop + dom.offsetHeight > dom2.scrollTop + dom1.offsetHeight || dom.offsetTop < dom2.scrollTop) {
          this.$nextTick(() => {
            dom2.scrollTop = dom2.scrollTop + (dom.offsetTop - dom0.offsetTop)
          })
        }
      }
      if (this.data[_current]) {
        this.$emit('setCurrentPidIds', [this.data[_current].pic_id])
        this.$emit('onItemClick', this.lang === 'CN' ? this.data[_current].box_url : this.data[_current].box_url_en)
      }
    },
    // 添加选中数据的Id
    addCurrentItem(start, end) {
      const _newArr = [...this.currentIds]
      for (let index = start; index < end + 1; index++) {
        if (!_newArr.includes(this.data[index].pic_id)) {
          _newArr.unshift(this.data[index].pic_id)
        }
      }
      this.$emit('setCurrentPidIds', _newArr)
    },
    // 根据id获取下标
    getIdxById(id) {
      let idx = 0
      this.data.some((it, index) => {
        if (it.pic_id === id) {
          idx = index
          return true
        }
      })
      return idx
    }
  }
}
</script>
<style lang="less">
.mid_search_select{
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}
  .mid_container {
    min-width:360px;
    .ant-list-items {
      display: flex;
      flex-wrap: wrap;
      .ant-list-item {
        padding: 0;
        border: none;
      }
    }
    .my_err_type {
      width: 120px;
      color: #ffffff;
      .ant-select-arrow {
        color: #ffffff;
      }
      .ant-select-selection {
        background-color: transparent;
        border-color: transparent;
      }
      &.ant-select-focused .ant-select-selection,
      &.ant-select-open .ant-select-selection,
      .ant-select-selection:focus,
      .ant-select-selection:active {
        box-shadow: none;
      }
    }
  }
</style>
<style scoped lang="less">
.filter-container {
  padding: 16px;
  display: flex;
  gap: 16px;
  align-items: center;
  border-bottom:1px solid #d9d9d9;
}
.mid_container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  height: 100%;
  position: relative;
  user-select: none;
  .controls {
    position: absolute;
    right: 25px;
    top: 10px;
    z-index: 100;
    background-color: rgba(0,0,0,.5);
    color: #ffffff;
    border-radius: 20px;
    padding: 0 10px;
    display: flex;
    .btn {
      font-size: 12px;
      color: #f4f4f4;
      cursor: pointer;
      &:hover {
        color: #ffffff;
      }
      .current {
        color: #ED5C36;
      }
      .up {
        transform: rotate(-90deg);
        margin-right: -3px;
      }
      .down {
        margin-left: -3px;
        transform: rotate(90deg);
      }
    }
    .confidence {margin-right: 5px;}
  }
  .mid_content {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    width: 100%;
    overflow-y: scroll;
  }
  .my_item {
    width: 100%;
    text-align: center;
    position: relative;
    cursor: pointer;
    .img_border {
      position: relative;
      margin: 0 auto;
      &.bordered {
        border: 4px solid #4a90e2;
      }
      &.redBordered {
        border: 4px solid #f56c6c;
      }
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
      .drag_mask {
        position: absolute;
        left: 0;
        top: 0;
        z-index: 1;
        width: 100%;
        height: 100%;
        &.move {
          cursor: move;
        }
      }
    }
    h5{
      display: block;
      margin: 5px 0 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .show_hover_img {
    position: fixed;
    background-color: #ffffff;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    z-index: 2000;
    width: 250px;
    .img {
      width: 100%;
      height: 250px;
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
    .dec {
      font-size: 12px;
      padding: 5px;
      color: #096dd9;
    }
  }
}
.prew_img[lazy=loading]{
  background: url('../../../assets/img/loading.svg') center no-repeat;
}
.prew_img[lazy=error]{
  background: url('../../../assets/img/error.svg') center no-repeat;
}
</style>
