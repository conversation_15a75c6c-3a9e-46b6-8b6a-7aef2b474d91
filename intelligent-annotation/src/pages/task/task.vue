<template>
  <div class="new-page" :style="`min-height: ${pageMinHeight}px`">
    <a-card :bordered="false">
      <div style="margin-bottom: 20px;">
        <a-form-model layout="inline" :model="formInline" @submit="handleSubmit(true)" @submit.native.prevent>
          <a-form-model-item :label="$t('label.teamOrId')">
            <a-input v-model="formInline.name" placeholder="name" />
          </a-form-model-item>
          <a-form-model-item>
            <a-button
              type="primary"
              html-type="submit"
            >
              {{ $t('buttons.search') }}
            </a-button>
          </a-form-model-item>
        </a-form-model>
      </div>
      <div v-if="loading">
        <a-skeleton active />
      </div>
      <div v-else class="task_lists">
        <div v-for="it in taskData" :key="it.project_id" class="task_item">
          <div class="project_dec">
            <div class="left">
              <div v-if="it.is_queue && it.is_queue === 'T'" class="item"><a-icon type="bulb" theme="filled" style="color: #ff0000;" /></div>
              <div class="item">
                <span class="name">ID：</span>
                <span class="value" :class="{'red': it.is_queue && it.is_queue === 'T'}">{{ it.project_id }}</span>
              </div>
              <div class="item">
                <span class="name">{{ $t('table.project') }}：</span>
                <span class="value" :class="{'red': it.is_queue && it.is_queue === 'T'}">{{ it.name }}</span>
              </div>
              <!-- 对标注员角色隐藏项目总数和项目进度 -->
              <div v-if="user.role_id !== 1" class="item">
                <span class="name">{{ $t('table.proTotal') }}：</span>
                <span class="value">{{ it.file_total }}</span>
              </div>
            </div>
            <div class="right">
              <span>{{ $t('table.creater') }}：{{ it.builder }}</span>
              <span>{{ $t('table.reTime') }}：{{ it.release_time }}</span>
            </div>
          </div>
          <div class="task_flow_lists">
            <div v-for="it2 in it.task_flow_list" :key="it2.task_flow_id" class="task_flow_item" @click="hanldButton(it2, it)">
              <div class="title">
                <div class="lc">
                  <a-button
                    type="primary"
                    size="small"
                    shape="circle"
                    :icon="taskFlowColor(it2.task_flow_type, 'icon')"
                    :style="{
                      backgroundColor: taskFlowColor(it2.task_flow_type, 'color'),
                      borderColor: taskFlowColor(it2.task_flow_type, 'color')
                    }"
                  />
                  {{ transTaskTape(it2.name) }}
                </div>
                <div class="rc">
                  <a-button
                    v-if="it2.task_flow_type !== 11"
                    :title="$t('contents.exportFolder')"
                    type="primary"
                    size="small"
                    shape="circle"
                    icon="cloud-download"
                    @click.stop="exportFolder(it2)"
                  />
                </div>
              </div>
              <div class="note"> {{ lang === 'CN' ? it2.notes: it2.notes_en }}</div>
              <template v-if="user.role_id!==1">
                <!-- 对标注员角色隐藏项目总数和项目进度 -->

                <div class="bot" style="margin-bottom: 10px;">
                  <span>{{ $t('table.validData') }}：{{ it.file_total - it2.invalid_number }}</span>
                  <span>{{
                    $t('table.validPro')
                  }}：{{ parseFloat(((it.file_total - it2.invalid_number) * 100 / it.file_total).toFixed(1)) }}%</span>
                </div>
                <div class="bot">
                  <span>{{
                    it2.task_flow_type === 12 ? $t('table.expTotal') : $t('table.completed')
                  }}：{{ it2.finish_number }}</span>
                  <span>{{ it2.task_flow_type === 12 ? $t('table.agTotal') : $t('table.incomplete') }}：{{
                    it2.number
                  }}</span>
                </div>
                <div>
                  <a-progress
                    :percent="it.file_total === 0 || it2.finish_number === 0 ? 0 :
                      parseFloat((it2.finish_number*100/it.file_total).toFixed(1))"
                    :stroke-color="taskFlowColor(it2.task_flow_type, 'color')"
                    :show-info="true"
                  />
                </div>
              </template>
            </div>
          </div>
        </div>
        <a-empty v-if="!loading && taskData.length === 0" />
        <a-pagination
          v-if="taskData.length > 0"
          style="text-align: right;"
          show-size-changer
          :page-size="paginations.pageSize"
          :current="paginations.current"
          :show-total="paginations.showTotal"
          :total="paginations.total"
          :page-size-options="paginations.pageSizeOptions"
          @change="handleTablePageChange"
          @showSizeChange="handleTablePageChange"
        />
      </div>
      <div v-if="exportLoading" class="export_loading">
        <a-spin size="large" :tip="$t('confirm.bigData')" />
      </div>
    </a-card>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { getMyTaskList } from '@/services/work'
import { sendWebsocket, closeWebsocket } from '@/utils/websocket'
export default {
  name: 'Task',
  data() {
    return {
      formInline: {
        name: ''
      },
      loading: true,
      paginations: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30', '50'],
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => {
          return this.$t('table.total') + total + this.$t('table.strip')
        },
        total: 0
      },
      taskData: [],
      exportLoading: false
    }
  },
  computed: {
    ...mapGetters('account', ['user']),
    ...mapState('setting', ['pageMinHeight', 'lang']),
    desc() {
      return this.$t('description')
    }
  },
  created() {
    const taskPage = sessionStorage.getItem('task_page')
    if (taskPage) { // 保留上次分页和搜索信息
      const obj = JSON.parse(taskPage)
      this.formInline.name = obj.name || ''
      this.paginations.current = obj.current
      this.paginations.pageSize = obj.pageSize
    }
    this.handleSubmit()
  },
  methods: {
    taskFlowColor(type, key) { // 不同流程配色
      const flows = [
        { id: 8, color: '#eb2f96', icon: 'folder-add' },
        { id: 9, color: '#2db7f5', icon: 'file-protect' },
        { id: 10, color: '#f56c6c', icon: 'filter' },
        { id: 11, color: '#cf9236', icon: 'audit' },
        { id: 12, color: '#5daf34', icon: 'file-done' }
      ]
      const item = flows.find(it => it.id === type)
      if (item) {
        return item[key]
      } else {
        return flows[0][key]
      }
    },
    // 搜索
    handleSubmit(first) {
      if (first) this.paginations.current = 1
      this.loading = true
      const param = {
        page: this.paginations.current,
        number: this.paginations.pageSize,
        search: this.formInline.name
      }
      this.setTaskPage()
      getMyTaskList(param).then(res => {
        this.loading = false
        if (res.errcode === 0) {
          this.taskData = res.data.data_list
          this.paginations.total = res.data.total
        }
      })
    },
    // 分页改变
    handleTablePageChange(page, pageSize) {
      this.paginations.current = page
      this.paginations.pageSize = pageSize
      this.handleSubmit()
    },
    // 保存上次的分页和搜索信息
    setTaskPage() {
      const page = {
        name: this.formInline.name,
        current: this.paginations.current,
        pageSize: this.paginations.pageSize
      }
      sessionStorage.setItem('task_page', JSON.stringify(page))
    },
    // 跳转到任务详情
    hanldButton(item, pro) {
      // 12汇总和队列任务
      if (item.task_flow_type === 12 && pro.is_queue && pro.is_queue === 'T') { // 当是队列项目时，12汇总流程不可点击
        this.$message.error(this.$t('confirm.noSum'))
        return
      }
      /*      if (!item.number || item.number === 0) {
        this.$message.error(this.$t('confirm.noCheckData'))
        return
      }*/
      if (item.task_flow_type === 8 || item.task_flow_type === 9) { // 注册和审核任务
        const param = {
          name: item.project,
          task_flow_id: item.task_flow_id,
          task_flow_type: item.task_flow_type,
          sku_type_id: pro.sku_type_id,
          sku_project_id: pro.sku_project_id
        }
        this.$router.push({
          path: '/skuTaskHandle',
          query: param
        })
        return
      }
      if (item.task_flow_type === 11 && item.name === '复核') { // 复核任务
        const param = {
          name: item.project,
          project_id: pro.project_id,
          task_flow_id: item.task_flow_id,
          task_flow_type: item.task_flow_type,
          sku_type_id: pro.sku_type_id,
          sku_project_id: pro.sku_project_id
        }
        this.$router.push({
          path: '/taskAutoCheck',
          query: param
        })
        return
      }
      // 清洗任务
      const param = {
        task_flow_id: item.task_flow_id,
        name: item.project,
        id: pro.project_id,
        task_flow_type: item.task_flow_type,
        is_invisible: item.is_invisible ? 'T' : 'F',
        is_clean_empty: pro.is_clean_empty ? 'T' : 'F',
        role_id: this.user.role_id
      }
      this.$router.push({
        path: '/taskHandle',
        query: param
      })
    },
    // 导出流程下任务文件夹 task_flow_type
    exportFolder(it) {
      this.exportLoading = true
      const param = {
        project_id: it.project_id,
        task_flow_id: it.task_flow_id,
        url: 'download_project_logs'
      }
      sendWebsocket(param, this.wsMessage, this.wsError)
    },
    // 长连接接受后台信息
    wsMessage(res, t) {
      this.exportLoading = false
      if (res.errcode === 0) {
        const _url = `${process.env.VUE_APP_API_BASE_URL2}/project/download_file/?file_name=${res.data.file_name}&access_token=${t}`
        const link = document.createElement('a')
        link.setAttribute('download', res.data.file_name)
        link.setAttribute('href', _url)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } else {
        this.$message.error(res.errmsg)
      }
      closeWebsocket()
    },
    wsError(msg) {
      this.exportLoading = false
      this.$message.error(msg.errmsg)
    },
    transTaskTape(name) {
      const taskType = {
        '清洗': 'clean',
        '检查': 'check',
        '检查2': 'check2',
        '抽检': 'spot_check',
        '抽查': 'spot_check',
        '审核': 'audit',
        '汇总': 'summary',
        '注册': 'register'
      }
      if (taskType[name]) {
        return this.$t('contents.taskType.' + taskType[name])
      }
      return name
    }
  }
}
</script>

<style scoped lang="less">
@import "index";
.export_loading {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.4);
  z-index: 10;
}
</style>
