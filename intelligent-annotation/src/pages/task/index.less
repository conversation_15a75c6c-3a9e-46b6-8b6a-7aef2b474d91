.new-page {
  height: 100%;
  background-color: @base-bg-color;
  border-radius: 4px;
  position: relative;

  //margin-top: -24px;
  h1 {
    font-size: 48px;
  }
}

.task_lists {
  font-size: 12px;

  .project_dec {
    background-color: rgb(245, 245, 245);
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;

    .left {
      display: flex;
      align-items: center;
      .item {
        margin-right: 15px;
        &:last-child {
          margin-right: 0;
        }
        .red {
          color: #ff0000;
        }
      }
    }
    .right {
      span {
        margin-right: 15px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}

.task_flow_lists {
  padding-top: 15px;
  display: flex;
  flex-wrap: wrap;

  .task_flow_item {
    width: 300px;
    min-height: 135px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    margin: 0 10px 10px;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    flex-direction: column;

    &:hover {
      box-shadow: 0 0 16px rgba(0, 0, 0, .3);
      transition: all .3s ease;
    }

    .title {
      font-size: 1.2em;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .rc {
        &:hover {
          background-color: #f4f4f4;
        }
      }
    }

    .note {
      flex: 1;
      padding: 10px 0;
    }

    .bot {
      display: flex;
      justify-content: space-between;
    }
  }
}
