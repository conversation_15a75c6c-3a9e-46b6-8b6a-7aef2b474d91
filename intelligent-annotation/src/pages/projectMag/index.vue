<template>
  <div class="new-page">
    <a-card>
      <div style="margin-bottom: 20px">
        <a-form-model
          layout="inline"
          :model="formInline"
          @submit="handleSubmit(true)"
          @submit.native.prevent
        >
          <a-form-model-item :label="$t('label.sTime')">
            <a-range-picker
              format="YYYY-MM-DD HH:mm"
              :show-time="{ format: 'HH:mm', defaultValue:[$moment('00:00', 'HH:mm'), $moment('23:59', 'HH:mm')] }"
              @change="handleTimeChange"
              @ok="handleSubmit(true)"
            />
          </a-form-model-item>
          <a-form-model-item :label="$t('label.projectName')">
            <a-input v-model="formInline.name" />
          </a-form-model-item>
          <a-form-model-item>
            <a-button type="primary" html-type="submit"> {{ $t('buttons.search') }} </a-button>
          </a-form-model-item>
        </a-form-model>
        <div class="top_control">
          <div class="left">
            <a-radio-group :default-value="formInline.status" button-style="solid" @change="hanldRadioButton">
              <a-radio-button v-for="(item, i) in statusList" :key="i" :value="item.value"> {{ lang === 'CN' ? item.label : item.enLabel }} </a-radio-button>
            </a-radio-group>
          </div>
          <div class="right">
            <a-button type="primary" @click="addProject">{{ $t('buttons.creatP') }}</a-button>
          </div>
        </div>
      </div>
      <a-table
        class="my_scroll_table"
        :scroll="{ x: true }"
        :columns="columns"
        :data-source="data"
        :loading="loading"
        bordered
        row-key="project_id"
        :pagination="paginations"
        @change="handleTableChange"
      >
        <template slot="project_id" slot-scope="text, record">
          <span v-if="record.is_queue && record.is_queue === 'T'" style="color: #ff0000;">
            <a-icon type="bulb" theme="filled" style="vertical-align: baseline;" /> {{ record.project_id }}
          </span>
          <span v-else> {{ record.project_id }} </span>
        </template>
        <template slot="p_name" slot-scope="text, record">
          <span v-if="record.is_queue && record.is_queue === 'T'" style="color: #ff0000;"> {{ record.name }} </span>
          <span v-else> {{ record.name }} </span>
        </template>
        <span slot="status" slot-scope="text, record">
          <a-tag v-if="record.state === 4" color="#909399">{{ $t('dic.done') }}</a-tag>
          <a-tag v-if="record.state === 3" color="#67c23a">{{ $t('dic.rAccept') }}</a-tag>
          <a-tag v-if="record.state === 2" color="#f56c6c">{{ $t('dic.ongoing') }}</a-tag>
          <a-tag v-if="record.state === 1" color="#e6a23c">{{ $t('dic.rStart') }}</a-tag>
          <a-tag v-if="record.state === 0" color="#409eff">{{ $t('dic.unreleased') }}</a-tag>
        </span>
        <span slot="is_clean_empty" slot-scope="text, record">
          <template v-if="record.is_clean_empty">{{ $t('dic.yes') }}</template>
          <template v-else>{{ $t('dic.no') }}</template>
        </span>
        <span slot="state" slot-scope="text, record">
          <a-switch
            :checked-children="$t('dic.function')"
            :un-checked-children="$t('dic.suspend')"
            :checked="record.is_function"
            @click="changeProjectFun($event, record)"
          />
        </span>
        <span slot="action" slot-scope="text, record">
          <a @click.prevent="openConfigTask(record)">{{ $t('buttons.task') }}</a>
          <template v-if="record.state === 0">
            <a-divider type="vertical" />
            <a-popconfirm
              :title="$t('confirm.release')"
              @confirm="() => releaseFun(record)"
            >
              <a>{{ $t('buttons.release') }}</a>
            </a-popconfirm>
          </template>
          <template v-if="record.is_queue !== 'T' && record.external_task_id === 0">
            <a-divider type="vertical" />
            <a-dropdown>
              <a class="ant-dropdown-link" @click="(e) => e.preventDefault()">
                {{ $t('buttons.more') }} <a-icon type="down" />
              </a>
              <a-menu slot="overlay">
                <a-menu-item>
                  <a
                    href="javascript:;"
                    @click="hanldButton(record)"
                  >{{ $t('buttons.exportPro') }}</a>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </template>
          <template v-else-if="record.state !== 0 && record.is_queue !== 'T' && record.external_task_id > 0">
            <a-divider type="vertical" />
            <a-popconfirm
              :title="$t('confirm.endProject')"
              @confirm="() => sendBackFun(record)"
            >
              <a>{{ $t('buttons.endProject') }}</a>
            </a-popconfirm>
          </template>
        </span>
      </a-table>
    </a-card>
    <div v-if="exportLoading" class="export_loading">
      <a-spin size="large" />
    </div>
    <add-project ref="addProject" @submitSuccess="handleSubmit(true)" />
    <config-task ref="configTask" @submitSuccess="handleSubmit" />
  </div>
</template>

<script>
import AddProject from './components/add-project'
import ConfigTask from './components/config-task'
import { getProjectList, changeProject, releaseProject, sendBackProject } from '@/services/project'
import { sendWebsocket, closeWebsocket } from '@/utils/websocket'
import { mapState } from 'vuex'
export default {
  name: 'ProjectMag',
  components: { AddProject, ConfigTask },
  data() {
    return {
      statusList: [
        { label: '全部', enLabel: 'Whole', value: -1 },
        { label: '未发布', enLabel: 'Unpublished', value: 0 },
        { label: '待开始', enLabel: 'To begin', value: 1 },
        { label: '进行中', enLabel: 'Have in hand', value: 2 },
        { label: '待验收', enLabel: 'To be accepted', value: 3 },
        { label: '已完成', enLabel: 'Completed', value: 4 }
      ],
      formInline: {
        name: '',
        start_time: '',
        end_time: '',
        status: -1
      },
      loading: true,
      exportLoading: false,
      paginations: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30', '50'],
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => {
          return this.$t('table.total') + total + this.$t('table.strip')
        },
        total: 0
      },
      data: []
    }
  },
  computed: {
    ...mapState('setting', ['lang']),
    columns() {
      const data = [
        { title: this.$t('table.id'), key: 'project_id', width: 85, align: 'center', scopedSlots: { customRender: 'project_id' }},
        { title: this.$t('table.project'), key: 'name', scopedSlots: { customRender: 'p_name' }},
        { title: this.$t('table.proTotal'), dataIndex: 'file_total', align: 'right' },
        {
          title: this.$t('table.state'),
          key: 'status',
          align: 'center',
          scopedSlots: { customRender: 'status' }
        },
        {
          title: this.$t('table.isFun'),
          key: 'state',
          align: 'center',
          scopedSlots: { customRender: 'state' }
        },
        { title: this.$t('table.reTime'), dataIndex: 'release_time', align: 'center', width: 185 },
        { title: this.$t('table.endTime'), dataIndex: 'finish_time', align: 'center', width: 185 },
        { title: this.$t('table.creater'), dataIndex: 'builder' },
        {
          title: this.$t('table.action'),
          key: 'action',
          fixed: 'right',
          align: 'center',
          width: 225,
          scopedSlots: { customRender: 'action' }
        }
      ]
      return data
    }
  },
  created() {
    this.handleSubmit()
  },
  methods: {
    // 时间选择改变
    handleTimeChange(dates, dateStrings) {
      this.formInline.start_time = dateStrings[0]
      this.formInline.end_time = dateStrings[1]
    },
    // 单选按钮改变事件
    hanldRadioButton(e) {
      this.formInline.status = e.target.value === -1 ? '' : e.target.value
      this.handleSubmit(true)
    },
    // 搜索
    handleSubmit(first) {
      if (first) this.paginations.current = 1
      this.loading = true
      const param = {
        page: this.paginations.current,
        number: this.paginations.pageSize,
        start_time: this.formInline.start_time,
        end_time: this.formInline.end_time,
        search: this.formInline.name,
        state: this.formInline.status === -1 ? '' : this.formInline.status
      }
      getProjectList(param).then(res => {
        this.loading = false
        if (res.errcode === 0) {
          this.data = res.data.data_list
          this.paginations.total = res.data.total
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 分页改变
    handleTableChange(obj) {
      this.paginations.current = obj.current
      if (obj.showSizeChanger) {
        this.paginations.pageSize = obj.pageSize
      }
      this.handleSubmit()
    },
    // 修改项目运行状态
    changeProjectFun(flag, item) {
      const param = {
        project_id: item.project_id,
        is_function: flag ? 'T' : 'F'
      }
      changeProject(param).then(res => {
        if (res.errcode === 0) {
          this.handleSubmit()
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 添加项目
    addProject() {
      this.$refs.addProject.showModal = true
    },
    // 配置任务
    openConfigTask(item) {
      this.$refs.configTask.formData = item
      if (item.data_file) {
        const _arr = item.data_file.split('/')
        this.$refs.configTask.fileName = _arr[_arr.length - 1]
      }
      this.$refs.configTask.openDraw = true
    },
    // 发布项目
    releaseFun(item) {
      this.exportLoading = true
      releaseProject(item.project_id).then(res => {
        this.exportLoading = false
        if (res.errcode === 0) {
          this.$message.success(res.errmsg)
          this.handleSubmit()
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 回传清洗数据到第三方工具
    sendBackFun(item) {
      this.exportLoading = true
      sendBackProject(item.project_id).then(res => {
        this.exportLoading = false
        if (res.errcode === 0) {
          this.$message.success(res.errmsg)
          this.handleSubmit()
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 导出项目数据
    hanldButton(item) {
      this.exportLoading = true
      const param = {
        project_id: item.project_id,
        url: 'download_project_data'
      }
      sendWebsocket(param, this.wsMessage, this.wsError)
    },
    // 长连接接受后台信息
    wsMessage(res, t) {
      this.exportLoading = false
      if (res.errcode === 0) {
        const _url = `${process.env.VUE_APP_API_BASE_URL2}/project/download_file/?file_name=${res.data.file_name}&access_token=${t}`
        const link = document.createElement('a')
        link.setAttribute('download', res.data.file_name)
        link.setAttribute('href', _url)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } else {
        this.$message.error(res.errmsg)
      }
      this.handleSubmit()
      closeWebsocket()
    },
    wsError(msg) {
      this.exportLoading = false
      this.$message.error(msg)
    },
    getNowDate() {
      const date = new Date()
      const year = date.getFullYear()
      const mouth = date.getMonth() + 1
      const day = date.getDay()
      return year + '' + mouth + '' + day
    },
    downJson(json, name) {
      const bold = new Blob([JSON.stringify(json)], { type: 'application/json,charset=utf-8;' })
      const link = document.createElement('a')
      link.href = window.URL.createObjectURL(bold)
      link.target = '_blank'
      link.download = name
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }
}
</script>

<style scoped lang="less">
.top_control {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.export_loading {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.4);
  z-index: 99999;
}
</style>
