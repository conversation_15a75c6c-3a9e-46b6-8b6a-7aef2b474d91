<template>
  <div>
    <a-button id="filePicker" class="filePicker" type="primary" :disabled="btnDisabled">
      {{ $t('buttons.upJson') }}
    </a-button>
    <a v-if="fileName" style="margin-left: 15px;color: #999;font-size: 12px;">{{ fileName }}</a>
    <my-web-uploader
      ref="uploader"
      :url="url"
      upload-button="#filePicker"
      :form-data="myParam"
      accept="json"
      @fileChange="fileChange"
      @progress="onProgress"
      @success="onSuccess"
      @uploadError="onError"
    />
  </div>
</template>

<script>
import MyWebUploader from '@/components/myWebUploader'
export default {
  name: 'UplaodJson',
  components: {
    MyWebUploader
  },
  props: {
    projectId: {
      type: Number,
      default: () => {
        return null
      }
    },
    fileName: {
      type: String,
      default: ''
    },
    btnDisabled: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    }
  },
  data() {
    return {
      url: process.env.VUE_APP_API_BASE_URL2 + '/project/upload_project_file/'
    }
  },
  computed: {
    myParam() {
      const param = {
        access_token: sessionStorage.getItem('Authorization') || '',
        project_id: this.projectId
      }
      return param
    }
  },
  mounted() {
  },
  methods: {
    fileChange(e) {
      this.$emit('fileStart', e)
    //   console.log('fileChange')
    //   console.log(e)
    },
    // 进度条
    onProgress(e, percentage) {
      const num = Math.floor(percentage * 100)
      this.$emit('changeProgress', num)
    },
    onSuccess(e) {
      this.$emit('fileSuccess')
      // console.log('onSuccess')
      // console.log(e)
    },
    onError(e) {
      this.$emit('fileError')
      this.$message.error('附件上传失败')
      console.log(e)
    }
  }
}
</script>

<style>
    .filePicker input[type='file']{
        opacity: 0;
        cursor: pointer;
    }
</style>
