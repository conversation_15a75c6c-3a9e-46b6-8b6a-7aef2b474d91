<template>
  <form-modal
    :title="modalTitle"
    :width="980"
    :visible="showModal"
    :custom-footer="true"
    @onlyClose="handleOnlyClose"
  >
    <a-table
      :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      :columns="columns"
      :data-source="data"
      bordered
      row-key="user_id"
      :pagination="false"
      :scroll="{ y: 350 }"
    >
      <span slot="num" slot-scope="text, record, index">
        {{ index + 1 }}
      </span>
    </a-table>
    <template slot="footer">
      <a-popconfirm
        :title="$t('confirm.unOffline')"
        @confirm="() => unOfflineFun()"
      >
        <a-button type="primary">{{ $t('buttons.unOffline') }}</a-button>
      </a-popconfirm>
    </template>
  </form-modal>
</template>

<script>
import FormModal from '@/components/formModal'
import { cleanTaskFlowUser } from '@/services/project'
export default {
  name: 'TaskPeople',
  components: { FormModal },
  props: {
    modalTitle: {
      type: String,
      default: ''
    },
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    flowId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      showModal: false,
      selectedRowKeys: []
    }
  },
  computed: {
    columns() {
      const data = [
        { title: this.$t('table.id'), key: 'user_id', scopedSlots: { customRender: 'num' }},
        { title: this.$t('table.name'), dataIndex: 'full_name' },
        { title: this.$t('table.mobile'), dataIndex: 'mobile' }
      ]
      return data
    }
  },
  methods: {
    handleOnlyClose() {
      this.showModal = false
    },
    // 强制下线
    unOfflineFun() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.error(this.$t('confirm.noPeople'))
        return
      }
      cleanTaskFlowUser({ task_flow_id: this.flowId, user_id_list: this.selectedRowKeys }).then(res => {
        if (res.errcode === 0) {
          this.$message.success(res.errmsg)
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    }
  }
}
</script>

<style>

</style>
