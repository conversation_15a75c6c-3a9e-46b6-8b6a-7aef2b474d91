<template>
  <form-modal
    :title="modalTitle"
    :width="980"
    :visible="showModal"
    @submitOk="handleSubmitOk"
    @onlyClose="handleOnlyClose"
  >
    <a-form-model
      layout="inline"
    >
      <a-form-model-item :label="$t('label.group')">
        <a-select
          style="width: 200px"
          allow-clear
          @change="groupChange"
        >
          <a-select-option
            v-for="item in groupList"
            :key="item.group_id"
            :value="item.group_id"
          >{{ item.name }}</a-select-option>
        </a-select>
      </a-form-model-item>
    </a-form-model>
    <div style="margin-top: 20px;">
      <a-transfer
        ref="myTransfer"
        :list-style="{ width: '445px', height: '350px' }"
        show-search
        :filter-option="filterOption"
        :data-source="mockData"
        :titles="[$t('label.main'), $t('label.ass')]"
        :render="item => item.title"
        :target-keys="targetKeys"
        :selected-keys="selectedKeys"
        @change="handleChange"
        @selectChange="handleSelectChange"
      />
    </div>
  </form-modal>
</template>

<script>
import FormModal from '@/components/formModal'
import { apiCommonServer } from '@/services'
export default {
  name: 'SelectPeople',
  components: { FormModal },
  props: {
    taskId: {
      type: Number,
      default: null
    },
    modalTitle: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showModal: false,
      cacheData: [],
      mockData: [],
      targetKeys: [],
      selectedKeys: [],
      groupList: [],
      groupId: null
    }
  },
  methods: {
    // 获取组
    getGroupListFun(teamId) {
      apiCommonServer.getGroupList(teamId).then(res => {
        if (res.errcode === 0) {
          this.groupList = res.data.group_list
          this.getPeople(teamId)
        }
      })
    },
    // 获取团队下的人员
    getPeople(teamId) {
      apiCommonServer.getTeamUserList(teamId).then(res => {
        if (res.errcode === 0) {
          const _data = res.data.user_list.map(item => {
            return {
              key: item.user_id.toString(),
              title: `${item.full_name} (${item.mobile})`,
              teamId: teamId,
              groupId: item.group_id
            }
          })
          this.cacheData = _data
          this.mockData = _data
        }
      })
    },
    // 下拉组改变
    groupChange(value) {
      this.groupId = value
      if (value) {
        this.mockData = this.cacheData.filter(item => item.groupId === value || this.targetKeys.indexOf(item.key) !== -1)
      } else {
        this.mockData = JSON.parse(JSON.stringify(this.cacheData))
      }
    },
    // 搜索匹配字段设置
    // 增加逗号分隔搜索 20220726
    filterOption(inputValue, option) {
      if (inputValue && inputValue.split(',').length > 1) {
        let flag = false
        const arr = inputValue.split(',')
        for (let index = 0; index < arr.length; index++) {
          if (arr[index] && option.title.includes(arr[index])) {
            flag = true
            // 终止循环
            break
          }
        }
        return flag
      }
      return option.title.includes(inputValue)
    },
    // 穿梭框值修改
    handleChange(nextTargetKeys, direction, moveKeys) {
      this.targetKeys = nextTargetKeys

      // console.log('targetKeys: ', nextTargetKeys)
      // console.log('direction: ', direction)
      // console.log('moveKeys: ', moveKeys)
    },
    // 穿梭框左侧选中状态修改
    handleSelectChange(sourceSelectedKeys, targetSelectedKeys) {
      this.selectedKeys = [...sourceSelectedKeys, ...targetSelectedKeys]

      // console.log('sourceSelectedKeys: ', sourceSelectedKeys)
      // console.log('targetSelectedKeys: ', targetSelectedKeys)
    },
    // 确定
    handleSubmitOk() {
      const data = this.targetKeys.map(item => { return parseFloat(item) })
      this.$emit('submitSuccess', data)
      this.handleOnlyClose()
    },
    // 取消
    handleOnlyClose() {
      this.targetKeys = []
      this.selectedKeys = []
      this.showModal = false
    }
  }
}
</script>

<style>

</style>
