<template>
  <form-drawer
    width="50%"
    :title="$t('buttons.task')"
    :visible="openDraw"
    :loading="fileLoading"
    @closeDrawer="handleDrawerClose"
  >
    <a-form :form="form">
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item :label="$t('label.projectName')">
            <span v-if="formData && formData.is_queue === 'T'" style="color: #ff0000;">
              <a-icon type="bulb" theme="filled" style="vertical-align: baseline;" /> {{ formData ? formData.name : '' }}
            </span>
            <span v-else>
              {{ formData ? formData.name : '' }}
            </span>
            <!-- <a-input disabled :value="formData ? formData.name : ''" /> -->
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('table.isClean')">
            <a-radio-group
              v-decorator="[
                'is_clean_empty',
                {
                  rules: [{ required: true }],
                  initialValue: formData && formData.is_clean_empty ? 'T' : 'F',
                },
              ]"
            >
              <a-radio value="T">{{ $t('dic.yes') }}</a-radio>
              <a-radio value="F">{{ $t('dic.no') }}</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('table.isFun')">
            <a-radio-group
              v-decorator="[
                'is_function',
                {
                  rules: [{ required: true }],
                  initialValue: formData && formData.is_function ? 'T' : 'F',
                },
              ]"
            >
              <a-radio value="T">{{ $t('dic.yes') }}</a-radio>
              <a-radio value="F">{{ $t('dic.no') }}</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('table.dirInvisible')">
            <a-radio-group
              v-decorator="[
                'is_invisible',
                {
                  rules: [{ required: true }],
                  initialValue: formData && formData.is_invisible ? 'T' : 'F',
                }
              ]"
            >
              <a-radio value="T">{{ $t('dic.yes') }}</a-radio>
              <a-radio value="F">{{ $t('dic.no') }}</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('label.isErrType')">
            <a-radio-group
              v-decorator="[
                'is_add_error_type',
                {
                  rules: [{ required: true }],
                  initialValue: formData && formData.is_add_error_type ==='T' ? 'T' : 'F',
                }
              ]"
            >
              <a-radio value="T">{{ $t('dic.yes') }}</a-radio>
              <a-radio value="F">{{ $t('dic.no') }}</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <!-- 任务规则 -->
          <a-form-item :label="$t('table.projectTaskRule')">
            <a-input v-decorator="['task_rule', { rules: [{ required: false, url: true }], initialValue: formData && formData.task_rule ? formData.task_rule : '' }]" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item :label="$t('label.upJson')">
            <uplaod-json
              :btn-disabled="!taskCanEdit || is_queue_p"
              :project-id="projectId"
              :file-name="fileName"
              @fileStart="handleFileStart"
              @changeProgress="handleChangeProgress"
              @fileSuccess="handleFileSuccess"
              @fileError="handleFileError"
            />
            <div v-if="showProgress">
              <a-progress :percent="fileProgress" status="active" />
            </div>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('table.projectType')">
            <a-select
              v-decorator="[
                'classification',
                {
                  rules: [{ required: true }], initialValue: formData && formData.classification ? formData.classification : 1,
                },
              ]"
              :disabled="!taskCanEdit"
              :get-popup-container="triggerNode => triggerNode.parentNode"
            >
              <a-select-option v-for="item in projectTypes" :key="item.id" :value="item.id">{{ lang === 'CN' ? item.name : item.en_name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('table.skuIndustry')">
            <a-select
              v-decorator="[
                'sku_type_id',
                {
                  rules: [], initialValue: formData && formData.sku_type_id ? formData.sku_type_id : first_h_id,
                },
              ]"
              :disabled="!taskCanEdit"
              show-search
              :filter-option="filterOption"
              :get-popup-container="triggerNode => triggerNode.parentNode"
            >
              <a-select-option v-for="item in hList" :key="item.type_id" :value="item.type_id">{{ item.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('table.skuProject')">
            <a-select
              v-decorator="[
                'sku_project_id',
                {
                  rules: [], initialValue: formData && formData.sku_project_id ? formData.sku_project_id : first_x_id,
                },
              ]"
              :disabled="!taskCanEdit"
              show-search
              :filter-option="filterOption"
              :get-popup-container="triggerNode => triggerNode.parentNode"
            >
              <a-select-option v-for="item in mList" :key="item.sku_project_id" :value="item.sku_project_id">{{ item.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('label.isFixSkuProject')">
            <a-radio-group
              v-decorator="[
                'is_fix_sku_project',
                {
                  rules: [{ required: true }],
                  initialValue: formData && formData.is_fix_sku_project ==='T' ? 'T' : 'F',
                }
              ]"
            >
              <a-radio value="T">{{ $t('dic.yes') }}</a-radio>
              <a-radio value="F">{{ $t('dic.no') }}</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item required :label="$t('label.taskFlow')">
            <a-input-group compact>
              <a-select
                v-decorator="[
                  'template_id',
                  {
                    initialValue: formData && formData.template_id ? formData.template_id : 1,
                  },
                ]"
                :disabled="!taskCanEdit"
                style="width: 50%;"
                :get-popup-container="triggerNode => triggerNode.parentNode"
              >
                <a-select-option v-for="item in flowTemps" :key="item.template_id" :value="item.template_id">{{ lang === 'CN' ? item.name : item.name_en }}</a-select-option>
              </a-select>
              <a-button type="danger" :disabled="!taskCanEdit" @click="changeFlowTemp"> {{ $t('buttons.temp') }} </a-button>
              <a-button type="primary" icon="plus" :disabled="!taskCanEdit" @click="addTask"> {{ $t('buttons.addTo') }} </a-button>
            </a-input-group>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="" :label-col="{ span: 0 }" :wrapper-col="{ span: 24 }">
            <task-list ref="taskList" :project-id="projectId" :can-edit="taskCanEdit" :lang="lang" />
          </a-form-item>
        </a-col>
        <!-- <a-col :span="24">
            <a-form-item label="项目描述">
              <tinymce-editor v-model="desc" :disabled="disabled" />
            </a-form-item>
          </a-col> -->
      </a-row>
    </a-form>
    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1,
      }"
    >
      <a-button style="margin-right: 8px" @click="handleDrawerClose">
        {{ $t("buttons.cancel") }}
      </a-button>
      <a-button type="primary" :loading="sureLoading" @click="onSubmit">
        {{ $t("buttons.define") }}
      </a-button>
    </div>
  </form-drawer>
</template>

<script>
import FormDrawer from '@/components/formDrawer'
// import TinymceEditor from '@/components/tinymceEditor'
import TaskList from './task-list'
import UplaodJson from './upload-json'
import { apiCommonServer } from '@/services'
import { changeProject, getProjectTemplate, changeProjectTemplate } from '@/services/project'
import { mapState } from 'vuex'
export default {
  name: 'ConfigTask',
  components: { FormDrawer, TaskList, UplaodJson },
  data() {
    return {
      openDraw: false,
      form: this.$form.createForm(this),
      formData: null,
      userList: [],
      fileName: '',
      desc: 'Welcome to Use Tinymce Editor',
      disabled: false,
      showTableData: false,
      projectId: null,
      taskCanEdit: true,
      fileLoading: false,
      fileProgress: 0,
      showProgress: false,
      sureLoading: false,
      reLoading: false,
      mList: [], // 项目目录
      hList: [], // 行业目录
      first_x_id: null, // 第一个项目目录id
      first_h_id: null, // 第一个行业目录id
      projectTypes: [ // 项目类型
        { id: 0, name: '挑杂', en_name: 'Pick miscellaneous' },
        { id: 1, name: '分到目标分类', en_name: 'Target classification' },
        { id: 2, name: '以图搜图', en_name: 'Search for pictures' },
        { id: 3, name: '未拟合数据', en_name: 'Unfixed data' }
      ],
      flowTemps: [] // 流程模板
    }
  },
  computed: {
    ...mapState('setting', ['lang']),
    is_queue_p() { // d
      const t = this.formData && (this.formData.is_queue === 'T' || this.formData.extend_task_id)
      return t
    }
  },
  watch: {
    formData(val) {
      if (!val) return
      this.projectId = val.project_id
      if (val.state !== 0) {
        this.taskCanEdit = false
      } else {
        this.taskCanEdit = true
      }
      this.fileName = val.data_file || ''
      this.$nextTick(() => {
        this.$refs.taskList.getTaskList()
      })
    }
  },
  created() {
    this.getMList()
    this.gethList()
    this.getProjectTemplateFun()
  },
  methods: {
    // 行业目录搜索
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    },
    // sku项目目录
    getMList() {
      apiCommonServer.skuProjectList().then(res => {
        if (res.errcode === 0) {
          if (res.data.sku_project_list && res.data.sku_project_list.length > 0) {
            this.mList = res.data.sku_project_list
            if (this.mList.length > 0) {
              this.first_x_id = this.mList[0].sku_project_id
            }
          }
        }
      })
    },
    // 获取sku行业目录
    gethList() {
      apiCommonServer.getSkuTypeList().then(res => {
        if (res.errcode === 0) {
          this.hList = res.data.sku_project_list
          if (this.hList.length > 0) {
            this.first_h_id = this.hList[0].type_id
          }
        }
      })
    },
    // 获取流程模板
    getProjectTemplateFun() {
      getProjectTemplate({}).then(res => {
        if (res.errcode === 0) {
          this.flowTemps = res.data.template_list
        }
      })
    },
    // 修改流程模板
    changeFlowTemp() {
      const _this = this
      this.$confirm({
        title: this.$t('confirm.title'),
        content: this.$t('confirm.useTemp'),
        onOk() {
          _this.changeFlowTempAPI()
        }
      })
    },
    changeFlowTempAPI() {
      const param = {
        project_id: this.projectId,
        template_id: this.form.getFieldValue('template_id')
      }
      changeProjectTemplate(param).then(res => {
        if (res.errcode === 0) {
          this.$nextTick(() => {
            this.$refs.taskList.getTaskList()
          })
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 关闭
    handleDrawerClose() {
      this.formData = null
      this.openDraw = false
    },
    // 确定
    onSubmit() {
      this.form.validateFields((err, values) => {
        if (!err) {
          this.sureLoading = true
          const param = Object.assign({ project_id: this.formData.project_id }, values)
          changeProject(param).then(res => {
            this.sureLoading = false
            if (res.errcode === 0) {
              this.$message.success(res.errmsg)
              this.$emit('submitSuccess')
              this.handleDrawerClose()
            } else {
              this.$message.error(res.errmsg)
            }
          })
        }
      })
    },
    // 附件上传开始
    handleFileStart(file) {
      this.showProgress = true
      this.fileLoading = true
      this.fileName = file.name
    },
    // 附件上传进度
    handleChangeProgress(num) {
      this.fileProgress = num
    },
    // 附件上传成功
    handleFileSuccess() {
      this.fileLoading = false
      this.showProgress = false
    },
    // 附件上传失败
    handleFileError() {
      this.fileLoading = false
      this.showProgress = false
    },
    // 添加任务
    addTask() {
      this.$refs.taskList.dataPush(this.formData.project_id)
    }
  }
}
</script>

<style>
    .ant-form-explain, .ant-form-extra {
        font-size: 12px;
    }
</style>
