<template>
  <form-modal
    :title="$t('modal.addPro')"
    :visible="showModal"
    @submitOk="handleSubmitOk"
    @onlyClose="handleOnlyClose"
  >
    <a-form :form="form" layout="inline">
      <a-form-item :label="$t('label.proName')">
        <a-input
          v-decorator="[
            'name',
            {
              rules: [{ required: true, message: $t('rules.proName') }], initialValue: ''
            },
          ]"
        />
      </a-form-item>
    </a-form>
  </form-modal>
</template>

<script>
import FormModal from '@/components/formModal'
import { createProject } from '@/services/project'
export default {
  name: 'AddProject',
  components: { FormModal },
  data() {
    return {
      showModal: false,
      form: this.$form.createForm(this)
    }
  },
  methods: {
    handleSubmitOk() {
      this.form.validateFields((err, values) => {
        if (!err) {
          createProject(values).then(res => {
            if (res.errcode === 0) {
              this.$emit('submitSuccess')
              this.handleOnlyClose()
            } else {
              this.$message.error(res.errmsg)
            }
          })
        }
      })
    },
    handleOnlyClose() {
      this.showModal = false
    }
  }
}
</script>

<style>

</style>
