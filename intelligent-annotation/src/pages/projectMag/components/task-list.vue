<template>
  <div>
    <a-table
      :columns="columns"
      :data-source="data"
      bordered
      row-key="task_flow_id"
      :pagination="false"
    >
      <template slot="orderNumber" slot-scope="text, record">
        <template v-if="record.task_flow_id === editingKey && record.order !== 0 && record.order !== 21">
          <!-- <a-tooltip title="Extra information">
            <a-icon type="info-circle" style="color: rgba(0,0,0,.45)" />
          </a-tooltip> -->
          <a-tooltip :title="$t('rules.intNum')">
            <a-input-number :value="text" :min="1" :max="20" @change="handleChange($event, 'order')" />
          </a-tooltip>
        </template>
        <span v-else>{{ text }}</span>
      </template>
      <template slot="name" slot-scope="text, record">
        <a-input
          v-if="record.task_flow_id === editingKey && record.order !== 0 && record.order !== 21"
          style="margin: -5px 0;width: 110px;"
          :value="text"
          @change="e => handleChange(e.target.value, 'name')"
        />
        <span v-else>{{ text }}</span>
      </template>
      <template slot="team_id" slot-scope="text, record">
        <a-select
          v-if="record.task_flow_id === editingKey"
          :default-value="text"
          @change="handleChange($event, 'team_id')"
        >
          <a-select-option
            v-for="item in teamList"
            :key="item.team_id"
            :value="item.team_id"
          >{{ item.name }}</a-select-option>
        </a-select>
        <span v-else>{{ getListNameById(record.team_id, teamList, 'team_id' ) }}</span>
      </template>
      <template slot="tool_id" slot-scope="text, record">
        <a-select
          v-if="record.task_flow_id === editingKey && record.order !== 0 && record.order !== 21"
          :default-value="text"
          @change="handleChange($event, 'tool')"
        >
          <a-select-option
            v-for="item in toolList"
            :key="item.id"
            :value="item.id"
          >{{ lang === 'CN' ? item.name : item.enName }}</a-select-option>
        </a-select>
        <span v-else>{{ getListNameById(record.tool, toolList, 'id' ) }}</span>
      </template>
      <template slot="task_type" slot-scope="text, record">
        <a-select
          v-if="record.task_flow_id === editingKey && record.order !== 0 && record.order !== 21"
          :default-value="text"
          @change="handleChange($event, 'task_flow_type')"
        >
          <a-select-option
            v-for="item in taskTypeList"
            :key="item.id"
            :value="item.id"
            :disabled="item.disabled"
          >{{ lang === 'CN' ? item.name : item.enName }}</a-select-option>
        </a-select>
        <span v-else> {{ getListNameById(record.task_flow_type, taskTypeList, 'id' ) }} </span>
      </template>
      <template slot="number" slot-scope="text, record">
        <a v-if="record.task_flow_id === editingKey" style="display: block;" @click.prevent="openChoosePeople(record)">{{ record.user_id_list.length }}</a>
        <template v-else>
          <a v-if="record.user_id_list.length > 0" style="display: block;" @click.prevent="openTaskPeople(record)"> {{ record.user_id_list.length }}</a>
          <span v-else>0</span>
        </template>
      </template>
      <template slot="action" slot-scope="text, record, index">
        <template v-if="record.task_flow_id === editingKey">
          <a @click.prevent="dataSave(record)">{{ $t('buttons.save') }}</a>
          <a-divider type="vertical" />
          <a @click.prevent="dataCancle(index)">{{ $t('buttons.cancel') }}</a>
        </template>
        <template v-else>
          <a :disabled="editingKey !== null" @click.prevent="dataEdit(index)">{{ $t('buttons.edit') }}</a>
          <template v-if="record.order !== 0 && record.order !== 21">
            <a-divider type="vertical" />
            <a-popconfirm
              :title="$t('confirm.del')"
              :disabled="editingKey !== null || !canEdit"
              @confirm="() => delDataFun(record)"
            >
              <a :disabled="editingKey !== null || !canEdit">{{ $t('buttons.delete') }}</a>
            </a-popconfirm>
          </template>
        </template>
      </template>
    </a-table>
    <select-people ref="selectPeople" :modal-title="taskPeopleTitle" :task-id="editingKey" @submitSuccess="handleChange($event, 'user_id_list')" />
    <task-people ref="showTaskPeople" :modal-title="taskPeopleTitle" :data="taskPeopleData" :flow-id="taskPeopleFlowId" />
  </div>
</template>

<script>
import SelectPeople from './select-people'
import TaskPeople from './task-people'
import { getTeamList } from '@/services/team'
import { getTaskFlowList, createTaskFlow, uploadTaskFlow, deleteTaskFlow } from '@/services/project'
export default {
  name: 'TaskList',
  components: { SelectPeople, TaskPeople },
  props: {
    projectId: { // 项目id
      type: [String, Number],
      default: () => {
        return ''
      }
    },
    canEdit: {
      type: Boolean,
      default: () => {
        return true
      }
    },
    lang: {
      type: String,
      default: 'CN'
    }
  },
  data() {
    return {
      cacheData: [],
      data: [],
      editingKey: null,
      toolList: [
        { id: 1, name: '清洗工具', enName: 'Cleaning tools' }
      ],
      taskTypeList: [
        { id: 8, name: '注册任务', enName: 'Register task', disabled: true },
        { id: 9, name: '审核任务', enName: 'Audit task', disabled: true },
        { id: 10, name: '清洗任务', enName: 'Cleaning task', disabled: true },
        { id: 11, name: '检查任务', enName: 'Inspection task', disabled: false },
        { id: 12, name: '汇总任务', enName: 'Summary task', disabled: true }
      ],
      teamList: [],
      taskPeopleTitle: '',
      taskPeopleData: [],
      taskPeopleFlowId: null
    }
  },
  computed: {
    columns() {
      const data = [
        { title: this.$t('table.sort'), dataIndex: 'order', scopedSlots: { customRender: 'orderNumber' }},
        { title: this.$t('table.taskName'), dataIndex: 'name', scopedSlots: { customRender: 'name' }},
        { title: this.$t('table.rTeam'), dataIndex: 'team_id', scopedSlots: { customRender: 'team_id' }},
        { title: this.$t('table.toolType'), dataIndex: 'tool', scopedSlots: { customRender: 'tool_id' }},
        { title: this.$t('table.taskType'), dataIndex: 'task_flow_type', scopedSlots: { customRender: 'task_type' }},
        { title: this.$t('table.people'), dataIndex: 'user_id_list', align: 'center', width: 80, scopedSlots: { customRender: 'number' }},
        { title: this.$t('table.action'), key: 'action', align: 'center', scopedSlots: { customRender: 'action' }}
      ]
      return data
    }
  },
  created() {
    this.getTeamListFun()
  },
  methods: {
    // 根据id获取下拉name
    getListNameById(id, arr, idkey) {
      const item = arr.filter(item => item[idkey] === id)[0]
      return item && item.name ? item.name : ''
    },
    // 获取团队数据
    getTeamListFun() {
      getTeamList({}).then(res => {
        if (res.errcode === 0) {
          this.teamList = res.data.team_list
        }
      })
    },
    // 获取任务列表
    getTaskList() {
      if (!this.projectId) return
      getTaskFlowList(this.projectId).then(res => {
        if (res.errcode === 0) {
          const _data = res.data.task_flow_list
          this.data = _data
          this.cacheData = JSON.parse(JSON.stringify(this.data))
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 添加任务流
    dataPush(id) {
      if (this.editingKey) {
        this.$message.error(this.$t('rules.noSave'))
        return
      }
      const param = {
        project_id: id, // 项目id
        name: '', // 名称
        tool: 1, // 工具类型((0, '标注'),(1, '清洗'),(2, 'QC'),)
        team_id: this.teamList[0].team_id, // 团队id
        task_flow_type: 11,
        order: 1, // 排序
        user_id_list: [],
        task_flow_id: new Date().getTime(),
        is_web_tem: true
      }
      this.editingKey = param.task_flow_id
      this.data.unshift(param)
    },
    // 数据改变时
    handleChange(value, column) {
      const key = this.editingKey
      const newData = [...this.data]
      const target = newData.filter(item => key === item.task_flow_id)[0]
      if (target) {
        if (column === 'team_id') {
          target['user_id_list'] = []
        }
        target[column] = value
        this.data = newData
      }
    },
    // 选择人
    openChoosePeople(item) {
      if (!item.team_id) {
        this.$message.error(this.$t('rules.checkTeam'))
        return
      }
      this.taskPeopleTitle = item.name
      this.$refs.selectPeople.targetKeys = item.user_id_list.map(i => { return i.toString() })
      this.$refs.selectPeople.getGroupListFun(item.team_id)
      this.$refs.selectPeople.showModal = true
    },
    // 查看任务选择的人
    openTaskPeople(item) {
      this.taskPeopleTitle = item.name
      this.taskPeopleData = item.user_list
      this.taskPeopleFlowId = item.task_flow_id
      this.$refs.showTaskPeople.showModal = true
    },
    // 编辑
    dataEdit(idx) {
      this.editingKey = this.data[idx].task_flow_id
    },
    // 保存
    dataSave(item) {
      if (item.is_web_tem) { // 临时数据
        this.createTaskFlowFun(item)
        return
      }
      uploadTaskFlow(item).then(res => {
        if (res.errcode === 0) {
          this.editingKey = null
          this.getTaskList()
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 新增接口
    createTaskFlowFun(item) {
      const param = {
        project_id: item.project_id, // 项目id
        name: item.name, // 名称
        tool: item.tool, // 工具类型((0, '标注'),(1, '清洗'),(2, 'QC'),)
        team_id: item.team_id, // 团队id
        task_flow_type: item.task_flow_type,
        order: item.order, // 排序
        user_id_list: item.user_id_list
      }
      createTaskFlow(param).then(res => {
        if (res.errcode === 0) {
          this.editingKey = null
          this.getTaskList()
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 取消
    dataCancle(index) {
      this.editingKey = null
      const newData = [...this.data]
      if (newData[index] && newData[index].is_web_tem) {
        newData.splice(index, 1)
        this.data = newData
        return
      }
      const _item = Object.assign(newData[index], this.cacheData[index])
      newData[index] = _item
      this.data = newData
    },
    // 删除流程
    delDataFun(item) {
      deleteTaskFlow(item.task_flow_id).then(res => {
        if (res.errcode === 0) {
          this.getTaskList()
        } else {
          this.$message.error(res.errmsg)
        }
      })
    }
  }
}
</script>

<style>
</style>
