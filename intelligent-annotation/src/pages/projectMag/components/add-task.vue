<template>
  <form-modal
    title="新增任务"
    width="50%"
    :visible="showModal"
    @submitOk="handleSubmitOk"
    @onlyClose="handleOnlyClose"
  >
    <a-form :form="form" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="任务名称">
            <a-input
              v-decorator="[
                'name',
                {
                  rules: [{ required: true, message: '请输入任务名称' }],
                  initialValue: '',
                },
              ]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="工具类型">
            <a-select
              v-decorator="[
                'tool_id',
                {
                  rules: [{ required: true, message: '请选择工具类型' }],
                  initialValue: formData ? formData.tool_id : '',
                },
              ]"
            >
              <a-select-option
                v-for="item in toolList"
                :key="item.id"
                :value="item.id"
              >{{ item.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="配置人员">
            <a-button type="primary"> 添加 </a-button>
          </a-form-item>
        </a-col>
        <a-col v-if="tableData && tableData.length > 0" :span="12">
          <a-form-item label="">
            <a-table
              :columns="columns"
              :data-source="tableData"
              bordered
              row-key="id"
              :pagination="false"
            >
              <span slot="action" slot-scope="text, record">
                <a-popconfirm
                  :title="$t('confirm.del')"
                  @confirm="() => delDataFun(record)"
                >
                  <a href="javascript:;">{{ $t('buttons.delete') }}</a>
                </a-popconfirm>
              </span>
            </a-table>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </form-modal>
</template>

<script>
import FormModal from '@/components/formModal'
export default {
  name: 'AddTask',
  components: { FormModal },
  data() {
    return {
      showModal: false,
      form: this.$form.createForm(this),
      formData: null,
      toolList: [
        { id: 0, name: '清洗工具' },
        { id: 2, name: '标注工具' }
      ],
      tableData: []
    }
  },
  methods: {
    handleSubmitOk() {
      this.form.validateFields((err, values) => {
        if (!err) {
          console.log(values)
          this.$emit('submitSuccess')
          this.handleOnlyClose()
        }
      })
    },
    handleOnlyClose() {
      this.showModal = false
    },
    // 删除人员
    delDataFun(item) {
      console.log(item)
    }
  }
}
</script>

<style>
</style>
