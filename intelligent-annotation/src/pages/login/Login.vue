<template>
  <common-layout>
    <div class="login">
      <h3 class="title">{{ $t('contents.loginStyle2') }}</h3>
      <a-form :form="form" @submit="onSubmit">
        <a-alert
          v-show="error"
          type="error"
          :closable="false"
          :message="error"
          show-icon
          style="margin-bottom: 24px"
        />
        <a-form-item>
          <a-input
            v-decorator="[
              'mobile',
              {
                rules: [
                  {
                    required: true,
                    message: $t('rules.account'),
                    whitespace: true,
                  },
                ],
              },
            ]"
            autocomplete="autocomplete"
            size="large"
            :placeholder="$t('placeholder.mobile')"
          >
            <a-icon slot="prefix" type="mobile" />
          </a-input>
        </a-form-item>
        <a-form-item>
          <a-input
            v-decorator="[
              'password',
              {
                rules: [
                  {
                    required: true,
                    message: $t('rules.password'),
                    whitespace: true,
                  },
                ],
              },
            ]"
            size="large"
            :placeholder="$t('placeholder.password')"
            autocomplete="autocomplete"
            type="password"
          >
            <a-icon slot="prefix" type="lock" />
          </a-input>
        </a-form-item>
        <a-form-item>
          <a-button
            :loading="logging"
            style="width: 100%; margin-top: 24px"
            size="large"
            html-type="submit"
            type="primary"
          >{{ $t("buttons.login") }}</a-button>
        </a-form-item>
        <div>
          <!-- <a-checkbox :checked="true" >自动登录</a-checkbox> -->
          <router-link to="/register">{{
            $t("contents.register")
          }}</router-link>
          <router-link style="float: right" to="/forgetPwd">{{
            $t("contents.forgetPwd")
          }}</router-link>
        </div>
      </a-form>
    </div>
  </common-layout>
</template>

<script>
const sha1 = require('js-sha1')
import CommonLayout from '@/layouts/CommonLayout'
import { login, loginAuth } from '@/services/user'
import { setAuthorization } from '@/utils/request'
// import { loadRoutes } from '@/utils/routerUtil'
import { mapMutations } from 'vuex'

export default {
  name: 'Login',
  components: { CommonLayout },
  data() {
    return {
      logging: false,
      error: '',
      form: this.$form.createForm(this)
    }
  },
  methods: {
    ...mapMutations('account', ['setUser', 'setPermissions']),
    onSubmit(e) {
      e.preventDefault()
      this.form.validateFields((err) => {
        if (!err) {
          this.logging = true
          const name = this.form.getFieldValue('mobile')
          const password = sha1(this.form.getFieldValue('password'))
          login(name, password).then(this.afterLogin).catch((err) => {
            console.log('login error' + err)
            this.logging = false
          })
        }
      })
    },
    afterLogin(res) {
      this.logging = false
      if (res.errcode === 0) {
        const loginRes = res.data
        // const {permissions, role} = loginRes
        // this.setPermissions(permissions)
        // this.setRoles(roles)
        setAuthorization({
          token: loginRes.access_token
        })
        this.loginAuthFun()
        // 获取路由配置
        // getRoutesConfig().then((result) => {
        //   const routesConfig = result.data;
        //   loadRoutes(routesConfig);
        //   this.$router.push("/demo");
        //   // this.$message.success(loginRes.message, 3)
        // });
      } else {
        this.error = res.errmsg
      }
    },
    loginAuthFun() {
      loginAuth().then(res => {
        if (res.errcode === 0) {
          const loginRes = res.data
          const user = {
            ...loginRes,
            name: loginRes.full_name ? loginRes.full_name : loginRes.mobile
          }
          this.setUser(user)
          const _arr = []
          if (loginRes.jurisdiction_list && loginRes.jurisdiction_list.length > 0) {
            loginRes.jurisdiction_list.forEach(item => {
              const _item = { id: item.jurisdiction_id }
              _arr.push(_item)
              // 多包装权限 郑州/上海
              if (item.jurisdiction_id === 404 && [1, 2].indexOf(user.team_id) > -1) {
                _arr.push({ id: 'my_package' })
              } else if (item.jurisdiction_id === 403 && [1, 2].indexOf(user.team_id) > -1) {
                _arr.push({ id: 'audit_package' })
              }
            })
          }
          this.setPermissions(_arr)
          this.$router.push('/personal/task')
        } else {
          this.error = res.errmsg
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.common-layout {
  .login {
    width: 450px;
    padding: 30px;
    background: #fff;
    border: 1px solid #eaeaea;
    box-shadow: 0 0 20px 2px rgba(0,0,0,.1);
    border-radius: 10px;
    // margin: 40px auto 0;
    @media screen and (max-width: 576px) {
      width: 95%;
    }
    @media screen and (max-width: 320px) {
      .captcha-button {
        font-size: 14px;
      }
    }
    .title {
      margin-bottom: 40px;
      text-align: center;
      color: #505458;
      font-weight: bolder;
      font-size: 24px;
    }
    .icon {
      font-size: 24px;
      color: @text-color-second;
      margin-left: 16px;
      vertical-align: middle;
      cursor: pointer;
      transition: color 0.3s;

      &:hover {
        color: @primary-color;
      }
    }
  }
}
</style>
