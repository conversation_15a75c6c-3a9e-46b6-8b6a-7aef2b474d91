<template>
  <common-layout>
    <div class="login">
      <h3 class="title">{{ $t('contents.register') }}</h3>
      <a-form :form="form" layout="horizontal" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" @submit="onSubmit">
        <a-alert
          v-show="error"
          type="error"
          :closable="true"
          :message="error"
          show-icon
          style="margin-bottom: 24px"
        />
        <a-form-item :label="$t('label.mobile')">
          <a-input-group compact>
            <country-code @changeCode="changeCountryCode" />
            <a-input
              v-decorator="[
                'mobile',
                { rules: [{ required: true, message: $t('rules.mobile') }] },
              ]"
              style="width: 70%"
              size="large"
            />
          </a-input-group>
        </a-form-item>
        <a-form-item :label="$t('label.fullname')">
          <a-input
            v-decorator="[
              'full_name',
              { rules: [{ required: true, message: $t('rules.fullname') }] },
            ]"
            size="large"
          />
        </a-form-item>
        <a-form-item :label="$t('label.code')">
          <a-row :gutter="8" style="margin: 0 -4px">
            <a-col :span="16">
              <a-input
                v-decorator="[
                  'ver_code',
                  { rules: [{ required: true, message: $t('rules.code') }] },
                ]"
                size="large"
                autocomplete="off"
              />
            </a-col>
            <a-col :span="8" style="padding-left: 4px; cursor: pointer" @click="resetVerCode">
              <img :src="imgUrl" style="width: 100%;height: 41px;" alt="">
            </a-col>
          </a-row>
        </a-form-item>
        <a-form-item :label="$t('label.mobileCode')">
          <a-row :gutter="8" style="margin: 0 -4px">
            <a-col :span="16">
              <a-input
                v-decorator="[
                  'mobile_vc',
                  { rules: [{ required: true, message: $t('rules.mobileCode') }] },
                ]"
                size="large"
                autocomplete="off"
              />
            </a-col>
            <a-col :span="8" style="padding-left: 4px">
              <a-button
                style="width: 100%"
                class="captcha-button"
                size="large"
                type="primary"
                :disabled="getCodeBtnStatus"
                @click="getCodeFun"
              >{{ getCodeText }}</a-button>
            </a-col>
          </a-row>
        </a-form-item>
        <a-form-item :label="$t('label.password')">
          <a-input
            v-decorator="[
              'password',
              {
                rules: [
                  {
                    required: true,
                    message: $t('rules.password'),
                    whitespace: true,
                  }, {
                    min: 6,
                    message: $t('rules.minPwd')
                  }
                ],
              },
            ]"
            size="large"
            autocomplete="off"
            type="password"
          />
        </a-form-item>
        <a-form-item :label="$t('contents.surePwd')">
          <a-input
            v-decorator="[
              'surePassword',
              {
                rules: [
                  {
                    required: true,
                    message: $t('rules.password'),
                    whitespace: true,
                  },{
                    validator: compareToFirstPassword
                  }
                ],
              },
            ]"
            size="large"
            autocomplete="off"
            type="password"
          />
        </a-form-item>
        <div class="r_btn" style="margin: 48px 0 15px;text-align: center;">
          <a-button
            :loading="logging"
            style="width: 60%;"
            size="large"
            html-type="submit"
            type="primary"
          >{{ $t('buttons.register') }}</a-button>
        </div>
        <div style="text-align: center">
          <router-link to="/login">{{ $t('contents.goLogin') }}</router-link>
        </div>
      </a-form>
    </div>
  </common-layout>
</template>

<script>
const sha1 = require('js-sha1')
import CommonLayout from '@/layouts/CommonLayout'
import { register, getPhoneVc } from '@/services/user'
import CountryCode from './component/countryCode'
// import Cookie from "js-cookie";
export default {
  name: 'Register',
  components: { CommonLayout, CountryCode },
  data() {
    return {
      logging: false,
      error: '',
      form: this.$form.createForm(this),
      imgUrl: process.env.VUE_APP_API_BASE_URL + '/user/get_pic_vc/?time=' + new Date().getTime(),
      code: '86',
      countTime: 60,
      getCodeBtnStatus: false,
      timer: null
    }
  },
  computed: {
    getCodeText() {
      const str = this.getCodeBtnStatus ? this.countTime + 's' : ''
      return str + this.$t('contents.getCaptcha')
    }
  },
  methods: {
    // 国别码改变
    changeCountryCode(value) {
      this.code = value
    },
    // 刷新随机码
    resetVerCode() {
      this.imgUrl = process.env.VUE_APP_API_BASE_URL + '/user/get_pic_vc/?time=' + new Date().getTime()
    },
    // 在此确认密码校验
    compareToFirstPassword(rule, value, callback) {
      const form = this.form
      if (value && value !== form.getFieldValue('password')) {
        callback(this.$t('rules.surePwdErr'))
      } else {
        callback()
      }
    },
    // 获取验证码
    getCodeFun() {
      const mobile = this.form.getFieldValue('mobile')
      if (!mobile) {
        this.$message.error(this.$t('rules.mobile'))
        return
      }
      const code = this.form.getFieldValue('ver_code')
      if (!code) {
        this.$message.error(this.$t('rules.code'))
        return
      }
      this.getCodeBtnStatus = true
      this.timer = setInterval(() => {
        this.countTimeFun()
      }, 1000)
      this.getPhoneVcFun()
    },
    // 获取验证码接口
    getPhoneVcFun() {
      const param = {
        verify_type: 1,
        ver_code: this.form.getFieldValue('ver_code'),
        mobile: this.form.getFieldValue('mobile'),
        code: this.code
      }
      getPhoneVc(param).then((res) => {
        if (res.errcode === 0) {
          this.$message.success(res.errmsg)
        } else {
          this.$message.error(res.errmsg)
          this.clearTimerFun()
        }
      })
    },
    // 倒计时
    countTimeFun() {
      this.countTime--
      if (this.countTime <= 0) {
        this.clearTimerFun()
      }
    },
    // 清除定时器
    clearTimerFun() {
      clearInterval(this.timer)
      this.getCodeBtnStatus = false
    },
    // 注册
    onSubmit(e) {
      e.preventDefault()
      this.form.validateFields((err, values) => {
        if (!err) {
          this.logging = true
          const param = {
            mobile: values.mobile,
            code: this.code,
            mobile_vc: values.mobile_vc,
            password: sha1(values.password),
            full_name: values.full_name
          }
          register(param).then((res) => {
            this.logging = false
            if (res.errcode === 0) {
              this.$message.success(res.errmsg)
              setTimeout(() => {
                this.$router.push('/login')
              }, 1000)
            } else {
              this.$message.error(res.errmsg)
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.common-layout {
  .login {
    width: 820px;
    padding: 30px;
    background: #fff;
    border: 1px solid #eaeaea;
    box-shadow: 0 0 20px 2px rgba(0,0,0,.1);
    border-radius: 10px;
    margin: 0 auto;
    @media screen and (max-width: 576px) {
      width: 95%;
    }
    @media screen and (max-width: 320px) {
      .captcha-button {
        font-size: 14px;
      }
    }
    .title {
      margin-bottom: 40px;
      text-align: center;
      color: #505458;
      font-weight: bolder;
      font-size: 24px;
    }
    .icon {
      font-size: 24px;
      color: @text-color-second;
      margin-left: 16px;
      vertical-align: middle;
      cursor: pointer;
      transition: color 0.3s;

      &:hover {
        color: @primary-color;
      }
    }
  }
}
</style>
