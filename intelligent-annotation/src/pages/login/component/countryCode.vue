<template>
  <a-select
    style="width: 28%"
    size="large"
    show-search
    :dropdown-match-select-width="false"
    :default-value="43"
    :filter-option="false"
    @popupScroll="codeListScroll"
    @blur="restShowCodeList"
    @search="countryListFilter"
    @select="handleCodeChange"
  >
    <a-select-option
      v-for="item in codeList"
      :key="item.id"
      :value="item.id"
    >
      {{ lang === 'CN' ? item.name_cn : item.name_en }} {{ '(' + item.code + ')' }}
    </a-select-option>
  </a-select>
</template>

<script>
import { countryList } from '@/services/user'
import { mapState } from 'vuex'
export default {
  name: 'CountryCode',
  data() {
    return {
      codeList: [{ id: 43, name_cn: '中国', name_en: 'China.', code: '86' }],
      cacheCodeList: [], // 接口返回的所有国家列表
      filterData: [], // 搜索结果集合
      maxShowCode: 30, // 最长展示的下拉数据长度
      showCodePage: 1 // 国家列表页码
    }
  },
  computed: {
    ...mapState('setting', ['lang'])
  },
  created() {
    this.countryListFun()
  },
  methods: {
    // 国别码改变
    handleCodeChange(value) {
      const item = this.cacheCodeList.filter(it => it.id === value)[0]
      this.$emit('changeCode', item.code)
    },
    restShowCodeList() {
      this.showCodePage = 1
      this.filterData = [...this.cacheCodeList]
      this.codeList = this.cacheCodeList.filter((it, index) => { return (index >= 0 && index < this.maxShowCode) || it.code === this.code })
    },
    countryListFilter(value, option) {
      const keyName = this.lang === 'CN' ? 'name_cn' : 'name_en'
      this.filterData = this.cacheCodeList.filter(it => { return it[keyName].toLowerCase().indexOf(value.toLowerCase()) >= 0 || it.code === this.code })
      this.showCodePage = 1
      this.codeList = this.filterData.filter((it, i) => { return i >= 0 && i < this.maxShowCode })
    },
    codeListScroll(e) {
      const { target } = e
      const rmHeight = target.scrollHeight - target.scrollTop
      const clHeight = target.clientHeight
      if (rmHeight === 0 && clHeight === 0) {
        this.showCodePage = 1
      } else {
        if (rmHeight < clHeight + 30) {
          if (this.codeList.length >= this.filterData.length) return
          this.showCodePage += 1
          const temp = this.filterData.filter((it, i) => { return i >= this.maxShowCode * (this.showCodePage - 1) && i < this.maxShowCode * this.showCodePage && it.code !== this.code })
          const newArr = this.codeList.concat(temp)
          this.codeList = newArr
        }
      }
    },
    // 获取手机国家编号数据
    countryListFun() {
      countryList().then((res) => {
        if (res.errcode === 0) {
          this.cacheCodeList = res.data.items
          this.restShowCodeList()
        }
      })
    }
  }
}
</script>

<style>

</style>
