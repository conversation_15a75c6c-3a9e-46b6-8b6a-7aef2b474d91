<template>
  <div class="page_container">
    <div class="contents">
      <div class="lt_content">
        <div class="t_canvas_container">
          <CanvasBigPic ref="bigPicCmp" :img-url="big_img_url" />
        </div>
        <div class="b_folder_container">
          <TaskFolders
            ref="taskFloderCmp"
            :current-folder-id="startFolderId"
            @folderItemClick="taskFolderClick"
            @getDirTotal="e => catTotal = e"
          />
        </div>
      </div>
      <div id="small_pic_container" class="mid_content">
        <FolderPics
          ref="folderPicsCmp"
          :row-num="midPicRowSize"
          :dir-id="startFolderId"
          :current-items="currentPics"
          @onChangeBigPic="e => big_img_url = e"
          @onChangeCurrentPics="e => currentPics = e"
          @pageLoading="e => pageLoading = e"
          @midPicTotal="e => curCutNum = e"
        />
      </div>
      <div class="rt_content">
        <RightResult :working="tasking" :sku-project-list="mList" :sku-temp-list="sku_temps" @onSkuTemp="handleSkuTemp" @onDeleteScuess="getTmpSkuListFun" />
      </div>
    </div>
    <div class="page_bot">
      <div class="lt">
        <span>{{ $t('table.project') }}：{{ project_name }}</span>
        <span style="margin-left: 15px;">{{ $t('contents.catTotal') }}：{{ catTotal }}</span>
        <span style="margin-left: 15px;">{{ $t('contents.curCutNum') }}：{{ curCutNum }}</span>
      </div>
      <div class="mid">
        <a-icon type="minus-circle" @click="minusNum" />
        <span class="scale_num">{{ cutPicRate + '%' }}</span>
        <a-icon type="plus-circle" @click="plusNum" />
      </div>
      <div class="rt">
        <a-button type="primary" class="btn_back" @click="goBack">{{ $t('buttons.return') }}</a-button>
        <template v-if="tasking">
          <!-- <a-button type="danger" class="btn_giveup" @click="giveupTask">{{ $t('buttons.giveUp') }}</a-button> -->
          <a-button type="primary" class="btn_register" @click="openRegisterForm">{{ $t('buttons.register') }}</a-button>
          <a-button type="danger" class="btn_submit" @click="submitTask">{{ $t('buttons.submit') }}</a-button>
        </template>
        <template v-else>
          <a-button type="primary" class="btn_start" @click="startTask">{{ $t('buttons.start') }}</a-button>
        </template>
      </div>
    </div>
    <AddSkuForm ref="addSkuCmp" :working="tasking" :task-folder-id="startFolderId" :sku-project-list="mList" :sku-type-list="hList" @onSuccess="handleSuccess" />
    <div v-if="pageLoading" class="page_fixed_load">
      <a-spin />
    </div>
  </div>
</template>

<script>
import CanvasBigPic from './components/canvas-big-pic'
import TaskFolders from './components/task-folders'
import FolderPics from './components/folder-pics'
import RightResult from './components/right-result'
import AddSkuForm from './components/add-sku-form'
import { apiCommonServer } from '@/services'
import { startSkuDir, getTmpSkuList, submitDirSku } from '@/services/work'
import { setHours, getHours, removeHours } from '@/utils/workHours.js'
const sha1 = require('js-sha1')
export default {
  name: 'NewTaskHandle',
  components: { CanvasBigPic, TaskFolders, FolderPics, RightResult, AddSkuForm },
  data() {
    return {
      taskFlowType: null, // 流程类型 8注册 9审核
      project_name: '',
      pageLoading: false,
      cutPicRate: 100,
      catTotal: 0, // 任务文件夹数量
      curCutNum: 0, // 中间切图数
      big_img_url: '', // 大图url
      currentPics: [], // 选中的图片集合
      midPicRowSize: 5, // 小图片一行多少个
      tasking: false, // 是否开始了任务
      startFolderId: null, // 开始的任务文件夹id
      pageStayTime: 0, // 页面停留时间
      workingHours: 0, // 工作工时
      isCleanTime: false, // 是否重新计时
      timer: null, // 定时器储存对象
      sku_temps: [], // 临时sku集合
      hList: [], // sku行业目录
      mList: [] // sku项目目录
    }
  },
  mounted() {
    this.project_name = this.$route.query.name
    this.taskFlowType = this.$route.query.task_flow_type
    this.customWatchEvent()
    this.getMList()
    this.gethList()
  },
  destroyed() {
    this.removeCustomWatchEvent()
  },
  methods: {
    customWatchEvent() {
      window.addEventListener('resize', this.windowResizeFun, true)
    },
    removeCustomWatchEvent() {
      window.removeEventListener('resize', this.windowResizeFun, true)
    },
    getMList() { // sku项目目录
      apiCommonServer.skuProjectList().then(res => {
        if (res.errcode === 0) {
          if (res.data.sku_project_list && res.data.sku_project_list.length > 0) {
            this.mList = res.data.sku_project_list
          }
        }
      })
    },
    gethList() { // 获取sku行业目录
      apiCommonServer.getSkuTypeList().then(res => {
        if (res.errcode === 0) {
          this.hList = res.data.sku_project_list
        }
      })
    },
    plusNum() { // 缩小图片
      if (this.midPicRowSize - 1 < 2) {
        return
      }
      this.midPicRowSize -= 1
      this.changeRowSizeRate()
    },
    minusNum() { // 放大图片
      const containerW = document.getElementById('small_pic_container').getElementsByClassName('pics_content')[0].offsetWidth
      if ((containerW / (this.midPicRowSize + 1)) < 100) {
        return
      }
      this.midPicRowSize += 1
      this.changeRowSizeRate()
    },
    changeRowSizeRate() {
      this.cutPicRate = this.midPicRowSize > 5 ? Math.floor(100 + (5 - this.midPicRowSize) * 10) : Math.floor(100 - (this.midPicRowSize - 5) * 10)
    },
    windowResizeFun() { // 窗口改变
      const containerW = document.getElementById('small_pic_container').getElementsByClassName('pics_content')[0].offsetWidth
      this.midPicRowSize = Math.floor(containerW / 100)
      this.changeRowSizeRate()
    },
    startTask() { // 开始任务
      if (!this.startFolderId) {
        this.$message.error(this.$t('confirm.cnt2'))
        return
      }
      this.pageLoading = true
      const param = {
        task_flow_id: parseFloat(this.$route.query.task_flow_id),
        dir_id: this.startFolderId
      }
      startSkuDir(param).then(res => {
        this.pageLoading = false
        if (res.errcode === 0) {
          this.tasking = true
          this.refreshTaskFolders()
          this.getTmpSkuListFun()
          const data = res.data
          this.workingHours = data && data.working_hours ? data.working_hours : 0
          this.isCleanTime = data.is_clean && data.is_clean === 'T'
          this.startCountTime()
        } else if (res.errcode === 100) {
          this.$message.error(res.errmsg)
          this.startFolderId = null
          this.refreshTaskFolders()
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    startCountTime() { // 开始记录工作时间
      const t = getHours()
      const folder_id = this.startFolderId
      if (!t) { // 当本地没有保存的工时时
        setHours(folder_id, 5)
      }
      if (t && t.workId && t.workId !== folder_id) { // 当开始的文件夹id不相同时，重新计时
        setHours(folder_id, 5)
      }
      if (this.isCleanTime) { // 当开始接口后端返回的是否重新计时为T，重新计时
        setHours(folder_id, 5)
      }
      clearInterval(this.timer)
      this.timer = setInterval(() => {
        if (this.pageStayTime <= 60) {
          const _time = getHours()
          if (_time) {
            const _t = parseInt(_time.value) + 5
            setHours(folder_id, _t)
            // this.showWorkHours(_t)
          } else {
            setHours(folder_id, 0)
          }
        }
        this.pageStayTime += 5
      }, 5000)
    },
    // 刷新任务文件夹列表
    refreshTaskFolders() {
      this.$refs.taskFloderCmp.getDirListFun(true)
    },
    // 获取任务文件夹下临时SKU
    getTmpSkuListFun() {
      getTmpSkuList({ dir_id: this.startFolderId }).then(res => {
        if (res.errcode === 0) {
          this.sku_temps = res.data.data_list
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    submitTask() { // 提交任务
      const _this = this
      let processName = ''
      if(_this.taskFlowType == 8) {
        processName = _this.$t('confirm.cnt81')
      } else if(_this.taskFlowType == 9) {
        processName = _this.$t('confirm.cnt9')
      }
      this.$confirm({
        title: this.$t('confirm.title'),
        content: processName,
        onOk() {
          _this.submitTaskApi()
        }
      })
    },
    submitTaskApi() {
      const _t = parseInt(getHours().value)
      const _str = _t + 'TD'
      const param = {
        dir_id: this.startFolderId,
        working_hours: _t,
        sha_str: sha1(_str)
      }
      this.pageLoading = true
      submitDirSku(param).then(res => {
        this.pageLoading = false
        if (res.errcode === 0) {
          this.tasking = false
          removeHours() // 清除本地工作时间
          this.startFolderId = null
          this.sku_temps = []
          this.refreshTaskFolders()
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    openRegisterForm() { // 打开sku注册表单
      if (this.currentPics.length > 0) {
        this.$refs.addSkuCmp.pic_url = this.currentPics[0].box_url
        this.$refs.addSkuCmp.cacheFileUrl = this.currentPics[0].box_url
      }
      this.$refs.addSkuCmp.openDraw = true
    },
    handleSuccess(data) { // 注册或修改sku成功反馈
      this.getTmpSkuListFun()
    },
    taskFolderClick(item) { // 任务文件夹点击
      if (this.startFolderId === item.dir_id) {
        this.$refs.folderPicsCmp.getData()
        return
      }
      if (this.tasking) return
      this.startFolderId = item.dir_id
      if (parseFloat(this.$route.query.task_flow_type) === 9) { // 审核流程下点击任务文件夹获取此文件夹下临时SKU
        this.getTmpSkuListFun()
      }
    },
    handleSkuTemp(item) { // 临时文件夹点击
      // if (!this.tasking) {
      //   this.$message.error(this.$t('confirm.noWork'))
      //   return
      // }
      this.$refs.addSkuCmp.pic_url = item.logo_url
      this.$refs.addSkuCmp.formData = item
      this.$refs.addSkuCmp.openDraw = true
    },
    goBack() { // 返回我的任务页
      if (!this.tasking) {
        this.$router.push({ path: '/personal/task' })
        return
      }
      const _this = this
      this.$confirm({
        title: this.$t('confirm.title'),
        content: this.$t('confirm.cnt'),
        onOk() {
          _this.$router.push({ path: '/personal/task' })
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.page_container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding-bottom: 56px;
  position: relative;
  .contents {
    width: 100%;
    height: 100%;
    display: flex;
    .lt_content {
      width: 25%;
      max-width: 520px;
      min-width: 250px;
      display: flex;
      flex-direction: column;
      .t_canvas_container {
        height: 45%;
        max-height: 400px;
        min-height: 250px;
        position: relative;
      }
      .b_folder_container {
        flex: 1;
        position: relative;
      }
    }
    .mid_content {
      flex: 1;
      position: relative;
    }
    .rt_content {
      width: 15%;
      max-width: 280px;
    }
  }
  .page_bot {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 56px;
    padding: 0 25px;
    background-color: #282828;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .lt {
      user-select: none;
    }
    .mid {
      font-size: 22px;
      .scale_num {
        margin: 0 10px;
        user-select: none;
      }
    }
    .rt {
      .ant-btn {
        margin-left: 15px;
      }
      .btn_back {
        background-color: #5daf34;
        border-color: #5daf34;
      }
    }
  }
}
</style>
