<template>
  <div class="folder_pics_contianer">
    <div v-if="data.length > 0" class="controls">
      <div class="btn confidence" @click="changeOrder('confidence')">
        <span>{{ $t('dic.confidence') }}</span>
        <a-icon class="up" :class="{'current': order === 0}" type="swap-right" />
        <a-icon class="down" :class="{'current': order === 1}" type="swap-right" />
      </div>
      <!-- <div class="btn distance" @click="changeOrder('distance')">
        <span>{{ $t('dic.distance') }}</span>
        <a-icon class="up" :class="{'current': order === 2}" type="swap-right" />
        <a-icon class="down" :class="{'current': order === 3}" type="swap-right" />
      </div> -->
    </div>
    <div class="pics_content">
      <LazyPics :col="rowNum" :data="data">
        <template slot-scope="item">
          <div class="my_item" @mousemove="handleEnter($event, item.data)" @mouseleave="showImgUrl = null">
            <div
              class="rect_border"
              :class="{
                'bordered': currentItems.findIndex(it => it.pic_id === item.data.pic_id) !== -1
              }"
              @click="itemClick($event, item.data)"
            >
              <div class="img_box">
                <img
                  v-lazy="lang === 'CN' ? item.data.box_url : item.data.box_url_en"
                  class="prew_img"
                  @dragstart="onDragstart($event, item.data)"
                >
              </div>
            </div>
            <h5 class="name">{{ item.data.name.split('_')[0] + '-' + parseFloat(item.data.confidence) }}</h5>
          </div>
        </template>
      </LazyPics>
      <div v-if="data.length === 0" class="data_empty">
        <a-empty />
      </div>
    </div>
    <transition name="loading-fade">
      <div v-show="showImgUrl" class="show_hover_img" :style="{left: img_left + 'px', top: img_top + 'px'}">
        <div class="img">
          <img :src="showImgUrl" alt="">
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import LazyPics from './lazy-pics.vue'
import { getAllPicList } from '@/services/work'
import AES from '@/utils/crypto-tool.js'
export default {
  name: 'FolderPics',
  components: { LazyPics },
  props: {
    currentItems: { // 选中的图片集合
      type: Array,
      default: () => {
        return []
      }
    },
    dirId: {
      type: Number,
      default: null
    },
    rowNum: { // 一行几张
      type: Number,
      default: () => {
        return 5
      }
    }
  },
  data() {
    return {
      showImgUrl: null, // 悬浮框图片
      img_top: 0, // 悬浮框图片坐标y
      img_left: 0, // 悬浮框图片坐标x
      data: [],
      order: 0 // 排序 0置信度倒叙 1置信度正序 2距离倒叙 3距离正序
    }
  },
  computed: {
    ...mapState('setting', ['lang']),
    pic_w() {
      const rate = Math.floor((100 / this.rowNum) * 100) / 100
      return {
        width: rate + '%'
      }
    }
  },
  watch: {
    dirId(val) {
      if (val) {
        this.getData()
      } else {
        this.data = []
      }
    }
  },
  methods: {
    itemClick(e, item) {
      const img_url = this.lang === 'CN' ? item.box_url : item.box_url_en
      const _has = this.currentItems.findIndex(it => it.pic_id === item.pic_id)
      if (_has !== -1) {
        this.$emit('onChangeCurrentPics', [])
        return
      }
      this.$emit('onChangeBigPic', img_url)
      this.$emit('onChangeCurrentPics', [item])
    },
    // 图片排序
    changeOrder(type) {
      switch (type) {
        case 'confidence': // 置信度
          this.order = this.order === 0 ? 1 : 0
          break
        case 'distance':
          this.order = this.order === 2 ? 3 : 2
          break
        default:
          break
      }
      this.getData()
    },
    // 获取图片数据
    getData() {
      this.showImgUrl = null
      this.$emit('pageLoading', true)
      const param = {
        dir_id: this.dirId,
        order_method: this.order
      }
      getAllPicList(param).then(res => {
        this.$emit('pageLoading', false)
        if (res.errcode === 0) {
          const result = JSON.parse(AES.decrypt(res.data))
          this.data = result.data
          this.$emit('midPicTotal', result.total)
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    onDragstart(e, item) { // 拖动开始
      this.showImgUrl = null
      const url = this.lang === 'CN' ? item.box_url : item.box_url_en
      e.dataTransfer.setData('text', url)
    },
    // 鼠标进入item,展示大图
    handleEnter(e, item) {
      this.showImgUrl = item.box_url
      const dom = document.body
      const _h = document.getElementsByClassName('show_hover_img')[0].offsetHeight
      if (_h + e.clientY > dom.offsetHeight) {
        this.img_top = dom.offsetHeight - _h
      } else {
        this.img_top = e.clientY
      }
      this.img_left = e.clientX - 350
    }
  }
}
</script>

<style lang="less" scoped>
.folder_pics_contianer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  .pics_content {
    width: 100%;
    height: 100%;
  }
  .controls {
    position: absolute;
    right: 25px;
    top: 10px;
    z-index: 100;
    background-color: rgba(0,0,0,.5);
    color: #ffffff;
    border-radius: 20px;
    padding: 5px 15px;
    display: flex;
    .btn {
      font-size: 12px;
      color: #f4f4f4;
      cursor: pointer;
      &:hover {
        color: #ffffff;
      }
      .current {
        color: #ED5C36;
      }
      .up {
        transform: rotate(-90deg);
        margin-right: -3px;
      }
      .down {
        margin-left: -3px;
        transform: rotate(90deg);
      }
    }
    .distance {
      margin-left: 5px;
    }
  }
  .my_item {
    width: calc(~"100% - 20px");
    margin: 10px auto 0;
    text-align: center;
    .rect_border {
      width: 100%;
      height: 0;
      padding-top: 100%;
      position: relative;
      border: 4px solid transparent;
      &.bordered {
        border-color:#4a90e2;
      }
      .img_box {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
    }
    h5 {
      display: block;
      margin: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .data_empty {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
  }
  .show_hover_img {
    position: fixed;
    background-color: #ffffff;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    z-index: 2000;
    width: 250px;
    .img {
      width: 100%;
      height: 250px;
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
    .dec {
      font-size: 12px;
      padding: 5px;
      color: #096dd9;
    }
  }
}
.prew_img[lazy=loading]{
  background: url('../../../assets/img/loading.svg') center no-repeat;
}
.prew_img[lazy=error]{
  background: url('../../../assets/img/error.svg') center no-repeat;
}
</style>
