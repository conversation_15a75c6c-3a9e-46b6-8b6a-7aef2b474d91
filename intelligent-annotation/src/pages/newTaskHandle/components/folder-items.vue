<template>
  <div class="folder_items_container">
    <div class="lists">
      <RecycleScroller
        id="folder_scroll"
        v-infinite-scroll="handleInfiniteOnLoad"
        style="height: 100%"
        :items="lists"
        :item-size="76"
        key-field="dir_id"
        :infinite-scroll-disabled="false"
        :infinite-scroll-distance="10"
      >
        <template #before>
          <a-alert v-if="showStart" :message="$t('contents.tips')" type="warning" show-icon />
        </template>
        <template v-slot="{ item }">
          <div :class="{'item': true, 'current': currentId === item.dir_id }" @click="itemClick(item)">
            <div class="left">
              <a-avatar
                :size="55"
                shape="square"
                icon="picture"
                :src="item.sku_logo"
                @mouseenter="handleEnter"
                @mouseleave="handleLeave"
              />
            </div>
            <div class="mid">
              <div class="mid_title_cnt">
                <div class="title" :title="item.name">{{ item.name }}</div>
                <div class="state">
                  <div v-if="item.state === 1" class="my_tag green">
                    {{ flowType === 8 ? $t('dic.register') : $t('dic.review') }}<span v-if="item.tmp_sku_number">&minus;{{ item.tmp_sku_number }}</span>
                  </div>
                  <div v-if="item.state === 2" class="my_tag red">
                    {{ flowType === 8 ? $t('dic.registered') : $t('dic.reviewed') }}<span v-if="item.tmp_sku_number">&minus;{{ item.tmp_sku_number }}</span>
                  </div>
                  <div v-if="item.state !== 1 && item.operator" class="my_tag green">{{ item.operator }}</div>
                  <template v-if="item.state !== 1 && item.grade">
                    <level-icon :grade="item.grade" style="transform: scale(0.75);transformOrigin: 0 50%;" />
                  </template>
                  <div class="pic_num">{{ $t('contents.picNum') }}: {{ item.number }}</div>
                </div>
              </div>
            </div>
          </div>
        </template>

        <template #after>
          <!-- <div v-if="showEnd" class="load_end">{{ $t('contents.end') }}</div> -->
        </template>
      </RecycleScroller>
      <a-spin v-if="loading" class="loading" />
    </div>
    <transition name="loading-fade">
      <div v-show="hoverImgSrc" class="left_hover_img">
        <img :src="hoverImgSrc" alt="">
      </div>
    </transition>
  </div>
</template>

<script>
import infiniteScroll from 'vue-infinite-scroll'
import { RecycleScroller } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
import LevelIcon from '@/components/levelIcon'
export default {
  name: 'FolderItems',
  directives: { infiniteScroll },
  components: {
    RecycleScroller,
    LevelIcon
  },
  props: {
    lists: {
      type: Array,
      default: () => {
        return []
      }
    },
    currentId: {
      type: Number,
      default: null
    },
    loading: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    showStart: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    showEnd: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  data() {
    return {
      hoverImgSrc: null,
      flowType: null
    }
  },
  mounted() {
    this.flowType = parseInt(this.$route.query.task_flow_type)
  },
  methods: {
    handleInfiniteOnLoad() {
      if (this.showEnd) return
      this.$emit('onScrollEnd')
    },
    // 文件夹点击事件
    itemClick(item) {
      this.$emit('onFolderClick', item)
    },
    // 鼠标移入
    handleEnter(e) {
      if (e.target.lastChild.src) {
        const domBody = document.body
        const dom = document.getElementsByClassName('left_hover_img')[0]
        if (domBody.offsetHeight < e.y + 200) {
          dom.style.top = parseInt(domBody.offsetHeight - 200) + 'px'
        } else {
          dom.style.top = e.y + 'px'
        }
        dom.style.left = parseInt(e.x + 100) + 'px'
        this.hoverImgSrc = e.target.lastChild.src
      }
    },
    // 鼠标移出
    handleLeave() {
      this.hoverImgSrc = ''
    },
    // 返回顶部
    scrollToTop(index) {
      const scrollDom = document.getElementById('folder_scroll')
      if (scrollDom) {
        if (index) {
          scrollDom.scrollTop = 76 * index
          return
        }
        scrollDom.scrollTop = 0
      }
    }
  }
}
</script>

<style lang="less" scoped>
.folder_items_container {
  width: 100%;
  height: 100%;
  .lists {
    width: 100%;
    height: 100%;
    position: relative;
    .item {
      display: flex;
      height: 76px;
      overflow: hidden;
      padding: 10px 15px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      &:hover {
        background-color: #f2f2f2;
        .mid {
          color: #333333;
        }
      }
      &.current{
        background-color: #fff3a9;
        .mid {
          color: #333333;
        }
      }
      .left {
        margin-right: 15px;
      }
      .mid {
        flex: 1;
        font-size: 13px;
        position: relative;
        .mid_title_cnt {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
        }
        .title {
          width: 100%;
          height: 32px;
          line-height: 1.3;
          margin-bottom: 3px;
          word-break: break-all;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          font-weight: bold;
        }
        .state {
          display: flex;
          align-items: center;
          .pic_num {
            flex: 1;
            text-align: right;
          }
        }
      }
    }
  }
  .loading {
    position: absolute;
    left: 0;top: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255,255,255, .4);
  }
  .load_end {
    text-align: center;
    padding: 10px 0 25px;
    color: #c4c4c4;
    font-size: 0.9em;
  }
  .my_tag {
    border: 1px solid #ffffff;
    font-size: 12px;
    border-radius: 12px;
    padding: 0 10px;
    margin-right: 5px;
    &.green {
      border-color: #67c23a;
      color: #67c23a;
    }
    &.red {
      border-color: rgb(245, 108, 108);
      color: rgb(245, 108, 108);
    }
    &.yellow {
      border-color: rgb(207, 146, 54);
      color: rgb(207, 146, 54);
    }
  }
}
.left_hover_img {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 10;
  background-color: #ffffff;
  border-radius: 3px;
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  width: 200px;
  height: 200px;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
</style>
