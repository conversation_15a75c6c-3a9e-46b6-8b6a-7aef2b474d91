<template>
  <div class="task_folders_container">
    <div class="top_input_container">
      <a-input class="input_no_radius" size="large" allow-clear @change="changeSearchStr">
        <a-icon slot="prefix" type="search" />
        <template slot="suffix">
          <div class="my_input_after">
            <div class="icon_i letter" :class="{'active': order === 2 || order === 3}" @click="handleChangeOrder('letter')">
              <span class="title">字母</span>
              <div class="icon_box">
                <a-icon :class="{'current_icon': order === 2 }" class="icon" type="caret-up" />
                <a-icon :class="{'current_icon': order === 3 }" class="icon" type="caret-down" />
              </div>
            </div>
            <div class="icon_i number" :class="{'active': order === 0 || order === 1}" @click="handleChangeOrder('number')">
              <span class="title">数量</span>
              <div class="icon_box">
                <a-icon :class="{'current_icon': order === 1 }" class="icon" type="caret-up" />
                <a-icon :class="{'current_icon': order === 0 }" class="icon" type="caret-down" />
              </div>
            </div>
          </div>
        </template>
      </a-input>
    </div>
    <div class="foldes_container">
      <FolderItems
        ref="folderListCmp"
        :current-id="currentFolderId"
        :lists="data"
        :loading="loading"
        :show-end="dataEnd"
        @onScrollEnd="addNextPage"
        @onFolderClick="taskFolderClick"
      />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import FolderItems from './folder-items.vue'
import { getDirList } from '@/services/work'
export default {
  name: 'TaskFolders',
  components: { FolderItems },
  props: {
    currentFolderId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      order: 2, // 排序方式 2字母正序3字母倒叙 0数量倒叙1数量正序
      searchStr: '',
      loading: false, // 数据加载中状态
      dataEnd: false, // 数据加载完
      data: [], // 展示的数据
      page: 1,
      number: 100
    }
  },
  computed: {
    ...mapGetters('account', ['user'])
  },
  methods: {
    changeSearchStr(e) { // 搜索框改变
      this.searchStr = e.target.value
      this.getDirListFun(true)
    },
    handleChangeOrder(type) { // order 排序改变
      switch (type) {
        case 'letter':
          this.order = this.order === 2 ? 3 : 2
          break
        case 'number':
          this.order = this.order === 0 ? 1 : 0
          break
        default:
          break
      }
      this.getDirListFun(true)
    },
    getDirListFun(first) {
      this.loading = true
      if (first) { this.page = 1 }
      const param = {
        dir_name: this.searchStr,
        task_flow_id: this.$route.query.task_flow_id,
        order: this.order,
        page: this.page,
        number: this.number
      }
      getDirList(param).then(res => {
        this.loading = false
        if (res.errcode === 0) {
          this.total = res.data.total
          const _list = res.data.data_list
          if (this.page === 1) {
            this.data = _list
            this.$nextTick(() => {
              this.$emit('getDirTotal', this.total)
              const cmp = this.$refs.folderListCmp
              if (cmp) {
                const folder_index = this.data.findIndex(it => it.operator_id === this.user.user_id && it.state === 2)
                if (folder_index !== -1) { // 如果有任务文件夹操作人是登陆人且状态值为2时，直接定位到对应位置
                  cmp.scrollToTop(folder_index)
                  this.taskFolderClick(this.data[folder_index])
                  return
                }
                cmp.scrollToTop()
              }
            })
          } else {
            this.data = this.data.concat(_list)
          }
          // 是否是最后一页
          this.dataEnd = this.data.length === this.total
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    addNextPage() { // 下一页
      if (this.data.length === 0) {
        this.getDirListFun(true)
        return
      }
      this.page += 1
      this.getDirListFun()
    },
    taskFolderClick(item) { // 任务文件夹点击
      this.$emit('folderItemClick', item)
    }
  }
}
</script>

<style lang="less" scoped>
.my_input_after {
  display: flex;
  .icon_i {
    display: flex;
    align-items: center;
    cursor: pointer;
    &:first-child {
      margin-right: 10px;
    }
    .title { margin-right: 2.5px; }
    .icon_box {
      display: flex;
      flex-direction: column;
      align-items: center;
      .icon:first-child {
        margin-bottom: -2.5px;
      }
      .icon:last-child {
        margin-top: -2.5px;
      }
    }
    &.letter.active {
      .title, .current_icon {
        color: #2ABCE6;
      }
    }
    &.number.active {
      .title, .current_icon {
        color: #ED5C36;
      }
    }
  }
}
.task_folders_container {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  padding-top: 40px;
  .top_input_container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
  }
  .foldes_container {
    width: 100%;
    height: 100%;
    border: 1px solid #f4f4f4;
  }
}
</style>
