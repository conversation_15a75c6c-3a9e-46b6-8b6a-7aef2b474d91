<template>
  <div class="sku_list">
    <div class="lists">
      <RecycleScroller
        style="height: 100%"
        :items="lists"
        :item-size="68"
        key-field="sku_id"
      >
        <template v-slot="{ item }">
          <div class="item">
            <div class="left">
              <a-avatar
                shape="square"
                :size="47"
                :style="{color: item.icon ? item.icon === 'delete' ? 'rgb(255, 52, 99)' : 'rgb(18, 150, 219)' : '#ffffff' }"
                :icon="item.icon ? item.icon : 'picture'"
                :src="lang === 'CN' ? item.master_url : item.master_url_en"
                @mouseenter="handleEnter"
                @mouseleave="handleLeave"
              />
            </div>
            <div class="mid">
              <div class="mid_title_cnt">
                <div v-search="{searchValue: searchStr, text: item.name}" class="title" :title="item.name">{{ item.name }}</div>
              </div>
            </div>
          </div>
        </template>

        <template #after>
          <!-- <div class="load_end">{{ $t('contents.end') }}</div> -->
        </template>
      </RecycleScroller>
      <a-spin v-if="loading" class="loading" />
    </div>
    <transition name="loading-fade">
      <div v-show="hoverImgSrc" id="right_hover_img" class="right_hover_img">
        <img :src="hoverImgSrc" alt="">
      </div>
    </transition>
  </div>
</template>

<script>
import { RecycleScroller } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
import { matchStrToHtml } from '@/utils/util'
import { mapState } from 'vuex'
export default {
  name: 'SkuItems',
  components: { RecycleScroller },
  directives: {
    search: {
      update: (el, binding) => {
        const { searchValue, text } = binding.value
        if (!searchValue) {
          el.innerHTML = text
          return
        }
        const sArr = searchValue.split(' ')
        let res = text
        sArr.forEach(item => {
          if (item) {
            res = matchStrToHtml(item, res)
          }
        })
        el.innerHTML = res
      }
    }
  },
  props: {
    lists: {
      type: Array,
      default: () => {
        return []
      }
    },
    loading: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    searchStr: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      hoverImgSrc: ''
    }
  },
  computed: {
    ...mapState('setting', ['lang'])
  },
  methods: {
    // 鼠标移入
    handleEnter(e) {
      if (e.target.lastChild.src) {
        const domBody = document.body
        const dom = document.getElementById('right_hover_img')
        if (domBody.offsetHeight < e.y + 556) {
          dom.style.top = parseInt(domBody.offsetHeight - 556) + 'px'
        } else {
          dom.style.top = e.y + 'px'
        }
        dom.style.left = parseInt(e.x - 550) + 'px'
        this.hoverImgSrc = e.target.lastChild.src
      }
    },
    // 鼠标移出
    handleLeave() {
      this.hoverImgSrc = ''
    }
  }
}
</script>

<style lang="less" scoped>
.sku_list {
  width: 100%;
  height: 100%;
  .lists {
    width: 100%;
    height: 100%;
    position: relative;
    .item {
      display: flex;
      height: 68px;
      overflow: hidden;
      padding: 10px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      &:hover {
        background-color: #f2f2f2;
        color: #000000;
      }
      .left {
        margin-right: 10px;
      }
      .mid {
        flex: 1;
        font-size: 12px;
        position: relative;
        .mid_title_cnt {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
        }
        .title {
          width: 100%;
          height: 100%;
          word-break: break-all;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          line-height: 1.3;
          font-weight: bold;
        }
      }
    }
  }
  .loading {
    position: absolute;
    left: 0;top: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255,255,255, .4);
  }
  .load_end {
    text-align: center;
    padding: 10px 0 25px;
    color: #c4c4c4;
    font-size: 0.9em;
  }
  .right_hover_img {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 10;
    background-color: #ffffff;
    border-radius: 3px;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    width: 500px;
    height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      max-width: 100%;
      max-height: 100%;
      min-width: 300px;
      min-height: 300px;
      object-fit: contain;
    }
  }
}
</style>
