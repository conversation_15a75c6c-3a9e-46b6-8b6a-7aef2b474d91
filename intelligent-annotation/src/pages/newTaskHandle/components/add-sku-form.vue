<template>
  <FormDrawer :title="title" :visible="openDraw" @closeDrawer="handleDrawerClose">
    <a-form :form="form" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item :label="$t('skuForm.name')" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
            <a-input
              v-decorator="[
                'tmp_sku_name',
                { rules: [
                  { required: true, message: $t('rules.notNull', {name: $t('skuForm.name')}) },
                  { trigger: 'change', validator: valSkuName },
                  { max: 200, message: $t('rules.maxLength', { number: '200' }) }
                ], validateFirst: true, initialValue: formData.tmp_sku_name ? formData.tmp_sku_name : '' },
              ]"
            />
          </a-form-item>
        </a-col>
        <a-divider style="margin: 20px 0;" />
        <a-col :span="12">
          <a-form-item :label="$t('table.skuIndustry')">
            <a-select
              v-decorator="[
                'sku_type_id',
                {
                  rules: [{required: true}], initialValue: formData.sku_type_id ? formData.sku_type_id : skuTypeId,
                },
              ]"
              show-search
              :filter-option="filterOption"
              :get-popup-container="triggerNode => triggerNode.parentNode"
              @select="handleTypeChange"
            >
              <a-select-option v-for="item in skuTypeList" :key="item.type_id" :value="item.type_id">{{ item.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('table.skuProject')">
            <a-select
              v-decorator="[
                'sku_project_id',
                {
                  rules: [{required: true}], initialValue: formData.sku_project_id ? formData.sku_project_id : skuProjectId,
                },
              ]"
              show-search
              :filter-option="filterOption"
              :get-popup-container="triggerNode => triggerNode.parentNode"
            >
              <a-select-option v-for="item in skuProjectList" :key="item.sku_project_id" :value="item.sku_project_id">{{ item.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('skuForm.sLanguage')">
            <a-radio-group
              v-decorator="['lang', { rules: [{ required: true }], initialValue: formData.lang ? formData.lang : lang }]"
              @change="changeLang"
            >
              <a-radio :value="0">
                {{ $t('dic.cn') }}
              </a-radio>
              <a-radio :value="1">
                {{ $t('dic.en') }}
              </a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('skuForm.dealer')">
            <InputSelect
              v-decorator="[
                'group_name',
                {
                  rules: [
                    { required: true, message: $t('rules.notNull', {name: $t('skuForm.dealer')}) },
                    { max: 100, message: $t('rules.maxLength', { number: '100' }) }
                  ], initialValue: formData.group_name ? formData.group_name : ''
                },
              ]"
              :lists="dealer_list"
              @onResult="handleGroupChange"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('skuForm.brand')">
            <InputSelect
              v-decorator="[
                'brand_name',
                {
                  rules: [
                    { required: true, message: $t('rules.notNull', {name: $t('skuForm.brand')}) },
                    { max: 100, message: $t('rules.maxLength', { number: '100' }) }
                  ], initialValue: formData.brand_name ? formData.brand_name : ''
                },
              ]"
              :lists="brand_list"
              @onResult="handleBrandChange"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('skuForm.brandChild')">
            <InputSelect
              v-decorator="[
                'subbrand_name',
                {
                  rules: [{ max: 100, message: $t('rules.maxLength', { number: '100' }) }], initialValue: formData.subbrand_name ? formData.subbrand_name : ''
                },
              ]"
              :show-check="true"
              :is-checked="is_p_brandChild === 'Y'"
              :lists="brand_child_list"
              @onResult="splicingName('subbrand_name')"
              @oncheckboxed="onPChange($event, 'brandChild')"
            />
          </a-form-item>
        </a-col>
        <!-- 口味 -->
        <a-col :span="12">
          <a-form-item :label="$t('skuForm.flavor')">
            <InputSelect
              v-decorator="[
                'flavor_name',
                { rules: [{ max: 100, message: $t('rules.maxLength', { number: '100' }) }], initialValue: formData.flavor_name ? formData.flavor_name : '' },
              ]"
              :show-check="true"
              :is-checked="is_p_flavor === 'Y'"
              :lists="flavor_list"
              @onResult="splicingName('flavor_name')"
              @oncheckboxed="onPChange($event, 'flavor')"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="$t('skuForm.category')">
            <InputSelect
              v-decorator="[
                'category_name',
                {
                  rules: [{ max: 100, message: $t('rules.maxLength', { number: '100' }) }], initialValue: formData.category_name ? formData.category_name : ''
                },
              ]"
              :lists="category_list"
            />
          </a-form-item>
        </a-col>
        <!-- 包装 -->
        <a-col :span="12">
          <a-form-item :label="$t('skuForm.packing')">
            <InputSelect
              v-decorator="[
                'shape_name',
                {
                  rules: [
                    { max: 100, message: $t('rules.maxLength', { number: '100' }) }
                  ], initialValue: formData.shape_name ? formData.shape_name : ''
                },
              ]"
              :lists="packing_list"
              @onResult="splicingName"
            />
          </a-form-item>
        </a-col>
        <!-- 规格 -->
        <a-col :span="12">
          <a-form-item :label="$t('skuForm.specs')">
            <InputSelect
              v-decorator="[
                'volume_name',
                {
                  rules: [
                    { max: 25, message: $t('rules.maxLength', { number: '25' }) }
                  ], initialValue: formData.volume_name ? formData.volume_name : ''
                },
              ]"
              :lists="specs_list"
              @onResult="splicingName"
            />
          </a-form-item>
        </a-col>
        <!-- 规格单位 -->
        <a-col :span="12">
          <a-form-item :label="$t('skuForm.specsUnit')">
            <a-select
              v-decorator="[
                'volume_number',
                {
                  rules: [], initialValue: formData.volume_number ? formData.volume_number : ''
                },
              ]"
              :get-popup-container="triggerNode => triggerNode.parentNode"
              show-search
              allow-clear
              @select="splicingName"
              @search="handleSearch"
              @blur="handleBlur"
              @change="handleChange"
            >
              <a-select-option v-for="it in specs_unit_list" :key="it.name" data-name="volume_number" :value="it.name">{{ it.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <!-- 销售单位 -->
        <a-col :span="12">
          <a-form-item :label="$t('skuForm.salesUnit')">
            <a-input-number
              v-decorator="[
                'number',
                { rules: [], initialValue: formData.number ? formData.number : 1 },
              ]"
              :min="1"
              :max="99999"
            />
          </a-form-item>
        </a-col>
        <!-- 外包装 -->
        <a-col :span="12">
          <a-form-item :label="$t('skuForm.outerPacking')">
            <InputSelect
              v-decorator="[
                'outer_shape_name',
                { rules: [{ max: 100, message: $t('rules.maxLength', { number: '100' }) }], initialValue: formData ? formData.outer_shape_name : ''}
              ]"
              :lists="outer_packing_list"
            />
          </a-form-item>
        </a-col>
        <!-- 额外字段 -->
        <a-col :span="12">
          <a-form-item :label="$t('skuForm.aFiles')">
            <a-input
              v-decorator="[
                'sku_name_flag',
                { rules: [{ max: 50, message: $t('rules.maxLength', { number: '50' }) }], initialValue: formData ? formData.sku_name_flag : '' },
              ]"
              @blur="splicingName"
            />
          </a-form-item>
        </a-col>
        <a-divider style="margin: 20px 0;" />
        <!-- 地区 -->
        <a-col :span="12">
          <a-form-item :label="$t('skuForm.sRegion')">
            <a-select
              v-decorator="[
                'country',
                {
                  rules: [{ required: true, message: $t('rules.notNull', {name: $t('skuForm.sRegion')}) }], initialValue: formData ? formData.country : ''
                },
              ]"
              show-search
              :filter-option="filterOption"
              :get-popup-container="triggerNode => triggerNode.parentNode"
            >
              <a-select-option v-for="it in region_list" :key="it.name" :value="it.name">{{ it.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item :label="$t('skuForm.packingPic')" required :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
            <div class="pics_container">
              <div class="control">
                <a-upload
                  accept=".jpg, .jpeg, .png"
                  :show-upload-list="false"
                  :before-upload="handleBeforeUpload"
                >
                  <a-button type="primary" :disabled="!working">{{ $t('skuForm.updatePic') }}</a-button>
                  <span class="extra">{{ $t('skuForm.updatePicMsg') }}</span>
                </a-upload>
              </div>
              <div class="lists">
                <div class="item">
                  <a-avatar shape="square" icon="picture" :size="105" :src="pic_url" />
                  <div v-if="pic_url" class="cover">
                    <div class="contorl">
                      <a-icon class="icon" type="eye" @click="showBigPic" />
                      <a-icon v-if="working" class="icon" type="delete" @click="delPic" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1,
      }"
    >
      <a-button :style="{ marginRight: '8px' }" @click="handleDrawerClose">
        {{ $t('buttons.cancel') }}
      </a-button>
      <!-- <a-button v-if="taskFlowType === '9' && formData.state === 0" type="danger" :style="{ marginRight: '8px' }" @click="onPass">
        {{ $t('buttons.pass') }}
      </a-button> -->
      <a-button type="primary" :disabled="!working" :loading="btnLoad" @click="onSubmit">
        {{ $t('buttons.define') }}
      </a-button>
    </div>
    <MoveModal :show="previewVisible" :title="$t('modal.bigPic')" @onClose="previewVisible = false">
      <div class="img_box">
        <img alt="example" :src="pic_url">
      </div>
    </MoveModal>
  </FormDrawer>
</template>

<script>
import FormDrawer from '@/components/formDrawer'
import InputSelect from '@/components/inputSelect'
import MoveModal from '@/components/moveModal'
import { apiCommonServer } from '@/services'
import { addTmpSku, uploadLogo, changeTmpSku, checkTmpSku } from '@/services/work'
function getBase64(img, callback) {
  const reader = new FileReader()
  reader.addEventListener('load', () => callback(reader.result))
  reader.readAsDataURL(img)
}
export default {
  name: 'AddSkuForm',
  components: { FormDrawer, InputSelect, MoveModal },
  props: {
    taskFolderId: { // 任务文件夹id
      type: Number,
      default: null
    },
    skuProjectList: { // sku项目下拉列表
      type: Array,
      default: () => {
        return []
      }
    },
    skuTypeList: { // sku行业下拉列表
      type: Array,
      default: () => {
        return []
      }
    },
    working: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  data() {
    return {
      title: this.$t('drawer.add', { name: 'SKU' }),
      form: this.$form.createForm(this),
      openDraw: false,
      selectSearchVal: '',
      taskFlowType: null, // 流程类型
      skuTypeId: null, // sku行业ID
      skuProjectId: null, // sku项目ID
      formData: {},
      lang: 0,
      pic_url: '',
      cacheFileUrl: null, // 要上传的图片网络路径
      cacheFile: null, // 上传的本地file
      btnLoad: false, // 提交按钮提交中状态
      dealer_list: [], // 品牌商
      brand_list: [], // 品牌
      brand_child_list: [], // 子品牌
      flavor_list: [], // 口味
      category_list: [], // 品类
      packing_list: [], // '包装'
      specs_list: [], // '规格
      specs_unit_list: [{ name: 'ml' }, { name: 'g' }, { name: 'fl oz' }, { name: 'oz' }], // 规格单位
      outer_packing_list: [], // '外包装',
      region_list: [], // '选择地区',
      previewVisible: false, // 是否展示预览大图
      logo_pic: null, // 新上传的图片 logo_key logo_url
      name_relation_field: ['brand_name', 'subbrand_name', 'flavor_name', 'shape_name', 'volume_name', 'volume_number', 'sku_name_flag'], // 产品名称联动字段
      is_p_flavor: 'Y',
      is_p_brandChild: 'Y'
    }
  },
  watch: {
    formData(val) {
      this.title = this.$t('drawer.edit', { name: 'SKU' })
      if (!val.tmp_sku_id) {
        this.title = this.$t('drawer.add', { name: 'SKU' })
        return
      }
      if (val.lang !== this.lang || val.sku_type_id !== this.skuTypeId) { // 语言不相同
        this.lang = val.lang
        if (val.sku_type_id) {
          this.skuTypeId = val.sku_type_id
        }
        this.getAlldis()
      }
      if (val.group_id) { // 有品牌商ID,查询品牌集
        this.getSelectList(1, val.group_id)
      } else {
        this.brand_list = []
      }
      if (val.brand_id) { // 有品牌ID,查询子品牌(系列)集
        this.getSelectList(2, null, val.brand_id)
      } else {
        this.brand_child_list = []
      }
      if (val.logo_url) {
        this.pic_url = val.logo_url
      }
    },
    openDraw(val) {
      if (val) {
        const flavor_p = localStorage.getItem('flavor_p')
        const brandChild_p = localStorage.getItem('brandChild_p')
        this.is_p_flavor = flavor_p || 'Y'
        this.is_p_brandChild = brandChild_p || 'Y'
      }
    }
  },
  mounted() {
    const query = this.$route.query
    this.taskFlowType = query.task_flow_type
    this.skuTypeId = parseFloat(query.sku_type_id)
    this.skuProjectId = parseFloat(query.sku_project_id)
    this.getAlldis()
  },
  methods: {
    valSkuName(rule, value, callback) {
      // const r = /^[\u4E00-\u9FA5A-Za-z0-9_+%]*[\u4E00-\u9FA5A-Za-z0-9_+%\s]*[\u4E00-\u9FA5A-Za-z0-9_+%]$/
      // if (!r.test(value)) {
      //   callback(this.$t('rules.skuReg'))
      // }
      const r2 = /\s{2,}/g
      if (r2.test(value)) {
        callback(this.$t('rules.has2Space'))
      }
      // const r3 = new RegExp('[\\u4E00-\\u9FFF]+', 'g')
      // if (this.lang === 1 && r3.test(value)) {
      //   callback(this.$t('rules.noChinese'))
      // }
      callback()
    },
    // 子品牌/口味是否自动拼接至文件名
    onPChange(res, type) {
      const value = res ? 'Y' : 'N'
      switch (type) {
        case 'flavor': // 口味 category
          this.is_p_flavor = value
          localStorage.setItem('flavor_p', value)
          break
        case 'brandChild': // 子品牌
          this.is_p_brandChild = value
          localStorage.setItem('brandChild_p', value)
          break
        default:
          break
      }
      this.splicingName()
    },
    // 行业和项目目录搜索
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    },
    handleTypeChange(value) { // sku行业改变
      this.skuTypeId = value
      this.changAboutFileNull()
      this.getAlldis()
    },
    changeLang(e) { // 语言切换
      this.lang = e.target.value
      this.changAboutFileNull()
      this.getAlldis()
    },
    handleGroupChange(name) { // 品牌商改变
      const item = this.dealer_list.find(it => it.name === name)
      if (item) {
        this.getSelectList(1, item.id)
      }
      this.form.setFieldsValue({ brand_name: '' })
      this.form.setFieldsValue({ subbrand_name: '' })
      this.splicingName()
    },
    handleBrandChange(name) { // 品牌改变
      const item = this.brand_list.find(it => it.name === name)
      if (item) {
        this.getSelectList(2, null, item.id)
      }
      this.form.setFieldsValue({ subbrand_name: '' })
      this.splicingName()
    },
    splicingName(name) { // 拼接产品名称 品牌+子品牌+口味+包装+规格+规格单位+额外字段
      if (name && name === 'subbrand_name' && this.is_p_brandChild === 'N') {
        return
      }
      if (name && name === 'flavor_name' && this.is_p_flavor === 'N') {
        return
      }
      const obj = this.form.getFieldsValue()
      const p = obj.brand_name || ''
      const z = this.is_p_brandChild === 'Y' && obj.subbrand_name ? obj.subbrand_name : ''
      const k = this.is_p_flavor === 'Y' && obj.flavor_name ? obj.flavor_name : ''
      const b = obj.shape_name || ''
      const g = obj.volume_name || ''
      const gd = obj.volume_number || ''
      const e = obj.sku_name_flag || ''
      // 中文 品牌+子品牌+口味+包装+规格+毫升+额外字段
      // 英文 品牌+子品牌+口味+包装+规格+毫升+额外字段 每个字段添加空格
      if (obj.lang === 0) {
        const str = `${p}${z}${k}${b}${g}${gd}${e}`
        this.form.setFieldsValue({ tmp_sku_name: str })
      } else {
        const strs = []
        if (p) { strs.push(p) }
        if (z) { strs.push(z) }
        if (k) { strs.push(k) }
        if (b) { strs.push(b) }
        if (g) { strs.push(g) }
        if (gd) { strs.push(gd) }
        if (e) { strs.push(e) }
        const str = strs.length > 0 ? strs.join(' ') : ''
        this.form.setFieldsValue({ tmp_sku_name: str })
      }
    },
    changAboutFileNull() { // 置空和行业，语言所关联的下拉值
      this.form.setFieldsValue({
        tmp_sku_name: '', // 名称
        group_name: '', // 品牌商
        brand_name: '', // 品牌
        subbrand_name: '', // 子品牌
        flavor_name: '', // 口味
        category_name: '', // 品类
        shape_name: '', // 包装
        volume_name: '', // 规格
        outer_shape_name: '', // 外包装
        country: '' // 国家
      })
    },
    handleBeforeUpload(file) { // 附件上传
      const isLt2M = file.size / 1024 / 1024 < 3
      if (!isLt2M) {
        this.$message.error(this.$t('skuForm.updatePicMsg'))
        return false
      }
      this.cacheFileUrl = null
      this.cacheFile = file
      getBase64(file, imageUrl => {
        this.pic_url = imageUrl
      })
      return false
    },
    showBigPic() { // 展示大图
      if (this.pic_url) {
        this.previewVisible = true
      }
    },
    delPic() { // 图片删除
      this.cacheFile = null
      this.cacheFileUrl = null
      this.pic_url = ''
      this.previewVisible = false
    },
    handleDrawerClose() { // 关闭侧拉
      this.openDraw = false
      this.formData = {}
      this.cacheFile = null
      this.cacheFileUrl = null
      this.pic_url = ''
      this.previewVisible = false
    },
    onSubmit() { // 提交
      this.form.validateFields((err, values) => {
        if (!err) {
          this.btnLoad = true
          const _values = Object.assign(this.formData, values)
          if (this.cacheFileUrl || this.cacheFile) { // 有新图片上传
            const param = new FormData()
            if (this.cacheFile) {
              param.append('image', this.cacheFile)
            }
            if (this.cacheFileUrl) {
              param.append('logo_url', this.cacheFileUrl)
            }
            if (this.formData.tmp_sku_id) {
              param.append('tmp_sku_id', this.formData.tmp_sku_id)
            }
            uploadLogo(param).then(res => {
              if (res.errcode === 0) { // logo_key logo_url
                const param = {
                  ..._values,
                  dir_id: this.taskFolderId,
                  logo_key: res.data.logo_key,
                  logo_url: res.data.logo_url
                }
                if (this.formData.tmp_sku_id) {
                  param['tmp_sku_id'] = this.formData.tmp_sku_id
                  this.changeTmpSkuFun(param)
                } else {
                  this.addTmpSkuFun(param)
                }
              } else {
                this.btnLoad = false
                this.$message.error(res.errmsg)
              }
            })
          } else { // 没有新图片
            const param = { ..._values, dir_id: this.taskFolderId }
            if (this.formData.tmp_sku_id) {
              param['tmp_sku_id'] = this.formData.tmp_sku_id
              this.changeTmpSkuFun(param)
            } else {
              this.$message.error(this.$t('rules.skuPic'))
              this.btnLoad = false
            }
          }
        }
      })
    },
    addTmpSkuFun(param) { // 新增
      const group = this.dealer_list.find(it => it.name === param.group_name)
      param['group_id'] = group && group.id ? group.id : ''
      const brand = this.brand_list.find(it => it.name === param.brand_name)
      param['brand_id'] = brand && brand.id ? brand.id : ''
      addTmpSku(param).then(res => {
        this.btnLoad = false
        if (res.errcode === 0) {
          this.$message.success(res.errmsg)
          this.$emit('onSuccess')
          this.handleDrawerClose()
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    changeTmpSkuFun(param) { // 修改
      const group = this.dealer_list.find(it => it.name === param.group_name)
      param['group_id'] = group && group.id ? group.id : ''
      const brand = this.brand_list.find(it => it.name === param.brand_name)
      param['brand_id'] = brand && brand.id ? brand.id : ''
      changeTmpSku(param).then(res => {
        this.btnLoad = false
        if (res.errcode === 0) {
          this.$message.success(res.errmsg)
          this.$emit('onSuccess')
          this.handleDrawerClose()
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    onPass() { // 通过
      if (this.formData.tmp_sku_id) {
        checkTmpSku(this.formData.tmp_sku_id).then(res => {
          if (res.errcode === 0) {
            this.$message.success(res.errmsg)
            this.$emit('onSuccess')
            this.handleDrawerClose()
          } else {
            this.$message.error(res.errmsg)
          }
        })
      }
    },
    handleSearch(value) {
      this.handleChange(value)
    },

    handleChange(value) {
      const result = (value != null && value !== '') ? value.trim() : undefined
      this.form.setFieldsValue({ volume_number: result })
      this.splicingName()
    },

    handleBlur(value) {
      this.form.setFieldsValue({ volume_number: value.trim() })
      this.splicingName()
    },
    async getAlldis() {
      await this.getSelectList(0)
      await this.getSelectList(3)
      await this.getSelectList(4)
      await this.getSelectList(5)
      await this.getSelectList(6)
      await this.getSelectList(7)
      await this.getSelectList(8)
    },
    async getSelectList(id, group_id, brand_id) {
      const param = {
        parameter_id: id, // (0,品牌商)(1,品牌)(2,系列(子品牌))(3,口味)(4,品类)(5,包装)(6,规格)(7,外包装)(8,国家)
        type_id: this.skuTypeId, // 行业id
        lang: this.lang,
        group_id: group_id || null,
        brand_id: brand_id || null
      }
      await apiCommonServer.getSkuParameterData(param).then(res => {
        if (res.errcode === 0) {
          const datas = res.data.data_list
          switch (id) {
            case 0:
              this.dealer_list = datas
              break
            case 1:
              this.brand_list = datas
              break
            case 2:
              this.brand_child_list = datas
              break
            case 3:
              this.flavor_list = datas
              break
            case 4:
              this.category_list = datas
              break
            case 5:
              this.packing_list = datas
              break
            case 6:
              this.specs_list = datas
              break
            case 7:
              this.outer_packing_list = datas
              break
            case 8:
              this.region_list = datas
              break
            default:
              break
          }
        } else {
          this.$message.error(res.errmsg)
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.pics_container {
  .control {
    .extra {
      font-size: 12px;
      color: #999;
      margin-left: 15px;
    }
    margin-bottom: 15px;
  }
  .lists {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .item {
      padding: 8px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      position: relative;
      cursor: pointer;
      overflow: hidden;
      &:hover {
        .cover {
          display: block;
        }
      }
      .cover {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,.4);
        display: none;
        .contorl {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          .icon {
            color: hsla(0,0%,100%,.85);
            font-size: 18px;
            &:first-child {
              margin-right: 15px;
            }
            &:hover {
              color: #ffffff;
            }
          }
        }
      }
    }
  }
}
.img_box {
  width: 100%;
  height: 100%;
  text-align: center;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    user-select: none;
  }
}
</style>
