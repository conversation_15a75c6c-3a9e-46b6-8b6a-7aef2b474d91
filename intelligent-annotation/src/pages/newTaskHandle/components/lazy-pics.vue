<template>
  <div id="lazy_pics_conainer" class="lazy_pics_conainer" @scroll="onScroll">
    <div id="lazy_items_box" class="items_box" :style="{ 'minHeight': minH + 'px' }">
      <div
        v-for="item in showData"
        :key="item.pic_id"
        class="lazy_item"
        :style="{width: `${item.domW}px`, transform: `translate(${item.translateX}px,${item.translateY}px)` }"
      >
        <slot :data="item" />
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'LazyPics',
  props: {
    col: { // 几列
      type: Number,
      default: 5
    },
    data: { // 总数据
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      showData: [],
      minH: 0
    }
  },
  computed: {
    ...mapState('setting', ['lang'])
  },
  watch: {
    col(val) {
      this.countShowData()
      this.countMinHeight()
    },
    data() {
      this.countMinHeight()
      this.countShowData()
    }
  },
  mounted() {
    this.countMinHeight()
  },
  methods: {
    countMinHeight() {
      const len = this.data.length
      if (len === 0) {
        this.minH = 0
        return
      }
      const w = document.getElementById('lazy_items_box').offsetWidth
      const item_h = (w / this.col) + 33
      const row_num = len % this.col === 0 ? Math.floor(len / this.col) : Math.floor(len / this.col) + 1
      this.minH = item_h * row_num
      // console.log(`row_num=${row_num},minH=${this.minH}`)
    },
    getBaseParam() { // 基础参数
      const cntDom = document.getElementById('lazy_items_box')
      const viewDom = document.getElementById('lazy_pics_conainer')
      const param = {
        w: cntDom.offsetWidth, // 内容区宽
        t: viewDom.scrollTop, // 可视区滚动条距离
        h: viewDom.offsetHeight // 可视区高度
      }
      // 每项的width
      param['iw'] = param.w / this.col
      return param
    },
    countShowData() { // 计算要展示的数据集合 每项标题高 18，margin-bottom: 15
      const param = this.getBaseParam()
      const itemH = param.iw + 33
      const cntH = param.h
      const showRow = cntH % itemH === 0 ? Math.floor(cntH / itemH) : Math.floor(cntH / itemH) + 2 // 视图高度可展示的行数
      const startRow = param.t === 0 ? 0 : Math.floor(param.t / itemH)
      const startIdx = startRow * this.col
      const endIdx = startIdx + Math.floor(showRow * this.col)
      const res = this.data.filter((it, index) => index >= startIdx && index <= endIdx)
      // console.log(`startRow=${startRow},startIdx=${startIdx},endIdx=${endIdx},param.iw=${param.iw}`)
      let row_num = Math.floor(startRow)
      let col_num = 0
      this.showData = res.map((it, index) => {
        const item = {
          ...it,
          domW: param.iw,
          translateX: col_num * param.iw,
          translateY: row_num * itemH
        }
        if (index !== 0 && (index + 1) % this.col === 0) {
          col_num = 0
          row_num++
        } else {
          col_num++
        }
        return item
      })
    },
    onScroll() {
      this.countShowData()
    }
  }
}
</script>

<style lang="less" scoped>
.lazy_pics_conainer {
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  .items_box {
    position: relative;
  }
  .lazy_item {
    position: absolute;
    left: 0;
    top: 0;
  }
}
</style>
