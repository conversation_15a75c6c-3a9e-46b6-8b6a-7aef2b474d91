<template>
  <div class="result_container">
    <div class="select_sku_box">
      <a-select class="input_no_radius" style="width: 100%" :value="defaultSkuProject" show-search :filter-option="filterOption" @change="handleChange">
        <a-select-option v-for="item in skuProjectList" :key="item.sku_project_id" :value="item.sku_project_id">{{ item.name }}</a-select-option>
      </a-select>
      <a-input class="input_no_radius" allow-clear :value="skuSearchStr" @change="onChange" @focus="onFouce" @blur="onBlur" />
    </div>
    <div class="content">
      <div v-show="showAddSku" class="my_add_skus">
        <div v-for="it in copySkuTempList" :key="it.tmp_sku_id" class="item">
          <div class="mid_img" @click="skuTempClick(it)">
            <div class="del_btn">
              <a-popconfirm
                :disabled="!working"
                :title="$t('confirm.del')"
                @confirm="() => deleteTmpSkuFun(it)"
              >
                <a-button type="danger" :disabled="!working" icon="delete" size="small" @click.stop />
              </a-popconfirm>
            </div>
            <div v-if="it.state === 1" class="state">
              <a-icon type="check-circle" theme="filled" />
            </div>
            <img
              :src="it.logo_url"
              class="prew_img"
              @dragover.prevent
              @drop="onDrop($event, it)"
            >
          </div>
          <div class="bot">
            <div class="people">
              <span>{{ `${$t('dic.applicant')}: ${it.operator_name}` }}</span>
            </div>
            <div class="title">{{ it.tmp_sku_name }}</div>
          </div>
        </div>
      </div>
      <SkuItems
        v-show="!showAddSku"
        :lists="showSkuList"
        :loading="loadingSku"
        :search-str="skuSearchStr"
      />
    </div>
  </div>
</template>

<script>
import { apiCommonServer } from '@/services'
import { deleteTmpSku, uploadLogo } from '@/services/work'
import SkuItems from './sku-items.vue'
export default {
  name: 'RightResult',
  components: { SkuItems },
  props: {
    working: { // 是否已开始任务
      type: Boolean,
      default: () => {
        return false
      }
    },
    skuTempList: { // 临时SKU集合
      type: Array,
      default: () => {
        return []
      }
    },
    skuProjectList: { // sku行业集合
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      defaultSkuProject: '', // 初始化的sku行业
      skuSearchStr: '', // sku搜索的字符串
      skuList: [], // 行业下sku集合
      showSkuList: [], // 页面展示出的sku集合
      loadingSku: true, // sku加载状态
      newSkuItems: [], // 新增的sku集合
      showAddSku: true // 展示新增的sku
    }
  },
  computed: {
    copySkuTempList() {
      return JSON.parse(JSON.stringify(this.skuTempList))
    }
  },
  watch: {
    skuProjectList(val) {
      if (val && val.length > 0) {
        const localSkuProject = localStorage.getItem('mySetSkuProjectId')
        if (localSkuProject) {
          this.defaultSkuProject = Number(localSkuProject)
        } else {
          this.defaultSkuProject = val[0].sku_project_id
        }
        this.handleChange(this.defaultSkuProject)
      }
    }
  },
  methods: {
    filterOption(input, option) { // 行业目录搜索
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    },
    handleChange(value) { // sku行业下拉切换,并获取行业下sku
      this.loadingSku = true
      this.defaultSkuProject = value
      this.skuSearchStr = ''
      localStorage.setItem('mySetSkuProjectId', value)
      apiCommonServer.skuList(value).then(res => {
        this.loadingSku = false
        if (res.errcode === 0) {
          this.skuList = res.data.sku_list
          this.showSkuList = [...res.data.sku_list]
        } else {
          this.skuList = []
          this.showSkuList = []
          this.$message.error(res.errmsg)
        }
      })
    },
    onFouce() { // 搜索框获取焦点
      this.showAddSku = false
    },
    onBlur() { // 搜索框失焦
      this.showAddSku = true
    },
    onChange(e) { // 搜索框值改变
      this.skuSearchStr = e.target.value
      if (e && e.target.value) {
        const arr = this.skuSearchStr.split(' ')
        this.showSkuList = this.skuList.filter(item => this.inspectData(item.name, arr))
      } else {
        this.showSkuList = [...this.skuList]
      }
    },
    inspectData(str, arr) {
      if (arr.length === 1) {
        return str.toLowerCase().includes(arr[0].toLowerCase())
      }
      let hasStr = true
      for (let i = 0; i < arr.length; i++) {
        if (!str.toLowerCase().includes(arr[i].toLowerCase())) {
          hasStr = false
          break
        }
      }
      return hasStr
    },
    // 临时sku点击
    skuTempClick(item) {
      this.$emit('onSkuTemp', item)
    },
    deleteTmpSkuFun(item) {
      deleteTmpSku(item.tmp_sku_id).then(res => {
        if (res.errcode === 0) {
          this.$message.success(res.errmsg)
          this.$emit('onDeleteScuess')
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    onDrop(e, item) { // 拖拽放置
      if (!this.working) return
      const url = e.dataTransfer.getData('text')
      if (!url) return
      const _this = this
      const cnt_before = this.$t('confirm.editSure')
      const cnt_end = this.$t('confirm.packing')
      this.$confirm({
        title: this.$t('confirm.title'),
        content: (
          <div>
            <span>{cnt_before}</span>
            <span style='color: #ff0000;padding: 0 5px;'>{item.tmp_sku_name}</span>
            <span>{cnt_end}</span>
          </div>
        ),
        onOk() {
          return new Promise((resolve, reject) => {
            const param = new FormData()
            param.append('tmp_sku_id', item.tmp_sku_id)
            param.append('logo_url', url)
            uploadLogo(param).then(res => {
              if (res.errcode === 0) {
                _this.$message.success(res.errmsg)
                _this.$emit('onDeleteScuess')
                resolve()
              } else {
                reject()
                _this.$message.error(res.errmsg)
              }
            })
          }).catch(() => console.log('Oops errors!'))
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.result_container {
  width: 100%;
  height: 100%;
  position: relative;
  padding-top: 64px;
  .select_sku_box {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
  }
  .content {
    width: 100%;
    height: 100%;
  }
  .my_add_skus {
    height: 100%;
    overflow: auto;
    .item {
      padding: 10px 15px 5px;
      // box-shadow: 0 2px 10px 0 rgb(0 0 0 / 10%);
      border-bottom: 1px solid #d9d9d9;
      cursor: pointer;
      &:hover {
        border-bottom-color: #67c23a;
        .title {
          color: #67c23a
        }
      }
      .mid_img {
        position: relative;
        text-align: center;
        .del_btn {
          position: absolute;
          right: 0;
          top: 0;
        }
        .state {
          position: absolute;
          left: 0;
          top: 0;
          color: #3a51ff;
        }
        .prew_img {
          width: 100%;
          height: 100%;
          max-height: 135px;
          object-fit: contain;
        }
      }
      .bot {
        text-align: center;
        .people {
          padding: 10px 0 5px;
        }
      }
    }
  }
}
.prew_img[lazy=loading]{
  background: url('../../../assets/img/loading.svg') center no-repeat;
}
.prew_img[lazy=error]{
  background: url('../../../assets/img/error.svg') center no-repeat;
}
</style>
