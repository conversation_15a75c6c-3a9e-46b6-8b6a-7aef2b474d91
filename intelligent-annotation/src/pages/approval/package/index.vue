<template>
  <a-card>
    <div style="margin-bottom: 20px">
      <a-form-model
        layout="inline"
        :model="formInline"
        @submit="handleSubmit(true)"
        @submit.native.prevent
      >
        <a-form-model-item :label="$t('label.skuId')">
          <a-input v-model="formInline.sku_id" />
        </a-form-model-item>
        <a-form-model-item :label="$t('label.sku_name')">
          <a-input v-model="formInline.sku_name" />
        </a-form-model-item>
        <a-form-model-item :label="$t('label.audit_result')">
          <a-select style="width: 150px" allow-clear @change="handleAuditStateChange">
            <a-select-option
              v-for="it in auditStateList"
              :key="it.value"
              :value="it.value"
            >
              {{ it.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item :label="$t('table.skuProject')">
          <a-select
            v-model="formInline.sku_project_id"
            allow-clear
            show-search
            style="width: 150px"
            :filter-option="filterOption"
            :get-popup-container="triggerNode => triggerNode.parentNode"
          >
            <a-select-option v-for="item in skuProjectList" :key="item.sku_project_id" :value="item.sku_project_id">{{ item.name }}</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item :label="$t('table.oper_time')">
          <a-range-picker
            format="YYYY-MM-DD"
            :default-picker-value="[moment().subtract(1, 'months'), moment()]"
            @change="onTimeChange"
            @ok="handleSubmit(true)"
          />
        </a-form-model-item>
        <a-form-model-item>
          <a-button type="primary" html-type="submit">
            {{ $t("buttons.search") }}
          </a-button>
        </a-form-model-item>
      </a-form-model>
    </div>
    <a-table
      class="my_scroll_table"
      :scroll="{ x: true }"
      :columns="columns"
      :data-source="data"
      :loading="loading"
      bordered
      :row-key="(record, index) => index"
      :pagination="paginations"
      @change="handleTableChange"
    >
      <template slot="img" slot-scope="text">
        <div
          class="hove_img_box"
          @mouseenter="showHoverImg($event, text)"
          @mouseleave="hideHoverImg"
          @click="showBigImg(text)"
        >
          <a-avatar :size="25" shape="square" icon="picture" :src="text" />
        </div>
      </template>
      <template slot="imgBig" slot-scope="text, record">
        <div
          class="hove_img_box"
          @mouseenter="showHoverImg($event, subUrl(record))"
          @mouseleave="hideHoverImg"
          @click="showBigImg(subUrl(record))"
        >
          <a-avatar :size="25" shape="square" icon="picture" :src="subUrl(record)" />
        </div>
      </template>
      <span slot="action" slot-scope="text, record">
        <a @click.prevent="openPackageAuditEdit(record,'detail')">{{ $t('buttons.detail') }}</a>
        <a-divider type="vertical" />
        <a v-if="record.audit_state===0" @click.prevent="openPackageAuditEdit(record,'audit')">{{ $t('buttons.audit') }}</a>
      </span>
    </a-table>
    <div v-show="hoverImg" class="hove_img" :style="{ top: hoverImgY + 'px', left: hoverImgX + 'px' }">
      <img alt="example" :src="hoverImgUrl">
    </div>
    <a-modal :visible="previewVisible" :footer="null" @cancel="closeBigImg">
      <img alt="example" style="width: 100%;max-height: 500px;object-fit: contain;" :src="previewImage">
    </a-modal>
    <package-audit-form ref="packageAudit" @onSuccess="handleSubmit" />
  </a-card>
</template>

<script>
import moment from 'moment'
import { mapState } from 'vuex'
import { getDicts } from '@/services/sys'
import { getPackageList } from '@/services/work'
import PackageAuditForm from '@/pages/approval/package/components/package-audit'
import { apiCommonServer } from '@/services'
export default {
  name: 'PackageAudit',
  components: { PackageAuditForm },
  data() {
    return {
      formInline: {
        sku_project_id: '',
        sku_id: '',
        sku_name: '',
        audit_state: null,
        start_time: '',
        end_time: ''
      },
      data: [],
      loading: false,
      paginations: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30', '50'],
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => {
          return this.$t('table.total') + total + this.$t('table.strip')
        },
        total: 0
      },
      previewVisible: false,
      previewImage: '',
      hoverImg: false,
      hoverImgUrl: '',
      hoverImgX: 0,
      hoverImgY: 0,
      dictList: [],
      skuProjectList: []
    }
  },
  computed: {
    ...mapState('setting', ['lang']),
    auditStateList() {
      const data = [
        { name: this.$t('dic.audit_to_audit'), value: 0 },
        { name: this.$t('dic.audit_pass'), value: 1 },
        { name: this.$t('dic.audit_unpassed'), value: 2 }
      ]
      return data
    },
    columns() {
      const data = [
        { title: this.$t('label.id'), dataIndex: 'id', width: 85, align: 'center' },
        { title: this.$t('table.pic'), dataIndex: 'before_url', align: 'center', scopedSlots: { customRender: 'img' }},
        { title: this.$t('table.sku_name'), dataIndex: 'sku_name', align: 'center' },
        { title: this.$t('table.skuProject'), dataIndex: 'sku_project_name', align: 'center' },
        { title: this.$t('table.oper_people'), dataIndex: 'operator', align: 'center' },
        { title: this.$t('table.teamName'), dataIndex: 'team', align: 'center' },
        { title: this.$t('table.audit_state'), dataIndex: 'audit_state_text', align: 'center' },
        { title: this.$t('table.audit_user'), dataIndex: 'audit_operator', align: 'center' },
        { title: this.$t('table.audit_time'), dataIndex: 'audit_time', align: 'center' },
        { title: this.$t('table.creatTime'), dataIndex: 'created_at', align: 'center' },
        { title: this.$t('table.updateTime'), dataIndex: 'updated_at', align: 'center' },
        {
          title: this.$t('table.action'),
          key: 'action',
          fixed: 'right',
          align: 'center',
          width: 225,
          scopedSlots: { customRender: 'action' }
        }
      ]
      return data
    }
  },
  mounted() {
    this.getDictFun()
    this.getMList()
  },
  methods: {
    moment,
    // 返回大图url
    subUrl(item) {
      const url = this.lang === 'CN' ? item.box_url : item.box_url_en
      if (url) {
        return url.slice(0, url.indexOf('?'))
      }
      return url
    },
    // 错误类型列表
    getDictFun() {
      getDicts({ dictionary_id: 1 }).then(res => {
        if (res.errcode === 0) {
          this.dictList = res.data.data_list
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 错误类型改变
    handleErrTypeChange(value) {
      this.formInline.sku_id = value
    },
    // 任务类型
    handleAuditStateChange(value) {
      this.formInline.audit_state = value
    },
    // 时间选择
    onTimeChange(date, dataStr) {
      this.formInline.start_time = dataStr[0]
      this.formInline.end_time = dataStr[1]
    },
    handleSubmit() {
      const param = {
        ...this.formInline,
        page: this.paginations.current,
        number: this.paginations.pageSize
      }

      this.loading = true
      getPackageList(param).then(res => {
        this.loading = false
        if (res.errcode === 0) {
          this.data = res.data.data_list
          this.paginations.total = res.data.total
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 分页改变
    handleTableChange(obj) {
      this.paginations.current = obj.current
      if (obj.showSizeChanger) {
        this.paginations.pageSize = obj.pageSize
      }
      this.handleSubmit()
    },
    showBigImg(url) {
      if (url) {
        this.previewImage = url
        this.previewVisible = true
      }
    },
    closeBigImg() {
      this.previewVisible = false
    },
    showHoverImg(e, url) {
      const obj = e.target.getBoundingClientRect()
      this.hoverImgX = obj.left - 250
      this.hoverImgY = obj.top - 225
      if (url) {
        this.hoverImgUrl = url
        this.hoverImg = true
      }
    },
    hideHoverImg() {
      this.hoverImg = false
    },
    // 配置任务
    openPackageAuditEdit(item, type = 'detail') {
      this.$refs.packageAudit.audit(item, type)
    },
    getMList() { // sku项目目录
      apiCommonServer.skuProjectList().then(res => {
        if (res.errcode === 0) {
          if (res.data.sku_project_list && res.data.sku_project_list.length > 0) {
            this.skuProjectList = res.data.sku_project_list
          }
        }
      })
    },
    filterOption(input, option) { // 行业目录搜索
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    }
  }
}
</script>

<style lang="less" scoped>
.hove_img_box {
  cursor: pointer;
}
.hove_img {
  position: fixed;
  left: 0;
  top: 0;
  width: 250px;
  height: 250px;
  background-color: #ffffff;
  z-index: 999;
  border-radius: 3px;
  border: 1px solid #ebeef5;
  -webkit-box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
</style>
