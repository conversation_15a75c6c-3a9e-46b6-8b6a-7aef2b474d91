<template>
  <FormDrawer :title="title" :visible="openDraw" @closeDrawer="handleDrawerClose">
    <a-form :form="form" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <a-row :gutter="16">

        <!-- 行业 -->
        <a-col :span="24">
          <a-form-item :label="$t('label.proName')">
            <a-select
              v-decorator="[
                'sku_project_id',
                {initialValue: formData ? formData.sku_project_id : ''
                },
              ]"
              disabled
            >
              <a-select-option :value="formData.sku_project_id">{{ formData.sku_project_name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item :label="$t('skuForm.packingPic')" required :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
            <div class="pics_container">
              <div class="control">
                <a-upload
                  :file-list="beforeFileList"
                  accept=".jpg, .jpeg, .png"
                  :multiple="false"
                  :show-upload-list="true"
                  list-type="picture-card"
                  :before-upload="(file, fileList)=>handleBeforeUpload(file, fileList, 'before')"
                  :disabled="!canEdit"
                  class="upload-list-inline"
                  :remove="(file)=>delPic(file,'before')"
                  @preview="handlePreview"
                >
                  <div v-if="beforeFileList.length<1">
                    <span class="extra">{{ $t('skuForm.updatePicMsg') }}</span>
                  </div>
                </a-upload>
                <a-upload
                  :file-list="afterFileList"
                  accept=".jpg, .jpeg, .png"
                  :multiple="false"
                  :show-upload-list="true"
                  list-type="picture-card"
                  :before-upload="(file, fileList)=>handleBeforeUpload(file, fileList, 'after')"
                  :disabled="!canEdit"
                  class="upload-list-inline"

                  :remove="(file)=>delPic(file,'after')"
                  @preview="handlePreview"
                >
                  <div v-if="afterFileList.length<1">
                    <span class="extra">{{ $t('skuForm.updatePicMsg') }}</span>
                  </div>
                </a-upload>
                <a-upload
                  :file-list="leftFileList"
                  accept=".jpg, .jpeg, .png"
                  :multiple="false"
                  :show-upload-list="true"
                  list-type="picture-card"
                  :before-upload="(file, fileList)=>handleBeforeUpload(file, fileList, 'left')"
                  :disabled="!canEdit"
                  class="upload-list-inline"

                  :remove="(file)=>delPic(file,'left')"
                  @preview="handlePreview"
                >
                  <div v-if="leftFileList.length<1">
                    <span class="extra">{{ $t('skuForm.updatePicMsg') }}</span>
                  </div>
                </a-upload>
                <a-upload
                  :file-list="rightFileList"
                  accept=".jpg, .jpeg, .png"
                  :multiple="false"
                  :show-upload-list="true"
                  list-type="picture-card"
                  :before-upload="(file, fileList)=>handleBeforeUpload(file, fileList, 'right')"
                  :disabled="!canEdit"
                  class="upload-list-inline"

                  :remove="(file)=>delPic(file,'right')"
                  :preview="handlePreview"
                >
                  <div v-if="rightFileList.length<1">
                    <span class="extra">{{ $t('skuForm.updatePicMsg') }}</span>
                  </div>
                </a-upload>
                <a-modal :visible="previewVisible" :title="previewTitle" :footer="null" @cancel="handleCancel">
                  <img alt="example" style="max-width: 50%;max-height: 50%" :src="previewImage">
                </a-modal>
              </div>
            </div>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item :label="$t('skuPackagesForm.masterPackage')">
            <a-switch
              v-decorator="[
                'is_master',
                {
                  valuePropName: 'checked',
                  initialValue: formData ? formData.is_master===1 : false
                }
              ]"
              :disabled="!canEdit"
            />
          </a-form-item>
        </a-col>

        <a-divider style="margin: 20px 0;" />
        <!-- 审核意见 -->
        <a-col :span="24">
          <a-form-item :label="$t('skuPackagesForm.audit_result')">
            <a-select
              v-decorator="[
                'audit_state',
                {
                  rules: [
                    { required: true, message: $t('rules.notNull', {name: $t('skuPackagesForm.audit_result')}) },
                    {validator:(rule, value, callback)=> {
                      if(value <=0){
                        callback($t('rules.notNull',{name: $t('skuPackagesForm.audit_result')}))
                      }
                      callback()
                    }},
                  ],
                  initialValue: formData ? formData.audit_state : null
                },
              ]"
              :disabled="!canEdit"
              :get-popup-container="triggerNode => triggerNode.parentNode"
            >
              <a-select-option v-for="it in audit_state_list" :key="it.value" :value="it.value">{{ it.name }}</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item :label="$t('skuPackagesForm.audit_remark')" required>
            <a-input
              v-decorator="[
                'audit_remark',
                { rules: [

                    { required: true, message: $t('rules.notNull', {name: $t('skuPackagesForm.audit_remark')}) },
                    { max: 100, message: $t('rules.maxLength', { number: '100' }) }
                  ],
                  initialValue: formData ? formData.audit_remark : ''}
              ]"
              :disabled="!canEdit"
            />
          </a-form-item>
        </a-col>
      </a-row>

    </a-form>
    <div
      :style="{
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1,
      }"
    >
      <a-button :style="{ marginRight: '8px' }" @click="handleDrawerClose">
        {{ $t('buttons.cancel') }}
      </a-button>
      <a-button v-if="canEdit" type="primary" :disabled="!working" :loading="btnLoad" @click="onSubmit">
        {{ $t('buttons.define') }}
      </a-button>
    </div>
    <MoveModal :show="previewVisible" :title="$t('modal.bigPic')" @onClose="previewVisible = false">
      <div class="img_box">
        <img alt="example" :src="pic_url">
      </div>
    </MoveModal>
  </FormDrawer>
</template>

<script>
import FormDrawer from '@/components/formDrawer'
import MoveModal from '@/components/moveModal'
import { uploadPackageLogo, auditSkuPackage } from '@/services/work'
import { mapGetters } from 'vuex'

function getBase64(img, callback) {
  const reader = new FileReader()
  reader.addEventListener('load', () => callback(reader.result))
  reader.readAsDataURL(img)
}

export default {
  name: 'PackageAuditForm',
  components: { FormDrawer, MoveModal },
  props: {
    working: {
      type: Boolean,
      default: () => {
        return true
      }
    }
  },
  data() {
    return {
      title: this.$t('drawer.addPackage'),
      form: this.$form.createForm(this),
      openDraw: false,
      type: 'edit',
      selectSearchVal: '',
      taskFlowType: null, // 流程类型
      skuTypeId: null, // sku行业ID
      skuProjectId: null, // sku项目ID
      formData: {},
      is_master: false,
      lang: 0,
      pic_url: '',
      fileList: new Map(),
      beforeFileList: [],
      afterFileList: [],
      leftFileList: [],
      rightFileList: [],
      audit_state_list: [
        { name: '请选择审核意见', value: 0 },
        { name: '通过', value: 1 },
        { name: '驳回', value: 2 }
      ],
      cacheFileUrl: null, // 要上传的图片网络路径
      cacheFile: null, // 上传的本地file
      btnLoad: false, // 提交按钮提交中状态
      packing_list: [], // '包装'
      outer_packing_list: [], // '外包装',
      previewVisible: false, // 是否展示预览大图
      logo_pic: null, // 新上传的图片 logo_key logo_url
      // 产品名称联动字段
      previewImage: '',
      previewTitle: '',
      canEdit: false
    }
  },
  computed: {
    ...mapGetters('taskHandle', ['sku', 'skuProject'])
  },
  mounted() {
    const query = this.$route.query
    this.taskFlowType = query.task_flow_type
    this.skuTypeId = 2// parseFloat(query.sku_type_id)
    this.skuProjectId = parseFloat(query.sku_project_id)
  },
  methods: {
    handleCancel() {
      this.previewVisible = false
      this.previewTitle = ''
    },

    handlePreview(file) {
      if (!file.url && !file.preview) {
        getBase64(file.originFileObj, imageUrl => {
          this.previewImage = imageUrl
        })
      }

      this.previewImage = file.url || file.preview
      this.previewVisible = true
      this.previewTitle = file.name || file.url.substring(file.url.lastIndexOf('/') + 1)
    },
    handleBeforeUpload(file, fileList, index) { // 附件上传
      const isLt2M = file.size / 1024 / 1024 < 3
      if (!isLt2M) {
        this.$message.error(this.$t('skuForm.updatePicMsg'))
        return false
      }
      getBase64(file, imageUrl => {
        file.preview = imageUrl
        file.url = imageUrl
        const field = index + 'FileList'
        this[field] = [...this[field], file]
        this.fileList.set(index, file)
      })
      // false 则 手动上传
      return false
    },
    showBigPic() { // 展示大图
      if (this.pic_url) {
        this.previewVisible = true
      }
    },
    delPic(file, index) { // 图片删除
      this.previewVisible = false
      const field = index + 'FileList'
      const fileIndex = this[field].indexOf(file)
      const newFileList = this[field].slice()
      newFileList.splice(fileIndex, 1)
      this[field] = newFileList
      this.fileList.delete(index)
    },
    handleDrawerClose() { // 关闭侧拉
      this.openDraw = false
      this.beforeFileList = []
      this.afterFileList = []
      this.leftFileList = []
      this.rightFileList = []
      this.fileList.clear()
      this.previewVisible = false
    },
    onSubmit() { // 提交
      this.form.validateFields((err, values) => {
        if (!err) {
          this.btnLoad = true
          const _values = Object.assign(this.formData, values)
          let upload = false
          const param = new FormData()
          this.fileList.forEach((file, index, map) => {
            if (file.status === undefined || file.status === 'removed') {
              param.append(index, file)
              upload = true
            }
          })
          console.log(this.form.getFieldValue('is_master'), '提交')
          if (upload) { // 有新图片上传
            uploadPackageLogo(param).then(res => {
              if (res.errcode === 0) { // logo_key logo_url
                Object.keys(res.data).forEach((key) => {
                  _values[key + '_path'] = res.data[key].logo_key
                  _values[key + '_url'] = res.data[key].logo_url
                })
                const param = {
                  ..._values
                }
                // console.log('新增包装图片', param)
                this.auditSkuPackageFun(param)
              } else {
                this.btnLoad = false
                this.$message.error(res.errmsg)
              }
            }).catch(err => {
              // console.log('错误', err)
              this.btnLoad = false
              this.$message.error('上传错误：' + err)
            })
          } else { // 没有新图片
            const param = { ..._values }
            // console.log(this.formData, param)
            this.auditSkuPackageFun(param)
            console.log(this.form.getFieldValue('is_master'), '提交结束1')
          }
        }
      })
    },
    auditSkuPackageFun(param) { // 修改
      auditSkuPackage(param).then(res => {
        this.btnLoad = false
        if (res.errcode === 0) {
          this.$message.success(res.errmsg)
          this.$emit('onSuccess')
          console.log(this.form.getFieldValue('is_master'), '提交结束2')
          this.handleDrawerClose()
        } else {
          this.$message.error(res.errmsg)
        }
      }).catch(err => {
        this.btnLoad = false
        this.$message.error('上传错误：' + err)
      })
    },
    audit(formData, type) {
      this.formData = formData

      if (formData.before_url) {
        this.beforeFileList.push({
          uid: -1,
          status: 'done',
          name: 'before',
          url: formData.before_url
        })
      }
      if (formData.after_url) {
        this.afterFileList.push({
          uid: -2,
          status: 'done',
          name: 'after',
          url: formData.after_url
        })
      }
      if (formData.left_url) {
        this.leftFileList.push({
          uid: -3,
          status: 'done',
          name: 'left',
          url: formData.left_url
        })
      }
      if (formData.right_url) {
        this.rightFileList.push({
          uid: -4,
          status: 'done',
          name: 'right',
          url: formData.right_url
        })
      }
      this.type = type
      if (formData.data_file) {
        const _arr = formData.data_file.split('/')
        this.fileName = _arr[_arr.length - 1]
      }
      this.canEdit = type === 'audit'
      this.title = type === 'audit' ? this.$t('drawer.auditPackage') : this.$t('buttons.detail')

      this.$nextTick(() => {
        this.form.setFieldsValue({
          is_master: !!this.formData.is_master,
          audit_state: this.formData.audit_state,
          audit_remark: this.formData.audit_remark
        })
      })
      this.openDraw = true
    }
  }
}
</script>
<style lang="less" scoped>
.pics_container {
  .control {
    .extra {
      font-size: 12px;
      color: #999;
      margin-left: 15px;
    }

    //margin-bottom: 15px;
  }

  .lists {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .item {
      padding: 8px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      position: relative;
      cursor: pointer;
      overflow: hidden;

      &:hover {
        .cover {
          display: block;
        }
      }

      .cover {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, .4);
        display: none;

        .contorl {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;

          .icon {
            color: hsla(0, 0%, 100%, .85);
            font-size: 18px;

            &:first-child {
              margin-right: 15px;
            }

            &:hover {
              color: #ffffff;
            }
          }
        }
      }
    }
  }
}

.img_box {
  width: 100%;
  height: 100%;
  text-align: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    user-select: none;
  }
}

/* tile uploaded pictures */
.upload-list-inline {
  float: left;
  width: auto;

  .ant-upload-list-item {
    float: left;
    width: 200px;
    margin-right: 8px;
  }
}

.upload-list-inline [class*='-upload-list-rtl'] {
  .ant-upload-list-item {
    float: right;;
  }
}
</style>
