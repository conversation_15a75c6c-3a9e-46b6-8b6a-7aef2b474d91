<template>
  <a-card>
    <div style="margin-bottom: 20px">
      <a-form-model
        layout="inline"
        :model="formInline"
        @submit="handleSubmit(true)"
        @submit.native.prevent
      >
        <a-form-model-item :label="$t('label.proId')">
          <a-input v-model="formInline.project_id" />
        </a-form-model-item>
        <a-form-model-item :label="$t('table.err_type')">
          <a-select allow-clear style="width: 150px" mode="multiple" :value="formInline.error_type_id" @change="handleErrTypeChange">
            <a-select-option v-for="it in dictList" :key="it.dictionary_id" :value="it.dictionary_id">
              {{ lang === 'CN' ? it.name : it.name_en }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item :label="$t('label.taskType')">
          <a-select style="width: 150px" allow-clear @change="handleTaskChange">
            <a-select-option
              v-for="it in taskList"
              :key="it.value"
              :value="it.value"
            >
              {{ it.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item :label="$t('table.oper_time')">
          <a-range-picker
            format="YYYY-MM-DD"
            :default-picker-value="[moment().subtract(1, 'months'), moment()]"
            @change="onTimeChange"
            @ok="handleSubmit(true)"
          />
        </a-form-model-item>
        <a-form-model-item>
          <a-button type="primary" html-type="submit">
            {{ $t("buttons.search") }}
          </a-button>
        </a-form-model-item>
      </a-form-model>
    </div>
    <a-table
      class="my_scroll_table"
      :scroll="{ x: true }"
      :columns="columns"
      :data-source="data"
      :loading="loading"
      bordered
      :row-key="(record, index) => index"
      :pagination="paginations"
      @change="handleTableChange"
    >
      <template slot="img" slot-scope="text">
        <div
          class="hove_img_box"
          @mouseenter="showHoverImg($event, text)"
          @mouseleave="hideHoverImg"
          @click="showBigImg(text)"
        >
          <a-avatar :size="25" shape="square" icon="picture" :src="text" />
        </div>
      </template>
      <template slot="imgBig" slot-scope="text, record">
        <div
          class="hove_img_box"
          @mouseenter="showHoverImg($event, subUrl(record))"
          @mouseleave="hideHoverImg"
          @click="showBigImg(subUrl(record))"
        >
          <a-avatar :size="25" shape="square" icon="picture" :src="subUrl(record)" />
        </div>
      </template>
    </a-table>
    <div v-show="hoverImg" class="hove_img" :style="{ top: hoverImgY + 'px', left: hoverImgX + 'px' }">
      <img alt="example" :src="hoverImgUrl">
    </div>
    <a-modal :visible="previewVisible" :footer="null" @cancel="closeBigImg">
      <img alt="example" style="width: 100%;max-height: 500px;object-fit: contain;" :src="previewImage">
    </a-modal>
  </a-card>
</template>

<script>
import moment from 'moment'
import { mapState } from 'vuex'
import { getErrorLog } from '@/services/kpi'
import { getDicts } from '@/services/sys'
export default {
  name: 'PersonalCenter',
  data() {
    return {
      formInline: {
        project_id: '',
        task_flow_type: null,
        error_type_id: [],
        start_time: '',
        end_time: ''
      },
      data: [],
      loading: false,
      paginations: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30', '50'],
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => {
          return this.$t('table.total') + total + this.$t('table.strip')
        },
        total: 0
      },
      previewVisible: false,
      previewImage: '',
      hoverImg: false,
      hoverImgUrl: '',
      hoverImgX: 0,
      hoverImgY: 0,
      dictList: []
    }
  },
  computed: {
    ...mapState('setting', ['lang']),
    taskList() {
      const data = [
        { name: this.$t('dic.clean'), value: 10 },
        { name: this.$t('dic.check'), value: 11 }
        // { name: this.$t('dic.sum'), value: 12 }
      ]
      return data
    },
    columns() {
      const data = [
        { title: this.$t('label.proId'), dataIndex: 'project_id', width: 85, align: 'center' },
        { title: this.$t('table.pic'), dataIndex: this.lang === 'CN' ? 'box_url' : 'box_url_en', align: 'center', scopedSlots: { customRender: 'img' }},
        { title: this.$t('table.oPic'), key: 'img_big', align: 'center', scopedSlots: { customRender: 'imgBig' }},
        { title: this.$t('table.flow_name'), dataIndex: 'task_flow', align: 'center' },
        { title: this.$t('table.err_sku'), dataIndex: 'err_sku', align: 'center' },
        { title: this.$t('table.oper_time'), dataIndex: 'operation_time', align: 'center' },
        { title: this.$t('table.modify_sku'), dataIndex: 'changed_sku', align: 'center' },
        { title: this.$t('table.modify_people'), dataIndex: 'changed_user', align: 'center' },
        { title: this.$t('table.modify_time'), dataIndex: 'changed_time', align: 'center' },
        { title: this.$t('table.err_type'), dataIndex: this.lang === 'CN' ? 'error_type' : 'error_type_en', align: 'center' }
      ]
      return data
    }
  },
  mounted() {
    this.getDictFun()
  },
  methods: {
    moment,
    // 返回大图url
    subUrl(item) {
      const url = this.lang === 'CN' ? item.box_url : item.box_url_en
      if (url) {
        return url.slice(0, url.indexOf('?'))
      }
      return url
    },
    // 错误类型列表
    getDictFun() {
      getDicts({ dictionary_id: 1 }).then(res => {
        if (res.errcode === 0) {
          this.dictList = res.data.data_list
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 错误类型改变
    handleErrTypeChange(value) {
      this.formInline.error_type_id = value
    },
    // 任务类型
    handleTaskChange(value) {
      this.formInline.task_flow_type = value
    },
    // 时间选择
    onTimeChange(date, dataStr) {
      this.formInline.start_time = dataStr[0]
      this.formInline.end_time = dataStr[1]
    },
    handleSubmit() {
      const param = {
        ...this.formInline,
        page: this.paginations.current,
        number: this.paginations.pageSize
      }
      if (param.error_type_id) {
        param.error_type_id = param.error_type_id.length > 0 ? param.error_type_id.join(',') : null
      }
      this.loading = true
      getErrorLog(param).then(res => {
        this.loading = false
        if (res.errcode === 0) {
          this.data = res.data.data_list
          this.paginations.total = res.data.total
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 分页改变
    handleTableChange(obj) {
      this.paginations.current = obj.current
      if (obj.showSizeChanger) {
        this.paginations.pageSize = obj.pageSize
      }
      this.handleSubmit()
    },
    showBigImg(url) {
      if (url) {
        this.previewImage = url
        this.previewVisible = true
      }
    },
    closeBigImg() {
      this.previewVisible = false
    },
    showHoverImg(e, url) {
      const obj = e.target.getBoundingClientRect()
      this.hoverImgX = obj.left - 250
      this.hoverImgY = obj.top - 225
      if (url) {
        this.hoverImgUrl = url
        this.hoverImg = true
      }
    },
    hideHoverImg() {
      this.hoverImg = false
    }
  }
}
</script>

<style lang="less" scoped>
.hove_img_box {
  cursor: pointer;
}
.hove_img {
  position: fixed;
  left: 0;
  top: 0;
  width: 250px;
  height: 250px;
  background-color: #ffffff;
  z-index: 999;
  border-radius: 3px;
  border: 1px solid #ebeef5;
  -webkit-box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
</style>
