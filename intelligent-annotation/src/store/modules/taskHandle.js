import { getSkuPackageList } from '@/services/work'

export default {
  namespaced: true,
  state: {
    showPackage: false,
    skuPackages: [],
    sku: {},
    skuProject: null
  },
  getters: {
    skuPackages: state => {
      return state.skuPackages
    },
    sku: state => {
      return state.sku
    },
    skuProject: state => {
      return state.skuProject
    },
    showPackage: state => {
      return state.showPackage
    }

  },
  mutations: {
    setSkuPackages(state, skuPackages) {
      state.skuPackages = skuPackages
    },
    setSku(state, sku) {
      state.sku = sku
    },
    setSkuProject(state, skuProject) {
      state.skuProject = skuProject
    },
    setShowPackage(state, showPackage) {
      state.showPackage = showPackage
    }

  },
  actions: {
    getSkuPackege(context, arg) {
      const sku_id = arg.sku_id
      getSkuPackageList({ sku_id: sku_id }).then(res => {
        this.start_loading = false
        if (res.errcode === 0) {
          if (res.data) {
            console.log(res.data)
            context.commit('setSkuPackages', res.data.list)
          } else {
            this.$message.error(res.errmsg)
          }
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    showSkuPackage(context, arg) {
      context.dispatch('getSkuPackege', arg.sku)
      context.commit('setSku', arg.sku)
      context.commit('setShowPackage', arg.showPackage)
    }
  }
}
