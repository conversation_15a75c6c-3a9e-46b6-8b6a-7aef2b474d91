
#app {

  .main-container {
    min-height: 100%;
    //transition: margin-left .28s;
    margin-left: $sideBarWidth;
    position: relative;
  }

  .sidebar-container {
    //transition: width 0.28s;
    width: $sideBarWidth !important;
    background: linear-gradient(360deg, #21CDFF 0%, #002E78 100%);
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    //overflow: hidden;
    box-shadow: 2px 0px 10px 0px rgba(0, 0, 0, 0.2);

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .el-scrollbar {
      width: calc(10px + #{$sideBarWidth});
    }

    .scrollbar-wrapper {
      //overflow-x: hidden !important;
      width: calc(10px + #{$sideBarWidth});
      padding-right: 10px;
      //padding-bottom:50px;
    }

    .el-scrollbar__bar.is-vertical {
      right: 10px;
      //background-color:rgba(0,0,0,.16);
      background-color: transparent;

      .el-scrollbar__thumb {
        background-color: rgba(0, 0, 0, .2);
      }
    }

    .el-scrollbar {
      height: calc(100vh - 219px);
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100vh - 219px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 16px;
      vertical-align: middle;
    }

    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
      background: transparent!important;
    }

    // menu hover
    .submenu-title-noDropdown,
    .el-submenu__title {
      &:hover {
        background: $menuHover!important;
        color: $menuActiveText !important;
        //&::before{
        //  content:"";
        //  position: absolute;
        //  top: 50%;
        //  margin-top: -6px;
        //  left: 0px;
        //  width: 0px;
        //  height: 0px;
        //  border: 6px solid;
        //  border-left-color: #00DCFF;
        //  border-right-color: transparent;
        //  border-top-color: transparent;
        //  border-bottom-color: transparent;
        //}
      }
    }

    .is-active > .el-submenu__title {
      color: $subMenuActiveText !important;
    }

    & .nest-menu .el-submenu > .el-submenu__title,
    & .el-submenu .el-menu-item {
      min-width: $sideBarWidth;
      background: transparent!important;
      &.is-active{
        background: $menuHover;
      }
      &:hover {
        background: $subMenuHover!important;
        color: $menuActiveText !important;
        //&::before{
        //  content:"";
        //  position: absolute;
        //  top: 50%;
        //  margin-top: -6px;
        //  left: 0px;
        //  width: 0px;
        //  height: 0px;
        //  border: 6px solid;
        //  border-left-color: #00DCFF;
        //  border-right-color: transparent;
        //  border-top-color: transparent;
        //  border-bottom-color: transparent;
        //}

      }
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 54px !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 2px;
      //background-color:rgba(0,0,0,.16);
      background-color: transparent;

      .el-scrollbar__thumb {
        background-color: rgba(0, 0, 0, .2);
      }
    }

    .el-menu-item.is-active {
      //background: $themeBgColor !important;
      width: auto !important;
      border-radius: 0;
    }


    .el-scrollbar {
      width: auto !important;
    }

    .scrollbar-wrapper {
      //overflow-x: hidden !important;
      width: auto !important;
      padding-right: 0;
    }

    .main-container {
      margin-left: 54px;
    }

    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

        .sub-el-icon {
          margin-left: 19px;
        }
      }
    }

    .el-submenu {
      overflow: hidden;

      & > .el-submenu__title {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

        .sub-el-icon {
          margin-left: 19px;
        }

        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }

    .el-menu--collapse {
      .el-submenu {
        & > .el-submenu__title {
          & > span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-submenu {
    min-width: $sideBarWidth !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform .28s;
      width: $sideBarWidth !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d($sideBarWidthTransform, 0, 0);
      }
    }
  }

  .withoutAnimation {

    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  & > .el-menu {
    .svg-icon {
      margin-right: 16px;
    }

    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }
  }

  & > .el-menu {
    width: $sideBarWidth !important;
  }

  .nest-menu .el-submenu > .el-submenu__title,
  .el-menu-item {
    &:hover {
      // you can use $subMenuHover
      background: $menuHover !important;
      color: $menuActiveText !important;

    }

    &.is-active {
      //background: $themeBgColor !important;
      border-radius: 0px 0px 0px 0px;
      width: auto !important;
      position: relative;
      z-index: 2;
    }
  }

  // the scroll bar appears when the subMenu is too long
  > .el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}

#app .sidebar-container a {
  overflow: visible !important;
}


#app .el-menu-item.is-active {
  //background: $themeBgColor !important;
  //border-radius: 0px 4px 4px 0px;
  //width: calc(10px + #{$sideBarWidth});
  background: rgba(255,255,255,0.11)!important;
  position: relative;
  z-index: 2;
  &::before{
    content:"";
    position: absolute;
    top: 50%;
    margin-top: -6px;
    left: 0px;
    width: 0px;
    height: 0px;
    border: 6px solid;
    border-left-color: #00DCFF;
    border-right-color: transparent;
    border-top-color: transparent;
    border-bottom-color: transparent;
  }

}


#app .sidebar-container .el-menu-item {
  height: 56px !important;
  line-height: 56px !important;
  padding-left: 30px !important;
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
}

#app .sidebar-container .el-submenu__title {
  height: 56px !important;
  line-height: 56px !important;
  padding-left: 30px !important;
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
}

.el-submenu__title i {
  color: $menuText;
}

.is-active .el-submenu__title i:hover {
  color: white;
}

.is-active .el-submenu__title i {
  color: white;
}

.svg-icon {
  fill: $menuText;
  stroke: $menuText;
}

.is-active .svg-icon {
  fill: $menuActiveText;
  stroke: $menuActiveText;
}

#app .scrollbar-wrapper::-webkit-scrollbar {
  display: none !important;
}

#app .scrollbar-wrapper {
  scrollbar-color: transparent;
  scrollbar-width: none;
}

//
//#app .el-menu.el-menu--inline{
//  transition:all 200ms!important;
//}

.el-menu-item.submenu-title-noDropdown, .el-submenu__title {
  //background: $menuBg;
  background: rgba(0,0,0,0)!important;
}

.el-menu--popup, .el-menu--popup .el-menu-item {
  background: $subMenuBg;
}
