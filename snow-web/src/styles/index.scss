@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './element-variables.scss';
@import './sidebar.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: PingFang SC, Helvetica Neue, Helvetica, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
  color: #333;
  background-color: #E6EDF3;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
  background-color: #E6EDF3;
}

#app {
  height: 100%;
  min-height: 100vh;
  background-color: #E6EDF3;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

.overflow {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.f13{
  font-size: 13px;
}
.f14{
  font-size: 14px;
}
.fl {
  float: left;
}

.fr {
  float: right;
}

.mr15 {
  margin-right: 15px;
}

em, i {
  font-style: normal;
}

.color__green {
  color: #67C23A;
}
.c66{
  color: #666;
}

.color__error {
  color: #F56C6C;
}

// main-container global css
.main-container {
  background-color: #E6EDF3;
}

.app-container {
  padding: 20px;
}
.ml10{
  margin-left: 10px;
}
.flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.flex-center {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.flex-y-start {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
}

.flex-y-center {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.flex-y-end {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
}

.flex-0 {
  -webkit-box-flex: 0;
  -ms-flex: 0;
  flex: 0;
}

.flex-1 {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.flex-bt {
  -ms-flex-pack: distribute;
  justify-content: space-between;
}

.flex-row {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
}

.flex-column {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}

.flex-x-end {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}

.flex-x-center {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.flex-x-start {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.flex-shrink {
  -webkit-flex-shrink: 0;
  flex-shrink: 0;
}

.flex-wrap {
  flex-wrap: wrap;
}
.relative{
  position: relative;
}

.flex-wrap--div {
  display: flex;
  flex-wrap: wrap;
}
.f500{
  font-weight: 500;
}
.cursor{
  cursor: pointer;
}
.wbg {
  background-color: white;
}

// 上下分割
.flex__direction {
  display: flex;
  height: 100%;
  flex-direction: column;
}

.direction__flex {
  flex: 1;
}

.br2 {
  border-radius: 2px;
}

.br8 {
  border-radius: 8px;
}

.mr10 {
  margin-right: 10px;
}

.mb10 {
  margin-bottom: 10px;
}
.mb5{
  margin-bottom: 5px;
}
.mb13 {
  margin-bottom: 13px;
}
.mt0{
  margin-top: 0!important;
}
.ml15{
  margin-left: 15px;
}
.page_container {
  padding: 24px;
}

.relative {
  position: relative;
}

.page_container_view {
  height: calc(100vh - 109px);
}

.theme_color {
  color: #3DD6FC !important;
}

.el-popup-parent--hidden .fixed-header {
  padding-right: 0 !important;
}

.dialog_container .el-select {
  display: block !important;
}

#nprogress .bar {
  background: $themeColor !important; //自定义颜色
}

.head__title {
  font-size: 16px;
  font-weight: 600;
  margin-left: 5px;
  position: relative;
  top: 2px;
}

.el-dialog {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%); /* IE 9 */
  -moz-transform: translate(-50%, -50%); /* Firefox */
  -webkit-transform: translate(-50%, -50%); /* Safari 和 Chrome */
  -o-transform: translate(-50%, -50%); /* Opera */
  overflow: hidden;
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);
}

.el-dialog .el-dialog__body {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  overflow: auto;
}

ul, li {
  padding: 0;
  margin: 0;
  list-style: none
}

.ele_table_text_btn:before {
  content: '';
  display: block !important;
  height: 13px !important;
  width: 1px !important;
  background: #0471D4 !important;
  opacity: .26 !important;
  position: absolute !important;
  right: 0 !important;
  top: 5px !important;
}

.account_frontend_view .el-loading-mask {
  z-index: 3 !important;
}

.el-form-item__error {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 17px;
}

.el_form_box .el-form-item__error {
  padding-top: 6px;
  padding-left: 8px;
}

.no_table_data_box {
  height: 300px;
  line-height: 260px;
  text-align: center;
  font-size: 14px;
  color: #909399;
}

.table_box {
  box-shadow: 0px 8px 10px 0px rgba(0, 0, 0, 0.02);
  border-radius: 8px;
}

.edit_view_btn .el-button {
  padding: 12px 31px;
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
}

.edit_view_btn .el-button--default {
  border-color: $themeColor;
  color: $themeColor;
}

.wbg_shadow {
  box-shadow: 0px 8px 10px 0px rgba(0, 0, 0, 0.02);
  border-radius: 8px;
}

.no_data_icon_txt {
  height: 20px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #999999;
  line-height: 20px;
}

.header_scroll_box::-webkit-scrollbar {
  height: 4px;
  width: 3px;
}

.el-table .cell {
  padding-left: 15px !important;
  padding-right: 15px !important;
}

.table__content .el-table__fixed-right {
  z-index: 0 !important;
}

.el-message-box__status.el-icon-warning {
  color: #F56C6C !important;
}

.el-popconfirm__icon.el-icon-warning {
  color: #F56C6C !important;
}

.popup .el-dialog--center .el-dialog__body {
  padding: 25px 35px 30px 25px
}

.mt12 {
  margin-top: 12px;
}

.mr12 {
  margin-right: 12px;
}

.store__devlist {
  .el-collapse {
    border-top: none !important;
  }
}

.f12{
  font-size: 12px;
}
.f15{
  font-size: 15px;
}
.mt15 {
  margin-top: 15px;
}
.mt20{
  margin-top: 20px;
}
.c00{
  color: #000;
}

.special-btn {
  background-image: url('~@/assets/screen/back.png');
  background-size: 100% 100%;
  height: 48px;
  line-height: 48px;
  text-align: center;
  cursor: pointer;
  display: inline-block;
  &:hover{
    background-image: url('~@/assets/screen/back-hover.png');
  }
}

.sales-sku-modal {
  position: fixed;
  background: rgba(0,44,73,0.95);
  box-shadow: inset 0px 0px 30px 0px #2176A7;
  border: 2px solid;
  border-image: linear-gradient(360deg, rgba(50, 180, 255, 1), rgba(0, 252, 249, 1)) 2 2;
  color: #ffffff;
  z-index: 10;
  &::before{
    content:"";
    position: absolute;
    top: -10px;
    left: -2px;
    width: 60px;
    height: 4px;
    background: #00FCF9;
  }
  &::after{
    content:"";
    position: absolute;
    bottom: -10px;
    right: -2px;
    width: 40px;
    height: 5px;
    background: #32B4FF;
    background: linear-gradient(25deg, transparent 5px, #32B4FF 0);
  }
  .sales-sku-list {
    padding-bottom: 10px;
    min-width: 200px;
  }

  .sales-sku-item {
    padding: 6px 10px;
    align-items: center;
    cursor: pointer;

    .sales-sku-image {
      height: 28px;
      margin-right: 5px;
      background: #ffffff;
      border-radius: 2px;
      width: 20px;

    }
    .sales-sku-range {
      margin-left: 5px;
    }
    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }

}

.sales-sku-image {
  &.big {
    height: 130px;
    width: 130px;
  }
}


/* 修改滚动条的宽度和颜色 */
::-webkit-scrollbar {
  max-width: 8px; /* 滚动条宽度 */
  max-height: 8px; /* 滚动条宽度 */
  background-color: rgba(0, 0, 0, 0); /* 滚动条背景颜色 */
  border-radius: 8px;
}

/* 修改滚动条滑块的颜色 */
::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.4); /* 滑块颜色 */
  border-radius: 8px;
}

/* 鼠标悬停时滚动条滑块的颜色 */
::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.6); /* 鼠标悬停时的滑块颜色 */
  border-radius: 8px;
}

.scrollbar-hidden {
  /* 修改滚动条的宽度和颜色 */
  &::-webkit-scrollbar {
    max-width: 0px; /* 滚动条宽度 */
    max-height: 0px; /* 滚动条宽度 */
    background-color: rgba(0, 0, 0, 0); /* 滚动条背景颜色 */
    border-radius: 0px;
  }

  /* 修改滚动条滑块的颜色 */
  &::-webkit-scrollbar-thumb {
    background-color: transparent; /* 滑块颜色 */
    border-radius: 0px;
  }

  /* 鼠标悬停时滚动条滑块的颜色 */
  &::-webkit-scrollbar-thumb:hover {
    background-color: transparent; /* 鼠标悬停时的滑块颜色 */
    border-radius: 0px;
  }

}

.switch__change {
  .el-switch__core {
    border-color: rgba(1, 240, 250, .4) !important;
  }

  .el-switch__core:after {
    content: "";
    background-color: #01F0FA !important;
  }
}
.swiper-pagination{
  color: #b3b9bd;
  text-align: right!important;
  padding-right: 10px;
  .swiper-pagination-current{
    font-weight: 600;
    color: #0471D4;
  }
}
.mt10{
  margin-top: 10px;
}
.mt5{
  margin-top: 5px;
}
.w100p{
  width: 100%!important;
}
.display_none{
  display: none;
}
.gmnoprint{
  display: none;
}
.gm-control-active{
  width: 30px!important;
  height: 30px!important;
}
.gmap__map{
  a{
    display: none!important;
  }
}
.process__button{
  i.el-icon-s-claim{
    font-size: 13px!important;
  }
}
.add__image{
  .chose__file{
    padding: 0!important;
    height: 73px!important;
    line-height: 73px!important;
    font-size: 12px!important;
    overflow: hidden;
  }
}
.overflow{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.empty__text{
  color:#006AB3;
  font-size: 14px;
  text-align: center;
  height: 30%;
  padding:9vh 0 ;
  display: flex;
  align-items: center;
  justify-content: center;
}


.c-primary{
  color: $themeColor;
}
.c-warnning {
  color: #E6A23C;
}
.c-danger{
  color: #E52828;
}
.c-success{
  color: #36C08E;
}
.c-gray{
  color: #909399;
}


.module__box {
  position: relative;
  margin-bottom: 14px;
  padding-top: 0;
  position: relative;
  .tit {
    position: relative;
    z-index: 5;
    border-radius: 90px 0 90px 3px;
    background: linear-gradient(134deg, rgba(80, 232, 255, 1) 0%, rgba(50, 210, 255, 0) 70%);
    padding: 1px;
    .text {
      font-size: 16px;
      line-height: 32px;
      letter-spacing: 2px;
      color: #fff;
      font-weight: 500;
      border-radius: 90px 0 90px 3px;
      background: linear-gradient(315deg, #06213D 0%, #04336A 100%);
      padding-left: 30px;
    }
    &::before{
      content: '';
      display: block;
      position: absolute;
      left: 0px;
      top: 0px;
      width: 15px;
      height: 15px;
      background-image: url("~@/assets/screen/three-left.png");
      background-size: 100% 100%;
    }
    &::after{
      content: '';
      display: block;
      position: absolute;
      right: 0px;
      bottom: 0px;
      width: 12px;
      height: 12px;
      background-image: url("~@/assets/screen/three-right.png");
      background-size: 100% 100%;
    }
  }
  .module__box-bg {
    margin-top: 10px;
    background: linear-gradient(135deg, rgba(8,104,122,0.06) 0%, rgba(9,66,121,0.06) 6%, rgba(8,104,122,0.1)  100%);
  }
}

.search-btn{
  display: inline-block;
  width: 88px;
  height: 30px;
  line-height: 30px;
  background: linear-gradient(360deg, #051526 0%, #04336B 100%);
  border: 1px solid;
  border-image: linear-gradient(270deg, rgba(12, 103, 171, 1), rgba(104, 202, 245, 1), rgba(26, 147, 253, 1)) 1 1;
  margin-left: 10px;
  cursor: pointer;
  color: #FFFFFF;
  text-align: center;
  &:hover{
    background: linear-gradient(360deg, #044695 0%, #082442 100%);
  }
}
.picker-popper .el-date-table .badge::after {
  position: absolute;
  content: "";
  right: 5px;
  top: 7px;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: #0487FB;
}

.ruleform__version {
  .el-form-item__label, .el-dialog__title{
    color: #000!important;
  }
  .el-dialog__title{
    font-weight: 500!important;
  }
}
.head__back{
  font-size: 14px;
  color: #0471D4;
  margin-bottom: 10px;
  cursor: pointer;
}
.el-tooltip__popper:has(.tooltip__list){
  padding: 0!important;
}

