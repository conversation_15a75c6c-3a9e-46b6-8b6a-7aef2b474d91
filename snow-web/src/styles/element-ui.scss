// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}


// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-form-item-label .el-form-item__label {
  text-align: left;
}

.filter__date {
  height: 36px;
  line-height: 36px;

  .el-input__inner {
    border: 1px solid #01F0FA;
    height: 36px;
  }
}


.store-scenes .el-form-item__label {
  color: #ffffff;
}

.store-scenes .el-input__inner {
  background: #071E35;
  border: 1px solid #293040;
  height: 36px;
  color: #ffffff;
}

.store-scenes .el-range-input {
  background: #071E35;
  color: #ffffff !important;
}

.store-scenes .el-date-editor .el-range-separator {
  color: #D8D8D8;
}
