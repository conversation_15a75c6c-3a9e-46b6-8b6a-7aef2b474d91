//theme
$themeColor: #0471D4;
$themeBgColor: linear-gradient(90deg, #0073F5 0%, #2BBAFF 100%);
// sidebar
$menuText: rgba(255, 255, 255, 0.6);
$menuActiveText: #fff;
$subMenuActiveText: #fff; // https://github.com/ElemeFE/element/issues/12951

$menuBg: linear-gradient(360deg, #21CDFF 0%, rgba(204, 204, 204, 0) 100%);;
$menuBg: linear-gradient(to right, #005CC6 0%, #0FA7F8 100%);
$menuHover:rgba(255, 255, 255, 0.11);

$subMenuBg: linear-gradient(to right, #006ED2 0%, #006ED2 100%);
$subMenuHover:rgba(255, 255, 255, 0.11);

$sideBarWidth: var(--sidebar-width, 208px);
$sideBarWidthTransform: var(--sidebar-width-transform, -208px);

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
  themeColor: $themeColor;
  themeBgColor: $themeBgColor;
  sideBarWidthTransform: $sideBarWidthTransform;
}
