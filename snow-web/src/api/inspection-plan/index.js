import request from '@/utils/request'

// 启/停检查计划
export function planToggleStatus(data) {
  return request({
    url: '/plan/toggle-status',
    method: 'post',
    data
  })
}

// 查看单个检查计划
export function planDetail(params) {
  return request({
    url: '/plan/get-plan',
    method: 'get',
    params
  })
}

// 检查计划新增
export function planAdd(data) {
  return request({
    url: '/plan/add',
    method: 'post',
    data
  })
}

// 检查计划编辑
export function planEdit(data) {
  return request({
    url: '/plan/complete',
    method: 'post',
    data
  })
}

// 检查计划删除
export function planDelete(data) {
  return request({
    url: '/plan/delete',
    method: 'post',
    data
  })
}

// 查看售点数量
export function planStoreCount(params) {
  return request({
    url: '/plan/count',
    method: 'get',
    params
  })
}

// 查看组织架构售点数量
export function organizeStoreCount(params) {
  return request({
    url: '/plan/org-store-count',
    method: 'get',
    params
  })
}
