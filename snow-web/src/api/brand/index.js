import request from '@/utils/request'

// 启用/禁用品牌
export function changeTenantStatus(data) {
  return request({
    url: '/tenant/change-status',
    method: 'post',
    data
  })
}

// 品牌编辑
export function tenantEdit(data) {
  return request({
    url: '/tenant/edit',
    method: 'post',
    data
  })
}

// 品牌查看
export function tenantDetail(params) {
  return request({
    url: '/tenant/one',
    method: 'get',
    params
  })
}

// 品牌新增
export function tenantCreate(data) {
  return request({
    url: '/tenant/create',
    method: 'post',
    data
  })
}

// 获取识别模型列表
export function recognitionProjectList(params) {
  return request({
    url: '/tenant/recognition-project-list',
    method: 'get',
    params
  })
}
