import request from '@/utils/request'

// 售点新增
export function storeAdd(data) {
  return request({
    url: '/store/add',
    method: 'post',
    data
  })
}

// 售点导入
export function storeImport(data) {
  return request({
    url: '/store/import',
    method: 'post',
    data
  })
}

// 售点删除
export function storeDelete(data) {
  return request({
    url: '/store/delete',
    method: 'post',
    data
  })
}

// 售点编辑
export function storeEdit(data) {
  return request({
    url: '/store/edit',
    method: 'post',
    data
  })
}

// 售点导出
export function storeExport(params) {
  return request({
    url: '/store/export',
    method: 'get',
    params
  })
}

//售点详情
export function storeDetail(params) {
  return request({
    url: '/store/get-store',
    method: 'get',
    params
  })
}
