import request from '@/utils/request'

// 获取冰柜纯净度和饱和度
export function getSceneDetail(params) {
  return request({
    url: '/data/get-scene-detail',
    method: 'get',
    params
  })
}

// 绑定场景-场景列表
export function storeScene(params) {
  return request({
    url: '/store-scene/list',
    method: 'get',
    params
  })
}

// 绑定场景-提交标注数据
export function storeSceneBind(data) {
  return request({
    url: '/store/scene-bind-plan',
    method: 'post',
    data
  })
}

// 删除场景
export function storeSceneDelete(data) {
  return request({
    url: '/store-scene/delete',
    method: 'post',
    data
  })
}

// 删除场景
export function storeSceneTree(params) {
  return request({
    url: '/store-scene/tree',
    method: 'get',
    params
  })
}

// 场景创建
export function storeSceneCreate(data) {
  return request({
    url: '/store-scene/create',
    method: 'post',
    data
  })
}

// 场景编辑
export function storeSceneEdit(data) {
  return request({
    url: '/store-scene/edit',
    method: 'post',
    data
  })
}
// 售点场景排面详情-大屏展示
export function sceneFacingList(params) {
  return request({
    url: '/store-scene-sale/scene-facing-list',
    method: 'get',
    params
  })
}








