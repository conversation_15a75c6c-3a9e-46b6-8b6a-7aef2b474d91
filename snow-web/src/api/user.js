import request from '@/utils/request'

// 修改密码
export function authReset(data) {
  return request({
    url: 'admin/auth/reset',
    method: 'post',
    data
  })
}

// 获取用户信息
export function authUserInfo(data) {
  return request({
    url: 'admin/auth/user-info',
    method: 'post',
    data
  })
}

// 获取用户菜单
export function authMenu(data) {
  return request({
    url: 'admin/auth/menu',
    method: 'post',
    data
  })
}

// 登录
export function authlogin(data) {
  return request({
    url: '/admin/auth/login',
    method: 'post',
    data
  })
}

// 首页报表
export function reportForm(params) {
  return request({
    url: '/statistics/index-visit-rate',
    method: 'get',
    params
  })
}

// 检测双英子认证是否开启
export function checkTwoFactors(params) {
  return request({
    url: '/auth/check-status',
    method: 'get',
    params
  })
}

// 手机号发送验证码
export function sendCode(params) {
  return request({
    url: '/auth/send-code',
    method: 'get',
    params
  })
}

// 校验验证码
export function toVerificationCode(data) {
  return request({
    url: '/auth/check-sms-code',
    method: 'post',
    data
  })
}

// 找回密码提交接口
export function submitForgotPassword(data) {
  return request({
    url: '/admin/find-pass',
    method: 'post',
    data
  })
}
