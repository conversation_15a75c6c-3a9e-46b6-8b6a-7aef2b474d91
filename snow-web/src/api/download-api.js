import request from '@/utils/request'

export const importTemplate = '/import/template'
// 新建安装任务-模板下载
export const installTemplateUrl = '/source/安装任务模板.xlsx'
export const storeTemplateUrl = '/source/售点导入模板.xlsx'
export const deviceTemplateUrl = '/source/设备导入模板.xlsx'
export const appAccountTemplateUrl = '/source/app账号导入模板.xlsx'
export const issueTemplateUrl = '/source/问题件导入模板.xlsx'


// 通用文件上传
export function importUpload(data) {
  return request({
    url: 'admin/import-file/upload',
    method: 'post',
    headers: {'Content-Type': 'multipart/form-data'},
    data
  })
}

// 通用文件上传进度
export function importProgress(params) {
  return request({
    url: 'admin/import-file/process',
    method: 'get',
    params
  })
}

// 通用文件下载
export function exportStart(data) {
  return request({
    url: 'admin/export-file/download',
    method: 'post',
    data
  })
}
// 获取下载模版链接
export function importTemplateUrl(params) {
  return request({
    url: 'admin/import-file/template',
    method: 'get',
    params
  })
}
// 通用文件下载进度
export function exportProgress(params) {
  return request({
    url: 'admin/export-file/process',
    method: 'get',
    params
  })
}

// 问题件列表下载
export function issueDownload(data) {
  return request({
    url: '/issue-record/download',
    method: 'post',
    data
  })
}
// 设备列表下载
export function deviceDownload(data) {
  return request({
    url: '/device/download-all',
    method: 'post',
    data
  })
}


