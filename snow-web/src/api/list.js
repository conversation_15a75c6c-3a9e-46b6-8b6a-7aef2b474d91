import request from '@/utils/request'

// 任务列表
export function taskList(params) {
  return request({
    url: 'admin/task/list',
    method: 'get',
    params
  })
}
// QC列表
export function qcRecognitionList(params) {
  return request({
    url: 'admin/recognition-qc/list',
    method: 'get',
    params
  })
}
// 安装列表
export function installList(params) {
  return request({
    url: '/install-task/list',
    method: 'get',
    params
  })
}

// 后台用户列表
export function adminList(params) {
  return request({
    url: '/admin/list',
    method: 'get',
    params
  })
}

// 后台角色列表
export function adminRoleList(params) {
  return request({
    url: '/admin-role/list',
    method: 'get',
    params
  })
}

// 前台用户列表
export function userList(params) {
  return request({
    url: '/app-user/list',
    method: 'get',
    params
  })
}

// 周期列表
export function cycleList(params) {
  return request({
    url: '/cycle/list',
    method: 'get',
    params
  })
}

// 拜访计划列表
export function surveyList(params) {
  return request({
    url: '/survey/list',
    method: 'get',
    params
  })
}

// 已拜访列表
export function surveyVisitedList(params) {
  return request({
    url: '/survey/visited-list',
    method: 'get',
    params
  })
}

// 异常门店列表
export function surveyErrorList(params) {
  return request({
    url: '/report-store-error/list',
    method: 'get',
    params
  })
}

// 检查计划列表
export function planList(params) {
  return request({
    url: '/plan/plan-list',
    method: 'get',
    params
  })
}

// 售点库表
export function storeList(params) {
  return request({
    url: '/store/list',
    method: 'get',
    params
  })
}

// 品牌列表
export function brandList(params) {
  return request({
    url: '/tenant/list',
    method: 'get',
    params
  })
}

// 日志列表
export function logList(params) {
  return request({
    url: '/log/client-db-list',
    method: 'get',
    params
  })
}

// SKU管理列表
export function skuList(params) {
  return request({
    url: '/sku/list',
    method: 'get',
    params
  })
}

// 识别模型列表
export function recognitionProjectList(params) {
  return request({
    url: '/recognition-project/list',
    method: 'get',
    params
  })
}

// 设备型号列表
export function deviceList(params) {
  return request({
    url: '/user/device-attr',
    method: 'get',
    params
  })
}

// API日志列表
export function apiLogList(params) {
  return request({
    url: '/api-log/list',
    method: 'get',
    params
  })
}

// 新增部分 TODO
// 售点-场景列表
export function storeSceneList(params) {
  return request({
    url: '/store/scene-unit-list',
    method: 'get',
    params
  })
}

// 售点-场景列表
export function qcList(params) {
  return request({
    url: '/qc/list',
    method: 'get',
    params
  })
}

// 设备列表
export function allDeviceList(params) {
  return request({
    url: '/device/list',
    method: 'get',
    params
  })
}

// 设备列表
export function boardIndex(data) {
  return request({
    url: '/data-board/index',
    method: 'post',
    data
  })
}

// 获取售点版本号列表
export function storeVersionList(params) {
  return request({
    url: '/store/relation-time-list',
    method: 'get',
    params
  })
}
// 设备寻检-列表查询
export function inspectionList(params) {
  return request({
    url: '/inspection/list',
    method: 'get',
    params
  })
}
// 设备列表-问题件列表
export function issueRecordList(params) {
  return request({
    url: '/issue-record/list',
    method: 'get',
    params
  })
}
// 更换列表
export function exchangeTaskList(params) {
  return request({
    url: '/exchange-task/list',
    method: 'get',
    params
  })
}

// 获取售点版本列表
export function storeVesList(params) {
  return request({
    url: '/store/version-list',
    method: 'get',
    params
  })
}
