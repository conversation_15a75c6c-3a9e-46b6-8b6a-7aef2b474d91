import request from '@/utils/request'

// 售点销量统计详情-大屏展示
export function getStoreSaleDetail(params) {
  return request({
    url: '/store-scene-sale/store-sale-detail',
    method: 'get',
    params
  })
}

// 售点销量分析详情-大屏展示
export function getStoreSaleAnalysis(params) {
  return request({
    url: '/store-scene-sale/store-analysis-detail',
    method: 'get',
    params
  })
}

// 售点场景销量列表-大屏展示
// export function sceneSaleList(params) {
//   return request({
//     url: '/store-scene-sale/list-dashboard',
//     method: 'get',
//     params
//   })
// }

// 售点销量场景销量详情-大屏展示
export function sceneSaleDetail(params) {
  return request({
    url: '/store-scene-sale/scene-sale-detail',
    method: 'get',
    params
  })
}


