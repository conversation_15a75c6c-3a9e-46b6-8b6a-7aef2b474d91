import request from '@/utils/request'

// 删除网关
export function gatewayDelete(data) {
  return request({
    url: '/recognition-project/delete',
    method: 'post',
    data
  })
}

// 新增网关
export function gatewayAdd(data) {
  return request({
    url: '/recognition-project/create',
    method: 'post',
    data
  })
}

// 编辑网关
export function gatewayEdit(data) {
  return request({
    url: '/recognition-project/edit',
    method: 'post',
    data
  })
}
