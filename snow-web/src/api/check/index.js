
import request from '@/utils/request'
// qc审核详情
export function recognitionInfo(params) {
  return request({
    url: '/admin/recognition-qc/info',
    method: 'get',
    params
  })
}
// 点击开始qc
export function recognitionQcStart(data) {
  return request({
    url: '/admin/recognition-qc/start',
    method: 'post',
    data
  })
}
// 点击qc通过
export function recognitionQcPass(data) {
  return request({
    url: '/admin/recognition-qc/audit-pass',
    method: 'post',
    data
  })
}
// 点击qc不通过/驳回
export function recognitionQcReject(data) {
  return request({
    url: '/admin/recognition-qc/audit-deny',
    method: 'post',
    data
  })
}
// 提交qc完成 合格/不合格
export function recognitionQcFinish(data) {
  return request({
    url: '/admin/recognition-qc/finish',
    method: 'post',
    data
  })
}
// 审核日志记录
export function recognitionQcInfoHistory(params) {
  return request({
    url: '/admin/recognition-qc/info-history',
    method: 'get',
    params
  })
}
// 取消qc
export function recognitionQcCancel(data) {
  return request({
    url: '/admin/recognition-qc/audit-cancel',
    method: 'post',
    data
  })
}