import request from '@/utils/request'

// 创建安装任务
export function installTaskCreate(data) {
  return request({
    url: '/install-task/create',
    method: 'post',
    data
  })
}

// 安装任务详情
export function installTaskOne(params) {
  return request({
    url: '/install-task/one',
    method: 'get',
    params
  })
}

// 安装设备信息
export function installTaskViewDevice(params) {
  return request({
    url: '/install-task/view-device',
    method: 'get',
    params
  })
}

// 创建场景
export function sceneCreate(data) {
  return request({
    url: '/scene/create',
    method: 'post',
    data
  })
}

// 角度验收-保存
export function installTaskSave(data) {
  return request({
    url: '/install-task/save',
    method: 'post',
    data
  })
}

// 创建场景
export function sceneDelete(data) {
  return request({
    url: '/scene/delete',
    method: 'post',
    data
  })
}

// 矫正完成
export function installCorrectFinish(data) {
  return request({
    url: '/install-task/install-device-finish',
    method: 'post',
    data
  })
}

// 安装任务结束
export function installTaskFinish(data) {
  return request({
    url: '/install-task/install-task-finish',
    method: 'post',
    data
  })
}

// 获取售点下所有场景
export function storeGetScenes(params) {
  return request({
    url: '/store/get-scenes',
    method: 'get',
    params
  })
}

// 安装任务编辑
export function installTaskEdit(data) {
  return request({
    url: '/install-task/edit',
    method: 'post',
    data
  })
}

// 问题件-编辑
export function issueRecordEdit(data) {
  return request({
    url: '/issue-record/edit',
    method: 'post',
    data
  })
}
// 问题件-创建
export function issueRecordCreate(data) {
  return request({
    url: '/issue-record/create',
    method: 'post',
    data
  })
}
// 更换列表-创建
export function exchangeCreate(data) {
  return request({
    url: '/exchange-task/create',
    method: 'post',
    data
  })
}

//  更换列表-详情
export function exchangeTaskOne(params) {
  return request({
    url: '/exchange-task/one',
    method: 'get',
    params
  })
}
// 更换任务-任务结束
export function exchangeTaskFinish(data) {
  return request({
    url: '/exchange-task/exchange-task-finish',
    method: 'post',
    data
  })
}
// 矫正完成后调用aiot识别
export function aiotRecognition(data) {
  return request({
    url: '/device-photo/aiot-recognition',
    method: 'post',
    data
  })
}
// 矫正完成后查看识别结果
export function aiotRecognitionResult(params) {
  return request({
    url: '/device-photo/aiot-recognition-result',
    method: 'post',
    params
  })
}


