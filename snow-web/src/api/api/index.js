import request from '@/utils/request'
import apiRequest from '@/utils/apiRequest'

// 获取api识别结果场景下拉筛选列表
export function apiStepsList(token, originUrl, params) {
  return apiRequest({
    url: originUrl + '/api/admin/list-steps',
    method: 'get',
    params,
    headers: {
      token
    }
  })
}

// api结果列表
export function apiResultList(token, originUrl, data, type) {
  return apiRequest({
    url: originUrl + (type ? '/report/list' : '/api/admin/list'),
    method: 'post',
    data,
    headers: {
      token
    }
  })
}

// api结果详情
export function apiResult(token, originUrl, data) {
  return apiRequest({
    url: originUrl + '/api/admin/get-result',
    method: 'post',
    data,
    headers: {
      token
    }
  })
}

// 获取项目配置列表
export function apiConfigList(params) {
  return request({
    url: '/api-config/list',
    method: 'get',
    params
  })
}

// 获取单个项目配置

export function apiConfigToken(params) {
  return request({
    url: '/api-config/token',
    method: 'get',
    params
  })
}

// api结果详情（蒙牛）
export function mengniuApiResult(token, originUrl, params) {
  return apiRequest({
    url: originUrl + '/report/detail',
    method: 'get',
    params,
    headers: {
      token
    }
  })
}
