import request from '@/utils/request'

// QC记录详情
export function getQcDetail(params) {
  return request({
    url: '/qc/one',
    method: 'get',
    params
  })
}

// sku列表-下拉框
export function getSkuList(params) {
  return request({
    url: '/sku/list-select',
    method: 'get',
    params
  })
}

// QC结果保存
export function saveResult(data) {
  return request({
    url: '/qc/sale-result-save',
    method: 'post',
    data
  })
}

// 设备组列表-下拉框
export function getDeviceGroup(data) {
  return request({
    url: '/device-group/list-select',
    method: 'post',
    data
  })
}

// 售点销量状态列表-下拉框
export function getSalesStatus(data) {
  return request({
    url: '/qc/sale-status-list-select',
    method: 'post',
    data
  })
}

// 删除数据
export function deleteVideo(data) {
  return request({
    url: '/qc/delete',
    method: 'post',
    data
  })
}

// QC识别详情
export function getRecognitionOne(params) {
  return request({
    url: '/qc/recognition-one',
    method: 'get',
    params
  })
}

// QC识别开始
export function saveRecognition(data) {
  return request({
    url: '/qc/recognition',
    method: 'post',
    data
  })
}


// QC识别状态列表-下拉框
export function getRecognitionStatus(params) {
  return request({
    url: '/qc/recognition-status-list-select',
    method: 'get',
    params
  })
}
// QC获取视频帧
export function getVideoImage(params) {
  return request({
    url: '/qc/get-video-frames',
    method: 'get',
    params
  })
}

// QC图片去畸变
export function imageUndistorted(data) {
  return request({
    url: '/qc/image-undistorted',
    method: 'post',
    data
  })
}

// QC保存视频图片
export function saveVideoImage(data) {
  return request({
    url: '/qc/save-video-image',
    method: 'post',
    data
  })
}



