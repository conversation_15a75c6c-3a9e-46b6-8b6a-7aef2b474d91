import request from '@/utils/request'

// 前台用户模板导入
export function userTemplateImport(data) {
  return request({
    url: '/user/template-import',
    method: 'post',
    data
  })
}

// 前台用户模板下载
export function userTemplateDownload(data) {
  return request({
    url: '/user/template-download',
    method: 'post',
    data
  })
}

// 前台用户密码重置
export function userReset(data) {
  return request({
    url: '/user/reset',
    method: 'post',
    data
  })
}

// 前台用户添加
export function userCreate(data) {
  return request({
    url: '/user/create',
    method: 'post',
    data
  })
}

// 前台用户删除
export function userDelete(data) {
  return request({
    url: '/app-user/delete',
    method: 'post',
    data
  })
}

// 前台用户禁用
export function userChangeStatus(data) {
  return request({
    url: '/app-user/switch-status',
    method: 'post',
    data
  })
}

// 前台用户编辑
export function userEdit(data) {
  return request({
    url: '/app-user/edit',
    method: 'post',
    data
  })
}

// 前台用户查看
export function userOne(params) {
  return request({
    url: '/user/one',
    method: 'get',
    params
  })
}

// 后台用户锁定
export function userLock(data) {
  return request({
    url: '/user/freeze',
    method: 'post',
    data
  })
}

// 后台用户解锁
export function userUnlock(data) {
  return request({
    url: '/user/unfreeze',
    method: 'post',
    data
  })
}

