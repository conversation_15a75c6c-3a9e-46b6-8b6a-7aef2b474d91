import request from '@/utils/request'

// 后台用户添加
export function adminCreate(data) {
  return request({
    url: '/admin/create',
    method: 'post',
    data
  })
}

// 后台用户删除
export function adminDelete(data) {
  return request({
    url: '/admin/delete',
    method: 'post',
    data
  })
}

// 后台用户编辑
export function adminEdit(data) {
  return request({
    url: '/admin/edit',
    method: 'post',
    data
  })
}

// 后台用户查看
export function adminOne(params) {
  return request({
    url: '/admin/one',
    method: 'get',
    params
  })
}

// 后台角色查看
export function adminRoleOne(params) {
  return request({
    url: '/admin-role/one',
    method: 'get',
    params
  })
}

// 后台角色列表筛选
export function adminRoleAll(params) {
  return request({
    url: '/admin-role/all',
    method: 'get',
    params
  })
}

// 后台品牌菜单
export function adminRoleTenantMenu(params) {
  return request({
    url: '/admin-role/tenant-menu',
    method: 'get',
    params
  })
}

// 后台角色删除
export function adminRoleDelete(data) {
  return request({
    url: '/admin-role/delete',
    method: 'post',
    data
  })
}

// 后台角色编辑
export function adminRoleEdit(data) {
  return request({
    url: '/admin-role/edit',
    method: 'post',
    data
  })
}

// 后台角色新增
export function adminRoleCreate(data) {
  return request({
    url: '/admin-role/create',
    method: 'post',
    data
  })
}

// 后台用户密码重置
export function adminReset(data) {
  return request({
    url: '/admin/reset',
    method: 'post',
    data
  })
}

// 后台用户禁用、启用
export function adminDisable(data) {
  return request({
    url: '/admin/user-disable',
    method: 'post',
    data
  })
}

// 后台用户锁定
export function adminLock(data) {
  return request({
    url: '/admin/freeze',
    method: 'post',
    data
  })
}

// 后台用户解锁
export function adminUnlock(data) {
  return request({
    url: '/admin/unfreeze',
    method: 'post',
    data
  })
}
