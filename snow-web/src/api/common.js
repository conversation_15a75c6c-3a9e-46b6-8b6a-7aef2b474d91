// 品牌列表下拉筛选
import request from '@/utils/request'

export function tenantAll(params) {
  return request({
    url: '/tenant/all',
    method: 'get',
    params
  })
}
// 菜单相关的下拉列表
export function enumList(params) {
  return request({
    url: '/admin/backend/enum-list',
    method: 'get',
    params
  })
}

// 售点下拉筛选列表
export function storeList(params) {
  return request({
    url: '/store/store-list',
    method: 'get',
    params
  })
}

// 周期下拉筛选列表
export function cycleList(params) {
  return request({
    url: '/cycle/cycle-list',
    method: 'get',
    params
  })
}

// 售点渠道类型下拉刷选列表
export function storeChannelList(params) {
  return request({
    url: '/store-channel/sub-channel-list-select',
    method: 'get',
    params
  })
}

// 省市区下拉刷选列表
export function cityList(params) {
  return request({
    url: '/store/region',
    method: 'get',
    params
  })
}

// 异常门店下拉筛选列表
export function surveyErrorType(params) {
  return request({
    url: '/report-store-error/error-type',
    method: 'get',
    params
  })
}

// 异常门店下拉筛选列表
export function sceneSystemList(params) {
  return request({
    url: '/scene/system-list',
    method: 'get',
    params
  })
}

// 组织架构下拉列表
export function organizeTenant(params) {
  return request({
    url: '/organize/tenant',
    method: 'get',
    params
  })
}

// 售点状态下拉列表
export function storeStatus(params) {
  return request({
    url: '/store/store-status',
    method: 'get',
    params
  })
}

// 检查项目下拉列表
export function planProjectList(params) {
  return request({
    url: '/plan/project-list',
    method: 'get',
    params
  })
}

// sku项目列表
export function tenantProject(params) {
  return request({
    url: '/tenant/project-list',
    method: 'get',
    params
  })
}

// 场景列表下拉选项
export function sceneTypeTree(params) {
  return request({
    url: '/scene-type/tree',
    method: 'get',
    params
  })
}

export function backendIndex(params) {
  return request({
    url: '/backend/index',
    method: 'get',
    params
  })
}
export function backendSale(params) {
  return request({
    url: '/backend/sale',
    method: 'get',
    params
  })
}

export function getStoreListSelect(params) {
  return request({
    url: '/store/list-select',
    method: 'get',
    params
  })
}
// SKU 下拉
export function skuOptions(params) {
  return request({
    url: '/data-board/sku-options',
    method: 'get',
    params
  })
}
// 下拉菜单相关
export function backendMenu(params) {
  return request({
    url: '/backend/menu',
    method: 'get',
    params
  })
}
// bu列表筛选
export function buListAll(params) {
  return request({
    url: '/tenant-bu/all',
    method: 'get',
    params
  })
}

// 不合格原因
export function nopassReason(params) {
  return request({
    url: '/config/no-pass-dropdown',
    method: 'get',
    params
  })
}
// 供应商列表
export function supplierList(params) {
  return request({
    url: '/supplier/list',
    method: 'get',
    params
  })
}
// 安装任务状态-下拉框
export function configInstallDropdown(data) {
  return request({
    url: '/config/install-task-status-dropdown',
    method: 'post',
    data
  })
}
// 设备安装状态-下拉框
export function configStatusDropdown(params) {
  return request({
    url: '/config/device-install-status-dropdown',
    method: 'get',
    params
  })
}
// 设备类型-下拉框
export function configDevTypeDropdown(params) {
  return request({
    url: '/config/device-type-dropdown',
    method: 'get',
    params
  })
}

// 处理方式-下拉框
export function resolutionDropdown(params) {
  return request({
    url: '/config/resolution-dropdown',
    method: 'get',
    params
  })
}


