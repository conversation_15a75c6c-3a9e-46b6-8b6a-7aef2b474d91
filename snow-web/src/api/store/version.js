
import request from '@/utils/request'
// 版本创建
// export function storeVersionCreate(data) {
//     return request({
//         url: '/store-version/create',
//         method: 'post',
//         data
//     })
// }
// 版本编辑
export function storeVersionEdit(data) {
    return request({
        url: '/store/update-version',
        method: 'post',
        data
    })
}
// 切换版本
export function storeSwitchVersion(data) {
    return request({
        url: '/store-version/switch-version',
        method: 'post',
        data
    })
}
// 删除版本
export function storeDeleteVersion(data) {
    return request({
        url: '/store-model/delete',
        method: 'post',
        data
    })
}
// 删除版本
export function storeVersionDetail(params) {
    return request({
        url: '/store-model/one',
        method: 'get',
        params
    })
}
