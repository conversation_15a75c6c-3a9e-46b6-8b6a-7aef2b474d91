import request from '@/utils/request'

// 新增周期
export function cycleAdd(data) {
  return request({
    url: '/cycle/add',
    method: 'post',
    data
  })
}

// 周期导入
export function cycleImport(data) {
  return request({
    url: '/cycle/cycle-import',
    method: 'post',
    data
  })
}

// 周期删除
export function cycleDelete(data) {
  return request({
    url: '/cycle/delete',
    method: 'post',
    data
  })
}

// 周期编辑
export function cycleEdit(data) {
  return request({
    url: '/cycle/edit',
    method: 'post',
    data
  })
}

