import request from '@/utils/request'

// 售点列表
export function getStoreList(params) {
  return request({
    url: '/store/list',
    method: 'get',
    params
  })
}

// 售点信息
export function getStoreInfo(params) {
  return request({
    url: '/store/info',
    method: 'get',
    params
  })
}

// sku信息
export function getStoreSkuInfo(params) {
  return request({
    url: '/store/sku-info',
    method: 'get',
    params
  })
}

// 区域信息
export function getStoreAreaInfo(params) {
  return request({
    url: '/store/area-info',
    method: 'get',
    params
  })
}

// 售点详情-查看
export function storeOne(params) {
  return request({
    url: '/store/view',
    method: 'get',
    params
  })
}

// 售点-创建
export function storeCreate(data) {
  return request({
    url: '/store/create',
    method: 'post',
    data
  })
}

// 售点-编辑
export function storeEdit(data) {
  return request({
    url: '/store/edit',
    method: 'post',
    data
  })
}


// 售点详情-大屏使用
export function storeOneDashboard(params) {
  return request({
    url: '/store/one-dashboard',
    method: 'get',
    params
  })
}
// 售点详情-日历红点
export function salePhotoCount(params) {
  return request({
    url: '/store-scene-sale/get-sale-photo-count',
    method: 'get',
    params
  })
}
// 售点模型编辑
export function storeModelEdit(data) {
  return request({
    url: '/store/edit-store-model',
    method: 'post',
    data
  })
}








