import request from '@/utils/request'

// 拜访计划导入
export function visitPlanImport(data) {
  return request({
    url: '/survey/visit-plan-import',
    method: 'post',
    data
  })
}

// 拜访计划删除
export function surveyDelete(data) {
  return request({
    url: '/survey/delete',
    method: 'post',
    data
  })
}

// 拜访计划编辑
export function surveyEdit(data) {
  return request({
    url: '/survey/edit',
    method: 'post',
    data
  })
}

// 售点报告详情
export function storeReport(params) {
  return request({
    url: '/report/store-report',
    method: 'get',
    params
  })
}

// 场景报告详情
export function sceneReport(params) {
  return request({
    url: '/report/scene-report',
    method: 'get',
    params
  })
}

// 售点全景3d/2d
export function storeModel(params) {
  return request({
    url: '/store/get-model',
    method: 'get',
    params
  })
}

