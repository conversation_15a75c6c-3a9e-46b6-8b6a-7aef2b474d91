import request from '@/utils/request'

// 拜访计划导入
export function visitPlanImport(data) {
  return request({
    url: '/survey/visit-plan-import',
    method: 'post',
    data
  })
}

// 拜访计划删除
export function surveyDelete(data) {
  return request({
    url: '/survey/delete',
    method: 'post',
    data
  })
}

// 拜访计划复制
export function surveyCopy(data) {
  return request({
    url: '/survey/copy',
    method: 'post',
    data
  })
}

// 拜访计划编辑
export function surveyEdit(data) {
  return request({
    url: '/survey/edit',
    method: 'post',
    data
  })
}


