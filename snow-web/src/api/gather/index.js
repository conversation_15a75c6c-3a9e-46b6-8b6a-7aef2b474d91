import request from '@/utils/request'

//冰柜视频详情
export function gatherCoolerDetail(data) {
  return request({
    url: '/admin/video/detail',
    method: 'get',
    params: data
  })
}

//冰柜视频保存识别图片
export function gatherCoolerSave(data) {
  return request({
    url: '/admin/video/save-image-key',
    method: 'post',
    data
  })
}

//sku映射列表
export function getSkuMapList(data) {
  return request({
    url: '/admin/sku-map/list',
    method: 'get',
    params: data
  })
}

//sku列表
export function getSkuList(data) {
  return request({
    url: '/admin/sku/list',
    method: 'get',
    params: data
  })
}

//销量记录添加、编辑
export function createSale(data) {
  return request({
    url: '/admin/sale/create',
    method: 'post',
    data
  })
}

//视频删除
export function deleteVideo(data) {
  return request({
    url: '/admin/video/delete',
    method: 'post',
    data
  })
}

//销量删除
export function deleteSale(data) {
  return request({
    url: '/admin/sale/delete',
    method: 'post',
    data
  })
}

//获取销量详情
export function getSaleDetail(data) {
  return request({
    url: '/admin/sale/detail',
    method: 'get',
    params: data
  })
}

//上传文件
export function uploadImageFile(data) {
  return request({
    url: '/admin/file/upload',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    }
  })
}

//视频抽帧
export function getVideoFrames(data) {
  return request({
    url: '/admin/video/get-video-frames',
    method: 'get',
    params: data,
  })
}

//视频抽帧
export function getNextTask(data) {
  return request({
    url: '/admin/video/get-next-id',
    method: 'get',
    params: data,
  })
}

