import request from '@/utils/request'

// 售点设备组（设备）列表
export function devWithScene(params) {
  return request({
    url: '/store/device-list',
    method: 'get',
    params
  })
}

// 设备组绑定售点场景
export function devWithSceneBind(data) {
  return request({
    url: '/store-scene/bind-device-group',
    method: 'post',
    data
  })
}

// 设备组绑定售点场景
export function deviceOne(params) {
  return request({
    url: '/device-group/one',
    method: 'get',
    params
  })
}
// 设备组网关感知数据
export function deviceOneSensorData(params) {
  return request({
    url: '/device-group/sensor-data',
    method: 'get',
    params
  })
}


// 删除设备
export function delDeviceGroup(data) {
  return request({
    url: '/device/delete',
    method: 'post',
    data
  })
}

// 设备组创建
export function addDevice(data) {
  return request({
    url: '/device-group/create',
    method: 'post',
    data
  })
}
// 设备组创建
export function editDevice(data) {
  return request({
    url: '/device/save-asset-code',
    method: 'post',
    data
  })
}
// 设备组创建
export function switchStatus(data) {
  return request({
    url: '/device/switch-status',
    method: 'post',
    data
  })
}
// 获取设备资产编号
export function getAssetCode(params) {
  return request({
    url: '/device/get-asset-code',
    method: 'get',
    params
  })
}





