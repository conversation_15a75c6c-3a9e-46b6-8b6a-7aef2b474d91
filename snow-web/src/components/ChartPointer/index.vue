<template>
  <div>
    <div id="main" class="caves__pointer" style="width: 100%; height: 170px;"></div>
  </div>

</template>
<script>
import * as echarts from 'echarts';
import pinterIcon from '@/assets/images/point.png'

export default {
  name: "eCharts",
  data() {
    return {
      baseInfo: {},
      pinterIcon,
      count: 0,
      myChart: null,
      axisPointerTime: null
    };
  },
  mounted() {
    this.myChart = echarts.init(document.getElementById('main'));
  },
  // watch:{
  //   axisPointerTime(e){
  //     console.log(e, 2222)
  //   },
  // },
  // computed{
  //   axisPointerVlaue
  // },
  methods: {
    /**
     * 更新指针
     * */
    updateIndex(e) {
      this.axisPointerTime = e
      this.myChart.setOption({
        xAxis: {
          axisPointer: {
            value: e
          }
        }
      })
    },
    /**
     * 初始化数据
     * */
    getDetail(e) {
      this.baseInfo = e
      const {salesChart, visitorChart, date, imageIndex} = e
      const startTime = new Date(date + ' 00:00:00');
      const endTime = new Date(date + ' 24:00:00');
      if(!salesChart){
        return
      }
      let defAxisPointerTime = salesChart.length && salesChart[imageIndex][0]
      let option = {
        backgroundColor: 'rgba(41, 90, 211, 0.40)',
        grid: {
          top: 30, // 顶部边距
        },
        // tooltip: {
        //   show: false,
        //   // 鼠标指上去改变位置
        //   triggerOn: 'none',
        // },
        tooltip: {
          show: false,
          triggerOn: 'none',
          // trigger: 'axis', //触发类型；轴触发，axis则鼠标hover到一条柱状图显示全部数据，item则鼠标hover到折线点显示相应数据，
          // axisPointer: {  //坐标轴指示器，坐标轴触发有效，
          //   type: 'line', //默认为line，line直线，cross十字准星，shadow阴影
          //   crossStyle: {
          //     color: '#fff'
          //   }
          // }
        },
        xAxis: {
          boundaryGap: false,
          type: "time",
          min: startTime, // 开始时间时间戳
          max: endTime, // 结束时间时间戳 如果实际的最大日期不确定，也可以不设定这个属性
          // x轴的字
          axisLabel: {
            interval: 2,
            show: true,
            color: '#D4D4D4', // 设置字体颜色为#333
            fontSize: 12, // 设置字体大小为12
            formatter: function (params, index) {
              let date = new Date(params)
              let hours = date.getHours()
              let minutes = date.getMinutes()
              if (hours == 0 && minutes == 0) {
                return echarts.format.formatTime('yyyy-MM-dd', params);
              }
              return echarts.format.formatTime('hh:mm', params);
            }
          },
          axisPointer: {
            triggerOn: 'none',
            // z: 1,
            // triggerTooltip: true,
            // 默认时间
            value: defAxisPointerTime,
            snap: true,
            // animation: true,
            lineStyle: {
              type: 'solid',
              color: '#ED6718',
              width: 2
            },
            // 指针数据
            label: {
              show: false,
            },
            // triggerOn: 'none',
            // 指针样式
            handle: {
              throttle: 60,
              show: true,
              size: 20,
              margin: 0,
              icon: 'image://' + this.pinterIcon,
              color: '#7581BD',
              // trigger: 'axis',
            }
          },
          splitLine: {
            show: false,
          }
        },
        yAxis: {
          name: this.$t('store.trafficSales') || '人流量/销量',
          nameLocation: 'end',
          nameTextStyle: {
            color: '#D4D4D4',
            align: 'center',
            fontSize: 12,
            lineHeight: 2,
            verticalAlign: 'middle',
          },
          axisTick: {
            show: false // 不显示坐标轴刻度线
          },
          axisLine: {
            show: true, // 不显示坐标轴线
          },
          axisLabel: {
            show: false, // 不显示坐标轴上的文字
          },
          splitLine: {
            show: false // 不显示网格线
          },
        },
        dataZoom: [
          {
            // rangeMode: ['value'],
            // startValue: 1,
            // endValue: 7,
            // xAxisIndex: 0,
            showDataShadow: false, // 数据阴影
            realtime: false,// 是否实时更新
            filterMode: 'weakFilter', // empty 不会影响其他数据
            textStyle: {
              color: '#D4D4D4',
            },
            height: 15,
            type: 'slider',
            start: 0,
            end: 100 // 结束百分比
          },
        ],
        // // 自动播放设置
        // animation: true,
        // animationDuration: 1000,
        // animationEasing: 'cubicOut',
        // // 自动播放速度
        // animationDelay: function (idx) {
        //   return idx * 10;
        // },
        series: [
          {
            legendHoverLink: false,
            // selectedMode: 'single',
            // select: {
            //   disabled: true,
            // },
            // axisPointer: {
            //   snap: false // 禁用自动吸附
            // },
            type: 'line',
            smooth: true,
            silent: true, // 不相应任何事件
            // symbol: 'circle',
            symbol: 'none',
            symbolSize: 5,
            sampling: 'average',
            itemStyle: {
              color: '#145DC7'
            },
            stack: 'a',
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: '#538BFF'
                },
                {
                  offset: 1,
                  color: '#173B83'
                }
              ])
            },
            lineStyle: {
              width: 1 // 设置线的粗细
            },
            data: visitorChart,
            z: 9,
          },
          {
            axisPointer: {
              type: 'cursor' // 在柱状图中显示手柄
            },
            type: 'bar',
            barGap: '100%',
            data: salesChart,
            barMaxWidth: 5,
            itemStyle: {
              color: '#18EDD0'
            },
            large: true,
            z: 10, // 设置z属性，控制折线图在后面
            // barWidth: 10,
          },
        ]
      };
      this.axisPointerTime = defAxisPointerTime
      option && this.myChart.setOption(option);
      // 获取拖拽进度
      const myChart = this.myChart
      this.myChart.on('dataZoom', (event) => {
        // 获取时间范围
        let startValue = myChart.getModel().option.dataZoom[0].startValue;
        let endValue = myChart.getModel().option.dataZoom[0].endValue;

        startValue = echarts.format.formatTime('yyyy-MM-dd hh:mm:ss', startValue)
        endValue = echarts.format.formatTime('yyyy-MM-dd hh:mm:ss', endValue)
        let start = echarts.format.formatTime('hh:mm:ss', startValue)
        let end = echarts.format.formatTime('hh:mm:ss', endValue)
        this.$emit('filterRange', {startValue, endValue, start, end})
      })
      // 拖拽指针获取当前绑定的value值
      this.myChart.getZr().on('mousemove', (params) => {
        let axisPointerValue = myChart.getModel().option.xAxis[0].axisPointer.value
        axisPointerValue = echarts.format.formatTime('yyyy-MM-dd hh:mm:ss', axisPointerValue)
        if (axisPointerValue != this.axisPointerTime) {
          // 数据更新发送事件
          this.$emit('updateImageIndex', {axisPointerValue})
        }
        this.axisPointerTime = axisPointerValue
      })
    },
  },
};
</script>
<style lang="scss" scoped>
#main {
  canvas {
    border-radius: 4px;
  }
}
</style>
