<template>
  <div ref="myKonva" :style="{width:konvaWidth || '100%'} ">
    <div id="map" ref="map" :style="{transform:`scale(${originalScale /100})`}" style="transform-origin: 0% 0%;"/>
  </div>
</template>
<script>
import Konva from 'konva'
import fa from 'element-ui/src/locale/lang/fa'

export default {
  name: 'my-konva',
  props: {
    imgSrc: {
      type: String,
      default: ''
    },
    originalScale: {
      type: [String, Number],
      default: 1
    }
  },
  data() {
    return {
      isDialog: false, // 场景面板配置是否打开
      stage: null,
      layer: null,
      shape: null,
      drawing: false, // 一开始不能绘画
      currentDrawingShape: null, // 现在绘画的图形
      pointStart: [], // 记录鼠标按下的起始坐标
      polygonPoints: [], // 存储绘画多边形各个顶点的数组
      konvaWidth: ''
    }
  },
  mounted() {
    // let points = [1514.643628509719, 514.7300215982721, 2192.656587473002, 496.0691144708423, 2074.4708423326133, 1584.622030237581, 1551.9654427645787, 1640.6047516198703]
    // setTimeout(() => {
    //   const poly = new Konva.Line({
    //     name: 'poly',
    //     points: points,
    //     fill: 'rgba(255,0,0,0.3)',
    //     stroke: 'red',
    //     strokeWidth: 2,
    //     draggable: true,
    //     lineCap: 'round',
    //     lineJoin: 'round',
    //     closed: true, // 是否需要闭合
    //     strokeScaleEnabled: true
    //   })
    //   // this.currentDrawingShape = poly
    //   this.layer.add(poly)
    //   this.layer.draw()
    // }, 2000)
  },
  methods: {
    /**
     * 删除画布标注内容
     * */
    clearCanvas() {
      // 如果有，就移除舞台上唯一一个的变形框
      if (this.stage.find('Transformer').length != 0) {
        this.stage.find('Transformer')[0].destroy()
      }
      // 如果不在绘画且舞台上的多边形被选中
      if (this.stage.find('Circle').length != 0) {
        var circlePoints = this.stage.find('Circle')
        for (var i = 0; i < circlePoints.length; i++) {
          // 隐藏顶点
          this.stage.find('Circle').forEach((element) => {
            element.hide()
          })
        }
      }
      this.layer.draw()
      if (this.poly) {
        this.poly.destroy()
        this.stage.draw()
      }
      this.polygonPoints = []
      this.pointStart = []
      this.currentDrawingShape = null
      this.$forceUpdate()
    },
    init(points) {
      this.konvaWidth = this.$refs.myKonva.clientWidth + 'px'
      const imageObj = new Image()
      imageObj.src = this.imgSrc
      imageObj.onload = () => {
        this.$emit('imageLoad', imageObj)
        this.scaleFactor = (this.$refs.myKonva.clientWidth / imageObj.width)
        this.$refs.map.style.width = (this.$refs.myKonva.clientWidth) * 2 + 'px'
        this.$refs.map.style.height = ((imageObj.height * this.scaleFactor).toFixed(2)) * 2 + 'px'
        this.initKonvaStage(imageObj, points)
        this.$nextTick(() => {
          this.$refs.myKonva.scrollTo({
            left: (this.$refs.myKonva.clientWidth / 2), // 指定滚动到的位置
            top: (this.$refs.myKonva.clientHeight / 2)  // 指定滚动到的位置
          })
        })
      }
    },
    dialogConfig(e) {
      this.isDialog = e
    },
    /**
     * 清空点数据
     * */
    clearPoint() {
      this.pointStart = []
      this.polygonPoints = []
    },
    /**
     *初始化konva舞台
     */
    initKonvaStage(imageObj, points) {
      if (this.stage) {
        this.stage.destroy()
        this.poly && this.poly.destroy()
        this.pointStart = []
        this.polygonPoints = []
        this.drawing = false
        this.layer.draw()
      }
      // 1实例化stage层
      this.stage = new Konva.Stage({
        container: 'map',
        width: this.$refs.map.clientWidth,
        height: this.$refs.map.clientHeight
      })
      this.stage.container().style.cursor = 'crosshair'
      // 2实例化layer层
      this.layer = new Konva.Layer()

      // imageObj的this是imagedom对象，不是vc
      var vc_this = this
      // 3实例化shape层
      vc_this.shape = new Konva.Image({
        x: vc_this.stage.width() / 4,
        y: vc_this.stage.height() / 4,
        width: vc_this.stage.width() / 2,
        height: vc_this.stage.height() / 2,
        image: imageObj
      })
      // 4将layer层添加到stage层
      vc_this.stage.add(vc_this.layer)
      // 5将shape层添加到layer层
      vc_this.layer.add(vc_this.shape)

      // 给***舞台***绑定事件
      // 鼠标按下
      this.stage.on('mousedown', (e) => {
        if (!this.isDialog) {
          this.$message.error('请先选择某个场景或者创建场景')
          return
        }
        // 图形起始点只能在图片层上，移除变形框
        if (e.target === this.shape || e.target === this.stage) {
          // 如果有，就移除舞台上唯一一个的变形框
          if (this.stage.find('Transformer').length != 0) {
            this.stage.find('Transformer')[0].destroy()
          }
          // 如果不在绘画且舞台上的多边形被选中
          if (!this.drawing && this.stage.find('Circle').length != 0) {
            var circlePoints = this.stage.find('Circle')
            for (var i = 0; i < circlePoints.length; i++) {
              if (circlePoints[i].visible()) {
                // 隐藏顶点
                this.stage.find('Circle').forEach((element) => {
                  element.hide()
                })
                return
              }
            }
          }
          this.layer.draw()
          // 开始初始绘画
          this.stageMousedown(e)
          return
        }
        // 允许后续点绘画在其他图形上
        if (this.drawing) {
          this.stageMousedown(e)
          return
        }
      })
      // 鼠标移动
      this.stage.on('mousemove', (e) => {
        if (this.drawing) {
          // 绘画中
          this.stageMousemove(e)
        }
      })
      // 鼠标放开
      this.stage.on('mouseup', (e) => {
        this.stageMouseup(e)
      })
      if (points && points.length) {
        for (let i = 0; i < points.length; i += 2) {
          let x = (points[i]) * this.scaleFactor + this.$refs.map.clientWidth / 4
          let y = (points[i + 1]) * this.scaleFactor + this.$refs.map.clientHeight / 4
          this.stageMousedown({ evt: { offsetX: x, offsetY: y } })
        }
        setTimeout(() => {
          this.dragEnd()
        }, 100)
      }
    },
    dragEnd() {
      this.currentDrawingShape.setAttr('points', this.polygonPoints)
      // 结束绘画多边形封闭
      this.drawing = false
      // 把现在的绘画对象更改为点和多边形合成的组
      this.currentDrawingShape = this.currentDrawingShape.getParent()
      this.stage.find('Circle').forEach((element) => {
        element.hide()
      })
      this.$emit('changeKonvaRange')
    },
    /**
     * 渲染底图已有的区域图层
     * */
    renderArea(passData) {
      this.initialPolygonPoints = []
      this.AllPolygonPoints = []
      this.AllPolygonPoints = [...passData.children]
      for (var i = 0; i < this.AllPolygonPoints.length; i++) {
        this.initialPolygonPoints.push(this.spArr(this.AllPolygonPoints[i].list, 2))
      }
      console.log(this.AllPolygonPoints, this.initialPolygonPoints, '初始处理数据之后渲染区域')
      // 进行渲染初始区域
      for (var j = 0; j < this.initialPolygonPoints.length; j++) {
        let pre = this.initialPolygonPoints[j]
        console.log(pre, j, '外层')
        let currentTool = this.toolObject[1]
        //拖拽组
        var group = new Konva.Group({
          name: currentTool.name + 'group',
          draggable: false
        })
        let polygonPoints = [...this.AllPolygonPoints[j].list]
        //绘画多边形
        this.drawPolygon(currentTool, polygonPoints, group)
        //添加多边形的边
        this.drawLine(currentTool, polygonPoints, group)
        this.layer.add(group)
        this.currentCancel = group
        for (var h = 0; h < pre.length; h++) {
          console.log(pre[h], '里层')
          let x = JSON.parse(JSON.stringify(pre[h][0]))
          let y = JSON.parse(JSON.stringify(pre[h][1]))
          //添加多边形的点
          this.drawCircle(currentTool, x, y, group, polygonPoints)
          //添加标签
          if (h == 0) {
            //添加标签
            var label = this.drawLabel(currentTool, x, y, group, this.label)
            label.hide()
          }
          // 使所有顶点在顶层显示
          this.stage.find('Circle').forEach(element => {
            element.moveToTop()
          })
          this.layer.draw()
        }
      }
      // 调用鼠标滚轮事件
      this.mouseWheel(this)
      // 调用舞台快捷键事件
      this.shortcutKey(this)
    },
    /**
     * 圆形
     * @param //x x坐标
     * @param //y y坐标
     */
    drawCircle(x, y) {
      const circle = new Konva.Circle({
        name: 'circle',
        x: x,
        y: y,
        radius: 5,
        visible: true, // 是否显示
        fill: 'red',
        draggable: false,
        strokeWidth: 0.5
      })
      var vc_this = this
      var xChange, yChange
      this.layer.add(circle)
      this.layer.draw()
      // 拖拽
      circle.on('dragstart', (e) => {
        var polyPoints = vc_this.currentDrawingShape
            .getChildren((element) => {
              return element.getClassName() === 'Line'
            })[0]
            .points()
        // 查找拖拽了多边形的哪个点
        for (var i = 0; i < polyPoints.length; i += 2) {
          if (
              circle.getAttr('x') == polyPoints[i] &&
              circle.getAttr('y') == polyPoints[i + 1]
          ) {
            xChange = i
            yChange = i + 1
            break
          }
        }
      })
      circle.on('dragmove', (e) => {
        // 更改拖拽多边形点的位置
        var polyPoints = vc_this.currentDrawingShape
            .getChildren((element) => {
              return element.getClassName() === 'Line'
            })[0]
            .points()
        polyPoints[xChange] = e.evt.offsetX - vc_this.currentDrawingShape.getAttr('x')
        polyPoints[yChange] = e.evt.offsetY - vc_this.currentDrawingShape.getAttr('y')
        vc_this.currentDrawingShape
            .getChildren((element) => {
              return element.getClassName() === 'Line'
            })[0]
            .points(polyPoints)
        this.polygonPoints = polyPoints
        vc_this.$emit('changeKonvaRange')
      })
      return circle
    },
    /**
     *多边形
     * @param points 多边形绘画的各个顶点，类型数组
     */
    drawPloygon(points) {
      const poly = new Konva.Line({
        name: 'poly',
        points: points,
        fill: 'rgba(255,0,0,0.3)',
        stroke: 'red',
        strokeWidth: 2,
        draggable: true,
        lineCap: 'round',
        lineJoin: 'round',
        closed: true, // 是否需要闭合
        strokeScaleEnabled: true
      })
      this.currentDrawingShape = poly
      this.layer.add(poly)
      this.layer.draw()
      var vc_this = this
      poly.on('mouseenter', (e) => {
        vc_this.stage.container().style.cursor = 'move'
      })
      poly.on('mouseleave', (e) => {
        vc_this.stage.container().style.cursor = 'crosshair'
      })
      poly.on('mousedown', (e) => {
        // 如果不是正在绘画图形时，可以显示顶点
        if (!vc_this.drawing) {
          vc_this.stage.container().style.cursor = 'move'
          // 设置现在绘画节点的对象为该多边形和顶点的组
          vc_this.currentDrawingShape = poly.getParent()
          // 先隐藏全部顶点
          vc_this.stage.find('Circle').forEach((element) => {
            element.hide()
            // 解绑第一个红色顶点的事件
            element.off('mousedown')
          })
          // 显示现在操作多边形的原来的顶点
          vc_this.currentDrawingShape
              .getChildren((element) => {
                return element.getClassName() === 'Circle'
              })
              .forEach((element) => {
                element.show()
                element.setAttr('draggable', true)
              })
          // 如果要让顶点和多边形一起拖拽，必须设置，多边形不能被拖拽
          poly.setAttr('draggable', false)
          poly.getParent().setAttr('draggable', true)
          // 使所有顶点在顶层显示
          vc_this.stage.find('Circle').forEach((element) => {
            element.moveToTop()
          })
          vc_this.layer.draw()
        } else {
          // 绘画时，鼠标移入多边形，设置组不可以拖动
          vc_this.stage.container().style.cursor = 'crosshair'
          poly.getParent().setAttr('draggable', false)
        }
      })
      poly.getParent().on('dragmove', (e) => {
        var updatePoints = []
        var polyPoints = poly.points()
        if (poly.getParent()) {
          for (var i = 0; i < polyPoints.length / 2; i++) {
            updatePoints.push(polyPoints[i * 2] + poly.getParent().x())
            updatePoints.push(polyPoints[i * 2 + 1] + poly.getParent().y())
          }
          this.polygonPoints = updatePoints
        }

        this.$emit('changeKonvaRange')
      })
      poly.getParent().on('dragend', (e) => {
        vc_this.stage.container().style.cursor = 'crosshair'
      })
      this.poly = poly
      return poly
    },
    /**
     * 在舞台上鼠标点下发生的事件
     * @param e 传入的event对象
     */
    stageMousedown(e) {
      if (!this.drawing && this.currentDrawingShape) {
        // this.currentDrawingShape.destroy()
        if (this.poly) {
          this.poly.destroy()
          this.stage.draw()
        }
        this.polygonPoints = []
        this.currentDrawingShape = null
      }
      // 如果数组长度小于2，初始化多边形和顶点，是它们成为一组,否则什么都不做
      if (this.polygonPoints.length < 2) {
        var x = e.evt.offsetX
        var y = e.evt.offsetY
        // 拖拽组
        var group = new Konva.Group({
          x: 0,
          y: 0,
          name: 'pointsAndPoly',
          draggable: false
        })
        // 添加多边形的点
        group.add(this.addPoint(e))
        // 绘画多边形
        this.polygonPoints = [x, y]
        group.add(this.drawPloygon(this.polygonPoints))
        // 使所有顶点在顶层显示
        this.stage.find('Circle').forEach((element) => {
          element.moveToTop()
        })
        this.group = group
        this.layer.add(group)
        this.stage.draw()
      } else {
        // 多边形增加顶点
        var x = e.evt.offsetX
        var y = e.evt.offsetY
        // group继续添加多边形的点
        this.currentDrawingShape.getParent().add(this.addPoint(e))
        this.polygonPoints.push(x)
        this.polygonPoints.push(y)
        // 绘画多边形
        this.currentDrawingShape.setAttr('points', this.polygonPoints)
        // 使所有顶点在顶层显示
        this.stage.find('Circle').forEach((element) => {
          element.moveToTop()
        })
        this.stage.draw()
        if (this.polygonPoints.length >= 8) {
          this.drawing = false
          this.currentDrawingShape = this.currentDrawingShape.getParent()
          this.stage.find('Circle').forEach((element) => {
            element.hide()
          })
          this.$emit('changeKonvaRange')
          return
        }
      }
      this.drawing = true
    },
    /**
     * 鼠标在舞台上移动事件
     * @param e 传入的event对象
     */
    stageMousemove(e) {
      // 多边形初始化后，如果数组长度大于2，鼠标移动时，实时更新下一个点
      if (this.polygonPoints.length >= 2) {
        var x = e.evt.offsetX
        var y = e.evt.offsetY
        var tempPoints = this.polygonPoints.concat()
        tempPoints.push(x)
        tempPoints.push(y)
        this.currentDrawingShape.setAttr('points', tempPoints)
      }
      this.layer.draw()
    },
    /**
     * 鼠标在舞台上移动事件
     * @param e 传入的event对象
     */
    stageMouseup(e) {
      this.layer.draw()
    },
    /**
     * 增加多边形顶点
     * @param e 传入的event对象
     */
    addPoint(e) {
      if (this.polygonPoints.length == 0) {
        var vc_this = this
        // 将第一个点标红,并显示
        return this.drawCircle(e.evt.offsetX, e.evt.offsetY)
            .setAttrs({
              fill: 'red'
            })
            .show()
            .on('mousedown', (e) => {
              // 点击第一个红点，绘画多边形结束
              // 绘画多边形
              this.currentDrawingShape.setAttr('points', this.polygonPoints)
              // 结束绘画多边形封闭
              vc_this.drawing = false
              // 把现在的绘画对象更改为点和多边形合成的组
              this.currentDrawingShape = this.currentDrawingShape.getParent()
            })
      } else {
        // 绘画点并显示
        return this.drawCircle(e.evt.offsetX, e.evt.offsetY).show()
      }
    },
    getPoints() {
      return this.polygonPoints.map((item, index) => {
        const points = index % 2 ? item - this.$refs.map.clientHeight / 4 : item - this.$refs.map.clientWidth / 4
        return points / this.scaleFactor
      })
    }
  }
}
</script>
<style>
#map {
  width: 100%;
}
</style>

