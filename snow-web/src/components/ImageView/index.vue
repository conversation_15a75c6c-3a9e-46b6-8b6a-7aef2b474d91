<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="80vw"
      top="5vh"
      class="box-form-item bu-form"
      title=""
      :visible.sync="isShow"
      custom-class="image-view-dialog"
      center
    >
      <div style="">
        <span class="el-image-viewer__btn el-image-viewer__prev"
              @click="onSwitch(index== 0 ? imageList.length -1 : index -1)"><i class="el-icon-arrow-left"/></span>
        <span class="el-image-viewer__btn el-image-viewer__next"
              @click="onSwitch(index == imageList.length -1 ? 0 : index + 1)"><i class="el-icon-arrow-right"/></span>
        <el-row :gutter="20">
          <el-col :span="4">
            <div style="height: 50px;width: 50px;"/>
          </el-col>
          <el-col :span="16">
            <div ref="imgView" style="height: 69vh;overflow: scroll;margin-top: -20px;background: #f1f1f1">
              <img :src="srcList[index]" alt="无效图片" :style="imgStyle" style="height: auto;"
                   @mousedown="handleMouseDown">
            </div>
          </el-col>
        </el-row>
        <div style="text-align: center;margin-top: 20px;">
          <div class="el-image-viewer__btn el-image-viewer__actions"
               style="display: inline-block;position: inherit;transform:translateX(0px);margin-right: 20px;">
            <div class="el-image-viewer__actions__inner">
              <i class="el-icon-zoom-out" @click="handleActions('zoomOut')"/>
              <i class="el-icon-zoom-in" @click="handleActions('zoomIn')"/>
              <i class="el-image-viewer__actions__divider"/>
              <i :class="mode.icon" @click="toggleMode"/>
              <i class="el-image-viewer__actions__divider"/>
              <i class="el-icon-refresh-left" @click="handleActions('anticlocelise')"/>
              <i class="el-icon-refresh-right" @click="handleActions('clocelise')"/>
            </div>
          </div>
        </div>
      </div>

    </el-dialog>
  </div>
</template>
<script>
import {on, off} from 'element-ui/src/utils/dom'
import {rafThrottle} from 'element-ui/src/utils/util'

const Mode = {
  CONTAIN: {
    name: 'contain',
    icon: 'el-icon-full-screen'
  },
  ORIGINAL: {
    name: 'original',
    icon: 'el-icon-c-scale-to-original'
  }
}
export default {
  props: {
    model: {
      type: String,
      default: '2'
    }
  },
  data() {
    return {
      index: 0,
      srcList: [],
      imageList: [],
      isShow: false,
      mode: Mode.CONTAIN,
      isZoom: true,
      transform: {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        imageWidth: 300,
        imageHeight: 300,
        enableTransition: false
      }
    }
  },
  computed: {
    imgStyle() {
      const {scale, deg, offsetX, offsetY, enableTransition, imageHeight, imageWidth} = this.transform
      const style = {
        transform: `scale(1) rotate(${deg}deg)`,
        transition: enableTransition ? 'transform .3s' : '',
        'margin-left': `${offsetX}px`,
        'margin-top': `${offsetY}px`,
        width: `${imageWidth * scale}px`,
        height: `${imageHeight * scale}px`
      }
      return style
    }
  },
  watch: {
    index: {
      handler: function () {
        this.reset()
      }
    }
  },
  methods: {
    showImage(imageList, index) {
      const arr = []
      this.index = index
      imageList.map((item) => {
        arr.push(item)
      })
      this.imageList = imageList
      this.srcList = arr
      this.isShow = true
      this.reset()
    },
    onSwitch(val) {
      this.index = val
    },
    prev() {
      if (this.activeIndex == 0) {
        this.activeIndex = this.imageList.length - 1
      } else {
        this.activeIndex = this.activeIndex - 1
      }
      this.changeActiveIndex()
    },
    next() {
      if (this.activeIndex == this.imageList.length - 1) {
        this.activeIndex = 0
      } else {
        this.activeIndex = this.activeIndex + 1
      }
      this.changeActiveIndex()
    },
    changeActiveIndex() {
      let i = 0
      this.imageList.map((item, itemindex) => {
        if (itemindex == this.activeIndex) {
          this.index = i
        }
        i = i + item.image.length
      })
    },
    handleActions(action, options = {}) {
      if (this.loading) return
      const {zoomRate, rotateDeg, enableTransition} = {
        zoomRate: 0.1,
        rotateDeg: 90,
        enableTransition: true,
        ...options
      }
      const {transform} = this
      switch (action) {
        case 'zoomOut':
          if (transform.scale > 0.1) {
            transform.scale = parseFloat((transform.scale - zoomRate).toFixed(3))
          }
          this.isZoom = true
          break
        case 'zoomIn':
          transform.scale = parseFloat((transform.scale + zoomRate).toFixed(3))
          this.isZoom = true
          break
        case 'clocelise':
          transform.deg += rotateDeg
          this.isZoom = false
          break
        case 'anticlocelise':
          this.isZoom = false
          transform.deg -= rotateDeg
          break
      }
      transform.enableTransition = enableTransition
    },
    /**
     * @拖拽
     */
    handleMouseDown(e) {
      if (this.loading || e.button !== 0) return

      const {offsetX, offsetY} = this.transform
      const startX = e.pageX
      const startY = e.pageY
      this._dragHandler = rafThrottle(ev => {
        this.transform.offsetX = offsetX + ev.pageX - startX
        this.transform.offsetY = offsetY + ev.pageY - startY
      })
      on(document, 'mousemove', this._dragHandler)
      on(document, 'mouseup', ev => {
        off(document, 'mousemove', this._dragHandler)
      })

      e.preventDefault()
    },
    toggleMode() {
      if (this.loading) return
      const modeNames = Object.keys(Mode)
      const modeValues = Object.values(Mode)
      const index = modeValues.indexOf(this.mode)
      const nextIndex = (index + 1) % modeNames.length
      this.mode = Mode[modeNames[nextIndex]]
      this.reset()
    },
    reset() {
      this.transform = {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        imageWidth: 300,
        imageHeight: 300,
        enableTransition: false
      }

      const image = new Image()
      image.src = this.srcList[this.index]

      image.onload = () => {
        const clientWidth = this.$refs.imgView.clientWidth
        const clientHeight = this.$refs.imgView.clientHeight
        let imgWidth = image.width
        let imgHeight = image.height
        if (imgHeight > clientHeight) {
          imgWidth = (clientHeight / imgHeight) * imgWidth
          imgHeight = clientHeight
        }
        if (imgWidth > clientWidth) {
          imgHeight = clientWidth / imgWidth * imgHeight

          imgWidth = clientWidth
        }
        this.transform.imageHeight = imgHeight
        this.transform.imageWidth = imgWidth
      }
    }
  }
}
</script>
<style>
.image-view-dialog {
  max-width: 1200px;
}

</style>
