<template>
  <div class="digital__box">
    <canvas :ref="refObj" :style="{ width: canvasW+'px', height: canvasH + 'px' }"></canvas>
  </div>
</template>

<script>
// import {fabric} from "fabric";
// import {deepJSON} from 'helper'

export default {
  props: {
    refObj: {
      type: String,
      default: 'figure'
    },
    canvasW: {
      type: Number | String,
      default: 320
    },
    canvasH: {
      type: Number | String,
      default: 600
    },
  },
  data() {
    return {
      disLeft: 0,
      disTop: 0,
      scale: 0,
      // modelData: {
      //     'model_2d': 'http://snapshot-1255412942.cos.ap-shanghai.myqcloud.com/store-scanner_prod/b7ba5e83-d206-446f-a665-71501ada1186.jpg',
      //     points: [
      //         {
      //             type: 1,
      //             point: [
      //                 { x: 100, y: 100 },
      //                 { x: 200, y: 100 },
      //                 { x: 200, y: 200 },
      //                 { x: 150, y: 250 },
      //                 { x: 100, y: 200 },
      //             ]
      //         },{
      //             type: 2,
      // point: [
      //     { x: 200, y: 200 },
      //     { x: 300, y: 300 },
      //     { x: 400, y: 600 },
      //     { x: 200, y: 300 },
      //     { x: 100, y: 200 },
      // ]
      //         }
      //     ]
      // },
      modelData: {},
      canvas: null,
    }
  },
  mounted() {
    // this.getDetail()
  },
  methods: {
    deepJSON(e) {
      return JSON.parse(JSON.stringify(e))
    },
    /**
     * 获取数据
     * */
    getDetail(e, sceneTypeItem) {
      // TODO 如果改的话需要调整
      // this.$refs.planeFigure1.highlight(sceneTypeItem.scene_type, true)
      e.sceneTypeItem = sceneTypeItem || false
      this.modelData = this.deepJSON(e)
      if (this.canvas) {
        this.canvas.dispose() // 销毁画布实例
      }
      setTimeout(() => {
        this.fabricInit()
      }, 10)
    },
    /**
     * 设置高亮
     * type 唯一标识
     * */
    highlight(type) {
      Object.keys(this.modelData.polygon).filter(v => {
        if (this.modelData.polygon[v]) {
          this.modelData.polygon[v].visible = type == v ? true : false
        }
      })
      this.canvas.renderAll()
      // if(this.modelData.polygon && this.modelData.polygon[type]){
      //     this.modelData.polygon[type].visible = visible
      //     this.canvas.renderAll()
      // }
    },
    /**
     *  绘制多边形
     */
    fabricPolygon(point, type, visible) {
      let arr = point.map(v => {
        v.x = v.x * this.scale + this.disLeft
        v.y = v.y * this.scale + this.disTop
        return v
      })
      let fill = 'rgba(216,216,216,.5)'
      let stroke = '#B2B2B2'
      if (visible) {
        fill = 'rgb(207,31,41,.5)'
        stroke = '#CF1F29'
      }
      const polygon = new fabric.Polygon(arr, {
        fill,
        stroke,
        random: type, // 唯一标识
        strokeWidth: 1,
        selectable: false,
        visible: true,
      });
      if (!this.modelData.polygon) {
        this.modelData.polygon = {}
      }
      this.modelData.polygon[type] = polygon
      this.canvas.add(polygon);
    },

    /**
     * 初始化画布
     * 以门的宽高作为画布尺寸 多扇门则计算总宽高（取最大值）
     * 其他尺寸则减掉门的尺寸
     */
    fabricInit() {
      let height = this.canvasH
      let width = this.canvasW
      let def = {
        width,
        height,
        // backgroundColor: '#fff',
      }
      const url = this.modelData['model_2d']
      let canvas = this.canvas = new fabric.Canvas(this.$refs[this.refObj], def)
      fabric.Image.fromURL(url, oImg => {
        let scale = this.scale = canvas.height / oImg.height
        let left = (canvas.width - oImg.width * scale) / 2
        let top = (canvas.height - oImg.height * scale) / 2
        this.disLeft = left
        this.disTop = top
        canvas.setBackgroundImage(oImg, canvas.renderAll.bind(canvas), {
          top,
          left,
          scaleX: scale,
          scaleY: scale
        });
        // 预画高亮
        const {sceneTypeItem, points} = this.modelData
        if (points) {
          this.modelData.points.filter(v => {
            if (sceneTypeItem) {
              if (v.scene_type == sceneTypeItem.scene_type) {
                this.fabricPolygon(v.point, v.scene_type, true)
              } else {
                this.fabricPolygon(v.point, v.scene_type, false)
              }
            } else {
              this.fabricPolygon(v.point, v.scene_type, false)
            }
          })
        }
      })
      // 放大缩小
      canvas.on('mouse:wheel', opt => {
        let delta = opt.e.deltaY // 滚轮，向上滚一下是 -100，向下滚一下是 100
        let zoom = canvas.getZoom() // 获取画布当前缩放值
        zoom *= 0.999 ** delta
        if (zoom > 20) zoom = 20
        if (zoom < 0.01) zoom = 0.01
        // 以左上角为原点
        // this.canvas.setZoom(zoom)
        // 以鼠标所在位置为原点缩放
        canvas.zoomToPoint(
          { // 关键点
            x: opt.e.offsetX,
            y: opt.e.offsetY
          },
          zoom
        )
        opt.e.preventDefault()
        opt.e.stopPropagation()
      })
      // //鼠标按下事件
      let panning = false
      canvas.on("mouse:down", e => {
        panning = true;
        canvas.selection = false;
        // 点击具体某个场景，进行跳转
        if (e.target) {
          this.$emit("nodeConfigHandler", {
            param: {
              random: e.target.random
            },
            component: "change_scene_tab"
          })
        }
      });
      //鼠标抬起事件
      canvas.on("mouse:up", function (e) {
        panning = false;
        canvas.selection = true;
      });
      // 移动画布事件
      canvas.on("mouse:move", function (e) {
        if (panning && e && e.e) {
          let delta = new fabric.Point(e.e.movementX, e.e.movementY);
          canvas.relativePan(delta);
        }
      })
      canvas.renderAll()

    }
  }
}
</script>

<style lang="scss">
.digital__box {
  // overflow: scroll;
  text-align: center;

  .canvas-container {
    margin: 0 auto
  }
}
</style>
