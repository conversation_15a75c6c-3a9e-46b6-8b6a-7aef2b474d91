<template>
  <div :class="className" :style="{ height: height, width: width }"/>
</template>

<script>
import * as echarts from 'echarts'

require('echarts/theme/macarons') // echarts theme
// import resize from '@/utils/resize'

export default {
  // mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      // 1650 160
      // 1920 ?
      default: '160px'
    }
  },
  data() {
    return {
      itemStyleMap: [
        {
          start: '#3371FF',
          end: '#447DFF'
        },
        {
          start: '#FF9903',
          end: '#FFF6D2'
        },
        {
          start: '#28FF9D',
          end: '#047140'
        },
        {
          start: '#00ABFF',
          end: '#00F4F8'
        }
      ],
      chart: null,
      resultData: {}
    }
  },
  mounted() {
    // this.$nextTick(() => {
    //     this.initChart()
    // })
  },
  beforeDestroy() {
    this.clear()
  },
  methods: {
    /**
     * 清空
     * */
    clear() {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    },
    /**
     * 初始化
     * */
    initChart(resultData) {
      this.clear()
      this.resultData = resultData
      this.chart = echarts.init(this.$el, 'macarons')
      const data = resultData.list.filter((v, index) => {
        if (v.value) {
          v.value = (v.value * 1).toFixed(1)
        }
        v.itemStyle = {
          color: new echarts.graphic.LinearGradient(
            0, 0, 1, 0,
            [
              {offset: 0, color: this.itemStyleMap[index] && this.itemStyleMap[index].start || '#3371FF'},
              {offset: 1, color: this.itemStyleMap[index] && this.itemStyleMap[index].end || '#447DFF'}
            ]
          )
        }
        return v
      })
      this.chart.setOption({
        // 鼠标点击上
        tooltip: {
          trigger: 'item',
          // formatter: '{a} <br/>{b} : {c} ({d}%)'
          formatter: function (params) {
            const item = params.data
            return `${item.name}：${item.value} /${item.total_score}`
          },
        },
        title: {
          text: this.resultData.total_score,
          // text: '20.2',
          top: '39%',
          left: '48%',
          textAlign: 'center',
          textStyle: {
            padding: 10,
            height: 30,
            color: '#fff',
            fontFamily: 'Helvetica-BoldOblique, Helvetica',
            fontSize: '20px',
            textAlign: 'center',
            lineHeight: 30
          }
        },
        //   legend: {
        //     left: 'center',
        //     bottom: '10',
        //     // 展示数据
        //     data: ['排面', '铺货', '冰柜', '渠道活动']
        //   },
        series: [
          {
            name: 'iRED Result',
            type: 'pie',
            // roseType: 'radius',
            // radius: [32, 90],
            // radius: [32, 90],
            radius: ['40%', '100%'],
            label: {
              normal: {
                // formatter: '{b|{b}}\n{c} /{c}',
                formatter: function (params) {
                  const item = params.data
                  return `${item.name}\n${item.value} /${item.total_score}`
                },
                color: '#fff',
                rich: {//定义不同地方的文字的字体大小和颜色
                  b: {
                    color: '#D4D4D4',
                    fontSize: 10,
                    letterSpacing: 1,
                    lineHeight: 15,
                  },
                },
              },
            },
            // hoverOffset: 2,
            // startAngle: 10,
            // clockwise: false,
            roseType: 'area',
            data: data,
            animationEasing: 'cubicInOut',
            animationDuration: 2600
          }
        ]
      })
    }
  }
}
</script>
