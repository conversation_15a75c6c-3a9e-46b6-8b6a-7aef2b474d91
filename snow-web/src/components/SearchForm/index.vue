<template>
  <div class="search_form_template wbg br2" :style="{'height':searchBoxHeight}">
    <div ref="search_sub_box" class="search_sub_box">
      <div class="search_form_title">{{ $t('reaCommon.rapidRetrieval') }}</div>
      <el-form
          ref="searchForm"
          :model="value"
          class="my_el_form"
          :rules="formRule"
          v-bind="$attrs"
          :size="size"
          v-on="$listeners"
      >
        <div
            v-for="(item,index) in formData"
            :key="index"
            class="form_item"
            :style="{ width:item.divWidth?item.divWidth:colWidth,display:item.hide?'none':'block'}"
        >
          <el-form-item
              v-show="!item.isshow"
              :label="$t(`reaCommon.${item.nameKey}`)"
              :prop="item.field"
              :required="item.required"
              :label-width="item.labelWidth"
          >
            <!-- 树形选择框 -->
            <template v-if="item.type === 'tree'">
              <slot :name="item.slot"/>
            </template>
            <!-- 普通输入框 -->
            <template v-if="item.type==='input'">
              <el-input
                  v-model="value[item.field]"
                  v-bind="item.attrs"
                  :style="{ width:item.width?item.width:'100%'}"
                  :placeholder="$t('reaCommon.pleaseEnter') + ' ' + $t(`reaCommon.${item.nameKey}`)"
                  @change="(val)=>selectChange(val,item.field)"
                  @keyup.enter.native="search"
              />
            </template>
            <!-- 下拉选择框-分组 -->
            <template v-else-if="item.type==='select_group'" >
              <el-select
                  v-loading="item.attrs && item.attrs.loading"
                  v-model="value[item.field]"
                  v-bind="item.attrs"
                  :style="{ width:item.width?item.width:'100%'}"
                  :placeholder="$t('reaCommon.pleaseSelect')+ ' ' + $t(`reaCommon.${item.nameKey}`)"
                  @change="(val)=>selectChange(val,item.field)"
              >
                <el-option-group
                    v-for="(group, ind) in item.options"
                    :key="ind"
                    :label="group.labelKey ? $t(`reaCommon.${group.labelKey}`) : group.label"
                   >
                  <el-option
                      v-for="item in group.options"
                      :key="item.value"
                      :label="item.labelKey ? $t(`reaCommon.${item.labelKey}`) : item.label"
                      :value="item.value">
                  </el-option>
                </el-option-group>
              </el-select>
<!--              <el-select-->
<!--                  v-loading="item.attrs && item.attrs.loading"-->
<!--                  v-model="value[item.field]"-->
<!--                  v-bind="item.attrs"-->
<!--                  :style="{ width:item.width?item.width:'100%'}"-->
<!--                  :placeholder="$t('reaCommon.pleaseSelect')+ ' ' + $t(`reaCommon.${item.nameKey}`)"-->
<!--                  @change="(val)=>selectChange(val,item.field)"-->
<!--              >-->
<!--                <el-option-->
<!--                    v-for="(it,ind) in item.options"-->
<!--                    :key="ind"-->
<!--                    :label="it.labelKey ? $t(`reaCommon.${it.labelKey}`) : it.label"-->
<!--                    :value="it.value"-->
<!--                />-->
<!--              </el-select>-->
            </template>
            <!-- 下拉选择框 -->
            <template v-else-if="item.type==='select'" >
              <el-select
                  v-loading="item.attrs && item.attrs.loading"
                  v-model="value[item.field]"
                  v-bind="item.attrs"
                  :style="{ width:item.width?item.width:'100%'}"
                  :placeholder="$t('reaCommon.pleaseSelect')+ ' ' + $t(`reaCommon.${item.nameKey}`)"
                  @change="(val)=>selectChange(val,item.field)"
              >
                <el-option
                    v-for="(it,ind) in item.options"
                    :key="ind"
                    :label="it.labelKey ? $t(`reaCommon.${it.labelKey}`) : it.label"
                    :value="it.value"
                />
              </el-select>
            </template>
            <!-- 下拉选择框--超级量 -->
            <template v-else-if="item.type=='more_select'">
              <!--                          :defoptionList="filterSelectOptionList[item.key]"-->
              <MoreSelect style="width: 100%" v-loading="!item.options.length" :indexRandom="0" :List="item.options"
                          :defValue="defSkuIds"
                          @nodeConfigHandler="nodeConfigHandler" :opts="{type: 'sku_ids'}"
              ></MoreSelect>
            </template>
            <!-- 时间范围选择器 -->
            <template v-else-if="item.type==='daterange'">
              <el-date-picker
                  v-model="value[item.field]"
                  v-bind="item.attrs"
                  :style="{ width:item.width?item.width:'100%'}"
                  format="yyyy-MM-dd"
                  :value-format="item['value-format'] || 'yyyy-MM-dd HH:mm:ss'"
                  :default-time="['00:00:00','23:59:59']"
                  type="daterange"
                  unlink-panels
                  :range-separator="$t('reaCommon.to')"
                  :start-placeholder="$t('reaCommon.startDate')"
                  :end-placeholder="$t('reaCommon.endDate')"
                  @change="(val)=>selectChange(val,item.field)"
              />
            </template>
            <!-- 多层级联动 -->
            <template v-else-if="item.type==='cascader'">
              <el-cascader
                  v-model="value[item.field]"
                  :disabled="item.disabled?item.disabled:false"
                  :style="{ width:item.width?item.width:'100%'}"
                  :options="item.options"
                  :props="item.props"
                  :placeholder="$t('reaCommon.pleaseSelect')+ ' ' + $t(`reaCommon.${item.nameKey}`)"
                  @change="(val)=>selectChange(val,item.field)"
              />
            </template>
          </el-form-item>
        </div>
        <div class="flex-1 flex-shrink" style="margin-bottom:22px;">
          <div class="flex-box flex-y-end flex-x-end frontend_control_box">
            <el-button type="primary" :size="size" @click.native="search">{{ $t('reaCommon.query') }}</el-button>
            <el-button style="margin-left:20px;" :size="size" @click.native="reset">{{ $t('reaCommon.reset') }}
            </el-button>
            <span v-if="formData.length > showCount" class="advanced_search_btn" style="margin-left:20px;"
                  @click.stop="isFold = !isFold">{{
                isFold ? $t('reaCommon.advancedSearch') : $t('reaCommon.packUp')
              }}<i
                  v-if="false" :class="[ isFold ? 'el-icon-arrow-down' : 'el-icon-arrow-up' ,'el-icon--right']"/></span>

            <!--          <el-button v-if="formData.length > showCount" :size="size" style="margin-left:20px;" type="text" @click.native="isFold = !isFold">{{ isFold ? '高级搜索' : '收起' }}<i v-if="false" :class="[ isFold ? 'el-icon-arrow-down' : 'el-icon-arrow-up' ,'el-icon&#45;&#45;right']" /></el-button>-->
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>
<script>
import MoreSelect from '@/components/MoreSelect'
import {mapGetters} from 'vuex'

export default {
  name: 'search-form',
  components: {
    MoreSelect,
  },
  props: {
    // 校验规则
    formRule: {
      type: Object,
      default: function () {
        return {}
      }
    },
    // 表单的值
    value: {
      type: Object,
      default: function () {
        return {}
      }
    },
    // 表单大小
    size: {
      type: String,
      default: 'medium'
    },
    // 单行个数
    lineCount: {
      type: Number,
      default: 0
    },
    // 单行宽度
    lineWidth: {
      type: Number | String,
      default: 0
    },
    // 表单数据
    formData: {
      type: Array,
      default: () => {
        return [
          // {
          //   name: '输入框', // 文字label
          //   type: 'input', // input,select,daterange,cascader
          //   maxlength: 30, // 输入长度限制
          //   field: 'id', // 字段名称
          //   width: false, // 文本宽度
          //   disabled: false, // 是否禁用
          //   placeholder: '请输入', // placeholder提示
          //   multiple: false, // 是否多选，基于select，checkbox
          //   filterable: false // 是否多选，基于select
          // }
        ]
      }
    }
  },
  computed: {
    ...mapGetters([
      'language'
    ])
  },
  data() {
    return {
      defaultValue: [],
      defSkuIds: [],
      skuIds: [],
      isFold: true,
      colWidth: '33.3%',
      showCount: 2,
      searchBoxHeight: '112px'
    }
  },
  watch: {
    isFold(value) {
      this.changeItemHide(value)
    },
    '$i18n.locale'() {
      this.setParam()
      this.needOpen()
    }
  },
  created() {
    this.searchBoxHeight = this.language === 'en' ? '180px' : '112px'
    this.setParam()
    this.needOpen()
  },
  methods: {
    updateFormData(val){
      this.formData = val
      this.$forceUpdate()
    },
    // 获取当天的开始时间（00:00:00）
    getDefaultStartTime() {
      const now = new Date();
      now.setHours(0, 0, 0, 0);
      return now;
    },
    // 获取当天的结束时间（23:59:59）
    getDefaultEndTime() {
      const now = new Date();
      now.setHours(23, 59, 59, 999);
      return now;
    },
    setParam() {
      this.colWidth = this.language === 'en' ? '50%' : '33.3%'
      this.showCount = this.language === 'en' ? 3 : 2
      // 当数量为3的时候 不需要高级搜索按钮
      if (this.formData.length == 3 && this.language != 'en') {
        this.colWidth = '26%'
        this.showCount = 3
      }
      if(this.lineCount){
        this.showCount = this.lineCount
      }
      if(this.lineWidth){
        this.colWidth = this.lineWidth
      }
      // this.showCount = 2
      // this.colWidth = '40%'
      this.changeItemHide(this.isFold)
    },
    nodeConfigHandler(data) {
      let {component, param} = data
      // this.skuIds = param.value
      this.value['sku_ids'] = param.value
    },
    selectChange(val, field) {
      this.$emit('selectChangeHandler', {name: field, value: val})
    },
    changeItemHide(value) {
      if (value) {
        this.formData.map((v, index) => {
          v.hide = index > this.showCount - 1
        })
        this.searchBoxHeight = this.language === 'en' ? '170px' : '112px'
      } else {
        this.formData.map((v) => {
          v.hide = false
        })
        this.$nextTick(() => {
          if (this.$refs['search_sub_box'] && this.$refs['search_sub_box'].offsetHeight) {
            this.searchBoxHeight = this.$refs['search_sub_box'].offsetHeight + 'px'
          } else {
            this.searchBoxHeight = 'auto'
          }
        })
      }
    },
    needOpen() {
      if (this.formData.length > this.showCount) {
        this.formData.map((v, index) => {
          if (index > this.showCount - 1) {
            if (Object.prototype.hasOwnProperty.call(this.formRule, v.field)) {
              this.isFold = false
            }
          }
        })
        this.changeItemHide(this.isFold)
      }
    },
    search() {
      this.$refs['searchForm'].validate((valid) => {
        if (valid) {
          this.$emit('nodeConfigHandler', {
            component: 'searchForm',
          })
          // param: {skuIds: this.skuIds}
        } else {
          if (this.formData.length > this.showCount) {
            this.formData.map((v, index) => {
              if (index > this.showCount - 1) {
                if (Object.prototype.hasOwnProperty.call(this.formRule, v.field) && this.value[v.field] == '') {
                  this.isFold = false
                }
              }
            })
            this.changeItemHide(this.isFold)
          }
        }
      })
    },
    reset() {
      this.$emit('resetQueryParam')
      const item = this.formData.find(v => v.type == 'more_select')
      if(item){
        this.skuIds = []
        this.defSkuIds = []
      }
      this.$refs['searchForm'].resetFields()
    }
  }
}
</script>

<style lang="scss">
.search_form_template {
  overflow: hidden;
  height: auto;
  transition: height .2s;
  -ms-transition: height .2s; /* IE 9 */
  -moz-transition: height .2s; /* Firefox */
  -webkit-transition: height .2s; /* Safari 和 Chrome */
  -o-transition: height .2s; /* Opera */
  padding-bottom: 10px;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.04);
  border-radius: 8px;

  .search_form_title {
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #0471D4;
    line-height: 22px;
    padding: 16px 0 16px 21px;
    position: relative;

    &::after {
      content: '';
      display: block;
      position: absolute;
      left: 0;
      top: 50%;
      margin-top: -9px;
      width: 4px;
      height: 18px;
      background-color: #0471D4;
    }
  }

  .el-form-item__label {
    color: #333 !important;
  }

  .flex-box {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }

  .flex-center {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }

  .flex-y-start {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
  }

  .flex-y-center {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }

  .flex-y-end {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end;
  }

  .flex-0 {
    -webkit-box-flex: 0;
    -ms-flex: 0;
    flex: 0;
  }

  .flex-1 {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
  }

  .flex-bt {
    -ms-flex-pack: distribute;
    justify-content: space-between;
  }

  .flex-row {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
  }

  .flex-column {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
  }

  .flex-x-end {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
  }

  .flex-x-center {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }

  .flex-x-start {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }

  .flex-shrink {
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .wbg {
    background-color: white;
  }

  .br2 {
    border-radius: 2px;
  }

  .my_el_form {
    display: flex;
    flex-wrap: wrap;
    padding-right: 25px;
    height: auto;
    transition: height .3s;
  }

  .form_item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }

  .advanced_search_btn {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #0471D4;
    line-height: 20px;
    cursor: pointer;
  }

  .frontend_control_box .el-button--default {
    border-color: #0471D4;;
    color: #0471D4;
  }
}
</style>
