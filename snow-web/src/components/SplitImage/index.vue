<template>
  <div>
    <el-dialog
      :before-close="handleClose"
      class="module_dialog"
      :title="$t('visitDetail.splicedPicture')"
      :visible.sync="dialogVisible"
      width="80%"
    >
      <div class="module_pop_view" @mouseup="canvasMouseUp($event)">
        <div class="pop_canvas_container">
          <div ref="canvasBox1" class="det-pro-img">
            <!-- canves -->
            <div
              id="imgPopBox"
              class="canvas-box"
              @mousedown="canvasMouseDown($event)"
              @mousemove="canvasMouseMove($event)"
              @mouseup="canvasMouseUp($event)"
            >
              <div
                ref="stageBase"
                class="stage-base"
                :style="{'transform': 'matrix('+transformDelta/100+', 0, 0, '+transformDelta/100+', '+stageX+', '+stageY}"
              >
                <canvas
                  id="canvas-bg1"
                  ref="canvasbg1"
                  :style="{height: canvasHeight+'px',width: canvasWidth+'px'}"
                >
                  您的浏览器不支持canvas
                </canvas>
                <canvas
                  id="canvas1"
                  ref="canvas1"
                  :style="{height: canvasHeight+'px',width: canvasWidth+'px'}"
                  @mousemove="showThisPop"
                  @mouseout="showPopMsg = false"
                >
                  您的浏览器不支持canvas
                </canvas>
              </div>
            </div>
          </div>
        </div>
        <!-- canves 鼠标指上去 -->
        <div v-show="showPopMsg" class="popMsg" :style="{'left':left + 'px','top':top + 'px'}" v-html="popText"/>
      </div>
      <div slot="footer">
        <div class="control_btn_box">
          <span class="control_opt-img" @click="scaleImg(200)"><i class="el-icon-zoom-out"/></span>
          <span class="control_opt-img" @click="scaleImg(100)"><i class="el-icon-zoom-in"/></span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import fullIcon from '@/assets/images/<EMAIL>'

export default {
  name: 'split-image',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    skuList: {
      type: Array,
      default: () => []
    },
    url: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      fullIcon,
      // 画图
      canvasHeight: 0,
      canvasWidth: 0,
      left: 0,
      top: 0,
      showPopMsg: false,
      popText: '',
      // 移动
      transformDelta: 100,
      stageX: 0,
      stageY: 0,
      startCanvesMouseX: 0,
      startCanvesMouseY: 0,
      isCanvasStartMouse: false,
      stageBaseLeft: 0,
      stageBaseTop: 0,
      movseCanvesDisX: 0,
      movseCanvesDisY: 0
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initCanvas()
    })
  },
  methods: {
    handleClose() {
      this.$emit('dialogClose')
    },
    // 将图渲染到canvas上
    initCanvas() {
      console.log(this.skuList)
      console.log(this.url)
      const canvas = this.$refs.canvas1
      const canvasbg1 = this.$refs.canvasbg1
      const context = canvasbg1.getContext('2d')
      const image = new Image()
      image.src = this.url
      this.canvasBoxW = this.$refs.canvasBox1.offsetWidth
      this.canvasBoxH = this.$refs.canvasBox1.offsetHeight
      image.onload = () => {
        this.scale = this.canvasBoxW / image.width
        this.canvasHeight = image.height * this.scale
        this.canvasWidth = image.width * this.scale
        canvas.width = this.canvasWidth
        canvas.height = this.canvasHeight
        canvasbg1.width = this.canvasWidth
        canvasbg1.height = this.canvasHeight
        context.fillRect(0, 0, canvas.width, canvas.height) // 填充颜色：距离左边 距离上面 宽度 高度
        context.drawImage(image, 0, 0, canvas.width, canvas.height)
      }
    },
    windowToCanvas(canvasW, canvasH, x, y) {
      var bbox = canvas1.getBoundingClientRect()
      return {
        x: (x - bbox.left * (canvasW / bbox.width)),
        y: (y - bbox.top * (canvasH / bbox.height))
      }
    },
    // 获取开始位置
    getStartPosition(clientX, clientY) {
      const stageBase = this.$refs.stageBase.style.transform.substring(7).split(',')
      this.startCanvesMouseX = clientX
      this.startCanvesMouseY = clientY
      this.stageBaseLeft = stageBase[4] ? parseFloat(stageBase[4]) : 0
      this.stageBaseTop = stageBase[5] ? parseFloat(stageBase[5]) : 0
    },
    // 设置位置
    setPosition(clientX, clientY) {
      this.movseCanvesDisX = clientX - this.startCanvesMouseX
      this.movseCanvesDisY = clientY - this.startCanvesMouseY
      this.stageX = this.movseCanvesDisX + this.stageBaseLeft
      this.stageY = this.movseCanvesDisY + this.stageBaseTop
    },
    // 点击放大缩小
    scaleImg(state) {
      // // 100 add 200 red
      if (state == 100) {
        if (this.transformDelta > 300) {
          return false
        }
        this.transformDelta = (parseFloat(this.transformDelta) + 10).toFixed(0)
      } else {
        if (this.transformDelta <= 10) {
          return false
        }
        this.transformDelta = (parseFloat(this.transformDelta) - 10).toFixed(0)
      }
      // this.overViewFun()
    },
    // 鼠标滑动过
    showThisPop(e) {
      const bbox = imgPopBox.getBoundingClientRect()
      const {left, top} = bbox
      const that = this
      const canvas = this.$refs.canvas1
      const context = canvas.getContext('2d')
      const width = canvas.width * this.transformDelta / 100
      const height = canvas.height * this.transformDelta / 100
      const obj = this.windowToCanvas(width, height, e.clientX, e.clientY) // 转化成相对于canves上面的点坐标
      this.left = e.clientX - left + 30 // 相对于 window 上的点
      this.top = e.clientY - top + 52 // 相对于 window 上的点
      that.popText = ''
      this.skuList.map((val, k) => {
        if (val.objects) {
          val.objects.map((v, k) => {
            if (obj.x > v.tl[0] * width && obj.x < v.br[0] * width && obj.y > v.tl[1] * height && obj.y < v.br[1] * height) {
              that.showPopMsg = true
              that.popText = val.name
              if (this.debug) {
                that.popText = val.name + '</br>' + v.confidence
              }
            }
          })
        }
      })
    },
    // canves移动开始
    canvasMouseDown(e) {
      this.isCanvasStartMouse = true
      if (this.isCanvasStartMouse) {
        this.getStartPosition(e.clientX, e.clientY)
      }
    },
    // canves画布移动
    canvasMouseMove(e) {
      e.preventDefault()
      if (this.isCanvasStartMouse) {
        this.setPosition(e.clientX, e.clientY)
      }
    },
    // canves画布结束
    canvasMouseUp(e) {
      this.isCanvasStartMouse = false
      this.startCanvesMouseX = 0
      this.startCanvesMouseY = 0
    }
  }

}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.control_btn_box {
  text-align: center;

  .control_opt-img {
    cursor: pointer;
    margin-left: 20px;
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    font-size: 24px;
    color: #606266;
  }
}

.popMsg {
  position: fixed;
  top: 0;
  left: 0;
  font-size: 15px;
  color: #fff;
  background: rgba(73, 4, 4, .5);
  border-radius: 15px;
  padding: 2px 5px;
  z-index: 335;
}

</style>
