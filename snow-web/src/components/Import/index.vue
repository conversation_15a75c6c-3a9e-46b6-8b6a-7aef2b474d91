<template>
  <div class="dialog_container import_component_view">
    <el-dialog
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      center
      :title="$t('reaCommon.uploadFile')"
      :visible.sync="dialogVisible"
      v-bind="$attrs"
      :width="width"
      :before-close="handleClose"
      v-on="$listeners"
    >
      <div class="upload__sell">
        <div class="chose__file button__import" :class="{'hasFile':filename}">
          <div v-if="!filename">{{ $t('reaCommon.selectFile') }}</div>
          <div v-else class="overflow">
            <div class="fl">{{ filename }}</div>
            <div class="fr">
              <div v-if="!upLoading">{{ $t('reaCommon.uploadAgain') }}</div>
              <div v-else><span class="el-icon-loading"/>{{ $t('reaCommon.uploading') }} {{ progress }}%</div>
            </div>
          </div>
          <!--如果是上传中 uplaod 这个则隐藏-->
          <input
            ref="file"
            class="upload__input--file"
            type="file"
            autocomplete="off"
            accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel,.xls,.xlsx"
            :class="{'isUploading':upLoading}"
            @change="goImport"
            @click="initFile"
          >
        </div>
        <div class="des">{{ $t('reaCommon.uploadAttention') }}</div>
        <div v-if="!fileError && step == 1" class="err-msg">{{ fileError }}</div>
        <div v-if="step == 2" class="upload__statistics">
          <div><span class="el-icon-success color__green"/>{{ validCount }}&nbsp;{{ $t('reaCommon.effectData') }}</div>
          <div v-if="repeatCount"><span class="el-icon-error color__error"/>{{
              repeatCount
            }}&nbsp;{{ $t('reaCommon.repeatData') }}<i
              @click.stop="downloadErrorData(2)">{{ $t('reaCommon.downloadRepeatData') }}</i></div>
          <div><span class="el-icon-error color__error"/>{{ errorCount }}&nbsp;{{ $t('reaCommon.errorData') }}<i
            v-if="errorCount > 0" @click.stop="downloadErrorData(1)">{{ $t('reaCommon.downloadErrorData') }}</i></div>
        </div>
        <div class="upload__desc">
          <div>{{ $t('reaCommon.dataMustSameTemplate') }}<span @click.stop="down">{{
              $t('reaCommon.downloadTemplate')
            }}</span></div>
        </div>
      </div>
      <span v-if="step == 1" slot="footer" class="dialog-footer">
        <el-button size="medium" @click="handleCancel">{{ $t('reaCommon.cancel') }}</el-button>
        <el-button size="medium" type="primary" :loading="upLoading" @click="uploadFn">{{
            $t('reaCommon.confirm')
          }}</el-button>
      </span>
      <span v-if="step == 2" slot="footer" class="dialog-footer">
        <el-button size="medium" type="primary" @click="closePop">{{ $t('reaCommon.close') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {importTemplate, importUpload, importProgress} from '@/api/download-api'
import {downloadTemplate} from '@/utils/index'

export default {
  name: 'import',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: '600px'
    },
    action: {
      type: String,
      default: 'plan'
    },
    importType: {
      type: String,
      default: '1'
    },
    id: [Number, String]
  },
  data() {
    return {
      step: 1,
      progress: 0,
      fileError: '',
      filename: '',
      validCount: 0,
      errorCount: 0,
      repeatCount: 0,
      upLoading: false,
      task_id: '',
      error_url: '',
      file: null
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    initFile() {
      this.step = 1
      this.$refs['file'].value = ''
      this.errorCount = 0
      this.validCount = 0
      this.repeatCount = 0
      this.fileError = ''
    },
    closePop() {
      if (this.step == 2) {
        this.$emit('refresh')
      }
      this.$emit('dialogClose')
    },
    goImport() {
      const file = this.$refs['file'].files[0]
      if (file) {
        this.filename = `${file.name}  ${this.formatSize(file.size)}`
        if ((file.size / 1024 / 1024 > 8)) {
          this.fileError = '文件大小不能超过8M'
          return
        }
        this.file = file
      }
    },
    formatSize(size) {
      let data = ''
      if (size < 0.1 * 1024) { // 如果小于0.1KB转化成B
        data = size.toFixed(2) + 'B'
      } else if (size < 0.1 * 1024 * 1024) { // 如果小于0.1MB转化成KB
        data = (size / 1024).toFixed(2) + 'KB'
      } else if (size < 0.1 * 1024 * 1024 * 1024) { // 如果小于0.1GB转化成MB
        data = (size / (1024 * 1024)).toFixed(2) + 'MB'
      } else { // 其他转化成GB
        data = (size / (1024 * 1024 * 1024)).toFixed(2) + 'GB'
      }
      const sizestr = data + ''
      const len = sizestr.indexOf('\.')
      const dec = sizestr.substr(len + 1, 2)
      if (dec == '00') { // 当小数点后为00时 去掉小数部分
        return sizestr.substring(0, len) + sizestr.substr(len + 3, 2)
      }
      return sizestr
    },
    uploadFn() {
      if (this.file) {
        const formData = new FormData()
        formData.append('uploadFiles', this.file)
        formData.append('action', this.action)
        formData.append('type', this.importType)
        formData.append('id', this.id)
        this.upLoading = true
        importUpload(formData).then(res => {
          this.task_id = res.data.task_id
          this.progress = 0
          setTimeout(() => {
            this.fileError = ''
            this.getProgress()
          }, 2000)
        }).catch((error) => {
          this.fileError = error
          this.upLoading = false
        })
      } else {
        this.upLoading = false
        this.$emit('dialogConfirm')
      }
    },
    getProgress() {
      if (!this.dialogVisible || !this.upLoading) {
        return
      }
      importProgress({task_id: this.task_id}).then(res => {
        this.progress = res.data.progress
        if (this.progress == 100) {
          this.step = 2
          this.upLoading = false
          this.validCount = res.data.success_count
          this.errorCount = res.data.fail_count
          this.repeatCount = res.data.repeat_count
          this.error_url = res.data.url
          if (this.action === 'plan') {
            this.$emit('setExportCount', {
              type: this.importType,
              count: this.validCount
            })
          }
        } else {
          setTimeout(() => {
            this.getProgress()
          }, 3000)
        }
      }).catch(() => {
        this.upLoading = false
      })
    },
    down() {
      downloadTemplate(`${importTemplate}?action=${this.action}`)
    },
    downloadErrorData(type) {
      downloadTemplate(`${this.error_url}?task_id=${this.task_id}&type=${type}`)
    },
    handleClose() {
      this.upLoading = false
      if (this.step == 2) {
        this.$emit('refresh')
      }
      this.$emit('dialogClose')
    },
    handleCancel() {
      this.upLoading = false
      this.$emit('dialogCancel')
    }
  }

}
</script>

<style lang="scss">
@import "~@/styles/variables.scss";

.import_component_view {
  .dialog_container {
    line-height: 20px;
  }

  .table_header_set_box {
    padding: 0 10px !important;

    .el-checkbox__label {
      color: #333;
    }
  }

  .cursor {
    cursor: pointer;
  }

  .text__align--center {
    text-align: center;
  }

  .upload__sell {
    font-size: 14px;
    padding-bottom: 20px;

    .des {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #737373;
      line-height: 20px;
    }

    .upload__desc {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #737373;
      line-height: 20px;

      span {
        cursor: pointer;
        color: $themeColor;
        margin-left: 5px;
      }
    }

    .upload__statistics {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #737373;
      line-height: 20px;

      span {
        margin-right: 5px;
      }

      i {
        cursor: pointer;
        color: $themeColor;
        margin-left: 5px;
      }
    }

    .chose__file {
      margin-bottom: 8px;
      color: $themeColor;
      position: relative;
      padding: 0 24px;
      cursor: pointer;
      height: 44px;
      line-height: 44px;
      background: #EFF7FF;
      border-radius: 4px;
      border: 1px solid $themeColor;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #0471D4;

      &.hasFile {
        border-color: #e6e6e6;
        background-color: white;
      }

      .fl {
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #262626;
      }

      .fr {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: $themeColor;
      }
    }
  }

  .upload__input--file {
    position: absolute;
    left: 0px;
    width: 100%;
    top: 0px;
    opacity: 0;
    height: 100%;
    cursor: pointer;
    z-index: 2;
  }

  .upload__input--file.isUploading {
    z-index: -1
  }

  .button__import {
    border: 1px dashed #DADADA;
    //padding-left: $button-padding;
    //padding-right: $button-padding;
    //padding: $button-padding;
    //font-size: $button-font-size;
    border-radius: 4px;
    text-align: center;
  }

  .err-msg {
    font-size: 12px;
    color: #D61E2A;
    line-height: 20px;
  }

  //.el-dialog__header{
  //  text-align:left;
  //  padding:24px;
  //  padding-bottom:10px;
  //  .el-dialog__title{
  //    font-size: 20px;
  //    font-family: PingFangSC-Semibold, PingFang SC;
  //    font-weight: 600;
  //    color: #262626;
  //    line-height: 26px;
  //  }
  //}
  //.el-dialog__body{
  //  padding:24px;
  //}
  //.el-dialog__footer{
  //  padding:24px;
  //  padding-top:37px;
  //  text-align:right;
  //}
}
</style>
