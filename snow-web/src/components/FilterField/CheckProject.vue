<template>
  <div class="dialog_container import_component_view page_container">
    <el-dialog
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="filter_field_box"
      center
      append-to-body
      :title="$t('reaCommon.dataExport')"
      :visible.sync="dialogVisible"
      v-bind="$attrs"
      :width="width"
      :before-close="handleClose"
      v-on="$listeners"
    >
      <div>
        <el-form
          ref="filterForm"
          size="medium"
          :rules="filterRules"
          :model="filterForm"
          label-position="left"
        >
          <el-form-item v-if="checkItemList.length" :label="$t('reaCommon.selectExportCheckProject')" prop="checkItem">
            <el-select v-model="filterForm.checkItem" class="check_item_select"
                       :placeholder="$t('reaCommon.selectExportCheckProject')" filterable clearable>
              <el-option
                v-for="item in checkItemList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('reaCommon.selectExportField')" prop="headers">
            <el-tree-select ref="treeSelect" v-model="filterForm.headers" collapse-tags :styles="styles"
                            :select-params="selectParams" :tree-params="treeParams"/>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="medium" @click.native="handleCancel">{{ $t(`reaCommon.cancel`) }}</el-button>
        <el-button size="medium" type="primary" @click.native="handleConfirm">{{ $t(`reaCommon.export`) }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  props: {
    fieldList: {
      type: Array,
      default() {
        return []
      }
    },
    checkItemList: {
      type: Array,
      default() {
        return []
      }
    },
    filterVisible: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: '600px'
    }
  },
  data() {
    return {
      filterForm: {
        headers: [],
        checkItem: ''
      },
      filterRules: {
        // checkItem: [
        //   { required: true, message: this.$t('reaCommon.selectExportCheckProject'), trigger: 'change' }
        // ],
        headers: [
          {type: 'array', required: true, message: this.$t('reaCommon.pleaseSelectField'), trigger: 'change'}
        ]
      },
      styles: {
        width: '100%'
      },
      selectParams: {
        multiple: true,
        clearable: true,
        placeholder: this.$t('reaCommon.pleaseSelectField')
      },
      treeParams: {
        filterable: false,
        'default-expand-all': true,
        'expand-on-click-node': false,
        'node-key': 'prop',
        'show-checkbox': true,
        leafOnly: true,
        data: [],
        props: {
          children: 'children',
          label: 'labelName',
          disabled: 'disabled',
          value: 'prop'
        }
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.filterVisible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    '$i18n.locale'() {
      this.initFieldList()
    }
  },
  mounted() {
    this.initFieldList()
  },
  methods: {
    initFieldList() {
      const fieldArr = []
      this.fieldList.map((v) => {
        if (v.children && v.children.length) {
          v.children.map((item) => {
            item.labelName = item.labelKey ? this.$t(`reaCommon.${item.labelKey}`) : item.label
          })
          fieldArr.push({
            prop: v.prop,
            labelName: this.$t(`reaCommon.${v.labelKey}`),
            children: v.children
          })
        } else {
          fieldArr.push({
            prop: v.prop,
            labelName: this.$t(`reaCommon.${v.labelKey}`)
          })
        }
      })
      this.treeParams.data = [{
        prop: 'select_all',
        labelName: this.$t('reaCommon.allFields'),
        children: fieldArr
      }]
    },
    handleClose() {
      this.$emit('dialogClose')
    },
    handleCancel() {
      this.$emit('dialogCancel')
    },
    handleConfirm() {
      this.$refs['filterForm'].validate((valid) => {
        if (valid) {
          const field_list = JSON.parse(JSON.stringify(this.filterForm.headers))
          const fieldObject = {
            field_list,
            checkItem: this.filterForm.checkItem
          }
          this.$emit('dialogConfirm', fieldObject)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.filter_field_box::v-deep .el-dialog__body {
  min-height: 180px;
}

.filter_field_box::v-deep .check_item_select.el-select {
  display: block !important;
}

.filter_field_box::v-deep .check_item_select.el-select .el-input {
  display: inline-block !important;
}

</style>
