<template>
  <div :class="className" :style="{ height: height, width: width }"/>
</template>

<script>
import * as echarts from 'echarts'

require('echarts/theme/macarons') // echarts theme
import resize from '@/utils/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'ring_chart'
    },
    width: {
      type: String,
      default: '110px'
    },
    height: {
      type: String,
      // 1650 160
      // 1920 ?
      default: '110px'
    }
  },
  data() {
    return {
      itemStyleMap: [
        {
          start: '#FFD84D',
          end: '#FF6B00'
        },
        {
          start: 'rgba(82, 41, 200, 0.20)',
          end: 'rgba(82, 41, 200, 0.20)'
        },
      ],
      chart: null,
      resultData: {}
    }
  },
  mounted() {
    this.initChart()
    // this.$nextTick(() => {
    //     this.initChart()
    // })
  },
  beforeDestroy() {
    this.clear()
  },
  methods: {
    /**
     * 清空
     * */
    clear() {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    },
    /**
     * 初始化
     * */
    initChart(resultData) {
      this.clear()
      // this.resultData = resultData
      // console.log(this.$el, 222)
      this.chart = echarts.init(this.$el, 'macarons')
      // const data = resultData.list.filter((v, index) => {
      //     if (v.value) {
      //         v.value = (v.value * 1).toFixed(1)
      //     }
      // v.itemStyle = {
      //     color: new echarts.graphic.LinearGradient(
      //         0, 0, 1, 0,
      //         [
      //             { offset: 0, color: this.itemStyleMap[index] && this.itemStyleMap[index].start || '#3371FF' },
      //             { offset: 1, color: this.itemStyleMap[index] && this.itemStyleMap[index].end || '#447DFF' }
      //         ]
      //     )
      // }
      //     return v
      // })
      const option = {
        tooltip: {
          trigger: 'item',
          // formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        series: [{
          name: '教室占用率',
          type: 'pie',
          radius: ['70%', '100%'],
          roseType: 'radius',
          itemStyle: {
            borderRadius: 0
          },
          label: {
            show: false
          },
          emphasis: {
            label: {
              show: false
            }
          },
          data: [{
            value: 191,
            name: '空闲数(个)',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(
                0, 0, 1, 0,
                [
                  {offset: 0, color: this.itemStyleMap[0] && this.itemStyleMap[0].start || '#3371FF'},
                  {offset: 1, color: this.itemStyleMap[0] && this.itemStyleMap[0].end || '#447DFF'}
                ]
              )
            }
          },
            {
              value: 123,
              name: '使用数(个)',
              itemStyle: {
                color: new echarts.graphic.LinearGradient(
                  0, 0, 1, 0,
                  [
                    {offset: 0, color: this.itemStyleMap[1] && this.itemStyleMap[1].start || '#3371FF'},
                    {offset: 1, color: this.itemStyleMap[1] && this.itemStyleMap[1].end || '#447DFF'}
                  ]
                )
              }
            },
          ]
        },],
        title: {
          // text: this.resultData.total_score,
          text: '20.2%',
          top: '39%',
          left: '48%',
          textAlign: 'center',
          textStyle: {
            padding: 10,
            height: 30,
            color: '#fff',
            fontSize: '24px',
            textAlign: 'center',
            lineHeight: 31
          }
        },
        // graphic: { // 将图片定位到最下方的中间：
        //     type: 'text',
        //     left: 'center', // 水平定位到中间
        //     top: "48%",
        //     color: '#fff',
        //     style: {
        //         color: '#fff',
        //         text: '41%',
        //         textAlign: "center",
        //         fontSize: 24,
        //     }
        // }
      }
      this.chart.setOption(option)
    }
  }
}
</script>
