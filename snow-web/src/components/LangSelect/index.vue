<template>
  <el-dropdown trigger="click" @command="handleSetLanguage">
    <div class="language_title">
      {{ getLanguageTitle() }}<i class="el-icon-arrow-down el-icon--right"/>
    </div>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item :disabled="language==='zh-Hans'" command="zh-Hans">
        简体中文
      </el-dropdown-item>
      <el-dropdown-item :disabled="language==='zh-Hant'" command="zh-Hant">
        繁體中文
      </el-dropdown-item>
      <el-dropdown-item :disabled="language==='en'" command="en">
        English
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
export default {
  props: {
    view: {
      type: String,
      default: () => {
        return 'navBar'
      }
    }
  },
  computed: {
    language() {
      return this.$store.getters.language
    }
  },
  methods: {
    getLanguageTitle() {
      switch (this.language) {
        case 'zh-Hans':
          return '简体中文'
        case 'zh-Hant':
          return '繁體中文'
        case 'en':
          return 'English'
      }
    },
    handleSetLanguage(lang) {
      this.$i18n.locale = lang
      this.$store.dispatch('app/setLanguage', lang)
      const iframeList = document.getElementsByClassName('child_iframe_box')
      if (iframeList) {
        for (let i = 0; i < iframeList.length; i++) {
          iframeList[i].contentWindow.postMessage({
            cmd: 'changeLanguage',
            params: {
              language: lang
            }
          }, '*')
        }
      }
      if (this.view === 'navBar') {
        this.closeOthersTags()
      }
    },
    refreshSelectedTag() {
      this.$store.dispatch('tagsView/delCachedView', this.$route).then(() => {
        const {fullPath} = this.$route
        this.$nextTick(() => {
          this.$router.replace({
            path: '/redirect' + fullPath
          })
        })
      })
    },
    closeOthersTags() {
      this.$router.push(this.$route)
      this.$store.dispatch('tagsView/delOthersViews', this.$route).then(() => {
        this.refreshSelectedTag()
      })
    }
  }
}
</script>
<style scoped lang="scss">
.language_title {
  cursor: pointer;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #262626;
  line-height: 24px;
}
</style>
