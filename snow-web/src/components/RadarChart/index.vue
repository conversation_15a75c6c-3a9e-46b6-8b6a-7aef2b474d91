<template>
  <div>
    <el-input ref="elInput" @blur="blurClick" style="width: 0px;height:0px;opacity: 0;position: absolute;"></el-input>
    <div id="chart-container" style="position: relative" ref="chartContainer" :class="className"
         :style="{ height: height, width: width }"/>
    <div class="sales-sku-modal" ref="salesSkuModal" v-show="showModal"
         :style="{left:positionLeft + 'px',top:positionTop + 'px'}" @mousedown="stopBlur">
      <div class="sales-sku-list">
        <div class="flex sales-sku-title">
          <div class="flex-1">{{ skuTitle }}</div>
          <div class="el-icon-close" @click.stop="blurClick"></div>
        </div>
        <div class="sales-sku-top"><span class="tag"></span><span class="top10">TOP 10</span></div>
        <div class="sales-sku-item flex" v-for="(item,index) in skuList" :key="item.sku_id">
          <div>
            <el-popover
              placement="left"
              width="120"
              trigger="hover">
              <el-image fit="contain" :initial-index="index" class="sales-sku-image big"
                        :src="item.sku_image || otherImage"></el-image>
              <!--              <el-image slot="reference" :preview-src-list="getPrivewImages(imageUrls,index)" fit="contain" :initial-index="index"  class="sales-sku-image" :src="item.sku_image || otherImage"></el-image>-->
              <el-image slot="reference" fit="contain" class="sales-sku-image"
                        :src="item.sku_image || otherImage"></el-image>
            </el-popover>
          </div>
          <div class="flex-1" style="white-space: nowrap;">{{ item.sku_name }}</div>
          <div class="sales-sku-range">{{ item.percent }}%</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import otherImage from '@/assets/images/other的副本.png'

export default {
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '160px'
    },
    title: {
      type: String,
      default: ''
    },

  },
  data() {
    return {
      chart: null,
      itemStyleMap: [
        {
          start: '#026DB2',
          end: '#12FEE0'
        },
        {
          start: '#6DD400',
          end: '#44D7B6'
        },
        {
          start: '#5C64FF',
          end: '#6988F8'
        },
        {
          start: '#FFA600',
          end: '#FEDB65'
        },
        {
          start: '#D73838',
          end: '#FE698B'
        },
        {
          start: '#9270CA',
          end: '#C4A9E7'
        },
      ],
      resultData: {},
      positionLeft: '',
      positionTop: '',
      showModal: false,
      skuList: [],
      imageUrls: [],
      skuTitle: '',
      otherImage
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    this.clear()
  },
  methods: {
    stopBlur() {
      this.canNotClose = true
      setTimeout(() => {
        this.canNotClose = false
        this.$refs.elInput.focus()
      }, 100)
    },
    blurClick() {
      setTimeout(() => {
        if (this.canNotClose) {
          this.canNotClose = false
          return;
        }
        this.showModal = false
      }, 10)
    },
    /**
     * 清空
     * */
    clear() {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    },
    /**
     * 初始化
     * */
    initChart(resultData) {
      resultData = resultData || []
      this.clear()
      this.resultData = resultData
      if (!resultData.length) {
        return;
      }
      let legendData = []
      let indicatorList = resultData.map((item, index) => {
        legendData.push(item.name)
        let itemStyle = {
          color: new echarts.graphic.LinearGradient(
            0, 0, 1, 0,
            [
              {
                offset: 0,
                color: this.itemStyleMap[index % this.itemStyleMap.length] && this.itemStyleMap[index % this.itemStyleMap.length].start || '#3371FF'
              },
              {
                offset: 1,
                color: this.itemStyleMap[index % this.itemStyleMap.length] && this.itemStyleMap[index % this.itemStyleMap.length].end || '#447DFF'
              }
            ]
          ),
          borderRadius: 0,
          borderColor: '#172942',
          borderWidth: 1
        }
        return {name: item.name, value: Number(item.count), label: '#fff', itemStyle,}
      })
      let chartDom = this.$refs.chartContainer
      this.chart = echarts.init(chartDom);

      let option = {
        // tooltip: {
        //   trigger: 'item',
        //   // padding: 0,
        //   borderWidth: 0,
        //   backgroundColor: 'rgba(0,0,0,0.3)',
        //   formatter: '<span style="color:#fff">{a} <br/>{b} : {c} ({d}%)</span>'
        // },
        series: [
          {
            name: this.title,
            type: 'pie',
            radius: ['40%', '80%'],
            label: {
              color: '#fff',
              // formatter: '{b} :{d}%'
            },
            data: indicatorList,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            selectedMode: 'single',
            selectedOffset: 5,
            clockwise: true,
          },

        ]
      };
      option && this.chart.setOption(option);
      this.chart.on('click', (param) => {
        this.skuTitle = `${param.name}：${param.percent}%`

        this.resultData.forEach((item, index) => {
          this.chart.dispatchAction({
            type: index == param.dataIndex ? 'highlight' : 'downplay',
            seriesIndex: 0,
            dataIndex: index,
          });
        })
        //获取自定义变量barIds的值,barIds要和option的series里自定义的一样
        this.skuList = this.resultData[param.dataIndex].sale_list || []
        this.skuList = this.skuList.slice(0, 10)
        let imageUrls = []
        this.skuList.map(item => {
          imageUrls.push(item.sku_image || this.otherImage)
        })
        this.imageUrls = imageUrls
        this.positionLeft = 350
        let height = window.innerHeight
        this.$nextTick(() => {
          this.positionTop = height - this.$refs.salesSkuModal.offsetHeight - 20
        })
        this.positionTop = 10
        this.showModal = true
        this.$refs.elInput.focus()

      });
    },
    getPrivewImages(imgList, index) {
      let tempImgList = [...imgList];//所有图片地址
      if (index == 0) return tempImgList;
      // 调整图片顺序，把当前图片放在第一位
      let start = tempImgList.splice(index);
      let remain = tempImgList.splice(0, index);
      return start.concat(remain);//将当前图片调整成点击缩略图的那张图片
    },
  }
}
</script>
<style lang="scss" scoped>
#chart-container {
  border-radius: 5px;
  margin: 0 auto;
}
.sales-sku-title {
  width: 100%;
  align-items: center;
  font-size: 16px;
  padding: 0 5px;
  font-weight: bold;
  border-bottom: 2px solid;
  border-image: linear-gradient(134deg, rgba(80, 232, 255, 1), rgba(50, 210, 255, 0.16), rgba(45, 206, 255, 0)) 1 1;
  background: linear-gradient(270deg, rgba(9,101,173,0.33) 0%, #0967B1 100%);
  position: relative;
  .el-icon-close{
    padding: 10px;
    font-size: 20px;
    cursor: pointer;
    font-weight: bold;
    background: -webkit-linear-gradient(bottom, #FFFFFF, #2E9BE2); /* Safari/Chrome */
    background: linear-gradient(to bottom, #FFFFFF, #2E9BE2); /* Standard syntax */
    -webkit-background-clip: text; /* Safari/Chrome */
    background-clip: text;
    color: transparent;
  }
  &::before{
    content: "";
    opacity: 0.4;
    position: absolute;
    right: 80px;
    top: 0;
    bottom: 0;
    width: 20px;
    background: linear-gradient(48deg, #0967B1 0%, #0967B1 100%);
    transform: skewX(35deg);
  }
  &::after{
    content: "";
    opacity: 0.4;
    position: absolute;
    right: 45px;
    top: 0;
    bottom: 0;
    width: 20px;
    background: linear-gradient(48deg, #0967B1 0%, rgba(9,101,173,0.33) 100%);
    transform: skewX(35deg);
  }
}

.sales-sku-top {
  font-size: 16px;
  padding: 0 5px;
  font-weight: bold;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  margin-top: 5px;
  margin-left: 8px;
  .tag{
    width: 4px;
    height: 20px;
    display: inline-block;
    background: linear-gradient(48deg, #27B0FF 0%, #FAFDFF 99%, #FAFDFF 100%);
    transform: skewX(-25deg);
  }
  .top10{
    padding-left: 15px;
    line-height: 20px;
    width: 100px;
    position: relative;
    color: #FFFFFF;
    &::after{
      content: "";
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      height: 100%;
      background: linear-gradient(90deg, #136AB3 0%, rgba(0,0,0,0) 100%);
      transform: skewX(-25deg);
      z-index: -1;
    }
  }
}
</style>
