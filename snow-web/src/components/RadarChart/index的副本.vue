<template>
  <div id="chart-container" ref="chartContainer" :class="className" :style="{ height: height, width: width }"/>
</template>

<script>
import * as echarts from 'echarts'

export default {
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '160px'
    },

  },
  data() {
    return {
      chart: null,
      colorList: ['#FF4545', '#AC45FF', '#FFB716', '#00fcff',
        '#F31CB3', '#0074ee', '#88bb24', '#fffe33'],
      resultData: {}
    }
  },
  mounted() {
    // this.initChart()
  },
  beforeDestroy() {
    this.clear()
  },
  methods: {
    /**
     * 清空
     * */
    clear() {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    },
    /**
     * 初始化
     * */
    initChart(resultData) {
      this.clear()
      this.resultData = resultData
      if (!resultData.length) {
        return;
      }
      let valueList = []
      let firstValueList = []
      let total = 0
      let max = resultData.sort(function (a, b) {
        total = total + Number(b.count)
        return Number(b.count) - Number(a.count);
      })[0]
      let maxCount = parseInt(Number(max.count) * 1.2)
      let indicatorList = resultData.map(item => {
        valueList.push(maxCount)
        firstValueList.push(Number(item.count))
        return {name: item.name, max: maxCount}
      })
      let chartDom = this.$refs.chartContainer
      this.chart = echarts.init(chartDom);
      let valueResultList = valueList.map((item, index) => {
        let list = valueList.map((items, indexs) => {
          if (index == indexs) {
            return item
          } else {
            return 999999
          }
        })
        return list
      })
      let paramsList = valueList.map((item, index) => {
        return {
          name: 'category' + index,
          type: 'radar',
          data: [
            {
              value: valueResultList[index],//设置第一种颜色的点
            }
          ],
          itemStyle: {
            borderWidth: 2,
            color: this.colorList[index]
          },
          lineStyle: {
            width: 0,
            labelLine: {
              show: false   //隐藏标示线
            }
          },
          // silent: true,
          z: index + 1,
        }
      })

      let option = {
        color: ['#00fcff'],
        legend: {
          data: []
        },
        radar: {
          indicator: indicatorList,
          radius: 50,
          axisName: {
            formatter: '{value}',
            color: '#fff'
          },
          splitArea: {
            areaStyle: {
              color: '#0d1a37',
              shadowColor: 'rgba(0, 0, 0, 0.2)',
              shadowBlur: 10
            }
          },
          axisLine: {
            lineStyle: {
              color: '#0b3463'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#0b3463'
            }
          },

        },
        series: [
          {
            name: 'category',
            type: 'radar',
            symbol: 'none',
            tooltip: {
              trigger: 'item'
            },
            data: [
              {
                value: firstValueList,
                name: 'Allocated Budget',
                areaStyle: {
                  color: 'rgba(0,252,255,0.5)'
                },
              },
            ]
          },
          ...paramsList
        ],
      };
      option && this.chart.setOption(option);

    }
  }
}
</script>
<style lang="scss" scoped>
#chart-container {
  border-radius: 5px;
  margin: 0 auto;
}
</style>
