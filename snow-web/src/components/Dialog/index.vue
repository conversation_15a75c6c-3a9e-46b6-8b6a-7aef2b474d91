<template>
  <!-- 封装弹框 -->
  <div class="popup">
    <el-dialog
      :close-on-click-modal="closeInAnyWay"
      :close-on-press-escape="closeInAnyWay"
      :visible.sync="dialogVisible"
      v-bind="$attrs"
      :width="width"
      :before-close="handleClose"
      v-on="$listeners"
    >
      <slot name="content"/>
      <span v-if="btnTxt && btnTxt.length" slot="footer" class="dialog-footer">
        <el-button v-if="btnTxt[0]" :size="btnSize" @click.native="handleCancel">{{
            $t(`reaCommon.${btnTxt[0]}`)
          }}</el-button>
        <el-button v-if="btnTxt[1]" :size="btnSize" type="primary"
                   @click.native="handleConfirm">{{ $t(`reaCommon.${btnTxt[1]}`) }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    closeInAnyWay: {
      type: Boolean,
      default: false
    },
    visible: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: '600px'
    },
    btnSize: {
      type: String,
      default: 'medium'
    },
    btnTxt: {
      type: Array,
      default: () => {
        return ['cancel', 'confirm']
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('dialogClose')
    },
    handleCancel() {
      this.$emit('dialogCancel')
    },
    handleConfirm() {
      this.$emit('dialogConfirm')
    }
  }
}
</script>

<style lang="scss">

</style>
