<template>
  <!--    @row-click="rowClick" :border="border" @selection-change="selectionChange"-->
  <div class="table__content">
    <el-table
      ref="multipleTable"
      v-loading="loading"
      :data="tableData"
      stripe
      border
      class="ele_table_container"
      v-bind="$attrs"
      :max-height="tableMaxHeight"
      :row-class-name="tableRowClassName"
      @row-click="rowClick"
      @selection-change="handleSelectionChange"
      v-on="$listeners"
    >
      <!--设置勾选模式-->
      <template v-if="tableHeader.length">
        <el-table-column
          v-if="tableHeader[0].selection"
          :selectable="selecTableFun"
          v-bind="tableHeader[0].attrs"
          type="selection"
          style="width: 55px;"
        />
      </template>
      <!--普通渲染-->
      <template v-for="(th, key) in tableHeader">
        <el-table-column
          :key="key"
          v-bind="th.attrs"
          :width="th.width"
          min-width="140px"
          :sortable="th.sortable"
          :prop="th.prop"
          :label="th.labelKey ? $t(`reaCommon.${th.labelKey}`) : th.label"
        >
          <template slot-scope="scope">
            <!--图片-->
            <template v-if="th.prop == 'image'">
              <el-image
                :style="th.style ? th.style : ''"
                :src="scope.row[th.prop]"
                fit="cover"
              />
            </template>
            <!--slot-->
            <template v-else-if="th.slot">
              <slot :name="th.slot" :index="scope.$index" :row="scope.row" :items="th"/>
            </template>
            <!-- 普通渲染-->
            <template v-else>{{ th.label ? (isEmpty(scope.row[th.prop]) ? '-' : scope.row[th.prop]) : '' }}</template>
          </template>
        </el-table-column>
      </template>
      <!-- 操作按钮 -->
      <el-table-column v-if="operates.length" class-name="wbg-cell control_btn_cell" v-bind="operatesProps"
                       :label="operatesProps.labelKey ? $t(`reaCommon.${operatesProps.labelKey}`) : operatesProps.label">
        <template slot-scope="{row}">
          <template v-for="(btn, index) in operates">
            <el-button
              v-if="btn.isButton"
              :type="btn.type ? btn.type : 'primary'"
              :size="btn.size ? btn.size : 'mini'"
              @click.stop="btn.method(index, row)"
            >{{ btn.labelKey ? $t(`reaCommon.${btn.labelKey}`) : btn.label }}
            </el-button>
            <slot v-else-if="btn.slot" :name="btn.slot" :items="btn" :row="row" :index="index"/>
            <span
              v-else
              class="table__oprate--item"
              @click.stop="btn.method(index, row)"
            >{{ btn.labelKey ? $t(`reaCommon.${btn.labelKey}`) : btn.label }}</span>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <div class="flex flex-x-start flex-y-center">
      <div v-if="tableHeader[0].selection" class="frontend_control_box" style="flex-shrink: 0;">
        <span class="checked_item" style="margin-right:15px;">{{ $t('reaCommon.checkedItems') }}<span
          style="display:inline-block;width:25px;text-align: right;"
          :class="{'txtCenter':language !== 'en'}">{{ selectedCount }}</span>{{ $t('reaCommon.item') }}</span>
        <el-button v-if="$havePermission('0301',$route.meta.functionList)" size="medium" @click.native="batchEditing">
          {{ $t('reaCommon.edit') }}
        </el-button>
        <el-button v-if="$havePermission('0501',$route.meta.functionList)" style="margin-left:20px;" size="medium"
                   @click.native="batchDelete">{{ $t('reaCommon.delete') }}
        </el-button>
      </div>
      <div class="flex-1">
        <el-pagination
          v-if="!isHidePage"
          background
          class="pagination__content"
          :current-page="pagination.page_num"
          :page-size="pagination.page_size"
          :page-sizes="pageSizes"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.count"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {mapGetters} from 'vuex'

export default {
  name: 'new-table',
  props: {
    outerHeight: {
      type: Number,
      default: 453
    },
    /**
     * 表数据
     * selection item 置灰：disabled 设置选中项active 默认需要table数据 新传的数据无效
     * */
    tableData: {
      type: Array,
      default: () => {
        return []
      }
    },
    /**
     * 表头
     * @description 必传
     * */
    tableHeader: {
      type: Array,
      default: () => {
        return []
      }
    },
    /**
     * 操作相关按钮
     * @example operates:[{ id:1, size: 'mini', label:'编辑', type:'info', method:(index,row)=>{ this.handleManage(index, row)}}],
     * */
    operates: {
      type: Array,
      default: () => {
        return []
      }
    },
    /**
     * 加载状态
     * */
    loading: {
      type: Boolean,
      default: false
    },
    /**
     * 表格宽高
     * */
    height: {
      type: String
    },
    /**
     * 分页数据
     * */
    pagination: {
      type: Object,
      default: () => {
        return {page_size: 10, page_num: 1, count: 20}
      }
    },
    /**
     * 隐藏分页
     * */
    isHidePage: {
      type: Boolean,
      default: false
    },
    /**
     * 操作按钮配置属性
     * */
    operatesProps: {
      type: Object,
      default: () => {
        return {align: 'center', width: '', label: '操作'}
      }
    }
  },
  data() {
    return {
      pageSizes: [10, 20, 30, 40, 50, 100],
      tableMaxHeight: '100%',
      selectedCount: 0,
      list: []
    }
  },
  computed: {
    ...mapGetters([
      'language'
    ])
  },
  watch: {
    tableData: {
      handler(val) {
        this.initSelection()
        this.$emit('update:tableData', val)
      },
      deep: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initSelection()
      this.getHeight()
    })
  },
  methods: {
    rowClick(row, column, event) {
      return
      console.log(column)
      const index = this.list.findIndex(item => {
        // 判断已选数组中是否已存在该条数据
        return item.id == row.id
      })
      if (index == -1) {
        // 如果未存在，设置已选状态，并在list中添加这条数据
        this.$refs.multipleTable.toggleRowSelection(row, true) // 设置复选框为选中状态
        this.list.push(row)
      } else {
        // 如果已存在，设置未选状态，并在list中删除这条数据
        this.$refs.multipleTable.toggleRowSelection(row, false) // 设置复选框为未选状态
        this.list.splice(index, 1)
      }
    },
    getHeight() {
      let outerHeight = this.outerHeight
      if (this.language === 'en') {
        outerHeight += 58
      }
      // console.log(outerHeight)
      if ((window.innerHeight - outerHeight) < 400) {
        this.tableMaxHeight = '400px'
      } else {
        this.tableMaxHeight = window.innerHeight - outerHeight + 'px'
      }
    },
    tableRowClassName({row}) {
      if (row.visit_status && row.visit_status == 1) {
        return 'warning-row'
      }
      return ''
    },
    /**
     * 判断是否为空值
     * */
    isEmpty(val) {
      return val + '' === 'null' || val + '' === 'undefined' || val === ''
    },
    /**
     * 动态表头重新渲染function
     * */
    doLayout() {
      this.$nextTick(() => {
        if (this.$refs['multipleTable']) {
          this.$refs['multipleTable'].doLayout()
        }
      })
    },
    /**
     * selection设置不可选择---disabled
     * */
    selecTableFun(row, index) {
      if (this.tableData.some(el => el.disabled && (el.id == row.id))) {
        return false
      } else {
        return true
      }
    },
    /**
     * selection选中数据赋值要求是原table数据----active
     * */
    initSelection() {
      if (this.tableHeader.length && this.tableHeader[0].selection) {
        this.tableData.some(row => {
          if (row.active) {
            this.$refs.multipleTable.toggleRowSelection(row, true)
          }
        })
      }
    },
    /**
     * 取消所有选中
     * */
    clearSelection() {
      if (this.tableHeader.length && this.tableHeader[0].selection) {
        this.$refs.multipleTable.clearSelection()
      }
    },
    /**
     * 多行选中
     * */
    handleSelectionChange(val) {
      this.selectedCount = val.length
      this.$emit('nodeConfigHandler', {
        param: {
          selection: val
        },
        component: 'selectionChange'
      })
    },
    /**
     * 多行选中编辑
     * */
    batchEditing() {
      if (!this.selectedCount) {
        this.$message.warning(this.$t('reaCommon.pleaseLeastOne'))
        return
      }
      this.$emit('nodeConfigHandler', {
        component: 'batchEditing'
      })
    },
    /**
     * 多行选中删除
     * */
    batchDelete() {
      if (!this.selectedCount) {
        this.$message.warning(this.$t('reaCommon.pleaseLeastOne'))
        return
      }
      this.$emit('nodeConfigHandler', {
        component: 'batchDelete'
      })
    },
    /**
     * 分页发送
     * */
    handleSizeChange(val) {
      this.$emit('pageConfigHandler', {
        param: {
          page_num: 1,
          page_size: val
        },
        component: 'pageChange'
      })
    },
    /**
     * 分页监听
     * */
    handleCurrentChange(val) {
      this.$emit('pageConfigHandler', {
        param: {
          page_num: val
        },
        component: 'pageChange'
      })
    }
  }
}
</script>
<style scoped>
.checked_item {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #262626;
  line-height: 20px;
}

.txtCenter {
  text-align: center !important;
}
</style>

<style lang="scss">
@import "~@/styles/variables.scss";

.frontend_control_box .el-button--default {
  border-color: $themeColor;
  color: $themeColor;
}

.cell {
  word-break: break-word !important;
}

.el-table .el-table__body .el-table__row td.el-table__cell.wbg-cell {
  background-color: white !important;
}

.el-table .el-table__body .control_btn_cell .cell {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.el-table .warning-row td.el-table__cell {
  background: rgba(219, 39, 195, 0.06) !important;
  box-shadow: 1px 1px 0px 0px #E7EAEC, -1px -1px 0px 0px #E7EAEC;
}

.el-table .warning-row:hover > td {
  background: rgba(219, 39, 195, 0.06) !important;
  box-shadow: 1px 1px 0px 0px #E7EAEC, -1px -1px 0px 0px #E7EAEC;
}

.table__content {
  background: #fff;
  height: 100%;

  .ele_table_container {
    border-radius: 6px;
  }

  .el-table th {
    background: #ECF6FF !important;
  }

  .el-table th > .cell {
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #0471D4;
  }

  .pagination__content {
    text-align: right;
    padding: 26px 0;
  }

  .table__oprate--item {
    font-size: 14px;
    color: #0067C4;
    position: relative;
    display: inline-block;
    padding: 0 10px;
    cursor: pointer;

    &:before {
      content: '';
      display: block;
      height: 13px;
      width: 1px;
      background: #0067C4;
      opacity: .26;
      position: absolute;
      right: 0;
      top: 5px;
    }

    &:first-child {
      //padding-left:0;
      //padding-right:20px;
    }

    &:last-child {
      //padding-left:20px;
      //padding-right:0;

      &:before {
        content: '';
        display: none;
      }
    }
  }

  .table__oprate--itemimport {
    &:before {
      content: '';
      display: block !important;
    }
  }

}
</style>
