<template>
  <div class="digital__box">
    <canvas ref="figure" :style="{ width: canvasW + 'px', height: canvasH + 'px' }" />
    <div v-if="showPopup" class="popup-photo-image" :style="{left: popupLeft, top: popupTop}">
      <el-image class="image-slot" style="height: 260px;width: 450px;" fit="contain" :src="currentPolygonUrl+'?imageView2/1/w/900/h/600'">
        <div slot="placeholder">
          <span class="el-icon-loading icon" /> {{ $t('store.loading') }}<span class="dot">...</span>
        </div>
        <div slot="error">
          <div class="" style="color: #ffffff">{{ $t('store.noImage') }}</div>
        </div>
      </el-image>
      <div class="scene-liu">
        <div class="scene-liu-item">
          <img class="xiaoliu-image" src="@/assets/screen/scene.png">
          <div class="liu-bg scene__name">
            <div>{{sceneName}}</div>
            <div>{{deviceCoce}}</div>
          </div>
        </div>
        <div class="scene-liu-item flex-1">
          <img class="xiaoliu-image" src="../../assets/screen/xiaoliu.png">
          <div class="liu-bg">{{ $t('store.sales') }}：<span class="count" >{{ saleNum || 0 }}</span></div>
        </div>
        <div class="scene-liu-item">
          <img class="renliu-image" src="../../assets/screen/renliu.png">
          <div class="liu-bg">{{ $t('store.trafficVolume') }}：<span class="count" >{{ flowNum || 0 }}</span></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// import { fabric } from 'fabric'
import salesPng from '../../assets/images/sales.png'

export default {
  props: {
    canvasW: {
      type: Number | String,
      default: 320
    },
    canvasH: {
      type: Number | String,
      default: 600
    }
  },
  data() {
    return {
      disLeft: 0,
      disTop: 0,
      scale: 0,
      // modelData: {
      //   'model_2d': 'https://rea-cos-1305849923.cos.ap-shanghai.myqcloud.com/2d.png',
      //   points: [{"type": 1, "point": [{"x": 221, "y": 259}, {"x": 335, "y": 259}, {"x": 335, "y": 373}, {"x": 221, "y": 373}]}]
      // },
      modelData: {},
      canvas: null,
      showPopup: false,
      popupLeft: '',
      popupTop: '',
      currentPolygonUrl: '',
      flowNum: '',
      saleNum: '',
      sceneName: '',
      deviceCoce: '',
      isLock: false
    }
  },
  mounted() {
    const image = new Image()
    image.src = salesPng
    this.image = image
    // this.getDetail()
  },
  methods: {
    /**
     * 获取数据
     * */
    getDetail(e, isInit) {
      e = JSON.parse(JSON.stringify(e))
      e.points = e.points.map(item => {
        item.type = 1
        item.point = item.points
        delete item.points
        return item
      })
      if (!this.canvas || isInit) {
        if (this.canvas) {
          this.canvas.clear()
          this.canvas.dispose()
        }
        this.modelData = e
        setTimeout(() => {
          this.fabricInit()
        }, 10)
      }
      this.showPopup = false
    },
    /**
     * 刷新数量
     */
    changeNum(e) {
      this.modelData.date = e.date
      this.modelData.points.map(item => {
        const samePoint = e.points.filter(items => {
          return items.scene_code == item.scene_code
        })[0]
        if (samePoint) {
          item.flow_num = samePoint.flow_num + ''
          item.sale_num = samePoint.sale_num + ''
          item.url = samePoint.url
        } else {
          item.flow_num = '0'
          item.sale_num = '0'
          item.url = ''
        }

        // item.flowPolygon.set({text: item.flow_num })
        // item.salePolygon.set({text: item.sale_num })
        return item
      })
      this.canvas.renderAll()
    },
    /**
     * 设置高亮
     * type 1=冰柜 2=货架
     * opacity 0=隐藏 1=显示
     * */
    highlight(type, visible) {
      if (this.modelData.polygon && this.modelData.polygon[type]) {
        this.modelData.polygon[type].visible = visible
        this.canvas.renderAll()
      }
    },
    /**
     *  绘制多边形
     */
    fabricPolygon(points, type) {
      const point = points.point
      const arr = point.map(v => {
        v.x = v.x * this.scale + this.disLeft
        v.y = v.y * this.scale + this.disTop
        return v
      })
      const polygon = new fabric.Polygon(arr, {
        fill: points.fill_color || '#00FCFF',
        stroke: points.fill_color || '#00FCFF',
        opacity: 0.7,
        strokeWidth: 1,
        visible: true,
        selectable: false,
        hoverCursor: 'pointer'
      })

      if (!this.modelData.polygon) {
        this.modelData.polygon = {}
      }
      this.modelData.polygon[type] = polygon
      this.canvas.add(polygon)
      polygon.url = points.url
      polygon.scene_code = points.scene_code
      polygon.on('mouseover', (e) => {
        this.showPopup = true
        const param = this.modelData.points.filter(item => {
          return polygon.scene_code == item.scene_code
        })[0]
        const currentPolygonUrl = param && param.url ? param.url : ''
        const scene_objects = param.device_group_bounding_box
        // 处理图片裁剪
        if (window.isOpenCut && Object.keys(scene_objects).length) {
          const sceneW = scene_objects.tr[0] - scene_objects.tl[0]
          const sceneH = scene_objects.bl[1] - scene_objects.tl[1]
          this.currentPolygonUrl = `${currentPolygonUrl}?imageMogr2/thumbnail/!30p/cut/${sceneW}x${sceneH}x${scene_objects.tl[0]}x${scene_objects.tl[1]}`
        } else {
          this.currentPolygonUrl = `${currentPolygonUrl}?imageMogr2/thumbnail/!30p`
        }
        this.saleNum = param && param.sale_num ? param.sale_num : 0
        this.flowNum = param && param.flow_num ? param.flow_num : 0
        this.deviceCoce = param && param.device_group_code ? param.device_group_code : ''
        this.sceneName = param && param.scene_name || ''
        const position = polygon.getCoords()
        this.popupLeft = position[0].x + (position[2].x - position[0].x) / 2 - 10 - 250 + 'px'
        this.popupTop = position[0].y - 20 - 340 + 'px'
      })
      polygon.on('mouseout', () => {
        this.showPopup = false
      })

      // this.fabricMarker(arr,points)
      return polygon
    },
    /**
     * 绘制人流量销量
     * @param point
     */
    fabricMarker(point, points) {
      const pisition = this.getPosition(point, points)
      const { left, top, imageLeft, imageTop, sales } = pisition

      // 画圆
      const circleObj = new fabric.Circle({
        radius: 5,
        fill: '#00FCFF',
        left: left,
        top: top,
        selectable: false,
        hoverCursor: 'default'
      })
      this.canvas.add(circleObj)
      const image = this.image
      const imageObj = new fabric.Image(image, {
        left: imageLeft,
        top: imageTop,
        scaleX: sales,
        scaleY: sales,
        selectable: false,
        hoverCursor: 'default'
      })
      this.canvas.add(imageObj)
      const textObj = new fabric.Textbox((points.sale_num || 0) + '', {
        left: imageLeft + 250 * sales,
        top: imageTop + 115 * sales,
        fontSize: 50 * sales,
        fontFamily: 'Microsoft YaHei',
        textAlign: 'center',
        fill: '#FFF',
        selectable: false,
        hoverCursor: 'default'
      })
      points.salePolygon = textObj
      this.canvas.add(textObj)
      const textObj1 = new fabric.Textbox((points.flow_num || 0) + '', {
        left: imageLeft + 250 * sales,
        top: imageTop + 220 * sales,
        fontSize: 50 * sales,
        fontFamily: 'Microsoft YaHei',
        textAlign: 'center',
        fill: '#FFF',
        selectable: false,
        hoverCursor: 'default'
      })
      this.canvas.add(textObj1)
      points.flowPolygon = textObj1
    },
    /**
     * 获取定位
     * @param point
     * @param points
     * @returns {{top: number, left: number, imageLeft: number, width: number, imageTop: number, sales: number, height: number}}
     */
    getPosition(point, points) {
      const width = parseInt(point[2].x) - parseInt(point[0].x)
      const height = parseInt(point[2].y) - parseInt(point[0].y)

      let left = 0
      let top = 0
      const type = points.index % 4
      // type = 3
      switch (type) {
        case 0: // 上
          left = point[0].x + width / 2 - 5
          top = point[0].y - 16
          break
        case 1: // 下
          left = point[0].x + width / 2 - 5
          top = point[2].y + 6
          break
        case 2: // 右
          left = point[1].x + 6
          top = point[0].y + height / 2 - 8
          break
        case 3:
          left = point[0].x - 16
          top = point[0].y + height / 2 - 8
          break
      }
      const sales = this.scale * 0.9
      let imageLeft = 0
      let imageTop = 0
      const image = this.image
      switch (type) {
        case 0:
          imageLeft = left - (image.width - 80) * sales / 2
          imageTop = top - image.height * sales + 20 * sales
          break
        case 1:
          imageLeft = left - (image.width - 80) * sales / 2
          imageTop = top + 40 * sales
          break
        case 2:
          imageLeft = left + 50 * sales
          imageTop = top - image.height * sales / 2 + 20 * sales
          break
        case 3:
          imageLeft = left - (image.width - 20) * sales
          imageTop = top - image.height * sales / 2 + 20 * sales
          break
      }
      return { left, top, imageLeft, imageTop, sales }
    },
    /**
     * 初始化画布
     * 以门的宽高作为画布尺寸 多扇门则计算总宽高（取最大值）
     * 其他尺寸则减掉门的尺寸
     */
    fabricInit() {
      const height = this.canvasH
      const width = this.canvasW
      const def = {
        width,
        height
        // backgroundColor: '#fff',
      }
      const url = this.modelData['model_2d']
      const canvas = this.canvas = new fabric.Canvas(this.$refs.figure, def)
      fabric.Image.fromURL(url, oImg => {
        this.$emit('imageOnload', false)
        const scale = this.scale = this.calculateScale(oImg, canvas.width, canvas.height)
        const left = (canvas.width - oImg.width * scale) / 2
        const top = (canvas.height - oImg.height * scale) / 2
        this.getBox(oImg.width, oImg.height)
        this.disLeft = left
        this.disTop = top
        canvas.setBackgroundImage(oImg, canvas.renderAll.bind(canvas), {
          top,
          left,
          scaleX: scale,
          scaleY: scale
        })
        // 预画高亮
        this.modelData.points = this.modelData.points.map((v, k) => {
          v.index = k
          const polygon = this.fabricPolygon(v, v.type)
          v.polygon = polygon
          return v
        })
      })

      // 放大缩小
      canvas.on('mouse:wheel', opt => {
        this.showPopup = false
        const delta = opt.e.deltaY // 滚轮，向上滚一下是 -100，向下滚一下是 100
        let zoom = canvas.getZoom() // 获取画布当前缩放值
        zoom *= 0.999 ** delta
        if (zoom > 20) zoom = 20
        if (zoom < 0.01) zoom = 0.01
        // 以左上角为原点
        // this.canvas.setZoom(zoom)
        // 以鼠标所在位置为原点缩放
        canvas.zoomToPoint(
          { // 关键点
            x: opt.e.offsetX,
            y: opt.e.offsetY
          },
          zoom
        )
        opt.e.preventDefault()
        opt.e.stopPropagation()
      })
      let panning = false
      canvas.on('mouse:down', (e) => {
        panning = true
        canvas.selection = false
      })
      // 鼠标抬起事件
      canvas.on('mouse:up', (e) => {
        panning = false
        canvas.selection = true
        if (e.isClick) {
          this.getPopup(e)
        }
      })
      // 移动画布事件
      canvas.on('mouse:move', (e) => {
        if (panning && e && e.e) {
          const delta = new fabric.Point(e.e.movementX, e.e.movementY)
          canvas.relativePan(delta)
        }
      })
      canvas.renderAll()
    },
    /**
     * 鼠标事件
     * @param e
     */
    getPopup(e) {
      let obj = null
      this.modelData.points.forEach(v => {
        if (v.polygon && v.polygon.containsPoint(e.pointer)) {
          obj = v
        }
      })
      // 点击进入详情
      if (obj) {
        window.localStorage.setItem('scenesDate', this.modelData.date)
        let query = {
          path: '/store/visit-scene-detail',
          query: {
            scene_code: obj.scene_code || '',
            date: this.modelData.date || '',
            group_id: obj.device_group_id || '',
            device_group_type: obj.device_group_type || '1',
            store_id: this.modelData.store_id || ''
          }
        }
        localStorage.removeItem('dashboardQuery')
        this.$router.push(query)
      }
    },
    getBox(width, height) {
      const smallWidth = this.image.width
      const smallHeight = this.image.height
      const box = []
      for (let i = 0; i <= width; i += smallWidth) {
        for (let j = 0; j <= height; j += smallHeight) {
          box.push({ x: i, y: j, width: smallWidth, height: smallHeight })
        }
      }
    },
    /**
     * 计算图片缩放比例
     * */
    calculateScale(img, width, height) {
      const [imgWidth, imgHeight] = [img.width, img.height]
      const [widthScale, heightScale] = [width / imgWidth, height / imgHeight]
      return Math.min(widthScale, heightScale)
    }
  }
}
</script>

<style lang="scss">
.digital__box {
  text-align: center;
  position: relative;

  .canvas-container {
    margin: 0 auto
  }
}

.popup-photo-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 500px;
  height: 350px;
  background-image: url("~@/assets/screen/hover-bg.png");
  background-size: 100% 100%;
  border-radius: 4px;
  overflow: hidden;
  padding: 10px;
  z-index: 101;
  padding-top: 30px;

}

.image-slot {
  height: 260px;
  line-height: 260px;
  font-size: 16px;
  width: 450px;
  background-color: rgba(255, 255, 255, 0.05);
  .icon {
    font-size: 16px;
  }
}

.scene-liu {
  padding: 0 15px;
  margin-top: 5px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;

  .scene-liu-item {
    //margin: 0 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    .xiaoliu-image {
      width: 30px;
    }
    .renliu-image {
      width: 30px;
    }
    .liu-bg{
      background: linear-gradient(90deg, rgba(2,52,80,0) 0%,  rgba(0,95,151,0.5) 20%, #005F97 50%, rgba(0,95,151,0.5) 80%, rgba(1,42,66,0) 100%);
      line-height: 30px;
      text-align: left;
      padding:0 5px;
      min-width: 80px;
    }
    .count {
      font-weight: bold;
    }
    .scene__name{
      background: transparent;
      & > div{
        line-height: 15px;
        font-size: 12px;
      }
    }
  }
}
.device-code{
  position: absolute;
  right: 30px;
  bottom: 65px;
  font-size: 16px;
}
</style>
