<template>
  <div class="header box">
    <Logo />
    <div v-if="storeName" class="title">{{ storeName }}</div>
    <div class="scene__name overflow">
      {{title || $t('store.storeDetail')}}
    </div>
    <div v-if="back" class="back-btn" @click="$router.go(-1)">{{ $t('reaCommon.back') }}</div>
  </div>
</template>
<script>
export default {
  name: 'header-box',
  props: {
    title: {
      type: String,
      default: ''
    },
    storeName: {
      type: String,
      default: ''
    },
    back: {
      type: Boolean,
      default: false
    }
  }
}
</script>
<style lang="scss" scoped>
.header {
  background: url('~@/assets/screen/header.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  height: 56px;
  margin-bottom: 8px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  min-width: 1000px;
  .title{
    font-size: 18px;
    margin-top: 15px;
    color: #fff;
  }
  .scene__name {
    color: #fff;
    font-size: 22px;
    line-height: 56px;
    text-align: center;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -28px;
    margin-left: -200px;
    width: 400px;
    text-align: center;
    letter-spacing: 5px;
    font-weight: 500;
  }

  .back__but {
    width: 88px;
    height: 36px;
    border-radius: 4px;
    border: 1px solid #07D3D7;
    line-height: 36px;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    color: #07D3D6;
    cursor: pointer;
    margin-right: 22px;
    margin-top: 7px;
    display: inline-block;

    &.primary {
      background: #07D3D6;
      color: #333333;
      margin-left: 20px;
      margin-right: 100px;
      height: 36px;
    }
  }
  .back-btn {
    background-image: url('~@/assets/screen/back.png');
    background-size: 100% 100%;
    height: 44px;
    line-height: 44px;
    text-align: center;
    cursor: pointer;
    display: inline-block;
    position: absolute;
    right: 7%;
    width: 8%;
    margin-right: 30px;
    margin-top:5px;
    color: #fff;
    &:hover{
      background-image: url('~@/assets/screen/back-hover.png');
    }
  }
  &.box {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }

}
</style>
