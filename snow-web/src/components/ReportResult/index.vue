<template>
  <div v-loading="loading" class="reportdetial-html">
    <div v-if="reportRet.image">
      <div class="report-time">识别时间：{{ reportRet.updated_at || '-' }}</div>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="det-pro-right">
            <div class="det-pro-imgshow flex">
              <label class="label flex-1">
                查看全部：
                <el-switch
                    v-model="isChoseAll"
                    inactive-color="#ccc"
                    @change="choseAll"
                />
              </label>
              <div class="opt-all">
                <span class="opt-img el-icon-zoom-out" @click="scaleImg(200)" />
                <span class="opt-img el-icon-zoom-in" @click="scaleImg(100)" />
              </div>
            </div>
            <div ref="canvasBox"  class="det-pro-img">
              <!-- canves -->
              <div class="canvas-box">
                <canvas
                    id="canvas-bg"
                    ref="canvasbg"
                    :style="{height: canvasHeight+'px',width: canvasWidth+'px'}"
                >
                  您的浏览器不支持canvas
                </canvas>
                <canvas
                    id="canvas"
                    ref="canvas"
                    :style="{height: canvasHeight+'px',width: canvasWidth+'px'}"
                    @mousemove="showThisPop"
                    @mouseout="showPopMsg = false"
                >
                  您的浏览器不支持canvas
                </canvas>
              </div>
            </div>
          </div>
          <div class="det-pro-right" style="margin-top: 10px;">
            <!-- 原图 -->
            <div class="context-box" style="padding-top: 0">
              <div class="bwt det-item">
                <div class="det-type-title"><span> 原图</span></div>
              </div>
              <div class="bwt original-img" style="margin-top: 10px">
                <el-image
                    :src="reportRet.image"
                    style="width: 80px;height: 80px;"
                    :preview-src-list="[reportRet.image]"
                />
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="det-pro-left">
            <div class="det-item margin0">
              <div class="det-type-title">SKU名称</div>
            </div>
            <div class="det-pro-list" style="margin-top: 10px;">
              <div>
                <div class="pro-item-srcoll">
                  <div v-for="(item, index) in proshopList">
                    <div
                        class="pro-item-tit pro-over box pro__item-li"
                        :class="item.isActive && !item.list ? 'pro-active' : ''"
                        @click="proFirstfloor(index)"
                    >
                      <div class="flex1 box">
                        <!--                        <div v-if="bigIndex == 2">{{ item.id }}^</div>-->
                        <div class="flex1 textover">{{ item.name }}</div>
                        <div>（{{ item.count }}）</div>
                      </div>
                      <div v-if="item.price_tag && item.price_tag.price > 0" class="mr10">
                        ¥{{ item.price_tag.price.toFixed(2) }}
                      </div>
                    </div>
                    <ul v-show="item.isActive">
                      <li
                          v-for="(it, ind) in item.list"
                          class="pro-item-li pro-over "
                          :class="it.isActive ? 'pro-active' : ''"
                          @click="proSecondfloor(index, ind)"
                      >
                        <span><i>{{ it.sku_id }}^</i>{{ it.sku_name }}</span>（{{ it.objects.length }}）
                        <i
                            v-if="it.price_tag && it.price_tag.price > 0"
                            class="fr"
                        >¥{{ it.price_tag.price.toFixed(2) }}</i>
                      </li>
                    </ul>
                  </div>
                  <div v-if="proshopList.length <= 0" class="bank-data">
                    <el-empty description="暂无数据" />
                  </div>
                </div>
              </div>

            </div>
          </div>
        </el-col>
      </el-row>

      <div class="contain-box mg25">
        <div>
          <!-- 场景+识别产品 -->
          <div class="bwt context-box">
            <div class="">
              <div class="overhidden pb25 " />
            </div>
          </div>

        </div>
      </div>
      <!-- canves 鼠标指上去 -->
      <div
          v-show="showPopMsg"
          class="popMsg"
          :style="{'left':(left-250) + 'px','top':top + 'px'}"
          v-html="popText"
      />
    </div>
    <div v-else class="bank-data">
      <el-empty v-if="!loading " >
        <div slot="description" class="flex-center">
          <template v-if="isError">
            暂无数据
          </template>
          <template v-else>
            <div>正在识别中，请耐心等待... </div>
            <div class="el-icon-loading"></div>
          </template>
        </div>
        <el-button v-if="!isError" type="primary" @click="reSend">重新请求</el-button>
      </el-empty>
    </div>
  </div>
</template>
<script>

import { getDetectReport } from '@/api/device'
import { getTime } from '@/utils/validate'
export default {
  components: {},
  props: {
    id: {
      type: [String, Number],
      default: ''
    },
    imageKey: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      showDialog: false,
      dialogTitle: '',
      haveDebug: false,
      debug: false,
      isHaveResults: true, // 是否有识别结果
      isChoseAll: false, // 是否全选
      isViewLarger: false,
      viewLargerSrc: '',
      viewLargerButtonStyle: '',
      viewLargerIndex: 0,
      loading: false,
      // ing: false,
      storeReportSkuList: [],
      storeReportSkuCount: 0,
      scene_report_id: '', // 场景报告id 用来获取场景列表数据
      protabList: [{
        name: this.$t('reaCommon.reportBrand'),
        isActive: false,
        id: 1,
        listData: []
      },
        {
          name: this.$t('reaCommon.reportCategory'),
          isActive: false,
          id: 2,
          listData: []
        },
        {
          name: this.$t('reaCommon.reportSKU'),
          isActive: true,
          id: 3,
          listData: []
        }
        // {
        //   name: '按物料',
        //   isActive: false,
        //   id: 4,
        //   listData: []
        // },
      ],
      activeName: 3,
      // tab当前数组内容
      proshopList: [{
        name: '',
        count: 0,
        isActive: false,
        list: [{
          name: '0',
          count: 0,
          isActive: false,
          objects: []
        }]
      }],
      // 选中商品信息
      selectSkuList: [],
      // 画图
      canvasHeight: 0,
      canvasWidth: 0,
      canvasBoxW: 0,
      canvasBoxWH: 0,
      avePonitW: 12,
      left: 0,
      top: 0,
      showPopMsg: false,
      popText: '',
      // 是否推送结果
      isPush: false,
      // 基础信息
      scene: {
        origin_pictures: [],
        origin_report: [],
        push_report: []
      },
      reportRet: [],
      bigIndex: 0,
      aroundArr: [],
      isVacancy: false,
      isSkuFiterRead: false,
      skufilterReadParam: {},
      isError:false,
    }
  },
  watch: {
    selectSkuList(e) {
      this.drawSku()
    },
    isAround() {
      if (this.isAround) {
        this.isVacancy = false
        this.isChoseAll = false
      }
    }
  },
  mounted() {
    this.haveDebug = true
  },
  methods: {
    reSend(){
      this.$emit('report')
    },
    initParams(res){
      this.reportRet = {}
      this.getBaseInfo(res)
    },
    /**
     * 修改debug按钮
     * */
    changeDebug() {
      this.scentabFun(0, 200)
      this.changeData()
    },

    /**
     * 清空数据
     * */
    clearParam() {
      this.selectSkuList = []
      this.isChoseAll = false
    },
    /**
     * 菜单点击事件（场景+【按品牌+ ...】）
     * */
    scentabFun(index, type) {
      this.clearParam()
      // 识别产品
      this.protabList.map((v, k) => {
        v.isActive = false
        if (k == index) {
          this.bigIndex = index
          v.isActive = true
          this.proshopList = v.listData
        }
      })
    },
    /**
     * 识别产品：点击第一级分类
     * */
    proFirstfloor(index) {
      this.isAround = false
      this.clearParam()
      if (this.proshopList[index].isActive) {
        this.proshopList[index].isActive = false
        return
      }
      this.proshopList = this.proshopList.map((v, k) => {
        if (k == index) {
          v.isActive = !v.isActive
          if (v.list) {
            v.list.map((val, key) => {
              this.selectSkuList.push.apply(this.selectSkuList, val.objects)
            })
          } else {
            if (v.objects) this.selectSkuList = v.objects
          }
        } else {
          v.isActive = false
        }
        if (v.list) {
          v.list.map((val, key) => {
            val.isActive = true
          })
        }
        return v
      })
    },
    /**
     * 识别产品：点击第二级分类
     * */
    proSecondfloor(index, ind) {
      this.clearParam()
      this.proshopList = this.proshopList.map((v, k) => {
        v.list.map((val, key) => {
          if (k == index) {
            val.isActive = false
            if (k == index && key == ind) {
              val.isActive = true
              this.selectSkuList = val.objects
            }
          }
        })
        return v
      })
    },
    /**
     * 点击查看大图
     * */
    viewLarger(index) {
      const src = this.scene.photos[index].url
      this.viewLargerIndex = index
      this.isViewLarger = true
      setTimeout(() => {
        this.setViewLarger(src)
      }, 20)
    },
    /**
     * 点击全选
     * */
    choseAll() {
      this.selectSkuList = []
      if (this.isChoseAll) {
        this.isVacancy = true
        this.reportRet.sku_list.map((v, k) => {
          this.selectSkuList.push.apply(this.selectSkuList, v.objects)
        })
      } else {
        this.isVacancy = false
        // this.selectSkuList = []
      }
    },
    /**
     * 推送结果
     * */
    pushRet() {
      if (this.scene && this.scene.push_report) {
        this.reportRet = this.isPush ? this.scene.push_report : this.scene.origin_report
        this.scentabFun(0, 200)
        this.changeData(false)
      }
    },
    /**
     * 头部基础信息
     * */
    getBaseInfo(res) {
      if (!res.image) {
        return
      }
      this.showDialog = true
      this.loading = true
      this.isError = false
      // getDetectReport({ id: this.id }).then(res => {
        this.loading = false
        const {image, format_result} = res
        if (image) {
          let structure_detections = []
          format_result.filter(v => {
            v.blocks.filter(val => {
              let structure_detections_item = val.bounding_box
              let tiers = []
              val.tiers.filter(va => {
                tiers.push(va.bounding_box)
              })
              structure_detections_item.layers = tiers
              structure_detections.push(structure_detections_item)
            })
            // 手动合并
            structure_detections.push(v.bounding_box)
          })
          res.brand_list = []
          res.category_list = []
          res.posm_list = []
          res.structure_detections = structure_detections
          res.updated_at = getTime(res.updated_at)
          this.reportRet = res
          this.scene = res
          setTimeout(() => {
            this.changeData(true)
          }, 200)
        } else {
          // 无识别结果
          this.scene = []
        }
      // }).catch(() => {
      //   this.isError = true
      //   this.loading = false
      // })
    },
    changeData(bools) {
      const res = this.reportRet
      const arrPointW = []
      res.brand_list.map((v, k) => {
        v.isActive = false
        v.name = v.brand_name
        v.id = v.brand_id
        v.list.map((val, key) => {
          val.isActive = true
        })
        return v
      })
      res.category_list.map((v, k) => {
        v.isActive = false
        v.name = v.category_name
        v.id = v.category_id
        v.list.map((val, key) => {
          val.isActive = true
        })
        return v
      })
      res.sku_list.map((v, k) => {
        v.isActive = false
        v.name = v.sku_name
        v.id = v.sku_id
        v.count = v.objects.length
        v.objects.map((val, key) => {
          const w = val.tr[0] - val.tl[0]
          arrPointW.push(w)
        })
        return v
      })
      res.posm_list.map((v, k) => {
        v.isActive = false
        v.name = v.sku_name
        v.count = v.objects.length
        v.objects.map((val, key) => {
          const w = val.tr[0] - val.tl[0]
          arrPointW.push(w)
        })
        return v
      })
      this.avePonitW = this.arrAverageNum2(arrPointW) / 5
      this.protabList[0].listData = res.brand_list
      this.protabList[1].listData = res.category_list
      this.protabList[2].listData = res.sku_list
      // this.protabList[3].listData = res.posm_list
      this.proshopList = this.protabList[2].listData
      this.reportRet = res
      this.scentabFun(2)
      this.initCanvas(bools)
    },
    arrAverageNum2(arr) {
      const myEval = (fn) => {
        return new Function('return ' + fn)()
      }
      var sum = myEval(arr.join('+'))
      return ~~(sum / arr.length * 100) / 100
    },
    pageOperat(type) {
      if (type == 100 && !this.base.pre_id) return false
      if (type == 200 && !this.base.next_id) return false
      const id = type == 100 ? this.base.pre_id : this.base.next_id
      this.$router.replace({
        query: { 'id': id }
      })
      window.location.reload()
    },
    /**
     * 将图渲染到canvas上
     * */
    initCanvas(first) {
      const canvas = this.$refs.canvas
      const canvasBg = this.$refs.canvasbg
      const context = canvasBg.getContext('2d')
      const image = new Image()
      image.src = this.reportRet.image
      this.canvasBoxW = this.$refs.canvasBox.offsetWidth
      this.canvasBoxH = this.$refs.canvasBox.offsetHeight
      image.onload = () => {
        if (first) {
          if (image.height > image.width) {
            this.scale = this.canvasBoxH / image.height
          } else {
            this.scale = this.canvasBoxW / image.width
          }
        }
        this.canvasHeight = image.height * this.scale
        this.canvasWidth = image.width * this.scale
        canvas.width = this.canvasWidth
        canvas.height = this.canvasHeight
        canvasBg.width = this.canvasWidth
        canvasBg.height = this.canvasHeight
        context.fillRect(0, 0, canvas.width, canvas.height) // 填充颜色：距离左边 距离上面 宽度 高度
        context.drawImage(image, 0, 0, canvas.width, canvas.height)
        // 绘制图线
        let minPointX = 1000000
        let minPointY = 1000000
        this.reportRet.structure_detections.map((item, k) => {
          minPointX = item.tl[0]< minPointX ? item.tl[0] : minPointX
          minPointY = item.tl[1]< minPointY ? item.tl[1] : minPointY
          // 绘制货架框或者冰柜框 scene_type:    1：冰柜(1条) ；2： 货架(2条)
          context.beginPath()
          context.strokeStyle = '#f700e1'
          context.lineWidth = 3
          context.setLineDash([0, 0])
          if (this.reportRet.scene_type == 2) {
            context.strokeStyle = '#f700e1'
            context.lineWidth = 3
            context.beginPath()
            context.moveTo(item.tl[0] * this.scale, item.tl[1] * this.scale)
            context.lineTo(item.bl[0] * this.scale, item.bl[1] * this.scale)
            context.closePath()
            context.stroke()
            // ---
            context.beginPath()
            context.lineTo(item.tr[0] * this.scale, item.tr[1] * this.scale)
            context.lineTo(item.br[0] * this.scale, item.br[1] * this.scale)
            context.closePath()
            context.stroke()
          } else if (this.reportRet.scene_type == 1) {
            context.moveTo(item.tl[0] * this.scale, item.tl[1] * this.scale)
            context.lineTo(item.tr[0] * this.scale, item.tr[1] * this.scale)
            context.lineTo(item.br[0] * this.scale, item.br[1] * this.scale)
            context.lineTo(item.bl[0] * this.scale, item.bl[1] * this.scale)
            context.closePath()
            context.stroke()
          }
          // 绘制层
          if(item.layers){
            item.layers.map((layers, key) => {
              context.beginPath()
              context.lineWidth = 3
              context.strokeStyle = '#50B7EB'
              context.setLineDash([4, 4]) // 虚线点特征
              const startLx = (layers.tl[0] + layers.bl[0]) / 2
              const startRy = (layers.tl[1] + layers.bl[1]) / 2
              const toLx = (layers.tr[0] + layers.br[0]) / 2
              const toLy = (layers.tr[1] + layers.br[1]) / 2
              context.moveTo(startLx * this.scale, startRy * this.scale) // 开始点
              context.lineTo(toLx * this.scale, toLy * this.scale) // 长宽
              context.closePath()
              context.stroke()
              // 绘制价签
              if (layers.pricetags && layers.pricetags.length > 0) {
                layers.pricetags.map((pricetags, key) => {
                  context.beginPath()
                  context.lineWidth = 3
                  context.strokeStyle = '#5207FB'
                  context.setLineDash([4, 4])
                  context.moveTo(pricetags.tl[0] * this.scale, pricetags.tl[1] * this.scale)
                  context.lineTo(pricetags.tr[0] * this.scale, pricetags.tr[1] * this.scale)
                  context.lineTo(pricetags.br[0] * this.scale, pricetags.br[1] * this.scale)
                  context.lineTo(pricetags.bl[0] * this.scale, pricetags.bl[1] * this.scale)
                  context.closePath()
                  context.stroke()
                  if (this.$route.query.debug) {
                    // 显示价格 bg
                    context.fillStyle = 'rgba(255,255,255,.8)'
                    context.fillRect(pricetags.tl[0] * canvas.width, pricetags.tl[1] * canvas.height, (
                        pricetags.tr[0] - pricetags.tl[0]) * canvas.width, (pricetags.br[1] - pricetags
                        .tr[1]) * canvas.height)
                    // text
                    const font = (this.avePonitW * this.canvasWidth) * 1.5
                    const price = pricetags.price.toFixed(1)
                    context.font = font + 'px Arial'
                    context.fillStyle = '#000'
                    const measureText = context.measureText(price)
                    const textL = (pricetags.tr[0] + pricetags.tl[0]) * canvas.width / 2 - measureText
                        .width / 2
                    const textR = (pricetags.br[1] + pricetags.tr[1]) * canvas.height / 2 + measureText
                        .actualBoundingBoxAscent / 2
                    context.fillText(price, textL, textR)
                  }
                })
              }
            })
          }
        })
        // if(first && minPointY > 0.2){
        //   this.scale = this.scale + minPointX * 1.5
        //   console.log(111111, this.scale)
        //   this.initCanvas(false)
        //   setTimeout(() => {
        //     this.$refs.canvasBox.scrollTop = parseInt(this.canvasHeight * ( minPointY) - 20)
        //     this.$refs.canvasBox.scrollLeft = parseInt(this.canvasWidth * ( minPointX) - 20)
        //   },100)
        // }
        this.drawSku()
      }
    },
    windowToCanvas(canvas, x, y) {
      var bbox = canvas.getBoundingClientRect()
      return {
        x: x - bbox.left * (canvas.width / bbox.width),
        y: y - bbox.top * (canvas.height / bbox.height)
      }
    },
    /**
     * @description展示上下左右
     * */
    aroundEach(arr) {
      this.clearAll()
      let color = ''
      arr.map((val, key) => {
        if (key == 0) {
          color = '#ef553e'
        } else if (key == 1) {
          color = '#77d5c8'
        } else if (key == 2) {
          color = '#f9f9f1'
        } else if (key == 3) {
          color = '#020202'
        }
        // 上下
        if (key == 0 || key == 3) {
          val.map((sk_v, k) => {
            const v = this.reportRet.class_detections[sk_v[0]].objects[sk_v[1]]
            if (v) {
              this.markSku(v.tl[0], v.tl[1], v.br[0], v.br[1], v.tr[0], v.tr[1], v.bl[0], v.bl[1], 0, color,
                  false, true)
            }
          })
        }
        if (key == 1 || key == 2) {
          if (val[0] || val[0] == 0) {
            const v = this.reportRet.class_detections[val[0]].objects[val[1]]
            if (v) {
              this.markSku(v.tl[0], v.tl[1], v.br[0], v.br[1], v.tr[0], v.tr[1], v.bl[0], v.bl[1], 0, color,
                  false, true)
            }
          }
        }
      })
    },
    /**
     * 鼠标滑动过
     * */
    showThisPop(e) {
      const that = this
      const canvas = this.$refs.canvas
      const context = canvas.getContext('2d')
      const width = canvas.width
      const height = canvas.height
      const obj = this.windowToCanvas(canvas, e.clientX, e.clientY) // 转化成相对于canves上面的点坐标
      this.left = e.clientX// 相对于 window 上的点
      this.top = e.clientY // 相对于 window 上的点
      that.popText = ''
      if (this.debug && this.isAround) {
        this.isChoseAll = false
        this.clearAll()
      }
      this.reportRet.sku_list.map((val, k) => {
        if (val.objects) {
          val.objects.map((v, k) => {
            if (obj.x > v.tl[0] * this.scale && obj.x < v.br[0] * this.scale && obj.y > v.tl[1] * this.scale && obj.y < v
                .br[1] * this.scale) {
              that.showPopMsg = true
              that.popText = val.name
              if (this.debug) {
                // 展示上下左右
                if (this.isAround) this.aroundEach([v.top_adj, v.left_adj, v.right_adj, v.bot_adj])
                that.popText = val.name + '</br>' + v.confidence
              }
            }
          })
        }
      })
    },
    /**
     * @description点击放大缩小
     * */
    scaleImg(state) {
      if (state == 100) {
        if (this.scale >= 2.5) return false
        this.scale += 0.1
        this.initCanvas(false)
      } else {
        if (this.scale <= 0.2) return false
        this.scale -= 0.1
        this.initCanvas(false)
      }
    },
    destroy() {
      this.selectSkuList = []
      const canvasBg = this.$refs.canvasbg
      const context = canvasBg.getContext('2d')
      context.clearRect(0, 0, canvasBg.width, canvasBg.height)
    },
    clearAll() {
      const canvas = this.$refs.canvas
      const context = canvas.getContext('2d')
      context.clearRect(0, 0, canvas.width, canvas.height)
    },
    /**
     * @description展示是否显示空缺位
     * */
    drawVacancy() {
      this.clearAll()
      this.drawSku()
      if (!this.isVacancy) return false
      this.isAround = false
      this.scene && this.scene.empties && (this.scene.empties.map((v, k) => {
        this.markSku(v.tl[0], v.tl[1], v.br[0], v.br[1], v.tr[0], v.tr[1], v.bl[0], v.bl[1], 1, '#f805f7', false)
      }))
    },
    /**
     * 显示所有sku
     * */
    drawSku() {
      let isPoint = true
      if (this.debug) isPoint = false
      this.clearAll()
      this.selectSkuList.map((v, k) => {
        let color = '#f805f7'
        if (v.confidence < 0.7) {
          color = '#ff0000'
        } else if (v.confidence <= 0.9) {
          color = '#f8e005'
        } else if (v.confidence > 0.9) {
          color = '#f805f7'
        }
        this.markSku(v.tl[0], v.tl[1], v.br[0], v.br[1], v.tr[0], v.tr[1], v.bl[0], v.bl[1], 1, color, isPoint)
      })
    },
    markSku(x1, y1, x2, y2, x3, y3, x4, y4, dashed, color, isPoint, isTost) {
      dashed = false
      if (!color) color = 'red'
      const canvas = this.$refs.canvas
      const context = canvas.getContext('2d')
      context.beginPath()
      let avePonitW = this.avePonitW * this.scale
      // 当中可直接取原坐标点
      if (this.$route.query.type == 200 && this.isPush) avePonitW = (x2 - x1) * this.scale
      if (isPoint) {
        // 半透明圆
        context.arc(
            (x1 + x2 + x3 + x4) / 4 * this.scale,
            (y1 + y2 + y3 + y4) / 4 * this.scale,
            avePonitW * 1.5,
            0,
            2 * Math.PI
        )
        context.fillStyle = 'rgba(0,0,0,.5)'
        context.fill()
        // 红色实心圆
        context.beginPath()
        context.arc(
            (x1 + x2 + x3 + x4) / 4 * this.scale,
            (y1 + y2 + y3 + y4) / 4 * this.scale,
            avePonitW,
            0,
            2 * Math.PI
        )
        context.fillStyle = 'red'
        context.fill()
        context.closePath()
      } else {
        if (isTost) {
          context.fillStyle = 'rgba(255,255,255,.3)'
          context.fillRect(x1 * this.scale, y1 * this.scale, (x3 - x1) * this.scale, (y2 - y3) *
              this.scale)
        }
        context.beginPath()
        if (dashed) {
          context.setLineDash([4, 4])
        } else {
          context.setLineDash([0, 0])
        }
        context.strokeStyle = color
        context.lineWidth = 3
        context.moveTo(x1 * this.scale, y1 * this.scale)
        context.lineTo(x3 * this.scale, y3 * this.scale)
        context.lineTo(x2 * this.scale, y2 * this.scale)
        context.lineTo(x4 * this.scale, y4 * this.scale)
        context.closePath()
        context.stroke()
      }
    }
  }
}
</script>
<style scoped lang="scss">
@import '@/styles/variables.scss';
.report-title{
  font-size: 18px;
  text-align: center;
  font-weight: 600;
  margin-bottom: 20px;
}
.box {
  display: -webkit-box;
}

.flex1 {
  -webkit-box-flex: 1;
}

.textover {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.pro-nav {
  background: transparent;
  width: 100%;
  border-bottom: 1px solid #D8D8D8;
}

.pro-nav li {
  width: 25%;
  border: none;
  height: 50px;
  line-height: 50px;
  cursor: pointer;
}

.pro-nav .active {
  border: none !important;
  border-bottom: 1px solid $themeColor !important;
  background: transparent;
  color: $themeColor
}

.pro-over {
  height: 40px;
  line-height: 40px;
  cursor: pointer;
  height: 40px;
  line-height: 40px;
}

.pro-item-tit {
  font-size: 16px;
  background-color: #f7f7f7;
  border-radius: 4px;
  margin-top: 10px;
  padding: 0 10px;
}

.pro-item-tit span, .pro-item-li span {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: inline-block;
  max-width: 80%;
  vertical-align: middle;
  margin-bottom: 3px;
}

.pro-item-tit .icon {
  margin-top: -1px;
  margin-right: 3px;
  transition: all 0.2s ease;
  font-size: 14px;
  transform-origin: center;
}

.pro-item-li {
  font-size: 14px;
  color: rgba(51, 51, 51, 0.87);
  padding-left: 43px;
  padding-right: 10px
}

.pro-active {
  background: #f5f5f5;
  color: #0471D4;
}

.imgTranform {
  transform: rotate(90deg);
}

.pro-item-srcoll {
  overflow-y: scroll;
  height: 70vh;
}

.det-pro-right {
  margin-right: 25px;
  position: relative;
}

.det-pro-imgshow {
  color: #000000;
  letter-spacing: 0;
  margin-bottom: 20px;
}

.det-pro-imgshow .tit {
  font-size: 16px;
  font-weight: 600;
}

.det-pro-imgshow label {
  font-size: 14px;
  cursor: pointer;
  margin-right: 25px;
  font-weight: 600
}

.det-pro-img {
  width: 100%;
  background: #F5F5F5;
  border-radius: 4px;
  height: 525px;
  margin-right: 25px;
  overflow: scroll;
  position: relative;
}

.opt-img {
  margin-left: 10px;
  cursor: pointer;
  color: #ffffff;
  background-color: $themeColor;
  padding: 3px;
  border-radius: 2px;
  font-size: 18px;
}

.det-pro-img canvas {
  position: absolute;
}

.det-pro-img #canvas {
  z-index: 2
}

.popMsg {
  position: fixed;
  top: 0;
  left: 0;
  font-size: 15px;
  color: #fff;
  background: rgba(#0471D4,0.5);
  border-radius: 10px;
  padding: 2px 5px;
  z-index: 335;
}
.det-pro-left,.det-pro-right{
  border: 1px solid #cccccc;
  border-radius: 4px;
  padding: 10px;
}
.det-type-title{
  font-weight: 600;
}
.report-time{
  margin-bottom: 15px;
  font-size: 14px;
  font-weight: bold;
}

</style>

