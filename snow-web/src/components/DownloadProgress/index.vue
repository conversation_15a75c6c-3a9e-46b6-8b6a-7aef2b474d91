<!-- 下载文件 -->
<template>
  <div class="popup">
    <el-dialog
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :width="width"
      :progress="progress"
      class="box-form-item bu-form"
      :title="$t('reaCommon.download')"
      :visible.sync="dialogVisible"
      center
    >
      <div>
        <div class="mt10">
          <el-progress :text-inside="true" :stroke-width="12" :percentage="progress"/>
          <div class="mt10">{{ progress < 100 ? $t('reaCommon.buildingExcel') : $t('reaCommon.buildSuccess') }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'download-progress',
  props: {
    downloadVisible: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: '600px'
    },
    progress: {
      type: Number,
      default: 0
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.downloadVisible
      },
      set(val) {
        this.$emit('update:downloadVisible', val)
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('downloadDialogClose')
    },
    handleCancel() {
      this.$emit('downloadDialogCancel')
    },
    handleConfirm() {
      this.$emit('downloadDialogConfirm')
    }
  }
}
</script>

<style lang="scss" scoped>
.mt10 {
  margin-top: 10px;
}
</style>
