<template>
  <div class="logo">
    <img v-if="env && logoObj[env]" :src="logoObj[env]">
    <img v-else src="@/assets/images/logo_head.png">
  </div>
</template>
<script>
export default {
  name: 'logo',
  props: {
  },
  data() {
    return {
      env: '',
      logoObj: {
        hw_tg: require('../../assets/logo/tg.png')
      }
    }
  },
  mounted() {
    this.env = window.ENV
  },
  methods: {
  }
}
</script>
<style lang="scss">
.logo {
  margin-right: 3%;
  height: 55px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 4%;
  margin-top: -5px;
  img {
    display: block;
    width: 140px;
  }
}
</style>
