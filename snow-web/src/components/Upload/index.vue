<template>
  <el-upload
    ref="el__upload"
    class="upload__demo"
    :class="className"
    :multiple="false"
    :limit="1"
    action="#"
    :http-request="uploadImageFn"
    :show-file-list="false"
    :accept="accept"
    :disabled="percent && percent != 100 ? true : false"
  >
    <div size="small" plain class="chose__file" v-if="percent == 0">{{$t('reaCommon.clickUpload')}}</div>
    <div v-else class="flex-y-center chose__file hasFile">
      <div class="flex-1 file__name">{{ fileName }}</div>
      <div class="uplaod__status">
        <template v-if="percent && percent != 100">
          <i class="el-icon-loading"></i>
          <i>{{$t('reaCommon.uploading')}}</i>
        </template>
        <span v-else>{{$t('reaCommon.uploadAgain')}}</span>
      </div>
    </div>
    <el-progress v-if="isProgress && percent" :percentage="percent" ></el-progress>
    <div slot="tip" class="el-upload__tip" v-if="tipDes">{{ tipDes }}</div>
  </el-upload>
</template>
<script>
import Config from "@/config";
import {formatSize, getPrefix} from '@/utils/validate'

export default {
  name: 'imageUpload',
  props: {
    className: {
      type: String,
      default: ''
    },
    // cos配置文件夹名称
    fileRoutePrefix: {
      type: String,
      default: ''
    },
    // 只能上传jpg/png文件
    accept: {
      type: String,
      default: 'image/png, image/jpeg, image/jpg'
    },
    // 提示描述信息
    tipDes: {
      type: String,
      default: ''
    },
    // 是否展示进度
    isProgress: {
      type: Boolean,
      default: false
    },
    // 是否直接返回文件资源
    isBackFile: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      fileInfo: '',
      imageKey: '',
      imageSrc: '',
      fileName: '',
      percent: 0,
    }
  },
  methods: {
    /**
     * 清空数据
     * */
    clearFun() {
      this.fileInfo = ''
      this.imageKey = ''
      this.imageSrc = ''
      this.fileName = ''
      this.percent = 0
    },
    /**
     * 上传图片
     * */
    uploadImageFn(res) {
      this.$refs.el__upload.clearFiles()
      const fileInfo = res.file
      let fileName = `${fileInfo.name}  ${formatSize(fileInfo.size)}`
      if ((fileInfo.size / 1024 / 1024 > 512)) {
        this.$message.error(this.$t('reaCommon.fileSize512M'))
        res.onError(this.$t('reaCommon.fileSize512M'))
        return
      }
      const fileType = fileInfo.name.split('.').reverse()[0]
      if (!(fileType && this.accept.includes(fileType)) || !fileType) {
        this.$message.error(this.$t('reaCommon.fileFormat'))
        res.onError(this.$t('reaCommon.fileFormat'))
        return
      }
      this.fileName = fileName
      this.imageSrc = ''
      this.imageKey = ''
      this.percent = 0
      this.fileInfo = ''
      const Key = getPrefix('scene_', '.' + fileType, this.fileRoutePrefix)
      if (this.isBackFile) {
        this.fileInfo = fileInfo
        this.percent = 100
        res.onSuccess(this.$t('reaCommon.operationSuccess'))
        return
      }
      this._cos.putObject(
        {
          Bucket: Config.cosConfig.Bucket,
          Region: Config.cosConfig.Region,
          Key,
          StorageClass: "STANDARD", // 上传模式
          Body: res.file,
          onProgress: (progressData) => {
            this.percent = parseInt(progressData.percent * 100)
          },
        },
        (err, data) => {
          if (data) {
            this.imageKey = Key
            this.imageSrc = `http://${data.Location}`
            this.$emit('uploadSuccess', {imageKey: this.imageKey, imageSrc: this.imageSrc})
          }
        }
      );
    },
  }
}
</script>
<style lang="scss">
@import "~@/styles/variables.scss";

.upload__demo {
  .el-upload__tip {
    margin-top: -10px;
  }

  .el-upload {
    width: 100%;
  }

  .hasFile {
    color: #333;
    text-align: left;
    background-color: #fff
  }

  .uplaod__status {
    font-size: 14px;
    color: $themeColor;
  }

  .file__name {
    height: 36px;
    line-height: 36px;
    overflow: hidden;
  }

  .chose__file {
    margin-bottom: 0;
    color: $themeColor;
    position: relative;
    padding: 0 24px;
    cursor: pointer;
    height: 35px;
    line-height: 35px;
    background: #EFF7FF;
    border-radius: 4px;
    border: 1px dashed $themeColor;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #1890FF;

    &.hasFile {
      border-color: #e6e6e6;
      background-color: white;
    }

    .fl {
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #262626;
    }

    .fr {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: $themeColor;
    }
  }
}
</style>
