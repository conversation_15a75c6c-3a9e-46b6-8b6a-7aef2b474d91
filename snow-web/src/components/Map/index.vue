<template>
  <div class="map__contant" v-loading="loading" :style="{height: wH+'px'}">
    <el-amap vid="amapDemo">
      <!--      :icon="item.icon" mapStyle="dark"-->
      <el-amap-marker
        v-for="(item, index) in markers"
        :key="index"
        :position="item.position"
        topWhenClick="true"
        :icon="item.icon ? item.icon : require('@/assets/images/map_def.svg')"
        :events="item.events"
      ></el-amap-marker>
      <el-amap-info-window
        v-if="currentWindow"
        :position="currentWindow.position"
        :visible="currentWindow.visible"
        :content="currentWindow.content">
      </el-amap-info-window>
    </el-amap>
  </div>
</template>

<script>
import Vue from 'vue'
import VueAMap from 'vue-amap';
import {storeList} from "@/api/list";
// import mapdef from "@/assets/images/mapdef.svg";
Vue.use(VueAMap);
// 初始化高德地图的 key 和插件
VueAMap.initAMapApiLoader({
  key: 'f52d22cebbe073a408b94586bded9e5f',
  plugin: ['AMap.Autocomplete', 'AMap.PlaceSearch', 'AMap.Scale', 'AMap.OverView', 'AMap.ToolBar', 'AMap.MapType', 'AMap.PolyEditor', 'AMap.CircleEditor'],
  v: '1.4.4'
});
export default {
  props: {
    queryParam: {
      type: Object,
      default: () => {
        return {}
      }
    },
    count: {
      type: Number,
      default: 0
    },
  },
  data() {
    return {
      wH: '550',
      loading: false,
      markers: [],
      currentWindow: null
    };
  },
  mounted() {
    this.wH = window.innerHeight * 0.7 || window.screen.height * 0.6
    this.getList()
  },
  methods: {
    /**
     * 获取列表
     * */
    getList() {
      this.loading = true
      const params = {...this.queryParam, page_size: this.count, page_num: 1}
      storeList(params).then(res => {
        const {list} = res.data
        this.listData = list
        this.initData()
      }).finally(() => {
        this.loading = false
      })
    },
    initData() {
      let markers = []
      const _this = this
      this.listData.filter(v => {
        if (v.gaode_lng && v.gaode_lat) {
          const position = [v.gaode_lng, v.gaode_lat]
          markers.push({
            position,
            icon: v.chain?.logo,
            events: {
              click(e) {
                _this.currentWindow = null
                const content = `<div style="font-size: 12px">
                    <div><span style="font-size: 14px;font-weight: bold">店铺名称:</span>${v.store_name}</div>
                    <div><span style="font-size: 14px;font-weight: bold">地址:</span>${v.address}</div>
                  </div>`;
                const currentWindow = {position, visible: true, content}
                _this.$nextTick(() => {
                  _this.currentWindow = currentWindow
                });
              }
            }
          })
        }
      })
      this.markers = markers
    }
  }
}
</script>

<style lang="scss">
.amap-icon {
  img {
    width: 32px;
    height: 32px;
  }
}

.map__contant {
  height: 550px;
  padding-bottom: 25px;
}
</style>
