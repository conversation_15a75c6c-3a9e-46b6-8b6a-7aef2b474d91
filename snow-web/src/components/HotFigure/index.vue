<template>
  <div class="digital__box">
    <canvas ref="figure" :style="{ width: canvasW + 'px', height: canvasH + 'px' }"></canvas>
    <div class="popup-content" v-if="overPopup" :style="{left: popupLeft, top: popupTop}">{{ overPopupTitle }}</div>
<!--    <img v-for="(item, index) in imageList" @mouseover="mouseoverFun(item)" @mouseout="mouseoutFun(item)" :style="{left: item.left+'px', top: item.top+'px', height: item.height+'px', width: item.width+'px'}" :key="index" :src="item.url" class="imgs">-->
  </div>
</template>

<script>
import {fabric} from "fabric";

export default {
  props: {
    canvasW: {
      type: Number | String,
      default: 320
    },
    canvasH: {
      type: Number | String,
      default: 600
    },
  },
  data() {
    return {
      isShowAll: false,
      imageH: 0,
      imageW: 0,
      imageList: [],
      disLeft: 0,
      disTop: 0,
      scale: 0,
      modelData: {},
      canvas: null,
      baseImageUrl: null,
      sku_map: [],
      overPopup: false,
      overPopupTitle: '',
      popupLeft: '',
      popupTop: '',
      arrPointW: 3,
    }
  },
  mounted() {
  },
  methods: {
    mouseoverFun(item){
      this.overPopup = true
      // this.overPopupTitle = (this.sku_map[item.sku_id] && this.sku_map[item.sku_id].name || item.sku_name)+'_'+item.sku_id
      this.overPopupTitle = (this.sku_map[item.sku_id] || item.sku_name)+'_'+item.sku_id
      let position = item.coords
      this.popupLeft = position[0].x + (position[2].x - position[0].x) / 2 - 10 + 'px'
      this.popupTop = position[0].y - 35 + 'px'
    },
    mouseoutFun(){
      this.overPopup = false
    },
    /**
     * 获取数据
     * */
    getDetail(e, sku_map) {
      if (e && e.sale_result && e.drawType && e.drawType == 1) {
        let arrPointW = []
        e.sale_result.filter(val => {
          // if(val.bounding_box){
          //   val.bounding_box.filter(v => {
          //     let w = v.tr[0] - v.tl[0]
          //     arrPointW.push(w)
          //   })
          // }
          let w = val.tr[0] - val.tl[0]
          arrPointW.push(w)
        })
        this.arrPointW = this.arrAverageNum(arrPointW) / 6
      }
      // this.$emit('imageFinshed', true)
      this.overPopup = false
      this.modelData = e
      this.sku_map = sku_map
      // if(this.canvas){
      //   // this.canvas.dispose() // 否则会消失
      //   this.canvas.clear() // 事件操作会消失
      // }
      setTimeout(() => {
        this.fabricInit()
      }, 10)
    },
    /**
     * 计算x和y转化后的坐标
     * type x= value * this.scale + this.disLeft
     * type y= value * this.scale + this.disTop
     * */
    getPostion(value, type) {
      let dis = this.disTop
      let data = value * this.scale * this.imageH
      if(type == 'x'){
        dis = this.disLeft
        data = value * this.scale * this.imageW
      }
      return data + dis
    },
    /**
     * 计算圆半径大小
     * */
    arrAverageNum(arr) {
      const myEval = (fn) => {
        return new Function('return ' + fn)()
      }
      var sum = myEval(arr.join('+'))
      return ~~(sum / arr.length * 100) / 100
    },
    /**
     * 识别结果打点
     * */
    fabricCircle(point, info, disLeft, disTop){
      let {bl, br, tl, tr} = point
      let x_tl = this.getPostion(tl[0], 'x')
      let x_tr = this.getPostion(tr[0], 'x')
      let y_tl = this.getPostion(tl[1], )
      let y_bl = this.getPostion(bl[1], )

      let radius = this.arrPointW * this.scale * 1.2 * this.imageW || 3
      let cw = (radius + 2 * 2) / 2
      let def = {
        left: (x_tl + x_tr) / 2 - cw,
        top: (y_tl + y_bl) / 2 - cw,
        radius: radius,  // 圆的半径
        fill: info.highlight ? '#1677FF' : 'red', // 如果是高亮状态，使用绿色，否则使用红色
        stroke: 'rgba(0,0,0,.4)', // 圆边框的颜色
        strokeWidth: 2, // 圆边框的宽度
      }
      const circle = new fabric.Circle(def);
      const groupPolygon = new fabric.Group([circle], {
        selectable: false,
        hoverCursor: 'pointer',
      })
      groupPolygon.sku_id = info.sku_id || ''
      if(info.sku_name){
        groupPolygon.sku_name = info.sku_name
      }
      groupPolygon.on('mouseover', (e) => {
        this.overPopup = true
        this.overPopupTitle = (this.sku_map[groupPolygon.sku_id] || groupPolygon.sku_name)+'_'+groupPolygon.sku_id
        let position = groupPolygon.getCoords()
        this.popupLeft = position[0].x + (position[2].x - position[0].x) / 2 - 10 + 'px'
        this.popupTop = position[0].y -35 + 'px'
      })
      groupPolygon.on('mouseout', (e) => {
        this.overPopup = false
      })
      this.canvas.add(groupPolygon)

    },
    /**
     *  绘制多边形
     */
    fabricPolygon(point, info, disLeft, disTop) {
      if (disLeft) {
        this.disLeft = disLeft
        this.disTop = disTop
      }
      let {bl, br, tl, tr} = point
      let def = {
        selectable: false,
        // skipTargetFind: false, // 保留选中操作(在canvas对象中去掉选中样式)
        // fill: 'rgba(0,244,248,.4)',
        fill: info.highlight ? 'rgba(0,255,0,.25)' : (this.modelData.isHeatMap ? 'rgba(250,54,0,.3)' : 'rgba(0,244,248,.25)'),
        stroke: this.modelData.isHeatMap ? '' : (info.highlight ? '#00FF00' : '#00ABFF'),
        strokeWidth: info.highlight ? 2 : 1,
        visible: true,
        hoverCursor: 'pointer',
      }
      if(!this.modelData.isHeatMap){
      }
      // 绘制类型 drawType 1=识别结果/百分比
      if(this.modelData.drawType && this.modelData.drawType == 1){
        def.fill = ''
        def.stroke = '#ef7f08'
        def.strokeWidth = 1.5
      }
      let arr = [
        {x: this.getPostion(tl[0], 'x'), y: this.getPostion(tl[1])},
        {x: this.getPostion(tr[0], 'x'), y: this.getPostion(tr[1])},
        {x: this.getPostion(br[0], 'x'), y: this.getPostion(br[1])},
        {x: this.getPostion(bl[0], 'x'), y: this.getPostion(bl[1])},
      ]
      const polygon = new fabric.Polygon(arr, def);
      const groupPolygon = new fabric.Group([polygon], {
        selectable: false,
        hoverCursor: 'pointer',
      })
      groupPolygon.sku_id = info.sku_id || ''
      if(info.sku_name){
        groupPolygon.sku_name = info.sku_name
      }
      groupPolygon.on('mouseover', (e) => {
        this.mouseoverFun({coords: groupPolygon.getCoords(), sku_name: groupPolygon.sku_name, sku_id: groupPolygon.sku_id})
      })
      groupPolygon.on('mouseout', (e) => {
        this.mouseoutFun()
      })
      this.canvas.add(groupPolygon)
    },
    /**
     * 计算图片缩放比例
     * */
    calculateScale(img, width, height) {
      const [imgWidth, imgHeight] = [img.width, img.height];
      const [widthScale, heightScale] = [width / imgWidth, height / imgHeight];
      return Math.min(widthScale, heightScale)
    },
    /**
     * 初始化画布
     * 以门的宽高作为画布尺寸 多扇门则计算总宽高（取最大值）
     * 其他尺寸则减掉门的尺寸
     */
    fabricInit() {
      let height = this.canvasH
      let width = this.canvasW
      let def = {
        preserveObjectStacking: true, // 锁定层级
        width,
        height,
        // enableGLcontext: true,
        backgroundColor: 'rgba(25, 87, 210, 0.05)',
      }
      if (!this.canvas) {
        this.canvas = new fabric.Canvas(this.$refs.figure, def)
      }
      let canvas = this.canvas
      const url = this.modelData['image_url']
      // 只取第一次的图
      fabric.Image.fromURL(url, oImg => {
        let scale = this.scale = this.calculateScale(oImg, canvas.width, canvas.height)
        let left = (canvas.width - oImg.width * scale) / 2
        let top = (canvas.height - oImg.height * scale) / 2
        this.disLeft = left
        this.disTop = top
        this.imageW = oImg.width
        this.imageH = oImg.height
        canvas.setBackgroundImage(oImg, canvas.renderAll.bind(canvas), {
          top,
          left,
          selectable: false,
          // opacity: 0.3,
          backgroundColor: '#fff',
          scaleX: scale,
          scaleY: scale,
          isLoading: true
        });
        // 清空分组数据
        let objects = canvas.getObjects();
        for (var i = 0; i < objects.length; i++) {
          if (objects[i].type === 'group') {
            canvas.remove(objects[i]);
          }
        }
        // 渲染热力图底图
        if (this.modelData.sale_result) {
          if (this.modelData.sale_result.length && this.modelData.isHeatMap) {
            const rect = new fabric.Rect({
              left,
              top,
              selectable: false,
              width: oImg.width * scale,
              height: oImg.height * scale,
              fill: 'rgba(216, 216, 216, .75)', // 设置半透明白色背景
            });
            this.canvas.add(new fabric.Group([rect], {
              selectable: false,
              hoverCursor: 'default',
            }))
          }
        }
        // 渲染框
        if (this.modelData.sale_result) {
          this.imageList = []
          this.modelData.sale_result.filter(val => {
            // if(val.bounding_box){
            //   val.bounding_box.filter((v,ind) => {
            //     // 渲染识别结果
            //     if(this.modelData.drawType && this.modelData.drawType == 1){
            //       this.fabricCircle(v, {sku_id: val.sku_id, sku_name: val.sku_name})
            //     } else {
            //       // this.fabricPolygon(v, {sku_id: val.sku_id, sku_name: val.sku_name, url: val.bounding_img[ind]})
            //       this.fabricPolygon(v, {sku_id: val.sku_id, sku_name: val.sku_name})
            //     }
            //   })
            // }
            // 渲染识别结果
            if(this.modelData.drawType && this.modelData.drawType == 1){
              this.fabricCircle(val, {sku_id: val.sku_id, sku_name: val.sku_name, highlight: val.highlight})
            } else {
              this.fabricPolygon(val, {sku_id: val.sku_id, sku_name: val.sku_name, highlight: val.highlight})
            }
          })
        }
        canvas.setViewportTransform([1, 0, 0, 1, 0, 0])
        this.$emit('imageFinshed', false)
      })
      canvas.renderAll()
      this.canvasMouseEvent()
    },
    canvasMouseEvent() {
      let canvas = this.canvas
      // 解绑之前的事件监听器
      canvas.off("mouse:wheel")
      canvas.off("mouse:down")
      canvas.off("mouse:up")
      canvas.off("mouse:move")
      // 放大缩小
      canvas.on('mouse:wheel', opt => {
        let delta = opt.e.deltaY // 滚轮，向上滚一下是 -100，向下滚一下是 100
        let zoom = canvas.getZoom() // 获取画布当前缩放值
        zoom *= 0.999 ** delta
        if (zoom > 20) zoom = 20
        if (zoom < 0.01) zoom = 0.01
        // 以鼠标所在位置为原点缩放
        canvas.zoomToPoint(
          { // 关键点
            x: opt.e.offsetX,
            y: opt.e.offsetY
          },
          zoom
        )
        opt.e.preventDefault()
        opt.e.stopPropagation()
      })
      //鼠标按下事件
      let panning = false
      canvas.on("mouse:down", function (e) {
        panning = true;
        canvas.selection = false;
      });
      //鼠标抬起事件
      canvas.on("mouse:up", function (e) {
        panning = false;
        canvas.selection = true;
      });
      // 移动画布事件
      canvas.on("mouse:move", function (e) {
        if (panning && e && e.e) {
          let delta = new fabric.Point(e.e.movementX, e.e.movementY);
          canvas.relativePan(delta);
        }
      }, { throttle: 16 })
    }
  }
}
</script>

<style lang="scss">
.digital__box {
  text-align: center;
  canvas{
    border-radius: 8px;
  }

  .canvas-container {
    margin: 0 auto
  }
}

.popup-content {
  position: absolute;
  top: 0;
  left: 0;
  background-color: (rgba(#043E76, 0.7));
  border-radius: 4px;
  overflow: hidden;
  padding: 5px 10px;
  z-index: 10000;
  color: #ffffff;
}
.imgs{
  position: absolute;
  top: 0;
  left: 0;
  border: 1px dashed #fff;
}
</style>
