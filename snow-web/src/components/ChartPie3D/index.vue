<template>
  <div style="position: relative">
    <span class="sales-label" v-if="activeIndex != -1">
      <span class="echart-label" :style="{backgroundColor:itemStyleMap[activeIndex % itemStyleMap.length].start}"></span>{{ activeName }}</span>
    <!-- 饼图下面的底座 -->
    <div class="buttomCharts"></div>
    <div :id="idName" :style="{ height: height, width: width }"></div>
  </div>
</template>

<script>
// import HighCharts from 'highcharts'

export default {
  props: {
    idName: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '160px'
    },
  },
  data() {
    return {
      charts: null,
      itemStyleMap: [
        {
          start: '#026DB2',
          end: '#12FEE0'
        },
        {
          start: '#6DD400',
          end: '#44D7B6'
        },
        {
          start: '#5C64FF',
          end: '#6988F8'
        },
        {
          start: '#FFA600',
          end: '#FEDB65'
        },
        {
          start: '#D73838',
          end: '#FE698B'
        },
        {
          start: '#9270CA',
          end: '#C4A9E7'
        },
      ],
      activeIndex: -1,
      activeName: '',
    }
  },
  created() {
    this.setcolor()
  },
  mounted() {
  },
  methods: {
    init(list) {
      this.activeIndex = -1
      let width = Number(this.width.replace('px',''))
      let height = Number(this.height.replace('px',''))
      let minValue = width > height ? height : width
      width = parseInt(minValue * 0.4)
      if (this.charts) {
        this.charts.destroy()
      }
      let data = list.map(item => {
        return [item.name, item.count]
      })
      let option = {
        credits: {
          enabled: false, //去掉右下角的"https://highcharts.com"的所有权
        },
        chart: {
          type: 'pie',
          backgroundColor: 'transparent',
          margin: parseInt(minValue * 0.01),
          options3d: {
            enabled: true,
            alpha: 60,//3D图旋转角度，此为α角，内旋角度默认为0
          }
        },
        title: {
          text: ''
        },
        subtitle: {
          text: ''
        },
        tooltip: {
          backgroundColor: 'rgba(0,0,0,0.3)',
          borderColor: 'none',
          style: {
            color: '#FFFFFF',
            fontSize: '14px',
          },
          pointFormat: '{point.name}:{point.percentage:.2f}%',
          headerFormat: '{series.name}<br>',
        },
        plotOptions: {
          pie: {
            states: {
              inactive: {
                opacity: 1
              }
            },
            allowPointSelect: true,//每个扇块能否选中
            cursor: 'pointer',//鼠标指针
            innerSize: width,
            depth: 30, //饼图的厚度
            dataLabels: {
              enabled: true,//是否显示饼图的线形tip
              distance: 10,
              format: '{point.name}:{point.percentage:.2f}%',//牵引线上的文字
              style: { //样式调整
                color: '#fff',
                fontSize: '11'
              }
            },
            events: {
              click: (e) => {
                //绑定的事件+传参：我绑的事件pieClick
                // pieClick(e.point)
                let index = e.point.selected ? -1 : e.point.index
                this.$emit('selected', index)
                this.activeIndex = index
                this.activeName = `${e.point.name}:${e.point.percentage.toFixed(2)}%`
              }
            }
          }
        },
        series: [{
          name: '',
          data: data
        }]
      };
      this.charts = HighCharts.chart(this.idName, option)
      this.charts.reflow()
    },
    setcolor() {
      // 颜色的填充
      let colorList = this.itemStyleMap
      let _this = this
      HighCharts.getOptions().colors = HighCharts.map(
        HighCharts.getOptions().colors,
        function (color, index) {
          return {
            radialGradient: {cx: 0.5, cy: 0.3, r: 0.5},
            stops: [
              [0, colorList[index % colorList.length].end],
              [1, colorList[index % colorList.length].start] // darken
            ]
          }
        }
      )
    }
  }
}
</script>
<style lang="scss" scoped>
.chartsGl {
  position: relative;
  z-index: 2;
}

.buttomCharts {
  position: absolute;
  background: center top url("~@/assets/images/bottom-charts.png") no-repeat;
  background-size: 100% 100%;
  width: 70%;
  height: 130px;
  margin-left: 15%;
  bottom: 8%;
}

.sales-label {
  position: absolute;
  display: inline-block;
  text-overflow: ellipsis;
  margin: 0 10px;
  margin-bottom: 10px;
  padding-left: 15px;
  font-size: 12px;
  right: 0px;
  top: 12%;
}

.echart-label {
  display: inline-block;
  content: '';
  position: absolute;
  top: 2px;
  left: 0px;
  width: 8px;
  height: 8px;
  background-color: #3371FF;
  transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  z-index: 3;
}
</style>
