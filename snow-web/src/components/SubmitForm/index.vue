<template>
  <div class="store_add_form wbg">
    <el-form
      ref="searchForm"
      :model="value"
      class="my_el_form"
      :rules="formRule"
      v-bind="$attrs"
      :size="size"
      v-on="$listeners"
    >
      <div
        v-for="(item,index) in formData"
        :key="index"
        class="form_item"
        :style="{ width:item.divWidth?item.divWidth:colWidth,display:item.hide?'none':'block'}"
      >
        <el-form-item
          v-show="!item.isshow"
          :label="$t(`reaCommon.${item.nameKey}`)"
          :prop="item.field"
          :required="item.required"
          :label-width="item.labelWidth"
        >
          <!-- 树形选择框 -->
          <template v-if="item.type === 'tree'">
            <slot :name="item.slot"/>
          </template>
          <!-- 手机号输入框 -->
          <template v-if="item.type==='mobile'">
            <el-input
              v-model="value[item.field]"
              v-bind="item.attrs"
              oninput="if(value.length>30)value=value.slice(0,30)"
              :style="{ width:item.width?item.width:'100%'}"
              :placeholder="$t('reaCommon.pleaseEnter') + ' ' + $t(`reaCommon.${item.nameKey}`)"
              @change="(val)=>selectChange(val,item.field)"
            />
          </template>
          <!-- 普通输入框 -->
          <template v-if="item.type==='input'">
            <el-input
              v-model="value[item.field]"
              v-bind="item.attrs"
              :style="{ width:item.width?item.width:'100%'}"
              :placeholder="$t('reaCommon.pleaseEnter') + ' ' + $t(`reaCommon.${item.nameKey}`)"
              @change="(val)=>selectChange(val,item.field)"
            />
          </template>
          <!-- 下拉选择框 -->
          <template v-else-if="item.type==='select'">
            <el-select
              v-model="value[item.field]"
              v-bind="item.attrs"
              :style="{ width:item.width?item.width:'100%'}"
              :placeholder="$t('reaCommon.pleaseSelect')+ ' ' + $t(`reaCommon.${item.nameKey}`)"
              @change="(val)=>selectChange(val,item.field)"
            >
              <el-option
                v-for="(item,index) in item.options"
                :key="index"
                :label="item.labelKey ? $t(item.labelKey) : item.label"
                :value="item.value"
              />
            </el-select>
          </template>
          <!-- 时间范围选择器 -->
          <template v-else-if="item.type==='daterange'">
            <el-date-picker
              v-model="value[item.field]"
              v-bind="item.attrs"
              :style="{ width:item.width?item.width:'100%'}"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00','23:59:59']"
              type="daterange"
              unlink-panels
              :range-separator="$t('reaCommon.to')"
              :start-placeholder="$t('reaCommon.startDate')"
              :end-placeholder="$t('reaCommon.endDate')"
              @change="(val)=>selectChange(val,item.field)"
            />
          </template>
          <!-- 多层级联动 -->
          <template v-else-if="item.type==='cascader'">
            <el-cascader
              v-model="value[item.field]"
              :disabled="item.disabled?item.disabled:false"
              :style="{ width:item.width?item.width:'100%'}"
              :options="item.options"
              :props="item.props"
              :placeholder="$t('reaCommon.pleaseSelect')+ ' ' + $t(`reaCommon.${item.nameKey}`)"
              @change="(val)=>selectChange(val,item.field)"
            />
          </template>
          <!-- radio -->
          <template v-else-if="item.type === 'radio'">
            <el-radio-group v-model="value[item.field]">
              <el-radio
                v-for="(item,index) in item.options"
                :key="index"
                :label="item.label"
              >{{ item.labelKey ? $t(`reaCommon.${item.labelKey}`) : item.label }}
              </el-radio>
            </el-radio-group>
          </template>
        </el-form-item>
      </div>
      <div class="flex-1 flex-shrink" style="margin-bottom:22px;">
        <div class="flex-box flex-y-center flex-x-end">
          <el-button v-if="formData.length > showCount" :size="size" style="margin-left:20px;" type="text"
                     @click.native="isFold = !isFold">{{ isFold ? $t('reaCommon.spread') : $t('reaCommon.fold') }}<i
            :class="[ isFold ? 'el-icon-arrow-down' : 'el-icon-arrow-up' ,'el-icon--right']"/></el-button>
        </div>
      </div>
    </el-form>
    <div style="padding-top:60px;padding-bottom:30px;" class="flex flex-y-center flex-x-center edit_view_btn">
      <el-button size="medium" @click.native="cancel">{{ $t('reaCommon.cancel') }}</el-button>
      <el-button size="medium" type="primary" style="margin-left:15px;" :loading="confirmButtonLoading"
                 @click.native="save">{{ $t('reaCommon.save') }}
      </el-button>
    </div>
  </div>
</template>
<script>
import {mapGetters} from 'vuex'

export default {
  name: 'submit-form',
  props: {
    // 校验规则
    formRule: {
      type: Object,
      default: function () {
        return {}
      }
    },
    // 表单的值
    value: {
      type: Object,
      default: function () {
        return {}
      }
    },
    // 保存按钮loading
    confirmButtonLoading: {
      type: Boolean,
      default: false
    },
    // 默认显示input个数
    showInputCount: {
      type: Number,
      default: 2
    },
    // 表单大小
    size: {
      type: String,
      default: 'medium'
    },
    // 表单数据
    formData: {
      type: Array,
      default: () => {
        return [
          // {
          //   name: '输入框', // 文字label
          //   type: 'input', // input,select,daterange,cascader
          //   maxlength: 30, // 输入长度限制
          //   field: 'id', // 字段名称
          //   width: false, // 文本宽度
          //   disabled: false, // 是否禁用
          //   placeholder: '请输入', // placeholder提示
          //   multiple: false, // 是否多选，基于select，checkbox
          //   filterable: false // 是否多选，基于select
          // }
        ]
      }
    }
  },
  computed: {
    ...mapGetters([
      'language'
    ])
  },
  data() {
    return {
      isFold: true,
      colWidth: '33.3%',
      showCount: 2
    }
  },
  watch: {
    isFold(value) {
      this.changeItemHide(value)
    },
    '$i18n.locale'() {
      this.setParam()
      this.needOpen()
      setTimeout(() => {
        if (this.$refs['searchForm']) {
          this.$refs['searchForm'].clearValidate()
        }
      })
    }
  },
  created() {
    this.setParam()
    this.needOpen()
  },
  methods: {
    setParam() {
      this.colWidth = this.language === 'en' ? '50%' : '33.3%'
      this.showCount = this.language === 'en' ? 3 : 2
      this.changeItemHide(this.isFold)
    },
    selectChange(val, field) {
      this.$emit('selectChangeHandler', {name: field, value: val})
    },
    changeItemHide(value) {
      if (value) {
        this.formData.map((v, index) => {
          v.hide = index > this.showInputCount - 1
        })
      } else {
        this.formData.map((v) => {
          v.hide = false
        })
      }
    },
    needOpen() {
      if (this.formData.length > this.showCount) {
        this.formData.map((v, index) => {
          if (index > (this.showInputCount - 1)) {
            if (Object.prototype.hasOwnProperty.call(this.formRule, v.field)) {
              this.isFold = false
            }
          }
        })
        this.changeItemHide(this.isFold)
      }
    },
    cancel() {
      this.$emit('cancelHandler', {
        component: 'submitForm'
      })
    },
    save() {
      this.$refs['searchForm'].validate((valid) => {
        if (valid) {
          this.$emit('saveHandler', {
            component: 'submitForm'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss">
.store_add_form {
  box-shadow: 0px 8px 10px 0px rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  padding: 30px 0 8px;

  .el-form-item__label {
    color: #333 !important;
  }

  .flex-box {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }

  .flex-center {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }

  .flex-y-start {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
  }

  .flex-y-center {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }

  .flex-y-end {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end;
  }

  .flex-0 {
    -webkit-box-flex: 0;
    -ms-flex: 0;
    flex: 0;
  }

  .flex-1 {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
  }

  .flex-bt {
    -ms-flex-pack: distribute;
    justify-content: space-between;
  }

  .flex-row {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
  }

  .flex-column {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
  }

  .flex-x-end {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
  }

  .flex-x-center {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }

  .flex-x-start {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }

  .flex-shrink {
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .wbg {
    background-color: white;
  }

  .br2 {
    border-radius: 2px;
  }

  .my_el_form {
    display: flex;
    flex-wrap: wrap;
    padding-right: 25px;
  }

  .form_item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }
}
</style>
