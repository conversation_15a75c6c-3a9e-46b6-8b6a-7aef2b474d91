<template>
  <div class="digital__box">
    <canvas ref="figure"></canvas>
    <div class="popup-content" v-if="overPopup && isPopup" :style="{left: popupLeft, top: popupTop}">{{
        overPopupTitle
      }}
    </div>
  </div>
</template>

<script>
import { calculatePostion } from '@/utils/tools'
// import { fabric } from 'fabric'
import { uuid } from 'vue-uuid'

export default {
  props: {
    src: {
      type: String,
      default: ''
    },
    canvasW: {
      type: Number | String,
      default: 320
    },
    isPopup: {
      type: Boolean,
      default: false
    },
    canvasH: {
      type: Number | String,
      default: 600
    }
  },
  data() {
    // 'model_2d': 'http://snapshot-1255412942.cos.ap-shanghai.myqcloud.com/store-scanner_prod/b7ba5e83-d206-446f-a665-71501ada1186.jpg',
    return {
      keyCode: '-',
      popupLeft: '',
      popupTop: '',
      overPopup: false,
      overPopupTitle: '',
      contextmenuVisible: true,
      disLeft: 0,
      disTop: 0,
      scale: 0,
      currentType: 'rect',
      editorCanvas: null,
      mouseFrom: {},
      mouseTo: {},
      isDrawing: false,
      drawingObject: null,
      tagData: [],
      activeEl: '',
      menuPosition: null,
      showCon: false,
      initData: {},
      url: '',
      allPointGoup: [],
      onePointGoup: {},
      isLock: false,
      relationship: { x: 0, y: 0, zoom: 1 }
      // url: 'https://smart-vision-1305849923.cos.ap-shanghai.myqcloud.com/iot-qc_prod/64c1e6dac08266.67650119.jpeg'
    }
  },
  mounted() {
    this.url = this.src
    // this.getDetail()
  },
  beforeDestroy() {
    fabric.util.removeListener(document, 'keydown', this.keyHandler)
  },
  methods: {
    /**
     * 更新图片
     * */
    updateBackground({ url }) {
      this.url = url
      const canvas = this.editorCanvas
      // 移除当前背景图片
      if (canvas.backgroundImage) {
        canvas.backgroundImage.dispose()
        canvas.renderAll()
      }
      // this.fabricInit()
      let height = this.canvasH
      let width = this.canvasW
      fabric.Image.fromURL(url, oImg => {
        let scale = this.scale = this.calculateScale(oImg, width, height) * 0.9
        canvas.setBackgroundImage(oImg, canvas.renderAll.bind(canvas), {
          scaleX: scale,
          scaleY: scale
        })
        canvas.setWidth(oImg.width * scale)
        canvas.setHeight(oImg.height * scale)
      })
    },
    /**
     * 覆盖全部的绘制
     * */
    drawCover() {
      this.darwPoint(true)
    },
    /**
     * 初始化参数
     * {point}
     * */
    initParams(e) {
      const { oneScene, allScene, isLock } = e
      this.overPopup = false
      this.onePointGoup = oneScene || {}
      this.allPointGoup = allScene || [] // 绘制所有 不可操作
      this.isLock = isLock || false
      this.fabricInit()
      if (!this.isLock) {
        this.fabricMouse()
        // 监听键盘时间，按下backspace进行删除
        // document.onkeydown = (e) => {
        //   let key = window.event.keyCode;
        //   console.log('查看键盘数', key)
        //   this.keyCode = key
        //   if (key == 8) {
        //     this.backSpaceDel()
        //   }
        // }
      }
    },
    /**
     * 获取缩放比例
     * */
    getScale() {
      return this.scale
    },
    /**
     * 初始化画布
     */
    fabricInit() {
      let height = this.canvasH
      let width = this.canvasW
      let def = {
        stopContextMenu: true // 禁止默认右键菜单
      }
      let canvas = this.editorCanvas
      if (!this.editorCanvas) {
        this.editorCanvas = new fabric.Canvas(this.$refs.figure, def)
        canvas = this.editorCanvas
        fabric.Image.fromURL(this.url, oImg => {
          let scale = this.scale = this.calculateScale(oImg, width, height) * 0.9
          canvas.setBackgroundImage(oImg, canvas.renderAll.bind(canvas), {
            scaleX: scale,
            scaleY: scale
          })
          canvas.setWidth(oImg.width * scale)
          canvas.setHeight(oImg.height * scale)
          this.darwPoint()
        })
      }
      if (this.scale) {
        this.darwPoint()
      }
      canvas.renderAll()
    },
    /**
     * 根据数据绘制
     * @params drawCover 绘制全部
     * */
    darwPoint(drawCover) {
      let canvas = this.editorCanvas
      // 某项渲染 清空分组数据
      let objects = canvas.getObjects()
      for (var i = 0; i < objects.length; i++) {
        if (objects[i].type === 'group') {
          canvas.remove(objects[i])
        }
      }
      // 绘制所有
      if (this.allPointGoup.length) {
        this.allPointGoup.filter((v, index) => {
          const { plan_bounding_box } = v
          const objArr = plan_bounding_box && Object.keys(plan_bounding_box).length
          if (objArr) {
            let random = `${uuid.v1()}_${index}_${v.id}` // 随机字符串
            this.drawRectFigure(calculatePostion(plan_bounding_box, this.scale), {
              random,
              fill_color: v.fill_color,
              base_info: v
            })
          }
        })
      } else {
        // 绘制单个
        let { bounding_box, is_all } = this.onePointGoup
        const objArr = this.onePointGoup && bounding_box && Object.keys(bounding_box).length
        let obj = {
          top: 0,
          left: 0,
          width: canvas.width,
          height: canvas.height
        }
        // 点击绘制覆盖
        if (drawCover && Object.keys(this.onePointGoup).length) {
          let obj = {
            top: 0,
            left: 0,
            width: canvas.width,
            height: canvas.height
          }
          this.drawRectFigure(obj, this.onePointGoup)
          this.$emit('updateCoords', { postion: obj, random: this.onePointGoup.random, is_all: true })
        } else {
          if (objArr) {
            this.drawRectFigure(calculatePostion(bounding_box, this.scale), this.onePointGoup)
          } else if (is_all) {
            // 没有坐标但是是全选状态
            this.drawRectFigure(obj, this.onePointGoup)
          }
        }

      }
    },
    /**
     * 删除
     * 按下backspace
     * */
    backSpaceDel() {
      if (this.editorCanvas.getActiveObject()) {
        this.$confirm(this.$t('reaCommon.sureDel'), this.$t('reaCommon.warning'), {
          confirmButtonText: this.$t('reaCommon.confirm'),
          cancelButtonText: this.$t('reaCommon.cancel'),
          type: 'warning'
        }).then(() => {
          const item = this.editorCanvas.getActiveObject()
          this.editorCanvas.remove(item)
          this.editorCanvas.requestRenderAll()
          this.$emit('delCoords', item.rectId)
        })

      }
    },
    /**
     * 计算图片缩放比例
     * */
    calculateScale(img, width, height) {
      const [imgWidth, imgHeight] = [img.width, img.height]
      const [widthScale, heightScale] = [width / imgWidth, height / imgHeight]
      return Math.min(widthScale, heightScale)
    },
    /**
     * 操作事件 ---拖拽单个可以超出上下边界
     * */
    fabricMouse() {
      let canvas = this.editorCanvas
      const relationship = this.relationship
      this.editorCanvas.off('mouse:down')
      this.editorCanvas.off('mouse:move')
      this.editorCanvas.off('mouse:up')
      this.editorCanvas.off('object:scaling')
      this.editorCanvas.off('object:moving')
      let panning = false
      this.editorCanvas.on('mouse:down', (options) => {
        // 记录当前鼠标的起点坐标
        if (!this.editorCanvas.getActiveObject()) {
          // let [x,y] = [
          //   options.pointer.x / this.relationship.zoom - this.relationship.x,
          //   options.pointer.y / this.relationship.zoom - this.relationship.y,
          // ]
          // this.mouseFrom.x = x
          // this.mouseFrom.y = y
          this.mouseFrom.x = options.pointer.x
          this.mouseFrom.y = options.pointer.y
          this.isDrawing = true
        }
        // 按下ctrl进行移动
        // if (options.e && options.e.shiftKey) {
        //   panning = true;
        //   canvas.selection = false;
        // }
      })
      const [maxWidth, maxHeight] = [this.editorCanvas.width, this.editorCanvas.height]
      // 监听鼠标移动
      this.editorCanvas.on('mouse:move', (options) => {
        if (!this.editorCanvas.getActiveObject() && this.isDrawing) {
          this.mouseTo.x =
              options.pointer.x > this.editorCanvas.width
                  ? this.editorCanvas.width
                  : options.pointer.x
          this.mouseTo.y =
              options.pointer.y > this.editorCanvas.height
                  ? this.editorCanvas.height
                  : options.pointer.y
        }
        // if (panning && options && options.e) { // padding 表示是否允许拖动画布
        //   const delta = new fabric.Point(options.e.movementX, options.e.movementY);
        //   canvas.relativePan(delta);
        //   //累计每一次移动时候的偏移量
        //   relationship.x += options.e.movementX / relationship.zoom;
        //   relationship.y += options.e.movementY / relationship.zoom;
        //   this.relationship = relationship
        // }
      })
      this.editorCanvas.on('mouse:up', (options) => {
        const [maxWidth, maxHeight] = [this.editorCanvas.width, this.editorCanvas.height]
        this.isDrawing = false
        // 如果没存在数据则可进行绘制
        const objArr = this.onePointGoup && this.onePointGoup.bounding_box && Object.keys(this.onePointGoup.bounding_box).length
        if (this.onePointGoup.random && !objArr) {
          let [x, y] = [options.pointer.x, options.pointer.y]
          // 解决绘制的时候超出边界
          this.mouseTo.x =
              x > maxWidth
                  ? maxWidth
                  : x
          this.mouseTo.y =
              y > maxHeight
                  ? maxHeight
                  : y
          // 宽高为负值或为0
          let width = this.mouseTo.x - this.mouseFrom.x
          let height = this.mouseTo.y - this.mouseFrom.y
          // 如果点击和松开鼠标，都是在同一个坐标点或者反向，不绘制矩形
          if (width <= 0 || height <= 0) return
          // TODO 修改了 开始坐标和结束坐标 明天试试结果-----------------
          // 计算矩形长宽
          let [left, top] = [this.mouseFrom.x, this.mouseFrom.y]
          left = left < 0 ? 0 : left
          top = top < 0 ? 0 : top
          width = width > maxWidth ? maxWidth : width
          height = height > maxHeight ? maxHeight : height
          this.drawRectFigure({ left, top, width, height }, { random: this.onePointGoup.random })
          this.$emit('updateCoords', { postion: { left, top, width, height }, random: this.onePointGoup.random })
        }
        // 鼠标抬起事件
        panning = false
        // canvas.selection = true;
        this.editorCanvas.renderAll()
      })
      // 元素拉伸
      this.editorCanvas.on('object:scaling', (options) => {
        panning = false
        const target = options.target
        const maxWidth = this.editorCanvas.width
        const maxHeight = this.editorCanvas.height
        const pointer = options.pointer
        const { tl, tr, bl } = target.lineCoords
        const disMaxR = maxWidth - tl.x
        const disMaxL = tr.x
        const disMaxB = maxHeight - tl.y
        const disMaxT = bl.y

        const scaleX = target.scaleX // 获取水平方向的缩放因子
        const scaleY = target.scaleY // 获取垂直方向的缩放因子
        const scalingWidth = target.width * scaleX // 计算拉伸后的宽度
        const scalingHeight = target.height * scaleY // 计算拉伸后的高度

        // 拉伸右边极限
        if (pointer.x >= maxWidth) {
          target.set({ scaleX: disMaxR / scalingWidth })
        }
        // 拉伸左边极限
        if (pointer.x <= 0) {
          target.set({ scaleX: disMaxL / scalingWidth, left: 0 })
        }
        // 拉伸底部极限
        if (pointer.y >= maxHeight) {
          target.set({ scaleY: disMaxB / scalingHeight })
        }
        // 拉伸顶部极限
        if (pointer.y <= 0) {
          target.set({ scaleY: disMaxT / scalingHeight, top: 0 })
        }
        // target.left = target.left < 0 ? 0 : target.left
        // target.top = target.top < 0 ? 0 : target.top
        // target.width = scalingWidth > maxWidth ? maxWidth : scalingWidth
        // target.height = scalingHeight > maxHeight ? maxHeight : scalingHeight
        // this.$emit('updateCoords', {postion: target.getBoundingRect(), random: this.onePointGoup.random})
        // let postion = target.getBoundingRect()
        // obj.left 是最后的结果需要反推之前的原始坐标
        // const {x, y, zoom} = this.relationship
        // let left = (target.left + x) * zoom
        // let top = (target.top + y) * zoom
        let lastPostion = {
          left: target.left < 0 ? 0 : target.left,
          top: target.top < 0 ? 0 : target.top,
          width: scalingWidth > maxWidth ? maxWidth : scalingWidth,
          height: scalingHeight > maxHeight ? maxHeight : scalingHeight
        }
        this.$emit('updateCoords', { postion: lastPostion, random: this.onePointGoup.random })
      })
      // 元素移动--拖拽鼠标超出也会有问题
      this.editorCanvas.on('object:moving', (e) => {
        panning = false
        const maxWidth = this.editorCanvas.width
        const maxHeight = this.editorCanvas.height
        // 边界处理
        let obj = e.target
        // if object is too big ignore
        if (
            obj.currentHeight > obj.canvas.height ||
            obj.currentWidth > obj.canvas.width
        ) {
          return
        }
        obj.setCoords()
        // top-left  corner
        if (obj.getBoundingRect().top < 0 || obj.getBoundingRect().left < 0) {
          obj.top = Math.max(obj.top, obj.top - obj.getBoundingRect().top)
          obj.left = Math.max(obj.left, obj.left - obj.getBoundingRect().left)
        }
        // bot-right corner
        if (
            obj.getBoundingRect().top + obj.getBoundingRect().height >
            obj.canvas.height ||
            obj.getBoundingRect().left + obj.getBoundingRect().width >
            obj.canvas.width
        ) {
          obj.top = Math.min(
              obj.top,
              obj.canvas.height -
              obj.getBoundingRect().height +
              obj.top -
              obj.getBoundingRect().top
          )
          obj.left = Math.min(
              obj.left,
              obj.canvas.width -
              obj.getBoundingRect().width +
              obj.left -
              obj.getBoundingRect().left
          )
        }
        let valueObj = obj.getBoundingRect()
        let lastPostion = {
          left: valueObj.left < 0 ? 0 : valueObj.left,
          top: valueObj.top < 0 ? 0 : valueObj.top,
          width: valueObj.width > maxWidth ? maxWidth : valueObj.width,
          height: valueObj.height > maxHeight ? maxHeight : valueObj.height
        }
        this.$emit('updateCoords', { postion: lastPostion, random: this.onePointGoup.random })
      })
      //  按下shift放大缩小
      this.editorCanvas.on('mouse:wheel', opt => {
        const e = opt.e
        if (!e.shiftKey) {
          return
        }
        const [x, y] = [e.offsetX, e.offsetY]
        let delta = e.deltaY
        let zoom = this.editorCanvas.getZoom()
        zoom *= 0.999 ** delta
        if (zoom > 20) zoom = 20
        if (zoom < 0.01) zoom = 0.01
        // 以鼠标所在位置为原点缩放
        this.editorCanvas.zoomToPoint(
            { x, y },
            zoom
        )
        // 计算缩放时产生的偏移量 这是重点代码
        relationship.x += x / zoom - x / relationship.zoom
        relationship.y += y / zoom - y / relationship.zoom
        relationship.zoom = zoom
        this.relationship = relationship

        e.preventDefault()
        e.stopPropagation()
      })
      // 监听键盘时间，按下backspace进行删除
      // if (!document.hasListenerAttached) {
      fabric.util.addListener(document, 'keydown', this.keyHandler)
      // document.hasListenerAttached = true
      // }
    },
    keyHandler(e) {
      const keyCode = e.keyCode
      if (keyCode == 8 || keyCode == 46) {
        this.backSpaceDel()
      }
    },
    /**
     * 绘制矩形
     * */
    drawRectFigure(postion, obj) {
      const [maxWidth, maxHeight] = [this.editorCanvas.width, this.editorCanvas.height]
      let { left, top, width, height } = postion
      left = left < 0 ? 0 : left
      top = top < 0 ? 0 : top
      width = width > maxWidth ? maxWidth : width
      height = height > maxHeight ? maxHeight : height

      let random = obj.random
      let color = this.$route.query.type
      const drawingObject = new fabric.Rect({
        width: width,
        height: height,
        fill: color && color == 200 ? '#03A9F4' : (obj.fill_color || '#00FCFF'),
        lockRotation: true,
        opacity: 0.5,
        rectId: random,
        lockScalingFlip: true, // 禁止负值反转
        originX: 'center',
        originY: 'center',
        selectable: !this.isLock // 是否可被选中
      })
      if (drawingObject) {
        const group = new fabric.Group([drawingObject], {
          rectId: random,
          left: left,
          top: top,
          width: width,
          height: height,
          lockScalingFlip: true,
          lockRotation: true,
          selectable: !this.isLock // 是否可被选中
        })
        if (this.isPopup) {
          group.on('mouseover', (e) => {
            this.overPopup = true
            this.overPopupTitle = `${this.$t('reaCommon.deviceCode')}：${obj.base_info && (obj.base_info.device_group_code || obj.base_info.device_code) || '-'}`
            let position = group.getCoords()
            this.popupLeft = position[0].x + 30 + 'px'
            this.popupTop = position[0].y - 35 + 'px'
          })
          group.on('mouseout', (e) => {
            this.overPopup = false
          })
        }
        this.editorCanvas.add(group)
        this.editorCanvas.renderAll()
      }
    },
    /**
     * 绘制时展示右键菜单栏内容
     * */
    showMenuCon(options) {
      // console.log(options);
      this.activeEl = options
      // 当前鼠标位置
      let pointX = options.left + options.width * options.scaleX
      let pointY = options.top

      this.menuPosition = `
                left: ${pointX}px;
                top: ${pointY}px;
              `
      this.showCon = true
    }

  }
}
</script>

<style lang="scss">
.digital__box {
  text-align: center;
  position: relative;

  .canvas-container {
    margin: 0 auto
  }
}

.popup-content {
  position: absolute;
  top: 0;
  left: 0;
  background-color: (rgba(#043E76, 0.7));
  border-radius: 4px;
  overflow: hidden;
  padding: 5px 10px;
  z-index: 10000;
  color: #ffffff;
}
</style>
