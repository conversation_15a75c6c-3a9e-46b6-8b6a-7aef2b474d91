<template>
  <div>
    <div ref="storePieChart" :class="className" :style="{ height: height, width: width }" style="margin: 0 auto"/>
    <div class="scrollbar-hidden" style="text-align: center;margin-top: 15px;max-height: 50px;overflow-y: scroll">
      <div>
        <span class="sales-label" v-for="(item,index) in this.resultData.list" :key="index"><span class="echart-label"
                                                                                                  :style="{backgroundColor:itemStyleMap[index % itemStyleMap.length].start}"></span>{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

require('echarts/theme/macarons') // echarts theme
// import resize from '@/utils/resize'

export default {
  // mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      // 1650 160
      // 1920 ?
      default: '160px'
    }
  },
  data() {
    return {
      itemStyleMap: [
        {
          start: '#026DB2',
          end: '#12FEE0'
        },
        {
          start: '#6DD400',
          end: '#44D7B6'
        },
        {
          start: '#5C64FF',
          end: '#6988F8'
        },
        {
          start: '#FFA600',
          end: '#FEDB65'
        },
        {
          start: '#D73838',
          end: '#FE698B'
        },
        {
          start: '#9270CA',
          end: '#C4A9E7'
        },
      ],
      chart: null,
      resultData: {}
    }
  },
  mounted() {
    // this.$nextTick(() => {
    //     this.initChart()
    // })
  },
  beforeDestroy() {
    this.clear()
  },
  methods: {
    /**
     * 清空
     * */
    clear() {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    },
    /**
     * 初始化
     * */
    initChart(resultData) {
      this.clear()
      this.resultData = resultData
      this.chart = echarts.init(this.$refs.storePieChart, 'macarons')
      const data = resultData.list.filter((v, index) => {
        if (v.value) {
          v.value = (v.value * 1)
        }
        v.itemStyle = {
          color: new echarts.graphic.LinearGradient(
            0, 0, 1, 0,
            [
              {
                offset: 0,
                color: this.itemStyleMap[index % this.itemStyleMap.length] && this.itemStyleMap[index % this.itemStyleMap.length].start || '#3371FF'
              },
              {
                offset: 1,
                color: this.itemStyleMap[index % this.itemStyleMap.length] && this.itemStyleMap[index % this.itemStyleMap.length].end || '#447DFF'
              }
            ]
          ),
          borderRadius: 0,
          borderColor: '#172942',
          borderWidth: 1
        }
        return v
      })
      this.chart.setOption({
        // 鼠标点击上
        tooltip: {
          trigger: 'item',
          formatter: '{b} :{d}%'

        },
        series: [
          {
            name: 'iRED Result',
            type: 'pie',
            center: ['50%', '50%'],
            radius: ['40%', '75%'],
            label: {
              show: false,
            },
            emphasis: {
              label: {
                show: true,
                color: '#39A0F4'
              },
              labelLine: {
                show: true,
                lineStyle: {
                  color: '#39A0F4',
                },
              },
            },
            data: data,
            animationEasing: 'cubicInOut',
            animationDuration: 2600,
            selectedMode: 'single',
            selectedOffset: 5,
            clockwise: true,
          }
        ]
      })
      this.chart.on('click', params => {
        this.$emit('selected', params.dataIndex)
        this.resultData.list.forEach((item, index) => {
          this.chart.dispatchAction({
            type: index == params.dataIndex ? 'highlight' : 'downplay',
            seriesIndex: 0,
            dataIndex: index,
          });
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.sales-label {
  display: inline-block;
  text-overflow: ellipsis;
  margin: 0 10px;
  margin-bottom: 10px;
  position: relative;
  padding-left: 15px;
  font-size: 12px;
}

.echart-label {
  display: inline-block;
  content: '';
  position: absolute;
  top: 2px;
  left: 0px;
  width: 8px;
  height: 8px;
  background-color: #3371FF;
  transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  z-index: 3;
}
</style>
