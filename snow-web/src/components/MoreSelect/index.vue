<template>
  <!-- 小数量直接展示下拉 有些不需要多选的-->
  <el-select
      v-if="opts.type != 'isIne' && opts.type != 'salesModeTypeId' && List.length > 30"
      v-model="value"
      filterable
      multiple
      :collapse-tags="true"
      ref="selectRef"
      size="medium"
      :placeholder="$t('reaCommon.pleaseEnter')"
      v-loadmore="morePage"
      @change="handleChange"
      :filter-method="listFilter"
      clearable>
    <el-option
        v-for="(item, index) in optionList"
        :key="item.value"
        :label="item.label"
        :value="item.value">
    </el-option>
  </el-select>
  <!--  如果数量小于20则直接展示下拉列表 是否INE是单选-->
  <el-select
      filterable
      :multiple="(opts.type == 'isIne') ? false : true"
      v-model="value"
      :placeholder="$t('reaCommon.pleaseEnter')"
      size="small"
      @change="handleChange"
      v-else>
    <el-option
        v-for="(item, index) in List"
        :key="item.value"
        :label="item.label"
        :value="item.value">
    </el-option>
  </el-select>
</template>

<script>
import Vue from 'vue'
import loadMore from '@/utils/pageScroll'

Vue.use(loadMore)
export default {
  props: ['refDom', 'List', 'opts', 'defoptionList', 'defValue', 'indexRandom'], // 绑定的dom[忽略]、原始所有list、传自己的参数{}、默认选中的optionList+defValue
  data() {
    return {
      limitFilter: 20,
      pagesize: 20,
      value: '',
      optionList: [], // 搜索到展现的数据条数
      originSerachList: [], // 搜索到的所有数据
      active: false,
    }
  },
  watch: {
    List(val) {
      this.$emit('update:List', val)
    },
    opts(val) {
      // this.updateInit() group更新的时候会出现清空数据的问题
      this.$emit('update:opts', val)
    },
    defoptionList(val) {
      this.$emit('update:defoptionList', val)
    },
    defValue(val) {
      this.updateInit()
      this.$emit('update:defValue', val)
    },
    indexRandom(val) {
      this.updateInit()
      this.$emit('update:indexRandom', val)
    }
  },
  created() {
    if (!this.List) {
      this.List = []
    }
    this.updateInit()
  },
  methods: {
    /**
     * @更新视图
     * */
    updateInit() {
      // console.log(this.opts)
      this.optionList = this.defoptionList ? this.defoptionList : []
      this.value = this.defValue ? this.defValue : ''
    },
    // 多关键词匹配 取交集
    searchClassRet (keywords, list) {
      let words = keywords.trim().split(/\s+/)
      let listArr = []
      if (words.length > 0) {
        words.map((val, key)=>{
          listArr[key] = []
          list && (list.map((v, k)=>{
            // 按照名字搜索和skuid
            // if(v.label.toUpperCase().indexOf(val.toUpperCase()) != -1 || v.value.toString().indexOf(val) != -1){
            if(v.label.toUpperCase().indexOf(val.toUpperCase()) != -1 || v.value.toString().indexOf(val) != -1){
              listArr[key].push(v)
            }
          }))
        })
      }
      return listArr
    },
    // 多关键词匹配 求并集
    searchClassFun (query, list) {
      let arrs = this.searchClassRet(query, list)
      let arr = arrs.shift()
      if(arrs.length > 0){
        for(let i = arrs.length; i--;){
          let p = {"boolean":{}, "number":{}, "string":{}}, obj = []
          arr = arr.concat(arrs[i]).filter(function (x) {
            let t = typeof x
            return !((t in p) ? !p[t][x] && (p[t][x] = 1) : obj.indexOf(x) < 0 && obj.push(x))
          })
          if(!arr.length) return null;
        }
      }
      return arr ? arr : []
    },
    /**
     * @数据过滤
     * */
    listFilter(query = '') {
      this.active = true
      this.$refs.selectRef.popperElm.querySelector('.el-select-dropdown .el-select-dropdown__wrap').scrollTop = 0
      let arr = this.searchClassFun(query, this.List)
      this.optionList = arr && this.pageCall(arr, this.limitFilter)
      this.originSerachList = arr
      this.pagesize = this.limitFilter
      setTimeout(() => {
        this.active = false
      }, 200)
    },
    /**
     * @裁剪数据长度
     * */
    pageCall(arr, pagesize) {
      return arr.length > pagesize ? arr.slice(0, pagesize) : arr
    },
    /**
     * @筛选项滚动加载
     * */
    morePage() {
      if (this.pagesize > this.originSerachList.length) {
        return false
      }
      if (this.active) {
        return false
      }
      this.active = true
      this.pagesize += 20

      this.optionList = this.pageCall(this.originSerachList, this.pagesize)
      this.active = false
    },
    handleChange() {
      this.$emit('nodeConfigHandler', {
        param: {
          opts: this.opts,
          value: this.value
        },
        component: 'groupSelect'
      })
    },
  }
}
</script>

<style scoped>
</style>
