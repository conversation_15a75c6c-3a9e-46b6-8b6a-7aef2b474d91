<template>
    <!--普通的下拉筛选 一级-->
    <!--        v-if="level == 1"-->
    <el-select
        filterable
        v-model="choseValue"
        placeholder="请选择"
        :multiple="multiple"
        @change="handleChange"
        size="small">
        <el-option
            v-for="item in selectOption"
            :key="item.value"
            :label="item.label"
            :value="item.value">
        </el-option>
    </el-select>
    <!--普通的下拉筛选 二级-->
<!--    <el-select  v-else filterable v-model="choseValue" placeholder="请选择" size="small">-->
<!--        <el-option-group-->
<!--                v-for="group in selectOption"-->
<!--                :key="group.id"-->
<!--                :label="group.name">-->
<!--            <el-option-->
<!--                    v-for="item in group.items"-->
<!--                    :key="item.id"-->
<!--                    :label="item.name"-->
<!--                    :value="item.id">-->
<!--            </el-option>-->
<!--        </el-option-group>-->
<!--    </el-select>-->

</template>

<script>
    export default {
        name: 'Page',
        data (){
            return {
                choseValue: ''
            }
        },
        props: ['selectOption', 'defValue', 'type', 'multiple', 'level'],
        watch: {
            defValue (val){
                this.choseValue = val
                this.$emit('update:defValue', val)
            },
            multiple (val){
                this.$emit('update:multiple', val)
            },
            type (val){
                this.$emit('update:type', val)
            },
            level (val){
                this.$emit('update:level', val)
            }
        },
        created() {
            this.choseValue = this.defValue
        },
        methods: {
            handleChange () {
                this.$emit('nodeConfigHandler', {
                    param: {
                        type: this.type,
                        value: this.choseValue
                    },
                    component: 'commonSelect'})
            },
        },
    }
</script>

<style scoped>
</style>
