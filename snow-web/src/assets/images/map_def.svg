<?xml version="1.0" encoding="UTF-8"?>
<svg width="54px" height="54px" viewBox="0 0 54 54" version="1.1" xmlns="http://www.w3.org/2000/svg"
     xmlns:xlink="http://www.w3.org/1999/xlink">
  <title>画板备份 3@1x</title>
  <defs>
    <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
      <stop stop-color="#FFA400" offset="0%"></stop>
      <stop stop-color="#FF7C00" offset="100%"></stop>
    </linearGradient>
    <path
      d="M10.7999906,0 C4.84403057,0 0,4.67159415 0,10.413573 C0,12.5452713 0.66490377,14.5971443 1.90831139,16.3233664 C1.97029394,16.4267764 2.03321562,16.526558 2.10740686,16.6236183 L9.99046088,26.6435075 C10.2045824,26.8730052 10.4919561,27 10.7999906,27 C11.1042686,27 11.3935205,26.8720981 11.6442681,26.6008735 L19.4906961,16.6227112 C19.5677047,16.5229296 19.6343829,16.4140769 19.6700699,16.3505795 C20.929087,14.6098539 21.6028958,12.5370091 21.5999906,10.413573 C21.5999906,4.67159415 16.7550115,0 10.7999906,0 Z"
      id="path-2"></path>
    <filter x="-30.1%" y="-20.4%" width="160.2%" height="148.1%" filterUnits="objectBoundingBox" id="filter-3">
      <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
      <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
      <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.0552611451 0" type="matrix"
                     in="shadowBlurOuter1"></feColorMatrix>
    </filter>
  </defs>
  <g id="画板备份-3" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
    <g id="default" transform="translate(16.000000, 21.000000)">
      <ellipse id="椭圆形" fill="#000000" opacity="0.203374818" cx="10.8" cy="27" rx="5.4" ry="1.8"></ellipse>
      <g id="默认" fill-rule="nonzero">
        <g id="形状">
          <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
          <use fill="url(#linearGradient-1)" xlink:href="#path-2"></use>
        </g>
      </g>
    </g>
  </g>
</svg>
