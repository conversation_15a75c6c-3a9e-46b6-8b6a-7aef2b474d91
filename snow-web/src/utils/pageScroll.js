import Vue from 'vue'
export default {}.install = (Vue, options = {}) => {
  Vue.directive('loadmore', {
    inserted(el, binding, vnode) {
        const dom = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap')
        dom.addEventListener('scroll', function() {
            const condtion = this.scrollHeight - this.scrollTop <= this.clientHeight
            if (condtion) {
                binding.value()
            }
        })
    }
  })
}
