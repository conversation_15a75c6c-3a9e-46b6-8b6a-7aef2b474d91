import CryptoJS from 'crypto-js'

const keyStr = 'ab685a4522b2a656'

// 加密
export function setEncrypt(word) {
  const key = CryptoJS.enc.Utf8.parse(keyStr)
  const srcs = CryptoJS.enc.Utf8.parse(word)
  const encrypted = CryptoJS.AES.encrypt(srcs, key, {mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7})
  return encrypted.toString()
}

// 解密
export function decrypt(word) {
  const key = CryptoJS.enc.Utf8.parse(keyStr)
  const decrypt = CryptoJS.AES.decrypt(word, key, {mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7})
  return CryptoJS.enc.Utf8.stringify(decrypt).toString()
}

// 需要加密字段
// export const needEncryptAndDecryptField = ['password', 'phone', 'confirm']
export const needEncryptAndDecryptField = []

