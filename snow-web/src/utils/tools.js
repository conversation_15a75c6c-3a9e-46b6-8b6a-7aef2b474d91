/**
 * 深拷贝
 */
export function deepJSON(key) {
  if (!key) {
    return
  }
  return JSON.parse(JSON.stringify(key))
}

/**
 * 打开Url
 * */
export function openWin(url, params) {
  window.open(`/#${url}?${params}`, '_blank')
  // window.location.href =
}
/**
 * 判断2个面积是否相交
 * */
export function doRectanglesIntersect(rect1, rect2) {
  if(!(rect1.tl && rect2.tl)){
    return false
  }
  // 矩形1的边界
  const left1 = rect1.tl[0];
  const right1 = rect1.tr[0];
  const top1 = rect1.tl[1];
  const bottom1 = rect1.bl[1];

  // 矩形2的边界
  const left2 = rect2.tl[0];
  const right2 = rect2.tr[0];
  const top2 = rect2.tl[1];
  const bottom2 = rect2.bl[1];

  // 检查边界是否重叠
  return left1 < right2 && left2 < right1 && top1 < bottom2 && top2 < bottom1;
}
/**
 * 根据4个点坐标计算出宽高
 * */
export function calculatePostion(point, scale) {
  if (!point || !Object.keys(point).length) {
    return {}
  }
  scale = scale ? scale : 1
  const { tl, bl, br, tr } = point
  return {
    left: tl[0] * scale,
    top: tl[1] * scale,
    width: (br[0] - bl[0]) * scale,
    height: (bl[1] - tr[1]) * scale
  }
}

/**
 * 获取当天时间
 * */
export function getNowTime() {
  let now = new Date()
  let year = now.getFullYear() //得到年份
  let month = now.getMonth() //得到月份
  let date = now.getDate() //得到日期
  month = month + 1
  month = month.toString().padStart(2, '0')
  date = date.toString().padStart(2, '0')
  return `${year}-${month}-${date}`
}

export function rangeDays(startDateStr, endDateStr, num = 15) {
  // 创建两个Date对象
  const startDate = new Date(startDateStr)
  const endDate = new Date(endDateStr)

  // 检查日期是否有效
  if (isNaN(startDate) || isNaN(endDate)) {
    throw new Error('Invalid date string')
  }

  // 确保结束日期晚于开始日期
  if (endDate <= startDate) {
    throw new Error('End date must be after start date')
  }

  // 计算日期差（以毫秒为单位）
  const diffInMilliseconds = endDate - startDate

  // 将毫秒转换为天
  const diffInDays = diffInMilliseconds / (1000 * 60 * 60 * 24)

  // 检查日期差是否大于15天
  return diffInDays > num
}

/**
 * 计算4个坐标点转化成bl br
 * */
export function calculateRectangleCorners({ top, left, width, height }, scale) {
  scale = scale || 1
  top = top / scale
  left = left / scale
  width = width / scale
  height = height / scale
  let tl = [left, top]
  let tr = [left + width, top]
  let bl = [left, top + height]
  let br = [left + width, top + height]
  return { tl, tr, bl, br }
}

/**
 * base64转成blob
 * */
export const dataURLtoBlob = (dataurl) => {
  let arr = dataurl.split(',')
  let mime = arr[0].match(/:(.*?);/)[1]
  let bstr = atob(arr[1])
  let n = bstr.length
  let u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new Blob([u8arr], { type: mime })
}

/**
 * 存储筛选条件和设置筛选条件
 * type 100=获取改变query查询条件 200=设置本地存储
 * 如果输入框输入不动，则是queryParam里没有设置好默认参数
 * */
export const setLocalQuery = (type, params, queryParam, pagination) => {
  const routeHash = window.location.hash
  let queryParams = localStorage.queryParams
  if (type == 100) {
    if (queryParams) {
      let parseQueryParams = JSON.parse(queryParams)
      const nowParseQueryParams = parseQueryParams[routeHash]
      if (nowParseQueryParams) {
        Object.keys(pagination).filter(v => {
          pagination[v] = nowParseQueryParams[v]
        })
      }
      return {
        queryParam: Object.assign(queryParam, nowParseQueryParams),
        pagination,
      }
    }
    return {
      queryParam, pagination,
    }
  } else {
    if (queryParams) {
      let parseQueryParams = JSON.parse(queryParams) || {}
      parseQueryParams[routeHash] = params
      localStorage.queryParams = JSON.stringify(parseQueryParams)
    } else {
      let queryParamsObj = {}
      queryParamsObj[routeHash] = params
      localStorage.queryParams = JSON.stringify(queryParamsObj)
    }
  }
}
