/**
 * 安装验收列表
 * */
export const installHeader = [
  {
    prop: 'store_code',
    label: '售点编号',
    labelKey: 'storeCode',
    disabled: true
  },
  {
    prop: 'store_name',
    label: '售点名称',
    labelKey: 'storeName'
  }, {
    prop: 'address',
    label: '售点地址',
    labelKey: 'storeAddress',
    width: '220px'
  },
  {
    prop: 'device_count',
    label: '设备总数',
    width: '100px',
    labelKey: 'deviceTotCount'
  },
  {
    prop: 'create_time',
    label: '创建时间',
    width: '220px',
    labelKey: 'createTime'
  },
  {
    prop: 'install_status_label',
    width: '130px',
    label: '任务状态',
    slot: 'taskStatus',
    labelKey: 'taskStatus'
  },
  {
    prop: 'installer_name',
    label: '安装人员',
    labelKey: 'installUser'
  },
  {
    prop: 'edit_user_name',
    label: '验收人员',
    labelKey: 'checkUser'
  },
  {
    prop: 'supplier_name',
    labelKey: 'supplier',
    label: '供应商'
  },
  {
    prop: 'finished_time',
    label: '任务完成时间',
    labelKey: 'taskFinishTime'
    // slot: 'finishTime',
  }
]
/**
 * 安装验收列表
 * */
export const replaceHeader = [
  {
    prop: 'store_code',
    label: '售点编号',
    labelKey: 'storeCode',
    disabled: true
  },
  {
    prop: 'store_name',
    label: '售点名称',
    labelKey: 'storeName'
  }, {
    prop: 'address',
    label: '售点地址',
    labelKey: 'storeAddress',
    width: '220px'
  },
  {
    prop: 'install_status_label',
    width: '130px',
    label: '任务状态',
    slot: 'taskStatus',
    labelKey: 'taskStatus'
  },
  {
    prop: 'installer_name',
    label: '安装人员',
    labelKey: 'installUser'
  },
  {
    prop: 'edit_user_name',
    label: '验收人员',
    labelKey: 'checkUser'
  },
  {
    prop: 'create_time',
    label: '创建时间',
    width: '220px',
    labelKey: 'createTime'
  },
  {
    prop: 'finished_time',
    label: '任务完成时间',
    labelKey: 'taskFinishTime'
    // slot: 'finishTime',
  }
  // {
  //   prop: 'supplier_name',
  //   labelKey: 'supplier',
  //   label: '供应商'
  // }
]

/**
 * 安装验收列表
 * */
export const issueHeader = [
  {
    prop: 'store_code',
    label: '售点编号',
    labelKey: 'storeCode',
    disabled: true
  },
  {
    prop: 'device_code',
    label: '设备编号',
    labelKey: 'deviceCode',
    disabled: true
  },
  {
    prop: 'store_name',
    label: '售点名称',
    labelKey: 'storeName'
  }, {
    prop: 'address',
    label: '售点地址',
    labelKey: 'storeAddress',
    width: '220px'
  },
  {
    prop: 'issue_status_label',
    width: '130px',
    label: '问题件状态',
    // slot: 'taskStatus',
    labelKey: 'issueStatus'
  },
  {
    prop: 'resolution_type_label',
    label: '处理方式',
    labelKey: 'methodStatus'
  },
  {
    prop: 'resolution_type_update_admin_name',
    label: '处理人员',
    labelKey: 'handelUser'
  },
  {
    prop: 'create_time',
    label: '创建时间',
    width: '220px',
    labelKey: 'createTime'
  },
  {
    prop: 'resolution_type_update_time',
    label: '处理时间',
    labelKey: 'handelTime'
    // slot: 'finishTime',
  },
  {
    prop: 'finished_time',
    label: '任务完成时间',
    labelKey: 'taskFinishTime'
    // slot: 'finishTime',
  }
  // {
  //   prop: 'supplier_name',
  //   labelKey: 'supplier',
  //   label: '供应商'
  // }
]
/**
 * 前台用户列表
 */
export const accountFrontendHeader = [
  {
    prop: 'id',
    label: '用户ID',
    labelKey: 'userId',
    width: '130px'
  },
  {
    prop: 'username',
    labelKey: 'name',
    label: '姓名'
  },
  {
    prop: 'phone',
    label: '手机号',
    labelKey: 'phone',
    width: '140px'
  },
  {
    prop: 'supplier_name',
    labelKey: 'supplier',
    label: '供应商'
  },
  {
    prop: 'status',
    label: '状态',
    labelKey: 'status',
    slot: 'accountStatus'
  },
  {
    prop: 'create_time',
    label: '创建时间',
    labelKey: 'createTime',
    width: '180px'
  },
  {
    prop: 'update_time',
    label: '更新时间',
    labelKey: 'updateDate',
    width: '180px'
  }
]

/**
 * 后台角色列表
 */
export const accountRoleHeader = [
  {
    prop: 'tenant_name',
    label: '品牌方',
    labelKey: 'tenant'
  },
  {
    prop: 'role_name',
    label: '角色名称',
    labelKey: 'roleName'
  },
  {
    prop: 'remark',
    label: '备注',
    labelKey: 'remark'

  },
  {
    prop: 'created_at',
    label: '创建时间',
    labelKey: 'createTime'
  }
]

/**
 * 后台用户列表
 */
export const accountBackendHeader = [
  {
    prop: 'admin_name',
    label: '用户名',
    labelKey: 'username'
  },
  {
    prop: 'phone',
    label: '手机号',
    labelKey: 'phone',
    width: '140px'
  },
  {
    prop: 'tenant_name',
    label: '品牌方',
    labelKey: 'tenant',
    sortable: true
  },
  // {
  //     prop: 'bu_list',
  //     label: '所属BU',
  //     slot: 'BU',
  //     labelKey: 'BU',
  // },
  {
    prop: 'position',
    label: '岗位',
    labelKey: 'position'
  },
  {
    prop: 'role_name',
    label: '角色',
    labelKey: 'role'
  },
  {
    prop: 'freeze_status',
    label: '是否锁定',
    labelKey: 'isLockOrNot',
    slot: 'freeze_status'
  },
  {
    prop: 'status',
    label: '状态',
    labelKey: 'status',
    slot: 'status',
    sortable: true

  },
  {
    prop: 'created_at',
    label: '创建时间',
    labelKey: 'createTime',
    sortable: true,
    width: '180px'
  }, {
    prop: 'updated_at',
    label: '更新时间',
    width: '180px'
  }
]

/**
 * 周期管理列表
 */
export const periodManageHeader = [
  {
    prop: 'cycle_code',
    label: '周期编号',
    labelKey: 'cycleNumber',
    width: '200px'
  },
  {
    prop: 'name',
    label: '周期名称',
    labelKey: 'cycleName'
  },
  {
    prop: 'start_date',
    label: '开始日期',
    labelKey: 'startDate'
  },
  {
    prop: 'end_date',
    label: '结束日期',
    labelKey: 'endDate'
  },
  {
    prop: 'brand_name',
    label: '品牌名称',
    labelKey: 'tenantName'
  },
  {
    prop: 'founder',
    label: '创建人',
    labelKey: 'creator'
  },
  {
    prop: 'created_at',
    label: '创建日期',
    labelKey: 'createDate',
    sortable: true,
    width: '180px'
  }
]

/**
 * 拜访计划列表
 */
export const visitPlanHeader = [
  {
    prop: 'brand_name',
    label: '品牌',
    labelKey: 'tenant',
    sortable: true,
    disabled: true
  },
  {
    prop: 'survey_id',
    label: '走访ID',
    labelKey: 'visitedId',
    sortable: true
  },
  {
    prop: 'visitor_code',
    label: '走访员编号',
    labelKey: 'visitorNumber',
    sortable: true,
    width: '190px',
    disabled: true

  },
  {
    prop: 'visitor_name',
    label: '走访员姓名',
    labelKey: 'visitorName',
    sortable: true,
    width: '150px',
    disabled: true
  },
  {
    prop: 'store_code',
    label: '售点编号',
    labelKey: 'storeCode',
    sortable: true,
    width: '260px',
    disabled: true

  },
  {
    prop: 'name',
    label: '售点名称',
    labelKey: 'storeName',
    sortable: true,
    width: '210px',
    disabled: true

  },
  {
    prop: 'start_date',
    label: '开始时间',
    labelKey: 'startTime',
    sortable: true,
    disabled: true

  },
  {
    prop: 'end_date',
    label: '结束时间',
    labelKey: 'endTime',
    sortable: true,
    disabled: true

  },
  {
    prop: 'type',
    label: '拜访类型',
    labelKey: 'visitType',
    sortable: true,
    slot: 'type',
    disabled: true
  },
  {
    prop: 'visit_date',
    label: '拜访日期',
    labelKey: 'visitDate',
    slot: 'visit_date',
    sortable: true
  },
  {
    prop: 'cycle_code',
    label: '周期编号',
    labelKey: 'cycleNumber',
    sortable: true,
    width: '210px'
  },
  {
    prop: 'days_left',
    label: '剩余天数',
    labelKey: 'remainingDays',
    sortable: true,
    width: '170px'
  },
  {
    prop: 'creator_name',
    label: '创建人',
    labelKey: 'creator',
    sortable: true,
    width: '130px'
  },
  {
    prop: 'created_at',
    label: '创建日期',
    labelKey: 'createDate',
    sortable: true,
    width: '190px'
  }
]

/**
 * 已拜访查询列表
 */
export const visitedListHeader = [
  {
    prop: 'brand_name',
    label: '品牌',
    labelKey: 'tenant',
    slot: 'brand_name',
    disabled: true
  },
  {
    prop: 'survey_id',
    label: '走访ID',
    labelKey: 'visitedId',
    width: '130px',
    slot: 'survey_id'
  },
  {
    prop: 'cycle_code',
    label: '周期编号',
    labelKey: 'cycleNumber',
    width: '210px'
  },
  {
    prop: 'cycle_name',
    label: '周期名称',
    labelKey: 'cycleName',
    width: '130px'
  },
  {
    prop: 'visitor_code',
    label: '走访员编号',
    labelKey: 'visitorNumber',
    width: '190px'
  },
  {
    prop: 'visitor_name',
    label: '走访员姓名',
    labelKey: 'visitorName',
    width: '130px'
  },
  {
    prop: 'position',
    label: '岗位',
    labelKey: 'position',
    width: '130px'
  },
  {
    prop: 'store_code',
    label: '售点编号',
    labelKey: 'storeCode',
    width: '260px',
    disabled: true

  },
  {
    prop: 'store_name',
    label: '售点名称',
    labelKey: 'storeName',
    width: '210px',
    disabled: true

  },
  {
    prop: 'address',
    label: '售点地址',
    labelKey: 'storeAddress',
    width: '190px'
  },
  {
    prop: 'channel_name',
    label: '渠道类型',
    labelKey: 'channelType',
    disabled: true
  },
  {
    prop: 'province_name',
    label: '省份',
    labelKey: 'provinces'
  },
  {
    prop: 'city_name',
    label: '城市',
    labelKey: 'city'
  },
  {
    prop: 'county_name',
    label: '区县',
    labelKey: 'county'
  },
  {
    prop: 'random_access',
    label: '随店随访',
    labelKey: 'wheneverWhereverVisit',
    slot: 'random_access',
    width: '210px',
    disabled: true
  },
  {
    prop: 'visit_time',
    label: '走访时间',
    labelKey: 'visitTime',
    width: '190px'
  },
  {
    prop: 'entry_time',
    label: '进店时间',
    labelKey: 'intoStoreTime',
    width: '190px'
  },
  {
    prop: 'departure_time',
    label: '离店时间',
    labelKey: 'leaveStoreTime',
    width: '190px'
  },
  {
    prop: 'duration',
    label: '在店时长',
    labelKey: 'visitDuration',
    width: '170px'
  },

  {
    prop: 'store_image',
    label: '门头照',
    labelKey: 'doorImage',
    slot: 'store_image',
    width: '170px',
    disabled: true
  },
  {
    prop: 'store_video',
    label: '视频',
    labelKey: 'video',
    slot: 'store_video',
    width: '230px',
    disabled: true
  }
]

/**
 * 异常门店列表
 */
export const abnormalStoreHeader = [
  {
    prop: 'brand_name',
    label: '品牌',
    labelKey: 'tenant'
  },
  {
    prop: 'cycle_code',
    label: '周期编号',
    labelKey: 'cycleNumber',
    width: '200px'
  },
  {
    prop: 'visitor_code',
    label: '走访员编号',
    labelKey: 'visitorNumber',
    width: '180px'
  },
  {
    prop: 'visitor_name',
    label: '走访员姓名',
    labelKey: 'visitorName'
  },
  {
    prop: 'store_code',
    label: '售点编号',
    labelKey: 'storeCode',
    width: '250px'
  },
  {
    prop: 'store_name',
    label: '售点名称',
    labelKey: 'storeName',
    width: '250px'
  },
  {
    prop: 'visit_time',
    label: '走访日期',
    labelKey: 'visitDate',
    width: '180px'
  },
  {
    prop: 'store_image',
    label: '照片',
    labelKey: 'picture',
    slot: 'store_image',
    width: '120px'
  },
  {
    prop: 'category',
    label: '异常类型',
    labelKey: 'errorType'
  },
  {
    prop: 'error_msg',
    label: '异常原因',
    labelKey: 'errorReason',
    width: '250px'
  },
  {
    prop: 'summary',
    label: '异常点说明',
    labelKey: 'errorDescription',
    width: '250px'
  }
]

/**
 * 检查计划列表
 */
export const planHeader = [
  {
    prop: 'created_at',
    label: '创建时间',
    labelKey: 'createTime',
    width: '200px'
  },
  {
    prop: 'brand_name',
    label: '品牌',
    labelKey: 'tenant'
  },
  {
    prop: 'org_name',
    label: '部门',
    labelKey: 'department'
  },
  {
    prop: 'id',
    label: '检查计划ID',
    labelKey: 'checkPlanId',
    width: '160px'
  }, {
    prop: 'project_name',
    label: '检查项目名称',
    labelKey: 'checkProjectName',
    slot: 'projectName',
    width: '300px'
  }, {
    prop: 'start_date',
    label: '开始日期',
    labelKey: 'startDate',
    width: '110px'
  }, {
    prop: 'end_date',
    label: '结束日期',
    labelKey: 'endDate',
    width: '110px'
  }, {
    prop: 'status',
    label: '状态',
    labelKey: 'status',
    slot: 'status'
  }
]

export const storeWarehouseHeader = [
  {
    prop: 'task_id',
    label: '任务ID',
    labelKey: 'taskId'
  },
  {
    prop: 'task_name',
    label: '任务名称',
    labelKey: 'taskName'
  },
  {
    prop: 'store_name',
    label: '企业名称',
    labelKey: 'storeName2'
  },
  {
    prop: 'store_province',
    label: '所属省',
    labelKey: 'provinces'
  },
  {
    prop: 'store_city',
    label: '所属市',
    labelKey: 'city'
  },
  {
    prop: 'store_county',
    label: '所属区',
    labelKey: 'county'
  },
  {
    prop: 'store_address',
    label: '详细地址',
    labelKey: 'detailAddress',
    width: '220px'
  },
  {
    prop: 'store_contact_name',
    label: '联系人',
    labelKey: 'theContact',
  },
  {
    prop: 'store_contact_phone',
    label: '联系电话',
    labelKey: 'contact',
  },
  {
    prop: 'task_deadline',
    label: '任务截止时间',
    labelKey: 'taskEndTime',
  },
  {
    prop: 'task_date_demand',
    label: '具体时间要求',
    labelKey: 'timeAskDetail',
  },
  {
    prop: 'task_desc',
    label: '任务描述',
    labelKey: 'taskDes',
  },
  {
    prop: 'task_group_name',
    label: '是否分组',
    labelKey: 'isdisGroup',
    slot: 'isGroup'
  },
  {
    prop: 'task_group_name',
    label: '任务分组',
    labelKey: 'taskDisGroup'
  }
  // {
  //   prop: 'task_remark',
  //   label: '备注',
  //   labelKey: 'remark'
  // }
]
export const checkIndexHeader = [
  {
    prop: 'task_id',
    label: '任务ID',
    labelKey: 'taskId'
  },
  {
    prop: 'task_name',
    label: '任务名称',
    labelKey: 'taskName'
  },
  {
    prop: 'store_name',
    label: '企业名称',
    labelKey: 'storeName2'
  },
  {
    prop: 'store_province',
    label: '所属省',
    labelKey: 'provinces'
  },
  {
    prop: 'store_city',
    label: '所属市',
    labelKey: 'city'
  },
  {
    prop: 'store_county',
    label: '所属区',
    labelKey: 'county'
  },
  {
    prop: 'store_address',
    label: '详细地址',
    labelKey: 'detailAddress',
    width: '220px'
  },
  {
    prop: 'task_desc',
    label: '任务描述',
    labelKey: 'taskDes',
  },
  {
    prop: 'task_group_name',
    label: '任务分组',
    labelKey: 'taskDisGroup',
  },
  {
    prop: 'created_at_f',
    label: '创建日期',
    labelKey: 'createDate',
  },
  {
    prop: 'audit_status_msg',
    label: '审核状态',
    labelKey: 'auditStatus',
    slot: 'auditStatus'
  },
  {
    prop: 'status_msg',
    label: '任务状态',
    labelKey: 'taskStatus',
  },
  {
    prop: 'admin_name',
    label: '审核人',
    labelKey: 'checkUser',
  },
  {
    prop: 'visitor_phone_suffix',
    label: '联系方式',
    labelKey: 'contact',
  }
]

/**
 * 品牌管理列表
 */
export const brandHeader = [
  {
    prop: 'tenant_code',
    label: '品牌ID',
    labelKey: 'tenantId',
    width: '130px'
  },
  {
    prop: 'tenant_name',
    label: '品牌名称',
    labelKey: 'tenantName'
  },
  {
    prop: 'created_at',
    label: '创建时间',
    labelKey: 'createTime',
    width: '200px'
  },
  {
    prop: 'status',
    label: '状态',
    slot: 'status',
    labelKey: 'status'
  }
]

/**
 * API识别结果列表
 */
export const apiHeader = [
  {
    prop: 'request_id',
    label: '任务ID',
    labelKey: 'taskId',
    width: '320px'
  },
  {
    prop: 'scene_name',
    label: '场景',
    slot: 'scene',
    labelKey: 'scene'
  },
  {
    prop: 'created_at',
    label: '创建时间',
    labelKey: 'createTime',
    slot: 'created_at',
    width: '180px'
  },
  {
    prop: 'report_status',
    label: '状态',
    labelKey: 'status',
    slot: 'step'
  }
]

/**
 * API识别结果列表（蒙牛）
 */
export const apiMengniuHeader = [
  {
    prop: 'task_name',
    label: '任务名称'
  },
  {
    prop: 'visit_date',
    label: '走访日期'
  },
  {
    prop: 'customer_name',
    label: '门店名称'
  },
  {
    prop: 'store_code',
    label: '售点编号'
  },
  {
    prop: 'created_at',
    label: '创建时间',
    width: '180px'
  },
  {
    prop: 'updated_at',
    label: '更新时间',
    width: '180px'
  }

]

/**
 * 日志列表
 */
export const logHeader = [
  {
    prop: 'id',
    label: 'ID',
    width: '100px'
  },
  {
    prop: 'name',
    label: '用户名称',
    width: '200px'
  },
  {
    prop: 'table_name',
    label: '表名',
    slot: 'table_name'
  }
]

/**
 * 日志场景报告列表
 */
export const sceneReportTableHeader = [
  {
    prop: 'id',
    label: 'ID'
  },
  {
    prop: 'survey_auto_id',
    label: '走访ID'
  },
  {
    prop: 'scene_auto_id',
    label: '场景表ID'
  },
  {
    prop: 'answer',
    label: '问卷答案',
    slot: 'answer'

  },
  {
    prop: 'status',
    label: '报告状态',
    slot: 'status'

  },
  {
    prop: 'is_submit',
    label: '提交状态',
    slot: 'is_submit'

  }
]

/**
 * 日志场景信息列表
 */
export const sceneTableHeader = [
  {
    prop: 'id',
    label: 'ID'
  },
  {
    prop: 'user_id',
    label: '用户ID'
  },
  {
    prop: 'survey_auto_id',
    label: '走访ID'
  },
  {
    prop: 'scene_code',
    label: '场景code',
    width: '150px'

  },
  {
    prop: 'scene_name',
    label: '场景名称'

  },
  {
    prop: 'is_deleted',
    label: '删除标识',
    slot: 'is_deleted'

  },
  {
    prop: 'is_push',
    label: '上传状态',
    slot: 'is_push'

  }
]

/**
 * 日志走访计划列表
 */
export const surveyTableHeader = [
  {
    prop: 'id',
    label: 'ID'
  },
  {
    prop: 'survey_id',
    label: '走访ID'
  },
  {
    prop: 'visit_type',
    label: '走访类型',
    slot: 'visit_type'
  },
  {
    prop: 'visit_date',
    label: '走访日期',
    width: '150px'

  },
  {
    prop: 'cycle_code',
    label: '周期code',
    width: '200px'

  },
  {
    prop: 'cycle_end_date',
    label: '周期结束时间戳',
    width: '150px'

  },
  {
    prop: 'question_list',
    label: '问卷列表',
    slot: 'question_list'

  },
  {
    prop: 'store_code',
    label: '售点编号',
    labelKey: 'storeCode',
    width: '250px'
  },
  {
    prop: 'store_name',
    label: '售点名称',
    labelKey: 'storeName',
    width: '200px'
  },
  {
    prop: 'store_address',
    label: '售点地址',
    labelKey: 'storeAddress',
    width: '250px'
  },
  {
    prop: 'store_image',
    label: '售点图片',
    width: '400px'

  },
  {
    prop: 'store_channel',
    label: '售点渠道',
    width: '150px'
  },
  {
    prop: 'store_lng',
    label: '售点经度',
    width: '150px'

  },
  {
    prop: 'store_lat',
    label: '售点纬度',
    width: '150px'
  },
  {
    prop: 'answer',
    label: '售点问卷答案',
    slot: 'answer',
    width: '150px'
  },
  {
    prop: 'check_type',
    label: '检查模式',
    slot: 'check_type',
    width: '150px'
  },
  {
    prop: 'lng',
    label: '签到打卡经度',
    width: '150px'
  },
  {
    prop: 'lat',
    label: '签到打卡纬度',
    width: '150px'
  },
  {
    prop: 'door_image_key',
    label: '拍摄门头照片',
    width: '150px'
  },
  {
    prop: 'video_key',
    label: '拍摄门头视频',
    width: '150px'
  },
  {
    prop: 'departure_time',
    label: '离店时间',
    width: '200px'
  },
  {
    prop: 'visit_status',
    label: '走访状态',
    slot: 'visit_status',
    width: '150px'
  },
  {
    prop: 'is_submit',
    label: '是否提交标识',
    slot: 'is_submit',
    width: '150px'
  },
  {
    prop: 'is_deleted',
    label: '删除标识',
    slot: 'is_deleted',
    width: '150px'
  },
  {
    prop: 'is_push',
    label: '上传状态',
    slot: 'is_push',
    width: '150px'
  }
]

/**
 *日志图片列表表头
 */
export const imageTableHeader = [
  {
    prop: 'id',
    label: 'ID'
  },
  {
    prop: 'survey_auto_id',
    label: '走访ID'
  },
  {
    prop: 'scene_auto_id',
    label: '场景ID'
  },
  {
    prop: 'question_id',
    label: '问卷ID'

  },
  {
    prop: 'store_code',
    label: '售点编号',
    labelKey: 'storeCode',
    width: '250px'
  },
  {
    prop: 'scene_code',
    label: '场景CODE',
    width: '150px'
  },
  {
    prop: 'path',
    label: '本地路径',
    width: '400px'
  },
  {
    prop: 'photo_time',
    label: '拍照时间',
    width: '150px'
  },
  {
    prop: 'key',
    label: 'COS key',
    width: '300px'
  },
  {
    prop: 'type',
    label: '图片类型',
    slot: 'type'

  },
  {
    prop: 'origin',
    label: '图片来源',
    slot: 'origin'

  },
  {
    prop: 'is_upload',
    label: '上传cos状态',
    width: '120px',
    slot: 'is_upload'

  },
  {
    prop: 'is_push',
    label: '上传状态',
    slot: 'is_push'
  }
]

/**
 * SKU管理列表
 */
export const skuManageHeader = [
  {
    selection: true,
    prop: 'group_name',
    label: '品牌',
    labelKey: 'tenant',
    width: '140px'
  },
  {
    prop: 'sku_name',
    label: 'sku名称',
    labelKey: 'skuName',
    slot: 'sku_name'
  },
  {
    prop: 'created_at',
    label: '导入日期',
    labelKey: 'importDate',
    width: '200px'
  },
  {
    prop: 'master_url',
    label: 'logo',
    labelKey: 'skuLogo',
    width: '130px',
    slot: 'master_url'
  }
]

/**
 * 识别模型列表
 */
export const recognitionProjectHeader = [
  {
    prop: 'name',
    label: '名称',
    labelKey: 'gatewayName',
    width: '150px'
  },
  {
    prop: 'project',
    labelKey: 'projectName',
    label: '模型',
    width: '200px'
  },
  {
    prop: 'recognition_url',
    labelKey: 'recognitionUrl',
    label: '识别接口地址'
  },
  {
    prop: 'stitching_queue',
    labelKey: 'stitchingQueue',
    label: '拼接队列名',
    width: '300px'
  }
]

/**
 * 设备型号列表
 */
export const deviceHeader = [
  {
    prop: 'device_id',
    labelKey: 'deviceId',
    label: '设备ID',
    width: '300px'
  },
  {
    prop: 'device_name',
    label: '设备机型',
    labelKey: 'deviceModel'
  },
  {
    prop: 'system',
    labelKey: 'systemNameAndOSVersion',
    label: '系统名称及OS版本',
    width: '240px'
  },
  {
    prop: 'app_version',
    labelKey: 'appVersion',
    label: 'app版本'
  },
  {
    prop: 'sdk_version',
    labelKey: 'sdkVersion',
    label: 'sdk版本'
  },
  {
    prop: 'start_use_time',
    labelKey: 'startUseTime',
    label: '开始使用时间',
    width: '180px'
  },
  {
    prop: 'last_use_time',
    labelKey: 'lastUseTime',
    label: '最后使用时间',
    width: '180px'
  },
  {
    prop: 'pixel_ratio',
    labelKey: 'devicePixelRatio',
    label: '设备像素比',
    width: '150px'
  },
  {
    prop: 'language',
    labelKey: 'deviceLang',
    label: '应用设置语言'
  }
]

/**
 * API日志
 */
export const apiLogHeader = [
  {
    prop: '1',
    labelKey: 'operationAuthority',
    label: '操作权限',
    slot: 'operationAuthority'
  },
  {
    prop: '2',
    label: '操作行为',
    labelKey: 'operationBehavior',
    slot: 'operationBehavior'
  },
  {
    prop: '3',
    labelKey: 'locationPosition',
    label: '地理位置',
    slot: 'locationPosition'
  },
  {
    prop: '4',
    labelKey: 'operationTime',
    label: '操作时间',
    slot: 'operationTime'
  }
]

/**
 * 场景列表
 */
export const sceneIndexHeader = [
  {
    prop: 'sort',
    labelKey: 'sort',
    label: '排序'
  },
  {
    prop: 'scene_name',
    labelKey: 'sceneName',
    label: '场景名称'
  },
  {
    prop: 'scene_images',
    label: '场景图',
    labelKey: 'sceneImage',
    slot: 'scene_images',
    width: '90px'
  },
  {
    prop: 'scene_type_name',
    labelKey: 'sceneType',
    label: '场景类型'
  },
  // {
  //   prop: 'open_at_msg',
  //   label: '场景总数',
  // },
  {
    prop: 'is_bind_device',
    labelKey: 'isBindDev',
    slot: 'is_bind_device',
    label: '是否绑定设备'
  },
  {
    prop: 'is_bind_plan',
    labelKey: 'isBindPlan',
    slot: 'is_bind_plan',
    label: '是否绑定平面图'
  },
  {
    prop: 'code',
    labelKey: 'edit',
    label: '操作',
    slot: 'edit',
    attrs: {
      fixed: 'right'
    }
  }
]
/**
 * 售点版本号列表
 */
export const versionHeader = [
  {
    prop: 'id',
    labelKey: 'versionCodeNumber',
    label: '版本编号'
  },
  // {
  //   prop: 'version_name',
  //   labelKey: 'versionCode',
  //   label: '版本名称'
  // },
  // {
  //     prop: 'version_name',
  //     labelKey: 'versionName',
  //     label: '版本名称',
  // },
  // {
  //     prop: 'status',
  //     labelKey: 'status',
  //     slot: 'status',
  //     label: '状态',
  // },
  // {
  //     prop: 'is_current_version',
  //     labelKey: 'currentVersion',
  //     width: '120px',
  //     slot: 'currentVersion',
  //     label: '当前使用版本',
  // },
  {
    prop: 'start_time',
    labelKey: 'effectiveStart',
    label: '生效开始时间'
  },
  {
    prop: 'end_time',
    labelKey: 'effectiveStop',
    label: '生效结束时间',
    slot: 'effectiveStop',
  },
  {
    prop: 'note',
    labelKey: 'versionDesc',
    label: '描述'
  },
  // {
  //     prop: 'tenant_name',
  //     labelKey: 'publishPersion',
  //     label: '发布人',
  // },
  {
    prop: 'created_at',
    labelKey: 'publishTime',
    label: '创建时间'
  },
  {
    prop: 'code',
    labelKey: 'operation',
    width: '190px',
    label: '操作',
    slot: 'edit',
    attrs: {
      fixed: 'right'
    }
  }
]

/**
 * QC
 */
export const qcHeader = [
  {
    prop: 'device_group_photo_id',
    label: '编号',
    labelKey: 'number'
  },
  {
    prop: 'device_group_code',
    label: '设备编号',
    labelKey: 'deviceCode'
  },
  {
    prop: 'device_group_name',
    label: '设备名称',
    labelKey: 'deviceName'
  },
  {
    prop: 'open_at',
    label: '拍照时间 ',
    labelKey: 'takePhotos'
  },
  // {
  //   prop: 'store_id',
  //   label: '售点ID ',
  // },
  {
    label: '销量处理',
    slot: 'sale_status',
    labelKey: 'gatherSales'
  },
  {
    prop: 'sale_start_at',
    label: 'QC开始时间',
    labelKey: 'QCStartTime'
  },
  {
    prop: 'sale_finish_at',
    label: 'QC完成时间',
    labelKey: 'QCFinishTime'
  },
  {
    prop: 'code',
    label: '操作',
    labelKey: 'edit',
    slot: 'edit'
  }
]

export const qcCoolerHeader = [
  {
    prop: 'device_group_photo_id',
    label: '编号',
    labelKey: 'number',
    width: '100px'
  },
  {
    prop: 'device_group_code',
    label: '设备编号',
    labelKey: 'deviceCode'
  },
  {
    prop: 'device_group_name',
    label: '设备名称',
    labelKey: 'deviceName'
  },
  {
    prop: 'open_at',
    label: '开门时间',
    labelKey: 'openAtTime',
    width: '160px'
  },
  {
    prop: 'close_at',
    label: '关门时间',
    labelKey: 'closeAtTime',
    width: '160px'
  },
  {
    prop: 'recognition_status_msg',
    label: '识别处理',
    labelKey: 'gatherDiscriminate',
    slot: 'recognition_status'
  },
  {
    prop: 'sale_status_msg',
    label: '销量处理',
    labelKey: 'gatherSales',
    slot: 'sale_status'
  },
  {
    prop: 'code',
    label: '操作',
    labelKey: 'edit',
    slot: 'edit',
    width: '230px'
  }
]

/**
 * 设备型号列表
 */
export const deviceIndexHeader = [
  {
    prop: 'device_code',
    labelKey: 'deviceNumber',
    label: '设备编号'
  },
  {
    prop: 'type',
    labelKey: 'deviceType',
    slot: 'deviceType',
    label: '设备类型'
  },
  {
    prop: 'adc',
    labelKey: 'voltage',
    label: '电压值'
  },
  {
    prop: 'store_code',
    labelKey: 'belongStoreNameCode',
    label: '所属售点编号'
  },
  {
    prop: 'store_name',
    labelKey: 'belongStore',
    label: '所属售点'
  },
  {
    prop: 'device_install_status_label',
    labelKey: 'deviceInStatus',
    label: '设备安装状态'
  },
  {
    prop: 'status',
    labelKey: 'deviceStatus',
    slot: 'deviceStatus',
    label: '设备状态'
  },
  {
    prop: 'create_time',
    width: '180px',
    labelKey: 'createdTime',
    label: '创建时间'
  },
  {
    prop: 'update_time',
    width: '180px',
    labelKey: 'updateDate',
    label: '更新时间'
  }
]

/**
 * 热点洞察
 */
export const dashHotHeader = [
  {
    prop: 'scene_name',
    labelKey: 'hotspot',
    label: '热点名称',
    slot: 'hotspot'
  },
  {
    prop: 'sales',
    labelKey: 'sales',
    label: '销量'
  },
  {
    prop: 'sovi',
    labelKey: 'sovi',
    label: 'SOVI',
    slot: 'sovi'
  },
  {
    prop: 'ave_facing',
    labelKey: 'aveFacing',
    label: '平均排面',
    slot: 'ave_facing'
  }
]
/**
 * 热点洞察-统计
 */
export const dashStatisticsHeader = [
  {
    prop: 'name',
    labelKey: 'hotZone',
    label: '热区分类'
  },
  {
    prop: 'num',
    labelKey: 'numberCount',
    label: '热区个数'
  },
  {
    prop: 'sales',
    labelKey: 'skuTotal',
    label: '总销量'
  }
]

