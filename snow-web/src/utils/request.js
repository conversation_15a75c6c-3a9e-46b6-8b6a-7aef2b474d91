import Vue from 'vue'
import {message} from '@/utils/resetMessage'
import {setEncrypt, decrypt, needEncryptAndDecryptField} from '@/utils/rsa'
import ElementUI from 'element-ui'
import {MessageBox} from 'element-ui'
import VueI18n from 'vue-i18n'

Vue.use(ElementUI)
Vue.prototype.$message = message
import axios from 'axios'
import store from '@/store'
import {getToken, setToken} from '@/utils/auth'
import {getLanguage} from '@/lang'
import {isObj} from '@/utils/index'

// 2.定义中英文语言包
const messages = {
  'en': {
    message: {
      loginFail: 'Login failed or your account has been logged in at another location, Please log in again',
      loginAgain: 'Login Again',
      tips: 'Tips'
    }
  },
  'zh-Hans': {
    message: {
      loginFail: '登录失效或您的账号已在其它地点登录，请重新登录',
      loginAgain: '重新登录',
      tips: '提示'
    }
  },
  'zh-Hant': {
    message: {
      loginFail: '登入失效或您的帳號已在其它地點登入，請重新登入',
      loginAgain: '重新登入',
      tips: '提示'
    }
  }
}


// 3.实例化一个i18n对象
const i18n = new VueI18n({
  locale: getLanguage(), // 语言标识
  messages: messages // 上面自己定义的语言包
})
// create an axios instance
const service = axios.create({
  baseURL: window.HOST_URL+'/snow-api' || process.env.VUE_APP_BASE_API,
  // baseURL: '/api',
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 60 * 1000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  config => {
    // do something before request is sent
    config.headers['language'] = getLanguage()
    if (store.getters.token) {
      // let each request carry token
      // ['X-Token'] is a custom headers key
      // please modify it according to the actual situation
      config.headers['Authorization'] = `Bearer ${getToken()}`
    }
    if (config.method === 'post') {
      if (config.headers['Content-Type'] != 'multipart/form-data') {
        if (config.data) {
          const postData = JSON.parse(JSON.stringify(config.data))
          Object.keys(postData).map((key) => {
            if (needEncryptAndDecryptField.includes(key)) {
              if (postData[key] && typeof (postData[key]) === 'string') {
                postData[key] = setEncrypt(postData[key])
              }
            }
          })
          config.data = postData
        }
      }
    } else if (config.method === 'get') {
      if (config.params) {
        const getParams = JSON.parse(JSON.stringify(config.params))
        Object.keys(getParams).map((key) => {
          if (needEncryptAndDecryptField.includes(key)) {
            if (getParams[key] && typeof (getParams[key]) === 'string') {
              getParams[key] = setEncrypt(getParams[key])
            }
          }
        })
        config.params = getParams
      }
    }
    return config
  },
  error => {
    console.error(error)
    // do something with request error
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    const res = response.data
    if (response.headers['new-token']) {
      setToken(response.headers['new-token'])
      store.dispatch('user/setUserToken', response.headers['new-token']).then(() => {
      })
    }
    if (res.code !== 0) {
      if (res.code == 401) {
        const doms = document.getElementsByClassName('el-message-box__wrapper')[0]
        if (doms == undefined) {
          MessageBox.confirm(i18n.tc('message.loginFail'), i18n.tc('message.tips'), {
            confirmButtonText: i18n.tc('message.loginAgain'),
            showClose: false,
            showCancelButton: false,
            beforeClose: (action, instance, done) => {
              if (action == 'confirm') {
                done()
              }
            },
            type: 'warning'
          }).then(() => {
            store.dispatch('user/resetToken').then(() => {
              location.reload()
            })
          }).finally(() => {
            const messageBoxDom = document.getElementsByClassName('el-message-box__wrapper')[0]
            if (messageBoxDom) {
              messageBoxDom.remove()
            }
          })
        }
      } else {
        Vue.prototype.$message({
          message: res.message || 'Error',
          type: 'error',
          duration: 3 * 1000
        })
        if (res.data && res.data.sub_code) {
          return Promise.reject(res.data)
        } else {
          return Promise.reject(new Error(res.message || 'Error'))
        }
      }
    } else {
      // 正常
      if (isObj(res.data)) {
        Object.keys(res.data).map((key) => {
          if (isObj(res.data[key])) {
            Object.keys(res.data[key]).map((subKey) => {
              if (needEncryptAndDecryptField.includes(subKey)) {
                if (res.data[key][subKey]) {
                  res.data[key][subKey] = decrypt(res.data[key][subKey])
                }
              }
            })
          } else if (Array.isArray(res.data[key])) {
            res.data[key].map((item) => {
              if (isObj(item)) {
                Object.keys(item).map((itemKey) => {
                  if (needEncryptAndDecryptField.includes(itemKey)) {
                    if (item[itemKey]) {
                      item[itemKey] = decrypt(item[itemKey])
                    }
                  }
                })
              }
            })
          } else {
            if (needEncryptAndDecryptField.includes(key)) {
              if (res.data[key]) {
                res.data[key] = decrypt(res.data[key])
              }
            }
          }
        })
      } else if (Array.isArray(res.data)) {
        res.data.map((item) => {
          if (isObj(item)) {
            Object.keys(item).map((itemKey) => {
              if (needEncryptAndDecryptField.includes(itemKey)) {
                if (item[itemKey]) {
                  item[itemKey] = decrypt(item[itemKey])
                }
              }
            })
          }
        })
      }
      return res.data || null
    }
  },
  error => {
    if (error.response.headers['new-token']) {
      setToken(error.response.headers['new-token'])
      store.dispatch('user/setUserToken', error.response.headers['new-token']).then(() => {
      })
    }
    if (error.response.status == 401) {
      const doms = document.getElementsByClassName('el-message-box__wrapper')[0]
      if (doms == undefined) {
        MessageBox.confirm(i18n.tc('message.loginFail'), i18n.tc('message.tips'), {
          confirmButtonText: i18n.tc('message.loginAgain'),
          showClose: false,
          showCancelButton: false,
          beforeClose: (action, instance, done) => {
            if (action == 'confirm') {
              done()
            }
          },
          type: 'warning'
        }).then(() => {
          store.dispatch('user/resetToken').then(() => {
            location.reload()
          })
        }).finally(() => {
          const messageBoxDom = document.getElementsByClassName('el-message-box__wrapper')[0]
          if (messageBoxDom) {
            messageBoxDom.remove()
          }
        })
      }
    } else {
      Vue.prototype.$message({
        message: error.message,
        type: 'error',
        duration: 3 * 1000
      })
      return Promise.reject(error)
    }
  }
)

export default service
