import Vue from 'vue'
import {message} from '@/utils/resetMessage'
import ElementUI from 'element-ui'
import {MessageBox} from 'element-ui'
import VueI18n from 'vue-i18n'

Vue.use(ElementUI)
Vue.prototype.$message = message
import axios from 'axios'
import store from '@/store'

// create an axios instance
const service = axios.create({
  timeout: 60 * 1000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  config => {
    return config
  },
  error => {
    // do something with request error
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  response => {
    const res = response.data
    if (res.code !== 0) {
      if (res.code === 1100) {
        return Promise.reject(res)
      } else {
        Vue.prototype.$message({
          message: res.message || 'Error',
          type: 'error',
          duration: 3 * 1000
        })
        return Promise.reject(new Error(res.message || 'Error'))
      }
    } else {
      return res
    }
  },
  error => {
    Vue.prototype.$message({
      message: error.message,
      type: 'error',
      duration: 3 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
