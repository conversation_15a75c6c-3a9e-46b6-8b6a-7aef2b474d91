/**
 * Created by PanJiaChen on 16/11/18.
 */

import {getLanguage} from '@/lang'

/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  const valid_map = ['admin', 'editor']
  return valid_map.indexOf(str.trim()) >= 0
}

export function isObject(obj) {
  return !(!obj || Object.prototype.toString.call(obj) !== '[object Object]')
}

export function checkPhone(phone) {
  return /^1[3-9]\d{9}$/.test(phone)
}

// form表单验证手机号
export function formCheckPhone(rule, value, callback) {
  let warnTxt = '仅可输入数字'
  switch (getLanguage()) {
    case 'en':
      warnTxt = 'Only Numbers Can Be Entered'
      break
    case 'zh-Hans':
      warnTxt = '仅可输入数字'
      break
    case 'zh-Hant':
      warnTxt = '僅可輸入數位'
      break
  }
  if (!/^\d*$/.test(value)) {
    return callback(new Error(warnTxt))
  }
  return callback()
}

// 保留两位小数
export function isFlow(num) {
  if (!isNaN(num)) {
    return ((num + '').indexOf('.') !== -1) ? num.toFixed(2) : num
  }
}

/**
 * 获取文件大小
 * */
export function formatSize(size) {
  let data = ''
  if (size < 0.1 * 1024) { // 如果小于0.1KB转化成B
    data = size.toFixed(2) + 'B'
  } else if (size < 0.1 * 1024 * 1024) { // 如果小于0.1MB转化成KB
    data = (size / 1024).toFixed(2) + 'KB'
  } else if (size < 0.1 * 1024 * 1024 * 1024) { // 如果小于0.1GB转化成MB
    data = (size / (1024 * 1024)).toFixed(2) + 'MB'
  } else { // 其他转化成GB
    data = (size / (1024 * 1024 * 1024)).toFixed(2) + 'GB'
  }
  const sizestr = data + ''
  const len = sizestr.indexOf('\.')
  const dec = sizestr.substr(len + 1, 2)
  if (dec == '00') { // 当小数点后为00时 去掉小数部分
    return sizestr.substring(0, len) + sizestr.substr(len + 3, 2)
  }
  return sizestr
}

/**
 * 生成文件前缀
 * fileRoutePrefix 存储路径
 * */
export function getPrefix(keyPrefix, suffix, fileRoutePrefix) {
  const random = Math.random().toString(36).slice(-6)
  const route = fileRoutePrefix ? `${fileRoutePrefix}/` : ''
  return route + (keyPrefix ? keyPrefix : 'meta-store_') + new Date().valueOf().toString() + '_' + random + (suffix || '.jpg')
}

export function compareDates(start, end) {
  const date1 = new Date(start);
  const date2 = new Date(end);

  if (isNaN(date1.getTime()) || isNaN(date2.getTime())) {
    throw new Error('Invalid date string');
  }
  return date1.getTime() > date2.getTime() ? true : false
}


// 获取时间戳
export const getTimestamp = () => {
  const now = new Date()
  const timestamp = now.getTime()
  return timestamp
}
/**
 * 转化php时间戳
 * @param {String} format '/'
 * @param {String} timeformat 'ymd'
 * @param {Bool} isDay 是日期还是时间戳
 */
export const getTime = (timestamp, timeformat = 'ymdhM', format = '-', isDay) => {
  let date = new Date(timestamp * 1000)
  if(isDay && timestamp){
    date = new Date(Date.parse(timestamp.replace(/-/g,'/')))
  }
  if(!timestamp){
    date = new Date()
  }
  const y = date.getFullYear()
  let m = date.getMonth() + 1
  m = m < 10 ? '0' + m : m
  let d = date.getDate();
  d = d < 10 ? ('0' + d) : d
  let h = date.getHours();
  h = h < 10 ? ('0' + h) : h
  let M = date.getMinutes();
  M = M < 10 ? ('0' + M) : M
  let str = ''
  switch (timeformat){
    case 'md':
      str = `${m}${format}${d}`
      break;
    case 'ymd':
      str = `${y}${format}${m}${format}${d}`
      break;
    default:
      str = `${y}${format}${m}${format}${d} ${h}:${M}`
  }
  return str
}

export const transformData = (arr) => {
  return arr.map(item => ({
    label: item.name,
    value: item.code
  }))
}

// 编辑器内容验证
export function checkEditor(rule, value, callback) {
  if (!value || value === '<p><br></p>') {
    return callback(new Error('请输入内容'))
  }
  return callback()
}
