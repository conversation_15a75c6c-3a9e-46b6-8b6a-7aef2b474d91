exports.install = function (Vue, options) {
  // 全局验证权限方法
  Vue.prototype.$havePermission = (authCode, allCodeList = []) => {
    let permission = false
    if (!authCode || !Array.isArray(allCodeList)) {
      permission = false
    }
    let authList = []
    if (Array.isArray(authCode)) {
      authList = authCode
    } else {
      authList = [authCode]
    }
    authList.map((code) => {
      allCodeList.map((fullCode) => {
        if (fullCode.function_id && fullCode.function_id.includes(code)) {
          permission = true
        }
      })
    })
    return permission
  }
  // 根据权限码控制操作栏按钮是否显示
  Vue.prototype.$controlTableOperates = (tableOperates = [], allCodeList = []) => {
    const permissionTableOperates = []
    if (!Array.isArray(allCodeList)) {
      allCodeList = []
    }
    const allSuffixList = []
    allCodeList.map((code, index) => {
      if (code.function_id && code.function_id.includes('-')) {
        allSuffixList.push(code.function_id.split('-')[1])
      }
    })
    if (Array.isArray(tableOperates) && tableOperates.length) {
      tableOperates.map((controlBtn, index) => {
        switch (controlBtn.labelKey) {
          case 'add':
            if (allSuffixList.includes('0101')) {
              permissionTableOperates.push(controlBtn)
            }
            break
          case 'toView':
            if (allSuffixList.includes('0201')) {
              permissionTableOperates.push(controlBtn)
            }
            break
          case 'edit':
            if (allSuffixList.includes('0301')) {
              permissionTableOperates.push(controlBtn)
            }
            break
          case 'enable':
            if (allSuffixList.includes('0401')) {
              permissionTableOperates.push(controlBtn)
            }
            break
          case 'copy':
            if (allSuffixList.includes('0901')) {
              permissionTableOperates.push(controlBtn)
            }
            break
          case 'delete':
            if (allSuffixList.includes('0501')) {
              permissionTableOperates.push(controlBtn)
            }
            break
          // 这里是否调整为新改的？？
          case 'unlock':
            if (allSuffixList.includes('1801')) {
              permissionTableOperates.push(controlBtn)
            }
            break
        }
      })
    }
    return permissionTableOperates
  }
}
