import Storage from 'storejs'

const TokenKey = 'rea_token'
const UserInfo = 'rea_user_info'
const UserMenu = 'rea_user_menu'

export function getToken() {
  return Storage.get(TokenKey)
}

export function setToken(token) {
  return Storage.set(Token<PERSON>ey, token)
}

export function removeToken() {
  return Storage.remove(TokenKey)
}

export function getUserInfo() {
  return Storage.get(UserInfo) || {}
}

export function setUserInfo(info) {
  return Storage.set(UserInfo, info)
}

export function removeUserInfo() {
  return Storage.remove(UserInfo)
}

export function getUserMenu() {
  return Storage.get(UserMenu) || []
}

export function setUserMenu(menu) {
  return Storage.set(UserMenu, menu)
}

export function removeUserMenu() {
  return Storage.remove(UserMenu)
}
