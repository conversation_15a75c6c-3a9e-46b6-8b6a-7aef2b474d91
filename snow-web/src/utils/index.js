/**
 * Created by PanJia<PERSON>hen on 16/11/18.
 */
import { getToken } from '@/utils/auth'
import { getLanguage } from '@/lang'
import { deepJSON } from '@/utils/tools' // get token from cookie
/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string')) {
      if ((/^[0-9]+$/.test(time))) {
        // support "1548221490638"
        time = parseInt(time)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), '/')
      }
    }

    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() +
      1 +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    )
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searchArr = search.split('&')
  searchArr.forEach(v => {
    const index = v.indexOf('=')
    if (index !== -1) {
      const name = v.substring(0, index)
      const val = v.substring(index + 1, v.length)
      obj[name] = val
    }
  })
  return obj
}

// 通用下载
export function downloadTemplate(url) {
  // const token = getToken()
  // const language = getLanguage()
  // let and = '?'
  // if (url.includes('?')) {
  //   and = '&'
  // }
  // and += 'token=' + token + '&language=' + language
  //   // 创建一个隐藏的a标签
  //   const a = document.createElement('a');
  //   a.style.display = 'none';
  //   a.href = (window.HOST_URL || process.env.VUE_APP_BASE_API) + url + and;
  //
  //   // 设置下载文件的名称
  //   // a.download = 'tem.xlsx';
  //
  //   // 将a标签添加到DOM并触发点击事件
  //   document.body.appendChild(a);
  //   a.click();
  //
  //   // 移除a标签并释放URL对象
  //   document.body.removeChild(a);
  //   window.URL.revokeObjectURL(url);
  const token = getToken()
  const language = getLanguage()
  let and = '?'
  if (url.includes('?')) {
    and = '&'
  }
  and += 'token=' + token + '&language=' + language
  let baseUrl = (window.HOST_URL || process.env.VUE_APP_BASE_API) + url + and
  if (url.includes('http')) {
    baseUrl = url + and
  }
  location.href = baseUrl
}

// 判断是否是object
export function isObj(object) {
  return object && typeof (object) === 'object' && Object.prototype.toString.call(object).toLowerCase() == '[object object]'
}

/**
 * 转换接口返回菜单
 * */
export function convertRoute(arr) {
  return deepJSON(arr).map(v => {
    const { module_url, module_code, module_name, parent_code } = v
    v.url = module_url
    v.module_id = module_code
    v.name = module_name
    v.pid = parent_code
    delete v.module_url
    delete v.module_code
    delete v.module_name
    delete v.parent_code
    v.function_list = v.function_list.map(val => {
      val.function_id = val.function_code
      val.name = val.function_name
      val.module_id = val.module_code
      delete val.function_code
      delete val.function_name
      delete val.module_code
      return val
    })
    if (v.items && v.items.length) {
      v.items = convertRoute(v.items)
    }
    return v
  })
}
