/**
 * 列表接口处理
 * @params {Object} queryParam 在外部data中定义 列表入参
 * @params {Object} apiUrl 在外部data中定义 接口地址
 * @params {Function} searchQuery 在外部data中定义 点击查询
 *
 */
import * as api from '@/api/list'
import {exportProgress, exportStart, issueDownload, deviceDownload} from '@/api/download-api'
import * as downloadApi from '@/api/download-api'
import {downloadTemplate} from '@/utils'
import {tenantAll} from "@/api/common";
import { clear } from 'core-js/internals/task'

export default {
    data() {
        return {
            fieldList: [],
            downloadFilterPop: false,
            srcList: [],
            task_id: '',
            progress: 0,
            downloadVisible: false,
            downloading: false,
            /* 数据源 */
            dataSource: [],
            brothersDataSource: [],
            /* 分页参数 */
            pagination: {
                count: 0,
                page_size: 10,
                page_num: 1
            },
            brothersPagination: {
                count: 0,
                page_size: 10,
                page_num: 1
            },
            timer: null,
            /* 加载状态 */
            loading: false,
            brothersLoading: false
        }
    },
    created() {
        // this.tableOperates = this.$controlTableOperates(this.tableOperates, this.$route.meta.functionList)
        if (this.apiUrl.brothersList) {
            this.getBrothersList()
        }
        this.getList()
    },
    methods: {
        filterCancel() {
            this.downloadFilterPop = false
        },
        filterConfirm(fieldArr1) {
            if (this.downloadType === 'visited') {
                const engine_result_ids = fieldArr1.checkItem ? [fieldArr1.checkItem] : []
                const fieldArr = Array.isArray(fieldArr1.field_list) ? fieldArr1.field_list : []
                const hasCheckProject = !!engine_result_ids.length
                if (hasCheckProject && !fieldArr.includes('engine_results')) {
                    fieldArr.unshift('engine_results')
                }
                if ((fieldArr.includes('origin_images') || fieldArr.includes('sku_facing') || fieldArr.includes('engine_results')) && (!fieldArr.includes('survey_id'))) {
                    fieldArr.unshift('survey_id')
                }
                const selectAllIndex = fieldArr.findIndex((item) => item === 'select_all')
                if (selectAllIndex >= 0) {
                    fieldArr.splice(selectAllIndex, 1)
                }
                if (engine_result_ids.length) {
                    this.exportFn(this.downloadType, fieldArr, engine_result_ids)
                } else {
                    this.exportFn(this.downloadType, fieldArr)
                }
            } else {
                this.exportFn(this.downloadType, fieldArr1)
            }
        },
        filterClose() {
            this.downloadFilterPop = false
        },
        openFilterPop() {
            if (this.downloading) {
                this.downloadVisible = true
                return
            }
            this.downloadFilterPop = true
        },
        resetQueryParam() {
            this.queryParam = JSON.parse(JSON.stringify(this.initQueryParam))
            if (this.treeParams && this.treeParams.data) {
                this.treeParams.data = []
                if (this.$refs.treeSelect) {
                    this.$refs.treeSelect.treeDataUpdateFun([])
                }
            }
        },
        processCancel() {
            this.downloadVisible = false
        },
        processConfirm() {
            this.downloadVisible = false
        },
        processClose() {
            this.downloadVisible = false
        },
        exportFn(action, fieldList, engine_result_ids) {
            const formData = Object.assign({where: this.queryParam}, {
                action_type: action,
                field_list: fieldList.length ? fieldList.join(',') : '',
                engine_result_ids
            })
            if (this.downloading) {
                this.downloadVisible = true
                return
            }
            this.downloading = true
            this.downloadVisible = true
            downloadApi[this.apiUrl.export](formData).then(res => {
                this.downloadFilterPop = false
                this.task_id = res.export_task_id
                this.progress = 0
                setTimeout(() => {
                    this.getProgress()
                }, 1000)
            }).catch(() => {
                this.downloading = false
                this.downloadVisible = false
            })
        },
        getProgress() {
            if (!this.downloading) {
                return
            }
            exportProgress({export_task_id: this.task_id}).then(res => {
                const {process, file_url, error_msg} = res
                this.progress = process
                if(error_msg){
                    clearTimeout(this.timer)
                    this.$confirm(error_msg, '下载失败', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.downloading = false
                        this.downloadVisible = false
                        this.progress = 0
                    })
                }
                if (this.progress == 100) {
                    file_url && downloadTemplate(file_url)
                    setTimeout(() => {
                        clearTimeout(this.timer)
                        this.downloading = false
                        this.downloadVisible = false
                        this.progress = 0
                    }, 200)
                } else {
                    this.timer = setTimeout(() => {
                        this.getProgress()
                    }, 2000)
                }
            }).catch(() => {
                this.downloading = false
                this.downloadVisible = false
            })
        },
        /**
         * 返回上一页
         * */
        goBack() {
            this.$router.go(-1)
        },
        /**
         * 跳转
         * */
        openItem(path, query) {
            this.$router.push({
                path,
                query
            })
        },
        /**
         * 查询
         * */
        searchQuery() {
            this.pagination.page_num = 1
            if (this.apiUrl.brothersList) {
                this.brothersPagination.page_num = 1
                this.getBrothersList()
            }
            this.getList()
        },
        /**
         * 两个列表共存另外一个列表数据处理
         * ps：筛选条件和另外列表数据相同
         * */
        getBrothersList() {
            this.brothersLoading = true
            api[this.apiUrl.brothersList](Object.assign(this.queryParam, this.brothersPagination)).then(res => {
                const {count, list} = res
                this.brothersPagination.count = parseFloat(count)
                this.brothersDataSource = list
            }).finally(() => {
                this.brothersLoading = false
            })
        },
        /**
         * 存储筛选条件和设置筛选条件
         * type 100=获取改变query查询条件 200=设置本地存储
         * 如果输入框输入不动，则是queryParam里没有设置好默认参数
         * */
        setLocalQuery(type, params) {
            if (!this.openLocalSearch) {
                return
            }
            const routeHash = window.location.hash
            let queryParams = localStorage.queryParams
            if (type == 100) {
                if (queryParams) {
                    let parseQueryParams = JSON.parse(queryParams)
                    const nowParseQueryParams = parseQueryParams[routeHash]
                    if (nowParseQueryParams) {
                        this.queryParam = Object.assign(this.queryParam, nowParseQueryParams)
                        Object.keys(this.pagination).filter(v => {
                            this.pagination[v] = nowParseQueryParams[v]
                        })
                        this.$forceUpdate()
                    }
                }
            } else {
                if (queryParams) {
                    let parseQueryParams = JSON.parse(queryParams) || {}
                    parseQueryParams[routeHash] = params
                    localStorage.queryParams = JSON.stringify(parseQueryParams)
                } else {
                    let queryParamsObj = {}
                    queryParamsObj[routeHash] = params
                    localStorage.queryParams = JSON.stringify(queryParamsObj)
                }
            }
        },
        /**
         * 获取列表
         * */
        getList() {
            this.loading = true
            let params = Object.assign(this.queryParam, this.pagination)
            delete params.count
            api[this.apiUrl.list](params).then(res => {
                // 存储筛选条件
                this.setLocalQuery(200, params)
                const srcList = []
                const {count, list, phone, name} = res
                this.pagination.count = parseFloat(count)
                list.map((v) => {
                    v.canPlay = true
                    v.isPlay = false
                    v.editStatus = false
                    srcList.push(v.store_image || '')
                })

                this.dataSource = list
                this.srcList = srcList
                if (!this.initListCount) {
                    this.initListCount = count
                }
                if (phone) {
                    this.phone = phone
                }
                if (name) {
                    this.name = name
                }
            }).finally(() => {
                this.loading = false
            })
        },
        /**
         * 分页回调
         * */
        pageHandler(data) {
            const {param, component} = data
            switch (component) {
                case 'pageChange':
                    this.pagination = Object.assign(this.pagination, param)
                    this.getList()
                    break
            }
        },
        pageBrothersHandler(data) {
            const {param, component} = data
            switch (component) {
                case 'pageChange':
                    this.brothersPagination = Object.assign(this.brothersPagination, param)
                    this.getBrothersList()
                    break
            }
        },
        /**
         * 获取品牌列表
         * */
        getTenantAll() {
            tenantAll().then((res) => {
                if (Array.isArray(res)) {
                    res.map((v) => {
                        v.value = v.id
                        v.label = v.tenant_name
                    })
                    this.brandList = res
                    const itemIndex = this.formData.findIndex(v => v.field == 'tenant_id')
                    if(itemIndex != -1){
                        this.formData[itemIndex].options = this.brandList
                    }
                }
            })
        },
    }

}
