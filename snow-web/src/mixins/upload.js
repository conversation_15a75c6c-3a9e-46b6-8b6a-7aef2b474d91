/**
 * 图片上传
 */
import Md5 from 'js-md5'
import COS from 'cos-js-sdk-v5'
import {configCos} from "@/api/test";
import {dataURLtoBlob} from '@/libs/format'
export default {
    data() {
        return {
            cosConfig: '',
            cos: '',
        }
    },
    created() {
        this.cosConfigFun()
    },
    methods: {
        /**
         * 上传base64图片
         * */
        putObjectImage(base64, call){
            console.log(this.cosConfig, 222)
            const body = dataURLtoBlob(base64)
            const fileSuffix = 'png'
            let key = 'success_image_' + Md5((new Date()).valueOf().toString()) + '.' + fileSuffix
            this.cos.putObject({
                Bucket: this.cosConfig.bucket,
                Region: this.cosConfig.region,
                Key: key,
                Body: body,
            }, (err, data) => {
                if(data && data.statusCode == 200 && data.Location){
                    call && call('https://'+data.Location)
                }
            })
        },
        /**
         * 上传正常input file图片
         * */
        postImage (file) {
            let _this = this
            let new_img_list = []
            let fileSuffix = file.name.split('.')[1]
            let key = 'RULE_IMAGE_' + Md5((new Date()).valueOf().toString()) + '.' + fileSuffix
            let obj = {'key': key, 'url': this.getObjectURL(file), 'progress': 0}
            this.imgList.push(obj)
            this.cos.sliceUploadFile({
                Bucket: this.cosConfig.bucket,
                Region: this.cosConfig.region,
                Key: key,
                Body: file,
                onHashProgress: function (progressData) {
                },
                onProgress: function (progressData) {
                    // console.log('上传中')
                    _this.imgList.map((v, k)=>{
                        if (v.key == key) {
                            v.progress = parseFloat((progressData.percent * 100).toFixed(0))
                        }
                        return v
                    })
                }
            }, function (err, data) {
                if (err) {
                    _this.$message.error(err)
                    return
                }
                _this.imgList.map((v, k)=>{
                    if (v.key == key) {
                        v.url = 'http://' + data.Location
                    }
                    if (!v.id) {
                        new_img_list.push(v)
                    }
                    return v
                })
                // this.new_img_list = new_img_list
                _this.$emit('inputChangeHandle', {
                    param: {
                        image_type: _this.image_type,
                        imgList: new_img_list,
                    },
                    component: 'image'}
                )
                _this.$refs['file'].value = ''
            })
        },
        /**
         * 获取cos配置
         * */
        cosConfigFun () {
            configCos({type: 1}).then(resp => {
                if(resp.code == 1 && resp.data){
                    let res = resp.data
                    let credentials = res.config && res.config.credentials || {}
                    this.cosConfig = res.config || {}
                    this.cos = new COS({
                        getAuthorization: (options, callback) => {
                            let config = {
                                TmpSecretId: credentials.tmpSecretId,
                                TmpSecretKey: credentials.tmpSecretKey,
                                XCosSecurityToken: credentials.sessionToken,
                                ExpiredTime: this.cosConfig.expiredTime
                            }
                            callback(config)
                        }
                    })
                }
            })
        },
    }
}
