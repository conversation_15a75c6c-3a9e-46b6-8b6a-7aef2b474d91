/**
 * 一些公共方法
 *
 */
export default {
  computed: {
    visitedViews() {
      return this.$store.state.tagsView.visitedViews
    }
  },
  data() {
    return {}
  },
  created() {
  },
  methods: {
    /**
     * 返回并且关闭当前tab
     * */
    backCloseViewFun() {
      this.closeView()
      this.$router.go(-1)
    },
    /**
     * 跳转
     * */
    openItem(path, query) {
      this.$router.push({
        path,
        query
      })
    },
    /**
     * 关闭tab页
     * */
    closeView() {
      const {path} = this.$route
      this.visitedViews.map((v) => {
        if (v.path === path) {
          this.$store.dispatch('tagsView/delView', v)
        }
      })
    },
  }

}
