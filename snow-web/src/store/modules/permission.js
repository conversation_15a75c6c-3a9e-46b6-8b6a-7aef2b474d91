import Vue from 'vue'
import { asyncRoutes, constantRoutes } from '@/router'
import { getToken, setUserMenu } from '@/utils/auth'
import defRoute from '@/utils/menu'
import { convertRoute } from '@/utils/index'

import Layout from '@/layout'
import { authMenu } from '@/api/user'

export const filterAsyncRouter = (routers) => { // 遍历后台传来的路由字符串，转换为组件对象
  routers = defRoute.concat(routers)
  const accessedRouters = []
  routers.map((route) => {
    route.component = Layout
    route.meta = {
      title: route.name,
      icon: route.icon,
      hidden: !!route.hidden,
      activeMenu: route.activeMenu || '',
      titleKey: route.index_name,
      functionList: route.function_list || []
    }
    route.path = route.is_iframe == 1 ? '/ifr-' + route.module_id : (route.url || route.module_id)
    if (route.items && route.items.length && !route.url) {
      route.children = []
      route.items.map((subRoute) => {
        subRoute.path = subRoute.is_iframe == 1 ? '/ifr-' + subRoute.module_id : (subRoute.url || subRoute.module_id)
        subRoute.component = loadView(subRoute.path)
        subRoute.meta = {
          titleKey: subRoute.index_name,
          title: subRoute.name,
          icon: subRoute.icon || '-',
          isIframe: subRoute.is_iframe,
          url: subRoute.url + (subRoute.is_iframe ? ((subRoute.url.indexOf('?') > -1 ? '&' : '?') + `token=${getToken()}`) : ''),
          hidden: !!subRoute.hidden,
          activeMenu: subRoute.activeMenu || '',
          functionList: subRoute.function_list || []
        }
        subRoute.name = subRoute.path && subRoute.path.slice(1).split('/').join('-')
        route.children.push(subRoute)
      })
      route.alwaysShow = true
    } else {
      route.children = [{
        name: route.path.slice(1).split('/').join('-'),
        path: route.path,
        component: loadView(route.path),
        meta: {
          titleKey: route.index_name,
          title: route.name,
          icon: route.icon,
          isIframe: route.is_iframe,
          url: route.url + (route.is_iframe ? ((route.url.indexOf('?') > -1 ? '&' : '?') + `token=${getToken()}`) : ''),
          hidden: !!route.hidden,
          activeMenu: route.activeMenu || '',
          functionList: route.function_list || []
        }
      }]
    }
    // 售点 '0101'新增 '0201'查看 '0301'编辑 TODO 场景列表权限、售点配置权限
    // && Vue.prototype.$havePermission(['0101', '0201', '0301'], route.meta.functionList)
    if (route.index_name === 'storeInventory') {
      // 场景数据权限 有场景权限则添加
      let sceneFun = []
      if (route.items && route.items.length) {
        route.items.forEach(itemss => {
          if (itemss.index_name == 'storeSceneList') {
            sceneFun = itemss.function_list
          }
        })
      }

      let storeArr = [
        {
          component: loadView('/store/config-detail'),
          name: 'store-config-detail',
          path: '/store/config-detail',
          meta: {
            title: '售点配置',
            titleKey: 'storeConfig',
            icon: '-',
            activeMenu: '/store/index',
            functionList: route.function_list || [],
            noTagView: false
          },
          hidden: true
        },
        {
          component: loadView('/store/scene'),
          name: 'store-scene',
          path: '/store/scene',
          meta: {
            title: '场景数据',
            titleKey: 'sceneData',
            icon: '-',
            activeMenu: '/store/index',
            functionList: sceneFun || [],
            noTagView: false
          },
          hidden: true
        }, {
          component: loadView('/store/scene-annotate'),
          name: 'store-scene-annotate',
          path: '/store/scene-annotate',
          meta: {
            title: '场景配置',
            titleKey: 'sceneConfig',
            icon: '-',
            activeMenu: '/store/index',
            functionList: sceneFun || [],
            noTagView: false
          },
          hidden: true
        }, {
          component: loadView('/store/store-version'),
          name: 'store-version',
          path: '/store/version',
          meta: {
            title: '版本配置',
            titleKey: 'versionConfig',
            icon: '-',
            activeMenu: '/store/index',
            functionList: route.function_list || [],
            noTagView: false
          },
          hidden: true
        }
      ]
      route.children = route.children.concat(storeArr)
      // route.children.push()
    }
    // 品牌方
    if (route.index_name === 'tenantManagement' && Vue.prototype.$havePermission(['0101', '0201', '0301'], route.meta.functionList)) {
      route.children.push({
        component: loadView('/brand/add'),
        name: 'brand-add',
        path: '/brand/add',
        meta: {
          title: '品牌方新增',
          titleKey: 'tenantAdd',
          icon: '-',
          activeMenu: '/brand/manage',
          noTagView: false
        },
        hidden: true
      })
    }
    if (route.index_name === 'accountManagement') {
      // route.children.push({
      //   component: loadView('/account/frontend'),
      //   name: 'account-frontend',
      //   path: '/account/frontend',
      //   meta: {
      //     title: '设备安装APP帐号管理',
      //     titleKey: 'installAppAccount',
      //     icon: '-',
      //     activeMenu: '/account/frontend',
      //     noTagView: false
      //   },
      //   hidden: false
      // })
      // route.children.push({
      //   component: loadView('/account/metastore'),
      //   name: 'account-metastore',
      //   path: '/account/metastore',
      //   meta: {
      //     title: 'MetaStore',
      //     titleKey: 'metaStoreAccount',
      //     icon: '-',
      //     activeMenu: '/account/metastore',
      //     noTagView: false
      //   },
      //   hidden: false
      // })
    }
    // 设备安装
    if (route.index_name === 'installIndexTask') {
      let taskFunction = route.function_list || []
      let replaceTaskFunction = route.function_list || []
      if (route.items) {
        route.items.filter(v => {
          if (v.index_name == 'installIndex') {
            taskFunction = v.function_list
          }
          if (v.index_name == 'replaceIndex') {
            replaceTaskFunction = v.function_list
          }
        })
      }
      route.children.push({
        component: loadView('/install/check-detail'),
        name: 'check-detail',
        path: '/install/check-detail',
        meta: {
          title: '任务详情',
          titleKey: 'installCheckDetail',
          icon: '-',
          activeMenu: '/install/index',
          noTagView: false,
          functionList: taskFunction || []
        },
        hidden: true
      }, {
        component: loadView('/install/check-detail'),
        name: 'check-replace-detail',
        path: '/install/check-replace-detail',
        meta: {
          title: '任务详情',
          titleKey: 'installCheckDetail',
          icon: '-',
          activeMenu: '/install/replace-index',
          noTagView: false,
          functionList: replaceTaskFunction || []
        },
        hidden: true
      }, {
        component: loadView('/install/check-scene'),
        name: 'check-replace-scene',
        path: '/install/check-replace-scene',
        meta: {
          title: '验收详情',
          titleKey: 'installCheck',
          icon: '-',
          activeMenu: '/install/replace-index',
          noTagView: false,
          functionList: replaceTaskFunction || []
        },
        hidden: true
      }, {
        component: loadView('/install/check-scene'),
        name: 'check-scene',
        path: '/install/check-scene',
        meta: {
          title: '验收详情',
          titleKey: 'installCheck',
          icon: '-',
          activeMenu: '/install/index',
          noTagView: false,
          functionList: taskFunction || []
        },
        hidden: true
      })
    }
    // 设备巡检
    if (route.index_name === 'qcIndexTask') {
      route.children.push({
        component: loadView('/check/detail'),
        name: 'check-detail',
        path: '/check/detail',
        meta: {
          title: 'QC详情',
          titleKey: 'qcIndexTaskDetail',
          icon: '-',
          activeMenu: '/check/index',
          noTagView: false,
          functionList: route.function_list || []
        },
        hidden: true
      })
    }
    accessedRouters.push(route)
  })
  return accessedRouters
}

export const loadView = (path) => { // 路由懒加载
  if (!path) {
    return
  }
  if (path.includes('ifr')) {
    return (resolve) => require(['@/views/iframe/index'], resolve)
  }
  return (resolve) => require([`@/views${path}`], resolve)
}

const state = {
  routes: [],
  addRoutes: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
    state.routes = state.routes.concat(asyncRoutes)
  }
}

const actions = {
  generateRoutes({ commit }) {
    return new Promise((resolve, reject) => {
        let response = [{
        "id": 2,
        "module_code": "02",
        "module_name": "任务列表",
        "module_url": "/store/index",
        "parent_code": "0",
        "sort": 70000,
        "icon": "storeWarehouse",
        "is_iframe": 0,
        "index_name": "taskList",
        "is_show": 1,
        "status": 1,
        "function_list": [
          {
            "id": 9,
            "module_code": "02",
            "module_name": "售点管理",
            "function_code": "02-0701",
            "function_name": "数据导出",
            "function_desc": "",
            "index_name": "dataExport",
            "sort": 5000,
            "is_show": 1,
            "status": 1
          },
          {
            "id": 72,
            "module_code": "02",
            "module_name": "售点管理",
            "function_code": "02-1501",
            "function_name": "场景数据",
            "function_desc": "",
            "index_name": "storeSceneList",
            "sort": 5000,
            "is_show": 1,
            "status": 1
          },
          {
            "id": 73,
            "module_code": "02",
            "module_name": "售点管理",
            "function_code": "02-1601",
            "function_name": "售点配置",
            "function_desc": "",
            "index_name": "storeConfig",
            "sort": 5000,
            "is_show": 1,
            "status": 1
          },
          {
            "id": 8,
            "module_code": "02",
            "module_name": "售点管理",
            "function_code": "02-0601",
            "function_name": "数据导入",
            "function_desc": "",
            "index_name": "dataImport",
            "sort": 4000,
            "is_show": 1,
            "status": 1
          },
          {
            "id": 7,
            "module_code": "02",
            "module_name": "售点管理",
            "function_code": "02-0501",
            "function_name": "删除",
            "function_desc": "",
            "index_name": "delete",
            "sort": 3000,
            "is_show": 1,
            "status": 1
          },
          {
            "id": 6,
            "module_code": "02",
            "module_name": "售点管理",
            "function_code": "02-0301",
            "function_name": "编辑",
            "function_desc": "",
            "index_name": "edit",
            "sort": 2000,
            "is_show": 1,
            "status": 1
          },
          {
            "id": 5,
            "module_code": "02",
            "module_name": "售点管理",
            "function_code": "02-0101",
            "function_name": "新增",
            "function_desc": "",
            "index_name": "add",
            "sort": 1000,
            "is_show": 1,
            "status": 1
          },
          {
            "id": 57,
            "module_code": "02",
            "module_name": "售点管理",
            "function_code": "02-0201",
            "function_name": "查看",
            "function_desc": "",
            "index_name": "view",
            "sort": 500,
            "is_show": 1,
            "status": 1
          }
        ],
        "items": []
      },]
      // authMenu().then(response => {
        const def = [
          {
            "function_list": [],
            "icon": "inspectionPlan",
            "id": 24,
            "index_name": "qcIndexTask",
            "is_iframe": 0,
            "is_show": 1,
            "items": [],
            "module_code": "12",
            "module_name": "QC列表",
            "module_url": "/check/index",
            "parent_code": "0",
            "sort": 100000,
            "status": 1
          }
        ]
        response = response.concat(def)
        const arr = convertRoute(response)
        // const routes = Object.values(arr).concat(def)
        const routes = Object.values(arr)
        setUserMenu(routes)
        const asyncRouter = filterAsyncRouter(routes).concat(asyncRoutes)
        commit('SET_ROUTES', asyncRouter)
        resolve(asyncRouter)
      // }).catch(error => {
      //   console.error(error)
      //   reject(error)
      // })
    })
  },
  setRoutes({ commit }, routes) {
    return new Promise((resolve, reject) => {
      try {
        const asyncRouter = filterAsyncRouter(routes).concat(asyncRoutes)
        commit('SET_ROUTES', asyncRouter)
        resolve(asyncRouter)
      } catch (error) {
        console.error(error)
        reject(error)
      }
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
