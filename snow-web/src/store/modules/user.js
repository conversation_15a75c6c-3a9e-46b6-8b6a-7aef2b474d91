import {authlogin, authUserInfo} from '@/api/user'
import {
  getToken,
  setToken,
  removeToken,
  getUserInfo,
  setUserInfo,
  removeUserInfo,
  removeUserMenu
} from '@/utils/auth'
import {resetRouter} from '@/router'

const getDefaultState = () => {
  return {
    token: getToken(),
    userInfo: getUserInfo(),
    name: ''
  }
}

const state = getDefaultState()

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState())
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_USER_INFO: (state, userInfo) => {
    state.userInfo = userInfo
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_MENU: (state, menu) => {
    state.menu = menu
  }
}

const actions = {
  // user login
  login({commit}, userInfo) {
    return new Promise((resolve, reject) => {
      authlogin(userInfo).then(response => {
        const {token} = response
        commit('SET_TOKEN', token)
        setToken(token)
        resolve(response)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // get user info
  getInfo({commit, state}) {
    return new Promise((resolve, reject) => {
      authUserInfo(state.token).then(response => {
        if (response) {
          commit('SET_USER_INFO', response)
          const {name} = response
          commit('SET_NAME', name)
          setUserInfo(response)
          resolve(response)
        } else {
          reject()
        }
      }).catch(error => {
        reject(error)
      })
    })
  },

  // user name
  setName({commit}, name) {
    commit('SET_NAME', name)
  },
  // user logout
  logout({commit}) {
    return new Promise((resolve) => {
      removeToken() // must remove  token  first
      removeUserInfo()
      resetRouter()
      removeUserMenu()
      commit('RESET_STATE')
      resolve()
    })
  },

  // remove token
  resetToken({commit}) {
    return new Promise(resolve => {
      removeToken() // must remove  token  first
      removeUserInfo()
      removeUserMenu()
      commit('RESET_STATE')
      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

