import Vue from 'vue'

const state = {
  iframeObj: {}
}

for (const key in state.iframeObj) {
  // eslint-disable-next-line
  if (state.iframeObj.hasOwnProperty(key)) {
    state.iframeObj[key].id = key
    state.iframeObj[key].enable = false
    state.iframeObj[key].show = false
  }
}

state.iframeObj['IFRAME_BOX_STATE'] = false

const getters = {
  iframeArr: state => {
    const arr = []
    for (const key in state.iframeObj) {
      // eslint-disable-next-line
      if (state.iframeObj.hasOwnProperty(key)) {
        if (state.iframeObj[key].enable) {
          arr.push(state.iframeObj[key])
        }
      }
    }
    return arr
  },
  iframeBoxState: state => {
    return state.iframeObj['IFRAME_BOX_STATE']
  }
}

const mutations = {
  ADD_NEW_IFRAME(state, iframe) {
    Vue.set(state.iframeObj, iframe.id, iframe)
  },
  ADD_IFRAME(state, id) {
    Vue.set(state.iframeObj[id], 'enable', true)
  },
  DELETE_IFRAME(state, id) {
    Vue.set(state.iframeObj[id], 'enable', false)
  },
  SHOW_IFRAME(state, id) {
    Vue.set(state.iframeObj[id], 'show', true)
  },
  HIDE_IFRAME(state, id) {
    Vue.set(state.iframeObj[id], 'show', false)
  },
  SHOW_IFRAME_BOX(state) {
    state.iframeObj['IFRAME_BOX_STATE'] = true
  },
  HIDE_IFRAME_BOX(state) {
    state.iframeObj['IFRAME_BOX_STATE'] = false
  },
  CHANGE_SRC(state, obj) {
    Vue.set(state.iframeObj[obj.id], 'src', obj.src)
  }
}

const actions = {
  createNewPage({commit}, obj) {
    commit('ADD_NEW_IFRAME', obj)
  },
  createdPage({commit}, obj) {
    if (obj.src) {
      commit('CHANGE_SRC', obj)
    }
    commit('ADD_IFRAME', obj.id)
    commit('SHOW_IFRAME', obj.id)
    commit('SHOW_IFRAME_BOX')
  },
  beforeDestroyPage({commit}, id) {
    commit('HIDE_IFRAME_BOX')
    commit('DELETE_IFRAME', id)
  },
  activatedPage({state, commit}, id) {
    if (state.iframeObj[id].show) return
    commit('SHOW_IFRAME', id)
    commit('SHOW_IFRAME_BOX')
  },
  deactivatedPage({state, commit}, id) {
    if (!state.iframeObj[id].show) return
    commit('HIDE_IFRAME_BOX')
    commit('HIDE_IFRAME', id)
  },
  changeSrc({commit}, obj) {
    commit('CHANGE_SRC', obj)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
