export default {
  planBoundingConfig: {
    isDefImage: true, // 判断是否是默认图片
    // emptyImageSuffix: 'store_map/empty.png',
    emptyImageSuffix: '%E7%A9%BA.png',
    PLANO_BOUNDING_BOX_COUNT_H: 15, // 平面图框横向数量
    PLAN_BOUNDING_BOX_SPACE_H: 89, // 平面图框横向间距，单位：px
    PLAN_BOUNDING_BOX_DEFAULT: { // 平面图每个场景第一个场景框的坐标，后续的场景框坐标根据这个坐标计算，格式：[一级场景编码 => 对应的坐标]
      // 1=冰柜 2=货架 3=地堆 4=端架
      'COOLER': {
        type: '1',
        name: '冰柜',
        index: 0,
        pos: {
          'bl': [467, 791],
          'br': [528, 791],
          'tl': [467, 610],
          'tr': [528, 610]
        }
      },
      'RACK': {
        type: '2',
        name: '货架',
        index: 0,
        pos: {
          'bl': [467, 250],
          'br': [528, 250],
          'tl': [467, 66],
          'tr': [528, 66]
        }
      },
      'GROUND': {
        type: '3',
        name: '地堆',
        index: 0,
        pos: {
          'bl': [467, 1342],
          'br': [528, 1342],
          'tl': [467, 1158],
          'tr': [528, 1158]
        }
      },
      'FRAME': {
        type: '4',
        name: '端架',
        index: 0,
        pos: {
          'bl': [467, 1886],
          'br': [528, 1886],
          'tl': [467, 1702],
          'tr': [528, 1702]
        }
      }
    }
  },
  cosConfig: {
    Bucket: 'meta-store-1305849923',
    Region: 'ap-shanghai',
    SecretId: 'AKIDVquiEUWyDBtKVQ5TaQ0JM8euudXDeohK',
    SecretKey: 'OVKtBCwVnoXzB67rMbZGgqiJdhU0FEwN'
  }
}
