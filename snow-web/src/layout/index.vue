<template>
  <div :class="classObj" class="app-wrapper">
    <div v-if="device==='mobile'&&sidebar.opened" class="drawer-bg" @click="handleClickOutside"/>
    <sidebar class="sidebar-container"/>
    <div :class="{hasTagsView:needTagsView}" class="main-container">
      <div :class="{'fixed-header':fixedHeader}">
        <navbar/>
        <tags-view v-if="needTagsView"/>    <!-- 此处增加tag-->
      </div>
      <app-main/>
    </div>
  </div>
</template>

<script>
import {Navbar, Sidebar, AppMain, TagsView} from './components'
import ResizeMixin from './mixin/ResizeHandler'

export default {
  name: 'layout',
  components: {
    Navbar,
    Sidebar,
    AppMain,
    TagsView
  },
  mixins: [ResizeMixin],
  computed: {
    language() {
      return this.$store.getters.language
    },
    sidebar() {
      return this.$store.state.app.sidebar
    },
    device() {
      return this.$store.state.app.device
    },
    fixedHeader() {
      return this.$store.state.settings.fixedHeader
    },
    needTagsView() {
      return this.$store.state.settings.tagsView
    },
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    }
  },
  watch: {
    '$i18n.locale'() {
      if (this.language === 'en') {
        document.getElementsByTagName('body')[0].style.setProperty('--sidebar-width', '278px')
        document.getElementsByTagName('body')[0].style.setProperty('--sidebar-width-transform', '-278px')
      } else {
        document.getElementsByTagName('body')[0].style.setProperty('--sidebar-width', '208px')
        document.getElementsByTagName('body')[0].style.setProperty('--sidebar-width-transform', '-208px')
      }
    }
  },
  created() {
    if (this.language === 'en') {
      document.getElementsByTagName('body')[0].style.setProperty('--sidebar-width', '278px')
      document.getElementsByTagName('body')[0].style.setProperty('--sidebar-width-transform', '-278px')
    } else {
      document.getElementsByTagName('body')[0].style.setProperty('--sidebar-width', '208px')
      document.getElementsByTagName('body')[0].style.setProperty('--sidebar-width-transform', '-208px')
    }
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', {withoutAnimation: false})
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
@import "~@/styles/variables.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 200;
  width: 100%;
  background-color: white;
  padding-left: #{$sideBarWidth};
  transition: all 0.28s;
}

.hideSidebar .fixed-header {
  width: 100%;
  padding-left: 54px;
}

.mobile .fixed-header {
  width: 100%;
  padding-left: 0;
}
</style>
