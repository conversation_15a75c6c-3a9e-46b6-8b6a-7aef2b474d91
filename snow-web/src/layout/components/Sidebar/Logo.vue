<template>
  <div class="sidebar-logo-container" :class="{'collapse':collapse}">
    <transition name="sidebarLogoFade">
      <div v-if="collapse" key="collapse" class="sidebar-logo-link" @click.stop="toHome">
        <img v-if="small_logo" :src="small_logo" class="sidebar-logo eye">
      </div>
      <div v-else key="expand" class="sidebar-logo-link" @click.stop="toHome">
        <img v-if="logo" :src="logo" class="sidebar-logo text">
      </div>
    </transition>
  </div>
</template>

<script>
import logo from '@/assets/images/banner_logo.png'
import small_logo from '@/assets/images/<EMAIL>'

export default {
  name: 'sidebar-logo',
  props: {
    collapse: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      logo,
      small_logo
    }
  },
  methods: {
    async toHome() {
      await this.$store.dispatch('tagsView/delAllViews')
      this.$router.push(`/`)
    }
  }
}
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  padding-top: 56px;
  padding-bottom: 45px;
  position: relative;
  width: 100%;
  text-align: center;
  overflow: hidden;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;
    text-align: center;
    cursor: pointer;

    & .sidebar-logo {
      width: 32px;
      height: 32px;
      vertical-align: middle;
      /*margin-right: 12px;*/
      &.eye {
        width: 54px;
        height: 28px;
        vertical-align: middle;
        // margin-right: 12px;
      }

      &.text {
        width: 146px;
        height: 36px;
        object-fit: contain;
      }
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #fff;
      font-weight: 600;
      line-height: 50px;
      font-size: 22px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}
</style>
