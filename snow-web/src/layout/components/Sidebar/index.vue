<template>
  <div :class="{'has-logo':showLogo}">
    <div class="nav_scrollbar_box">
      <logo v-if="showLogo" :collapse="isCollapse"/>
      <el-scrollbar wrap-class="scrollbar-wrapper">
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapse"
          :background-color="variables.menuBg"
          :text-color="variables.menuText"
          :unique-opened="false"
          :active-text-color="variables.menuActiveText"
          :collapse-transition="false"
          mode="vertical"
        >
          <sidebar-item v-for="(route,index) in constantRoutes" :key="index" :item="route" :base-path="route.path"/>
        </el-menu>
      </el-scrollbar>
    </div>
  </div>
  </div>
</template>

<script>
import {mapGetters} from 'vuex'
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/styles/variables.scss'
import {constantRoutes} from '@/router/index'

export default {
  components: {SidebarItem, Logo},
  // 临时使用本地菜单
  data() {
    return {
      constantRoutes
    }
  },
  mounted() {
    this.constantRoutes = this.permission_routes
  },
  computed: {
    ...mapGetters([
      'permission_routes',
      'sidebar'
    ]),
    activeMenu() {
      const route = this.$route
      const {meta, path} = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    }
  }
}
</script>
