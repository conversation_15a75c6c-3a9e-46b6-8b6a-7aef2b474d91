<template>
  <div id="tags-view-container" class="tags-view-container">
    <div class="top_tag_box">
      <el-tabs ref="tabs" v-model="activePath" closable @tab-remove="removeTab" @tab-click="tabClick">
        <el-tab-pane v-for="tag in visitedViews" :key="tag.path" :label="$t(`route.${tag.meta.titleKey}`)"
                    :name="tag.path"/>
      </el-tabs>
    </div>
    <div class="el-dropdown_box">
      <el-dropdown trigger="click" @command="handleCommand">
        <span class="el-dropdown-link">
          <i class="el-icon-arrow-down"/>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="tab in tabList" :key="tab.id" :command="tab.id">{{
              $t(`tagsView.${tab.titleKey}`)
            }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import path from 'path'
import {mapGetters} from 'vuex'
import { recognitionQcCancel } from '@/api/check'

export default {
  data() {
    return {
      activePath: '',
      visible: false,
      top: 0,
      left: 0,
      selectedTag: {},
      affixTags: [],
      tabList: [{
        title: '关闭当前标签页',
        titleKey: 'closeCurrentTab',
        id: 1
      }, {
        title: '关闭其他标签页',
        titleKey: 'closeOthersTab',
        id: 2
      }, {
        title: '关闭全部标签页',
        titleKey: 'closeAllTab',
        id: 3
      }, {
        title: '刷新当前标签页',
        titleKey: 'refreshCurrentTab',
        id: 4
      }]
    }
  },
  computed: {
    ...mapGetters([
      'userInfo',
      'language'
    ]),
    visitedViews() {
      return this.$store.state.tagsView.visitedViews
    },
    routes() {
      return this.$store.state.permission.routes
    }
  },
  watch: {
    '$i18n.locale'() {
      this.resetTabActivePosition(this.$refs.tabs.$el)
    },
    $route(val) {
      const {meta} = this.$route
      if (!meta.noTagView) {
        this.addTags()
        this.moveToCurrentTag()
      }
    }
  },
  mounted() {
    this.initTags()
    const {meta} = this.$route
    if (!meta.noTagView) {
      this.addTags()
      this.moveToCurrentTag()
    }
  },
  methods: {
    resetTabActivePosition($el) {
      setTimeout(() => {
        const activeEl = $el.querySelector('.el-tabs__item.is-active')
        const lineEl = $el.querySelector('.el-tabs__active-bar')
        const style = getComputedStyle(activeEl)
        const pl = style.paddingLeft.match(/\d+/)[0] * 1
        const pr = style.paddingRight.match(/\d+/)[0] * 1
        const w = style.width.match(/\d+/)[0] * 1
        lineEl.style.transform = 'translateX(' + (activeEl.offsetLeft + pl) + 'px)'
        lineEl.style.width = (w - pl - pr) + 'px'
      }, 100)
    },
    moveToCurrentTag() {
      this.$nextTick(() => {
        this.visitedViews.map((tag) => {
          if (tag.path === this.$route.path) {
            this.activePath = tag.path
            this.selectedTag = tag
          }
          if (tag.fullPath !== this.$route.fullPath) {
            this.$store.dispatch('tagsView/updateVisitedView', this.$route)
          }
        })
      })
    },
    tabClick(tab) {
      this.visitedViews.map((tag) => {
        if (tag.path === tab.name) {
          this.selectedTag = tag
          this.$router.push({
            path: tag.path,
            query: tag.query,
            fullPath: tag.fullPath
          })
        }
      })
    },
    handleCommand(command) {
      switch (command) {
        case 1:
          this.closeSelectedTag(this.selectedTag)
          break
        case 2:
          this.closeOthersTags()
          break
        case 3:
          this.closeAllTags(this.selectedTag)
          break
        case 4:
          this.refreshSelectedTag(this.selectedTag)
          break
      }
    },
    removeTab(target) {
      this.visitedViews.map((v) => {
        if (v.path === target) {
          this.closeSelectedTag(v)
        }
      })
    },
    isActive(route) {
      return route.path === this.$route.path
    },
    isAffix(tag) {
      return tag.meta && tag.meta.affix
    },
    filterAffixTags(routes, basePath = '/') {
      let tags = []
      routes.forEach(route => {
        if (route.meta && route.meta.affix) {
          const tagPath = path.resolve(basePath, route.path)
          tags.push({
            fullPath: tagPath,
            path: tagPath,
            name: route.name,
            meta: {...route.meta}
          })
        }
        if (route.children) {
          const tempTags = this.filterAffixTags(route.children, route.path)
          if (tempTags.length >= 1) {
            tags = [...tags, ...tempTags]
          }
        }
      })
      return tags
    },
    initTags() {
      const affixTags = this.affixTags = this.filterAffixTags(this.routes)
      for (const tag of affixTags) {
        // Must have tag name
        if (tag.name) {
          this.$store.dispatch('tagsView/addVisitedView', tag)
        }
      }
    },
    addTags() {
      const {name} = this.$route
      if (name) {
        this.$store.dispatch('tagsView/addView', this.$route)
      }
      return false
    },
    refreshSelectedTag(view) {
      this.$store.dispatch('tagsView/delCachedView', view).then(() => {
        const {fullPath} = view
        this.$nextTick(() => {
          this.$router.replace({
            path: '/redirect' + fullPath
          })
        })
      })
    },
    closeSelectedTag(view) {
        // 从查询参数中获取id
        const row_id = view.query && view.query.id
        if (row_id) {
          recognitionQcCancel({ id: row_id }).then(res => {
            // console.log(res)
          }) 
        }
      
      this.$store.dispatch('tagsView/delView', view).then(({visitedViews}) => {
        if (this.isActive(view)) {
          this.toLastView(visitedViews, view)
        }
      })
    },
    closeOthersTags() {
      this.$router.push(this.selectedTag)
      this.$store.dispatch('tagsView/delOthersViews', this.selectedTag).then(() => {
        this.moveToCurrentTag()
      })
    },
    closeAllTags(view) {
      this.$store.dispatch('tagsView/delAllViews').then(({visitedViews}) => {
        if (this.affixTags.some(tag => tag.path === view.path)) {
          return
        }
        this.toLastView(visitedViews, view)
      })
    },
    toLastView(visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0]
      if (latestView) {
        this.$router.push(latestView.fullPath)
      } else {
        // now the default is to redirect to the home page if there is no tags-view,
        // you can adjust it according to your needs.
        if (view.name === 'Home') {
          // to reload home page
          this.$router.replace({path: '/redirect' + view.fullPath})
        } else {
          this.$router.push('/')
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.tags-view-container {
  height: 50px;
  width: 100%;
  background: #fff;
  border-bottom: 1px solid #E7EAEC;
  /*box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);*/
  position: relative;

  .top_tag_box {
    padding: 0 55px 0 15px;
  }

  .el-dropdown_box {
    background-color: #EFF3F6;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    cursor: pointer;
    color: #ccc;
  }
}

.tags-view-container::v-deep .el-tabs__nav-next {
  line-height: 50px;
}

.tags-view-container::v-deep .el-tabs__nav-prev {
  line-height: 50px;
}
</style>
<style lang="scss">
.top_tag_box {
  .el-tabs__item {
    height: 50px;
    line-height: 50px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
  }

  .el-tabs__item.is-active {
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
  }

  .el-tabs__active-bar {
    height: 3px;
  }

  // .el-tabs__item .el-icon-close {
  // }

  .el-tabs__item .el-icon-close:before {
    transition: all 0s !important;
    transform: scale(1) !important;
  }

  .el-tabs__nav-wrap::after {
    background-color: transparent !important;
  }
}

.el-dropdown_box {
  .el-dropdown-link {
    display: inline-block;
    width: 50px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    background-color: #ECF6FF;
  }
}
</style>
