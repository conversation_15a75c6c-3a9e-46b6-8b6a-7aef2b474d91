<template>
  <div
    v-show="iframeBoxState"
    class="iframe-box"
  >
    <iframe
      v-for="( value ) in iframeArr"
      v-show="value.show"
      :key="value.name"
      class="child_iframe_box"
      :src="value.src + (value.src.indexOf('?') > -1 ? '&' : '?') + `language=${language}&is_super=${userInfo.is_super}&readonly=${userInfo.readonly}`"
      :name="value.name"
      frameborder="0"
      width="100%"
      height="100%"
    />
  </div>
</template>

<script>
import {mapGetters} from 'vuex'

export default {
  name: 'iframe-box',
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      iframeArr: 'iframes/iframeArr',
      iframeBoxState: 'iframes/iframeBoxState'
    }),
    ...mapGetters([
      'language',
      'userInfo'
    ])
  },
  created() {
  }
}
</script>

<style>
.iframe-box {
  position: absolute;
  top: 109px;
  left: 0;
  width: 100%;
  z-index: 10;
  height: calc(100% - 109px);
  overflow-x: visible;
  overflow-y: visible;
  padding: 24px;
}
</style>
