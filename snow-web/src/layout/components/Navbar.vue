<template>
  <div class="navbar">
    <hamburger :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar"/>
    <!--    <breadcrumb class="breadcrumb-container" />-->

    <div class="right-menu flex-center">
      <div class="lang_box flex flex-x-end flex-y-center">
        <lang-select/>
      </div>
      <el-dropdown class="avatar-container" trigger="click">
        <div class="avatar-wrapper flex flex-x-end flex-y-center">
          <img :src="userLogo" class="user-avatar">
          <span style="margin-left:10px;color:#333;">{{ userInfo.name || userInfo.admin_name || ''}}</span>
          <!--          <i class="el-icon-caret-bottom" />-->
        </div>
        <el-dropdown-menu slot="dropdown" class="user-dropdown">
          <el-dropdown-item @click.native="openDialog">
            <span style="display:block;">{{ $t('navBar.changePassword') }}</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span style="display:block;">{{ $t('navBar.logout') }}</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <div class="dialog_container">
      <ele-dialog :append-to-body="true" :title="$t('navBar.changePassword')" :visible.sync="dialogVisible" center
                  @dialogCancel="dialogCancel" @dialogConfirm="dialogConfirm" @dialogClose="dialogClose">
        <template slot="content">
          <div class="dialog_form">
            <el-form ref="dialogForm" size="medium" :model="dialogForm" :rules="dialogRules"
                     :label-width="dialogFormLabelWidth" class="demo-ruleForm change_password_form">
              <el-form-item :label="$t('reaCommon.phone')" prop="name">
                <div>{{ userInfo.phone }}</div>
              </el-form-item>
              <el-form-item :label="$t('reaCommon.newPassword')" prop="newPassword">
                <el-input v-model="dialogForm.newPassword" type="password"
                          :placeholder="$t('reaCommon.pleaseEnter') + ' ' + $t('reaCommon.newPassword')" show-password/>
              </el-form-item>
              <el-form-item :label="$t('reaCommon.confirmPassword')" prop="confirmPassword">
                <el-input v-model="dialogForm.confirmPassword" type="password"
                          :placeholder="$t('reaCommon.pleaseConfirmPassword')" show-password/>
              </el-form-item>
              <el-form-item v-if="status" :label="$t('reaCommon.verificationCode')" prop="code">
                <div class="flex flex-y-center flex-x-start">
                  <el-input v-model="dialogForm.code" type="text"
                            :placeholder="$t('reaCommon.pleaseEnterVerificationCode')" clearable/>
                  <el-button style="margin-left:6px;text-align: center;" class="cn_code_btn"
                             :class="{'en':language == 'en'}" :disabled="!sending" :loading="isGetting"
                             @click.native="toGetCode">{{ codeText }}
                  </el-button>
                </div>
              </el-form-item>

            </el-form>
          </div>
        </template>
      </ele-dialog>

    </div>
  </div>
</template>

<script>
import {mapGetters} from 'vuex'
import Hamburger from '@/components/Hamburger'
import LangSelect from '@/components/LangSelect'
// import Breadcrumb from '@/components/Breadcrumb'
import userLogo from '@/assets/images/<EMAIL>'
// import { EleDialog } from 'ele-form'
import EleDialog from '@/components/Dialog'
import {authReset, checkTwoFactors, sendCode} from '@/api/user'

export default {
  components: {
    Hamburger,
    EleDialog,
    LangSelect
  },
  data() {
    const verifyLength = (rule, value, callback) => {
      if (value.length < 8 || value.length > 16) {
        callback(new Error(this.$t('reaCommon.passwordLength')))
      } else {
        callback()
      }
    }

    const verifySpecialCode = (rule, value, callback) => {
      // 1.全部包含：大写、小写、数字、特殊字符；
      const regex1 = '(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[\\W_])^.*$'
      // 2.无大写：小写、数字、特殊字符；
      const regex2 = '(?=.*[a-z])(?=.*[0-9])(?=.*[\\W_])^.*$'
      // 3.无小写：大写、数字、特殊字符；
      const regex3 = '(?=.*[A-Z])(?=.*[0-9])(?=.*[\\W_])^.*$'
      // 4.无数字：大写、小写、特殊字符；
      const regex4 = '(?=.*[A-Z])(?=.*[a-z])(?=.*[\\W_])^.*$'
      // 5.无特殊字符：大写、小写、数字；
      const regex5 = '(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])^.*$'
      const reg = '(' + regex1 + ')|(' + regex2 + ')|(' + regex3 + ')|(' + regex4 + ')|(' + regex5 + ')'
      const pwdRegex = new RegExp(reg)
      if (pwdRegex.test(value)) {
        callback()
      } else {
        callback(new Error(this.$t('reaCommon.passwordSpecialVerify')))
      }
    }
    const equalToPassword = (rule, value, callback) => {
      if (this.dialogForm.newPassword !== value) {
        callback(new Error(this.$t('reaCommon.notSamePassword')))
      } else {
        callback()
      }
    }
    return {
      isGetting: false,
      codeText: this.$t('reaCommon.verificationCode'),
      second: 60,
      sending: true,
      status: false,
      userLogo,
      dialogForm: {
        newPassword: '',
        confirmPassword: '',
        code: ''
      },
      dialogRules: {
        code: [{required: true, trigger: 'blur', message: this.$t('reaCommon.pleaseEnterVerificationCode')}],
        newPassword: [
          {required: true, trigger: 'blur', message: this.$t('reaCommon.pleaseEnterNewPassword')},
          {required: true, validator: verifyLength, trigger: 'blur'},
          {required: true, validator: verifySpecialCode, trigger: 'blur'}
        ],
        confirmPassword: [
          {required: true, trigger: 'blur', message: this.$t('reaCommon.pleaseConfirmNewPassword')},
          {required: true, validator: verifyLength, trigger: 'blur'},
          {required: true, validator: verifySpecialCode, trigger: 'blur'},
          {required: true, validator: equalToPassword, trigger: 'blur'}

        ]
      },
      dialogVisible: false,
      dialogFormLabelWidth: '80px'

    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'userInfo',
      'language'
    ])
  },
  watch: {
    '$i18n.locale'() {
      this.setFormRule()
      this.codeText = this.$t('reaCommon.verificationCode')
      this.dialogFormLabelWidth = this.language === 'en' ? '150px' : '80px'
    }
  },
  created() {
    this.dialogFormLabelWidth = this.language === 'en' ? '150px' : '80px'
    this.codeText = this.$t('reaCommon.verificationCode')
    // this.getLoginConfig()
    this.setFormRule()
  },
  methods: {
    getLoginConfig() {
      checkTwoFactors({}).then((res) => {
        if (res.code == 1) {
          this.status = res.data.status
        }
      })
    },
    toGetCode() {
      if (this.sending) {
        this.isGetting = true
        sendCode({phone: this.userInfo.phone}).then((res) => {
          if (res.code == 1) {
            this.timeDown()
            this.sending = false
          }
        }).finally(() => {
          this.isGetting = false
        })
      }
    },
    // 60秒倒计时
    timeDown() {
      this.codeText = this.second
      const result = setInterval(() => {
        --this.second
        this.codeText = this.second
        if (this.second < 1) {
          clearInterval(result)
          this.sending = true
          // this.disabled = false;
          this.second = 60
          this.codeText = this.$t('reaCommon.verificationCode')
        }
      }, 1000)
    },
    setFormRule() {
      const verifyLength = (rule, value, callback) => {
        if (value.length < 8 || value.length > 16) {
          callback(new Error(this.$t('reaCommon.passwordLength')))
        } else {
          callback()
        }
      }
      const verifySpecialCode = (rule, value, callback) => {
        // 1.全部包含：大写、小写、数字、特殊字符；
        const regex1 = '(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[\\W_])^.*$'
        // 2.无大写：小写、数字、特殊字符；
        const regex2 = '(?=.*[a-z])(?=.*[0-9])(?=.*[\\W_])^.*$'
        // 3.无小写：大写、数字、特殊字符；
        const regex3 = '(?=.*[A-Z])(?=.*[0-9])(?=.*[\\W_])^.*$'
        // 4.无数字：大写、小写、特殊字符；
        const regex4 = '(?=.*[A-Z])(?=.*[a-z])(?=.*[\\W_])^.*$'
        // 5.无特殊字符：大写、小写、数字；
        const regex5 = '(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])^.*$'
        const reg = '(' + regex1 + ')|(' + regex2 + ')|(' + regex3 + ')|(' + regex4 + ')|(' + regex5 + ')'
        const pwdRegex = new RegExp(reg)
        if (pwdRegex.test(value)) {
          callback()
        } else {
          callback(new Error(this.$t('reaCommon.passwordSpecialVerify')))
        }
      }
      const equalToPassword = (rule, value, callback) => {
        if (this.dialogForm.newPassword !== value) {
          callback(new Error(this.$t('reaCommon.notSamePassword')))
        } else {
          callback()
        }
      }
      this.dialogRules = {
        code: [{required: true, trigger: 'blur', message: this.$t('reaCommon.pleaseEnterVerificationCode')}],
        newPassword: [
          {required: true, trigger: 'blur', message: this.$t('reaCommon.pleaseEnterNewPassword')},
          {required: true, validator: verifyLength, trigger: 'blur'},
          {required: true, validator: verifySpecialCode, trigger: 'blur'}
        ],
        confirmPassword: [
          {required: true, trigger: 'blur', message: this.$t('reaCommon.pleaseConfirmNewPassword')},
          {required: true, validator: verifyLength, trigger: 'blur'},
          {required: true, validator: verifySpecialCode, trigger: 'blur'},
          {required: true, validator: equalToPassword, trigger: 'blur'}

        ]
      }
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      this.$confirm(this.$t('navBar.confirmExit'), this.$t('reaCommon.warning'), {
        confirmButtonText: this.$t('reaCommon.confirm'),
        cancelButtonText: this.$t('reaCommon.cancel'),
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('user/logout')
        this.$store.dispatch('tagsView/delAllViews')
        // this.$router.push(`/login`)
        location.reload()
      }).finally(() => {
        const messageBoxDom = document.getElementsByClassName('el-message-box__wrapper')[0]
        if (messageBoxDom) {
          messageBoxDom.remove()
        }
      })
    },
    dialogClose() {
      this.dialogVisible = false
    },
    dialogConfirm() {
      this.$refs['dialogForm'].validate((valid) => {
        if (valid) {
          authReset({
            phone: this.userInfo.phone,
            password: this.dialogForm.newPassword,
            confirm: this.dialogForm.confirmPassword,
            code: this.dialogForm.code
          }).then((res) => {
            this.$message.success(this.$t('reaCommon.operationSuccess'))
            this.dialogVisible = false
            this.$store.dispatch('tagsView/delAllViews')
            this.$store.dispatch('user/logout').then(() => {
              this.$router.replace('/login')
            })
          })
        }
      })
    },
    dialogCancel() {
      this.dialogVisible = false
    },
    openDialog() {
      this.dialogForm = {
        password: '',
        newPassword: '',
        confirmPassword: ''
      }
      this.dialogVisible = true
      if (this.$refs['dialogForm']) {
        this.$refs['dialogForm'].resetFields()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.navbar {
  height: 59px;
  overflow: hidden;
  position: relative;
  background: #fff;
  border-bottom: 1px solid #E7EAEC;

  .hamburger-container {
    //line-height: 64px;
    padding-top: 20px !important;
    padding-bottom: 15px !important;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .right-menu {
    float: right;
    height: 100%;
    //line-height: 64px;
    line-height: 24px;
    padding: 20px 0 15px;
    cursor: pointer;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 24px;
          height: 24px;
          border-radius: 50%;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}

.reset_password ::v-deep .el-input__inner {
  border: none;
}

.lang_box {
  margin-right: 20px;
}

.cn_code_btn {
  width: 160px;
}

.cn_code_btn.en {
  width: 240px;
}

.cn_code_btn.is-disabled {
  color: #c0c4cc !important;
  cursor: not-allowed;
  background-image: none;
  background-color: #fff !important;
  border-color: #ebeef5 !important;

}

.change_password_form ::v-deep .el-button--default {
  border-color: $themeColor;
  color: $themeColor;
  padding: 10px 0 !important;
}
</style>
