import router from './router'
import store from './store'
import {Message} from 'element-ui'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import {getToken, getUserInfo, getUserMenu} from '@/utils/auth' // get token from cookie
import getPageTitle from '@/utils/get-page-title'

NProgress.configure({showSpinner: false}) // NProgress Configuration

const whiteList = ['/login', '/forgot-password'] // no redirect whitelist

router.beforeEach(async (to, from, next) => {
  // start progress bar
  NProgress.start()
  const {type, id} = to.query
  if (to.path === '/brand/add') {
    if (id) {
      // document.title = getPageTitle(type == 1 ? '品牌方查看' : '品牌方编辑')
      to.meta.titleKey = type == 1 ? 'tenantToView' : 'tenantEdit'
    } else {
      to.meta.titleKey = 'tenantAdd'
      // document.title = getPageTitle('品牌方新增')
    }
  } else if (to.path === '/store-warehouse/add') {
    if (id) {
      // document.title = getPageTitle('售点编辑')
      to.meta.titleKey = 'storeToEdit'
    } else {
      to.meta.titleKey = 'storeToAdd'
      // document.title = getPageTitle('售点新增')
    }
  } else if (to.path === '/inspection-plan/add') {
    if (id) {
      // document.title = getPageTitle(type == 1 ? '检查计划查看' : '检查计划编辑')
      to.meta.titleKey = type == 1 ? 'checkPlanToView' : 'checkPlanToEdit'
    } else {
      to.meta.titleKey = 'checkPlanToAdd'
      // document.title = getPageTitle('检查计划新增')
    }
  }
  document.title = getPageTitle(to.meta.titleKey)
  // set page title

  // determine whether the user has logged in
  const hasToken = getToken()
  if (hasToken) {
    if (to.path === '/login') {
      // if is logged in, redirect to the home page
      next({path: '/'})
      NProgress.done()
    } else {
      const name = store.getters.name
      if (name) {
        next()
      } else {
        try {
          const userInfo = getUserInfo()
          if (!userInfo.admin_name) {
            await store.dispatch('user/getInfo')
          } else {
            await store.dispatch('user/setName', userInfo.admin_name)
          }
          const storageRoutes = getUserMenu()
          let accessRoutes = []
          if (!storageRoutes.length) {
            accessRoutes = await store.dispatch('permission/generateRoutes')
          } else {
            accessRoutes = await store.dispatch('permission/setRoutes', storageRoutes)
          }

          router.addRoutes(accessRoutes)
          next({...to, replace: true})
        } catch (error) {
          // remove token and go to login page to re-login
          await store.dispatch('user/resetToken')
          Message.error(error || 'Has Error')
          next(`/login?redirect=${to.path}`)
          NProgress.done()
        }
      }
    }
  } else {
    /* has no token*/

    if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      next()
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})
