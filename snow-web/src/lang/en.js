export default {
  route: {
    home: 'Home',
    add: 'Add',
    view: 'View',
    edit: 'Edit',
    update: 'Update',
    enable: 'Disable / Enable',
    delete: 'Delete',
    dataImport: 'Import',
    dataExport: 'Export',
    dataReset: 'Data Restore',
    copy: 'Copy',
    retailEyeSystemName: 'Operation Platform',
    skuManagement: 'SKU Management',
    tenantManagement: 'Brand Group',
    storeWarehouse: 'Outlet Management',
    ownStoreWarehouse: 'RE Outlet Warehouse',
    tenantStoreWarehouse: 'Brand Outlet Warehouse',
    storeMap: 'Mapping Relation',
    skuWarehouse: 'SKU Warehouse',
    ownMasterData: 'RE Main Data',
    tenantMasterData: 'Brand Main Data',
    masterDataMap: 'Mapping Relation',
    questionnaireManagement: 'Questionnaire Management',
    checkProject: 'Inspection Item',
    checkPlan: 'Inspection Plan',
    accountManagement: 'User Management',
    frontAccountManagement: 'App User',
    backendAccountManagement: 'Platform User',
    replaceIndex: 'Replace List',
    questionIndex: 'Question List',
    roleManagement: 'Role Management',
    visitManagement: 'Visit Management',
    visitPlan: 'Visit Plan',
    cycleManagement: 'Cycle Management',
    visitedEnquiry: 'Visited List',
    abnormalStoreQuery: 'Exceptional Visit List',
    apiResult: 'API',
    appLog: 'APP Log',
    systemConfiguration: 'System Configuration',
    gateway: 'Gateway',

    checkPlanAdd: 'Add Inspection Plan',
    tenantStoreAdd: 'Brand Outlet Warehouse Add',
    tenantAdd: 'Add Brand',
    storeReport: 'Outlet Report',
    tenantToView: 'Brand View',
    tenantEdit: 'Edit Brand',
    checkPlanToView: 'Inspection Plan View',
    checkPlanToEdit: 'Edit Inspection Plan',
    checkPlanToAdd: 'Add Inspection Plan',
    storeToEdit: 'Edit Outlet',
    storeToAdd: 'Add Outlet',
    apiReport: 'Identification Details',
    deviceList: 'Login Device List',
    operationLog: 'Operation Log',
    lockAndUnlock: 'Lock/UnLock',
    sceneReportTime: 'Scenario Report Time',
    gatherCooler: 'Freezer Situation Handling',
    gatherRack: 'QC',
    gatherDiscriminate: 'Identification Processing',
    gatherSales: 'Sales Processing',
    gatherRackSales: 'Shelf sales Processing',
    storeInventory: 'Outlet List',
    sceneList: 'Scene list',
    sceneDetail: 'Outlet Details',
    QC: 'QC',
    qcRack: 'Shelf Camera',
    qcCooler: 'Freezer Camera',
    rackQcDetail: 'Shelf QC details',
    coolerQcDetail: 'Refrigerator QC Details',
    sceneAnnotate: 'Scene Annotation',
    sceneData: 'Scene Data',
    sceneConfig: 'Scene Configuration',
    storeConfig: 'Point Of Sale Configuration',
    versionConfig: 'Version configuration',
    storeSceneList: 'Scene List',
    coolerQcDiscriminate: 'Freezer Identification Processing',
    gatherImage: 'Image Processing',
    deviceIndex: 'Device List',
    dashboard: 'Data Board',
    installAppAccount: 'Device installation APP account',
    metaStoreAccount: 'MetaStore Account'
  },
  login: {
    welcomeToUse: 'Welcome',
    retailEyeSystemName: 'Operation Platform',
    pleaseEnterUserName: 'Please Enter User Name',
    pleaseEnterPassword: 'Please Enter Password',
    login: 'Log In'
  },
  navBar: {
    changePassword: 'Change Password',
    logout: 'Log Out',
    confirmExit: 'Are You Sure To Exit?'
  },
  tagsView: {
    closeCurrentTab: 'Close Current Tab',
    closeOthersTab: 'Close Other Tabs',
    closeAllTab: 'Close All Tabs',
    refreshCurrentTab: 'Refresh Current Tab'
  },
  reaCommon: {
    hasCamera: '是否安装设备',
    fullScene: '是否100%覆盖',
    bindDevType: '安装设备类型',
    sceneLocation: 'Scene Location',
    sceneDisplayPosition: 'Scene Display Position',
    displayPosition: "Display Position Closer to Competitor\'s and Human Traffic Flow",
    haveGateway: 'Presence of Gateway',
    openDirection: 'Door opening direction',
    uploadF: 'Upload',
    pleaseUploadF: 'Please upload the file',
    versionInformation: 'Version Information',
    configList: 'Configuration List',

    today: 'Today',
    yesterday: 'Yesterday',
    thisWeek: 'This Week',
    thisMonth: 'This Month',
    thisQuarter: 'This Quarter',
    threeDays: '3 Days',
    sevenDays: '7 Days',
    thirtyDays: '30 Days',
    ninetyDays: '90 Days',
    subtotal: 'Subtotal',
    hotspotInsights: 'Hotspot Insight',
    assetNumber: 'Asset Number',
    versionCodeNumber: 'Version number',
    versionCode: 'Version Name',
    currentVersion: 'Current version used',
    effectiveTime: 'Data effective time',
    effectiveStart: 'Effective start time',
    effectiveStop: 'Effective end time',
    versionDesc: 'Version Description',
    publishPersion: 'Publisher',
    publishTime: 'Creation Time',
    longTerm: 'Long-term',

    versionConfig: 'Version configuration',
    aveFacing: 'Average Facing',
    numberCount: 'Number',
    skuTotal: 'Total SKUs',
    hotZone: 'Hot Zone Classification',
    sortName: 'Ranking',
    hotspot: 'Hotspot Name',
    sales: 'Sales',
    sovi: 'SOVI',
    skuID: 'SKU ID',
    selectTime: 'Select Date',
    sureDel: 'Are you sure to delete?',
    success: 'Operation successful',
    passwordNotChange: 'Please Change Your password, Or You Will Be Unable To Log In',
    confirmPassword: 'Confirm Password ',
    pleaseConfirmPassword: 'Please Confirm Password',
    notSamePassword: 'Password not matched!',
    passwordLength: 'Password Length Should Between 8-16 Digits',
    phonePlaceholder: 'Please Enter Phone Number',
    isLockOrNot: 'Locked Status',
    locked: 'Locked',
    unlocked: 'Not Locked',
    unlockSuccess: 'Unlock Success',
    lockSuccess: 'Lock Success',
    lockAndUnlock: 'Lock/UnLock',
    sceneReportTime: 'Scenario Report Time',
    unlock: 'Unlock',
    lock: 'Lock',
    updatePassword: 'Update Password',
    passwordSpecialVerify: 'The Password Must Contain Letters, Numbers And Special Characters',
    verificationCode: 'Verification Code',
    verificationCodeError: 'Error In Verification Code, Please Re-enter',
    pleaseConfirmNewPassword: 'Please Confirm The New Password',
    pleaseEnterNewPassword: 'Please Enter A New Password',
    passwordRequirements: 'Please enter a new password (the password is 8-16 digits long and needs to contain letters, numbers and special characters)',
    pleaseEnterPhoneReceiveCode: 'Please enter the login mobile number to receive the verification code',
    pleaseEnterVerificationCode: 'Please Enter The Verification Code',
    forgotPassword: 'Forgot Password',
    lastLoginTime: 'Last Login Time',
    // grade protection
    operationTime: 'Operation Time',
    operationAccount: 'Operating Account',
    operationAuthority: 'Operation Authority',
    operationDate: 'Date',
    operationTimeSecond: 'Time',
    apiLog: 'API Log',
    systemLogManage: 'System Log Management',
    logConfig: 'Log Configuration',
    clearLog: 'Clear Log',
    deleteLog: 'Delete Log',
    operationNode: 'Operation Node',
    operationBehavior: 'Operation Behavior',
    operationDescribe: 'Operation Description',
    locationAddress: 'Location Address',
    locationPosition: 'Geographical Position',
    selectExportCheckProject: 'Select Export Inspection Item',
    taskName: 'Task Name',
    dateRange: 'Date Range',
    startUseTime: 'Start Time',
    deviceId: 'Device ID',
    deviceLang: 'APP Language',
    devicePixelRatio: 'Device Pixel Ratio',
    lastUseTime: 'Last Use Time',
    sdkVersion: 'SDK Version',
    appVersion: 'APP Version',
    systemNameAndOSVersion: 'System Name And OS Version',
    switchMap: 'Switch Map Mode',
    switchList: 'Switch List Mode',
    addGateway: 'Add Gateway',
    editGateway: 'Edit Gateway',
    gatewayName: 'Name',
    projectName: 'Model',
    recognitionUrl: 'Recognition Url',
    stitchingQueue: 'Stitching Queue Name',
    originImages: 'Original Image',
    skuFacing: 'SKU Facing Number',
    engineResults: 'Rule Result',
    videoFailed: 'Failed',
    imageFailed: 'Failed',
    sku_rate: 'SKU Distribution Rate',
    sku_face: 'SKU Shortage Rate',
    allRevivificationTxt: 'This Operation Will Restore All SKU Data. Do You Want To Continue?',
    skuLogo: 'SKU Logo',
    phoneError: 'The Phone Number Format Is Incorrect',
    batchDeleteWarningPrevTxt: 'This Operation Will Delete The ',
    batchDeleteWarningNextTxt: ' Checked Data. Do You Want To Continue?',
    changeSkuName: 'Change SKU Name',
    pleaseEnterSkuName: 'Please Enter SKU Name',
    pleaseLeastOne: 'Please Check At Least One Item',
    checkedItems: '',
    item: ' Items Checked',
    revivification: 'Data Restore',
    skuName: 'SKU Name',
    importDate: 'Import Date',
    deviceModel: 'Device Model',
    clickPlay: 'Click Play',
    export: 'Export',
    selectExportField: 'Select The Fields To Export',
    pleaseSelectField: 'Please Select The Fields To Export',
    taskId: 'Task ID',
    visitDuration: 'Visit Duration',
    pleaseSelectCycle: 'Please Select A Cycle',
    pleaseEnterSelectStore: 'Please Select A Outlet',
    pleaseEnterToSearch: 'Please Enter Outlet Name To Search',
    visitStatus: 'Visit Status',
    visiting: 'Visiting',
    completed: 'Completed',
    password: 'Password',
    loginFail: 'Login Failed Or Your Account Has Been Logged In At Another Location, Please Log In Again',
    loginAgain: 'Login Again',
    visitPlan: 'Visit Plan',
    storeCollection: 'Outlet Selection',
    imageRecognition: 'Image Recognition',
    ruleEngine: 'Rule Engine',
    set: 'Set',
    download: 'Download',
    buildingExcel: 'Excel Is Being Generated. Please Wait Patiently.',
    buildSuccess: 'Success',
    downloadTemplate: 'Download Template',
    downloadErrorData: 'Download Failed Data',
    downloadRepeatData: 'Download Duplicate Data',
    note: 'Note',
    account: 'Account',
    originalPassword: 'Original Password',
    newPassword: 'New Password',
    welcomeToUseRE: 'Welcome To REA Management Platform',
    back: 'Return',
    nextStep: 'Next',
    previousStep: 'Back',
    complete: 'Done!',
    cancel: 'Cancel',
    confirm: 'Yes',
    operationSuccess: 'Succeed',
    confirmDelete: 'Are You Sure To Delete',
    warning: 'Tips',
    confirmDeleteThisOne: 'Are You Sure To Delete This Data',
    dataImport: 'Import',
    dataExport: 'Export',
    setTableTitle: 'Header Set',
    allFields: 'All Fields',
    addStore: 'Add Outlet',
    spread: 'Unfold',
    fold: 'Fold',
    yes: 'Yes',
    no: 'No',
    save: 'Save',
    submit: 'Submit',
    templateDownload: 'Template',
    addUser: 'Create User',
    rapidRetrieval: 'Quick Search',
    startDate: 'Start Date',
    endDate: 'End Date',
    to: 'To',
    query: 'Search',
    reset: 'Reset',
    advancedSearch: 'Advanced Search',
    packUp: 'Fold',
    status: 'Status',
    operation: 'Operation',
    toView: 'View',
    disable: 'Disable',
    normal: 'Normal',
    enable: 'Enable',
    edit: 'Edit',
    delete: 'Delete',
    add: 'Add',
    disableSuccess: 'Disable Succeed',
    enableSuccess: 'Enable Succeed',
    visitorNumber: 'Visitor ID',
    visitorName: 'Visitor',
    cycleNumber: 'Cycle ID',
    planStartDate: 'Planed Start Date',
    planEndDate: 'Planed End Date',
    createDate: 'Created Date',
    tenantNameOrId: 'Brand Name/ID',
    ConfigModule: 'Configuration Module',
    tenant: 'Brand',
    BU: 'BU',
    organization: 'Organization',
    storeNameCode: 'Outlet Code/Name',
    provinces: 'Province',
    city: 'City',
    county: 'District',
    channelType: 'Channel Type',
    storeStatus: 'Outlet Status',
    checkDate: 'Inspection Date',
    checkProject: 'Inspection Item',
    userId: 'User ID',
    phone: 'Phone Number',
    supplier: 'Supplier',
    name: 'Name',
    openTime: 'Created Date',
    roleName: 'Role',
    serialNumber: 'Serial Number',
    position: 'Title',
    tenantId: 'Brand ID',
    tenantName: 'Brand Name',
    createTime: 'Created Date',
    storeId: 'Outlet ID',
    storeCode: 'Outlet Code',
    storeName: 'Outlet Name',
    storeAddress: 'Outlet Address',
    ownerName: 'Outlet Manager Name',
    ownerTel: 'Outlet Manager Tel',
    theContact: 'Contacts',
    contact: 'Phone Number',
    scottLong: 'Gaode Longitude',
    scottLat: 'Gaode Latitude',
    baiduLong: 'Baidu Longitude',
    baiduLat: 'Baidu Latitude',
    shopArea: 'Outlet Area',
    hasShopCar: 'Shopping Cart or Basket',
    remark: 'Remark',
    updateDate: 'Update Date',
    department: 'Department',
    checkPlanId: 'Inspection Plan ID',
    checkProjectName: 'Inspection Item Name',
    visitedId: 'Visited ID',
    startTime: 'Start Time',
    endTime: 'End Time',
    visitType: 'Visit Type',
    visitDate: 'Visit Date',
    remainingDays: 'Remaining Days',
    creator: 'Creators',
    uploadFile: 'Upload File',
    selectFile: 'Select File',
    uploadAttention: 'Attention: System Supports Files Up To 8MB',
    dataMustSameTemplate: 'Imported File Should Be Consistent With Template File',
    clickUpload: 'Click To Upload',
    uploadAgain: 'Re-Upload',
    uploading: 'Uploading',
    fileSize512M: 'File Size Cannot Exceed 512M',
    fileFormat: 'File Format Is Incorrect',
    effectData: 'Effective Data',
    repeatData: 'Repeated Data',
    errorData: 'Error Data',
    close: 'Close',
    addPlan: 'Add Inspection Plan',
    copy: 'Copy',
    copyVisitPlan: 'Copy Visit Plan',
    editVisitPlan: 'Edit Visit Plan',
    unfixedDate: 'Unset date',
    fixedDate: 'Fixed Date',
    cycleName: 'Cycle Name',
    cycleTime: 'Cycle Time',
    addCycle: 'Add Cycle',
    wheneverWhereverVisit: 'Flexible Visit',
    inTheShopTime: 'Outlet Visit Duration',
    less5Minutes: 'Under 5 mins',
    to10Minutes: '5-10mins',
    to40Minutes: '10-40mins',
    moreThan40Minutes: 'Above 40 mins',
    visitTime: 'Visit Time',
    intoStoreTime: 'Check-in Time',
    leaveStoreTime: 'Check-out Time',
    doorImage: 'Shop Front Image',
    video: 'Video',
    sceneData: 'Scene Data',
    errorType: 'Exception Type',
    errorReason: 'Discard Reason',
    picture: 'Picture',
    errorDescription: 'Description',
    pleaseEnter: 'Please Enter',
    pleaseSelect: 'Please Select',
    cashNumber: 'Number Of Cash Registers',
    scannerNumber: 'Number of Scanners',
    isEnabled: 'Enabled',
    isNotEnabled: 'Not Enabled',
    isDisabled: 'Disabled',
    noData: 'No Data',
    deleteSuccess: 'Delete Succeed',
    pcc: 'Province/City/District',
    completeSet: 'Finished',
    editUser: 'Edit User',
    resetPassword: 'Password Reset',
    resetPasswordSuccess: 'Password Reset Succeed',
    confirmCopyData: 'Are You Sure To Copy The Data?',
    selectDate: 'Select Date',
    copySuccess: 'Copy Success',
    editCycle: 'Edit Cycle',
    // cycle: 'Cycle',
    cycle: 'Time Period',
    questionnaireDimension: 'Questionnaire Dimension',
    isIrQuestionnaire: 'Whether Is IR Questionnaire ',
    questionnaireIdOrName: 'Questionnaire Id/Name',
    questionnaireName: 'Questionnaire Name',
    questionnaireType: 'Questionnaire Type',
    completion: 'Completion',
    choice: 'Choice Question',
    yesNoQuestion: 'Y/N Question',
    questionnaireScene: 'Questionnaire Scene',
    questionnaireId: 'Questionnaire ID',
    questionnaireNamePlaceholder: 'Please Enter Questionnaire Stem',
    questionnaireTypePlaceholder: 'Please Select Questionnaire Types',
    questionnaireDimensionPlaceholder: 'Please Select Questionnaire Dimension',
    isIrQuestionnairePlaceholder: ',Please Select Whether It\'s IR Questionnaire',
    pleaseEnterAnswerType: 'Please Tick Answer Type',
    answerType: 'Answer Type',
    anyValue: 'Any Value',
    integer: 'Integer',
    percent: '2 Decimal Places',
    pleaseSelectQuestionnaireScene: 'Please Select Questionnaire Scene',
    saveUse: 'Save and Enable',
    checkProjectIdName: 'Inspection Item ID/Name',
    checkProjectId: 'Inspection Item ID',
    storeQuestionnaire: 'Outlet Questionnaire',
    sceneQuestionnaire: 'Scene Questionnaire',
    option: 'Option',
    store: 'Outlet',
    scene: 'Scene',
    scenePlaceholder: 'Please Select Scene',
    oops: 'OOPS',
    backHome: 'Back To Home',
    notFindView: 'This Page Cannot Be Found',
    copyright: 'Copyright',
    reaName: '零眸智能',
    checkUrlRight: 'Please check whether the web address you entered is correct, or click the button below to return to the home page',
    username: 'Username',
    role: 'Role',
    priceTag: 'Price Tag',
    showAll: 'Show All',
    // TODO
    deviceTotCount: 'Total Number Of Devices',
    sceneTotCount: 'Total Dumber Of Scenes',
    storeConfig: 'Configuration',
    config: 'Configuration',
    sceneImage: 'Scene Image',
    sceneType: 'Scene Type',
    isBindDev: 'Bind Device Or Not',
    isBindPlan: 'Bind Plan Or Not',
    deviceCode: 'Device Number',
    takePhotos: 'Taking Time',
    deviceStatus: 'Device Status',
    qcStatus: 'QC Status',
    storeInventory: 'Outlet List',
    channel: 'Affiliated Channel',
    sort: 'Sort',
    sceneName: 'Scene Name',
    sceneSort: 'Scene Sort',
    uploadSceneImage: 'Upload Scene Images',
    pleaseUploadSceneImage: 'Please Upload Scene Images',
    sceneConfig: 'Scene Configuration',
    deviceGroup: 'Device Group',
    storeImage: 'Outlet Plan',
    storeImageConfig: 'Plan View Configuration',
    noSceneInfo: 'There is currently no scene information bound to this device',
    alsoSceneConfig: 'The scene has been configured',
    thisDeviceGroup: 'This Device Group',
    otherDeviceGroup: 'Other Device Group',
    sureUnlinkScene: 'Do you need to unbind this scene and re associate it?',
    alsoLinkDevice: 'Associated Device',
    pleaseBindData: 'Please Submit Binding Data',
    number: 'Encoding',
    deviceName: 'Device Name',
    QCStartTime: 'QC Start Time',
    QCFinishTime: 'QC Completion Time',
    gatherSales: 'Sales Processing',
    deviceInfo: 'Device Information',
    SKUList: 'SKU List',
    count: 'Quantity',
    outList: 'Retrieve List',
    inList: 'Put Back To list',
    pleaseSelectSKU: 'Please Select SK',
    extraBox: 'Out Of Bounds',
    openAtTime: 'Opening Time',
    closeAtTime: 'Closing Time',
    gatherDiscriminate: 'Identification Processing',
    gatherImage: 'Image Processing',
    updateSales: 'Modify Sales Volume',
    discriminateReport: 'Identify Report',
    downloadVideo: 'Download Video',
    imageFrame: 'Frame Drawing',
    correctImage: 'Correction Image',
    generateCurrentImage: 'Generate Correction Image',
    uploadCurrentImage: 'Upload Correction Image',
    belongStore: 'Belonging Outlet',
    deviceNumber: 'Device Number',
    createdTime: 'Creation Time',
    installFinishTime: 'Install Finish Time',
    taskFinishTime: '任务完成时间',
    deviceType: '设备类型',
    voltage: '电压值'
  },
  tenantAdd: {
    photoModePlaceholder: 'Please select at least one photographing mode',
    frontCameraMode: 'Front Camera Mode',
    videoAnalysis: 'Video Analysis',
    VRMode: 'Augmented reality/VR Mode',
    ARMode: 'Augmented reality/AR Mode',
    flowMode: 'OF/Optical Flow Mode',
    rearCameraMode: 'Rear Camera Mode',
    photoMode: 'Photo Mode',
    openInspectionPlan: 'Open Inspection Plan',
    modelMonitor: 'Model Monitor',
    withDisplay: 'With Stack Background Display',
    withShelfBackgroundDisplay: 'With Shelf Background Display',
    withBottles: 'Single Bottle Inhibition',
    chooseLeastOne: 'At Least Select One Type Of Visit',
    checkPlanEffect: 'Configuration Worked',
    meters: 'Meters',
    tenantAdd: 'Add Brand',
    smsCodeEnabled: 'Enable SMS verification code',
    scenereportTimeEnabled: 'Enable scene report time',
    pleaseSelectModule: 'Please Select And Set Module That Brand Needed',
    storeCollection: 'Outlet Selection',
    imageRecognition: 'Image Recognition',
    ruleEngine: 'Rule Engine',
    completeConfiguration: 'Finished',
    tenantName: 'Brand Name',
    tenantNamePlaceholder: 'Please Enter The Brand Name',
    collectWay: 'Collect Method',
    regionType: 'Region',
    regionOverseas: 'Others',
    regionChina: 'China',
    cameraGroup: 'Enable SDK grouping',
    scene: 'Scene',
    scenePlaceholder: 'Please Select Scene',
    cloudStorage: 'Cloud Storage',
    cloudStoragePlaceholder: 'Please Select Cloud Storage Method',
    skuMasterDataIndustry: 'SKU Main Data Industry',
    IRMasterDataSource: 'SKU Master Data Source',
    IRMasterData: 'IR Master Data',
    REAMappingTable: 'REA Mapping Table',
    skuMasterDataIndustryPlaceholder: 'Please Select SKU Main Data Industry',
    identifyConfig: 'Identify Config',
    priceTag: 'Price Tag',
    recognitionModel: 'Recognition Model',
    recognitionModelPlaceholder: 'Please Select Recognition Model',
    successfulImage: 'Success Image',
    acquisitionMode: 'Collect Mode',
    acquisitionModePlaceholder: 'Select At Least 1 Collect Mode',
    manualMode: 'Manual Mode',
    irMode: 'IR Mode',
    checkProjectTackEffect: 'Configuration Worked',
    instant: 'Instant',
    planToVisit: 'Plan Visit',
    wheneverWhereverVisit: 'Outlet Follow-Up',
    doorImage: 'Shop Front Image',
    noPictures: 'No',
    takePictures: 'Must',
    selectPictures: 'Optional',
    doorVideo: 'Shop Front video',
    signIn: 'Sign In',
    SignInEffectiveDistance: 'Sign in Effective Distance',
    SignInEffectiveDistancePlaceholder: 'Please Enter the Effective Distance Of Sign In',
    organization: 'Organization Structure',
    organizationConfig: 'Organization Structure configuration',
    checkOrganizationConfig: 'Check Organization Structure',
    editOrganizationConfig: 'Edit Organization Structure',
    levelOneDepartment: 'Level 1 Department',
    addLevelOneDepartment: 'Add Level 1 Department',
    addSameDepartment: 'Add Same Level Department',
    addChildDepartment: 'Add Lower Level Department',
    departmentName: 'Department Name',
    deleteDepartment: 'Delete Department',
    changeDepartmentName: 'Edit Department Name',
    departmentNamePlaceholder: 'Please Enter Department Name',
    sameNameLevelOneDepartment: 'Level 1 Department With Same Name Existed',
    sameNameSameLevelDepartment: 'Same Level Department With Same Name Existed',
    sameNameChildDepartment: 'Sub-Department With Same Name Existed',
    collectModuleWarning: 'Collect Module Required',
    planModuleWarning: 'Visit Plan Module Required',
    changeName: 'Change Name',
    haveChild: 'There Are Sub Department In This Department, Please Delete Sub Department First',
    department: 'Department',
    retailEye: 'RE',
    cos: 'COS',
    blob: 'BLOB',
    pleaseEnter: 'Enter',
    pleaseConfigOrganization: 'Please Configure Organization Structure',
    detectPlatform: 'Select Configuration Platform',
    pleasedetectPlatform: 'Please select a configuration platform',
    basicDetectPlatform: 'Basic configuration',
    omniDetectPlatform: 'OMNI Configuration',
    flowId: 'Process number',
    pleaseFlowId: 'Please enter the process number',
    apiUrl: 'Identify interface addresses',
    pleaseApiUrl: 'Please enter the identification interface address',
    apiSecret: 'Signing Key',
    pleaseApiSecret: 'Please enter the signature key',
    linkHelp: 'Please contact the algorithm department to provide'
  },
  checkPlanAdd: {
    article: '',
    checkPlanAdd: 'Add Inspection Plan',
    configProjectTime: 'Configure Project And Time',
    configStore: 'Configure Outlet',
    tenant: 'Brand',
    tenantPlaceholder: 'Please Select Brand',
    checkProject: 'Inspection Item',
    checkProjectPlaceholder: 'Please Select Inspection Item ',
    checkDate: 'Inspection Date',
    checkDatePlaceholder: 'Please Select Inspection Date',
    addStore: 'Add Outlet',
    addDepartment: 'Please Select Department',
    importStore: 'Import Outlet',
    alreadyImportStore: 'Already imported',
    organizationStore: 'Organizational Outlet',
    deleteStore: 'Delete Outlet',
    batchDeleteStore: 'Delete In Batches',
    checkCount: 'Check Count',
    downloadStore: 'Download Imported Outlet',
    screenStore: 'You Have Filtrated',
    storeCount: 'Outlets'
  },
  frontUserAdd: {
    belongTenant: 'Belonged Brand',
    belongTenantPlaceholder: 'Please Select Belonged Brand',
    belongStore: 'Belonged Store',
    belongStorePlaceholder: 'Please Select Belonged Store',
    namePlaceholder: 'Please Enter Name',
    phonePlaceholder: 'Please Enter Phone Number',
    positionPlaceholder: 'Please Enter Position',
    normal: 'Normal',
    disabled: 'Disabled'
  },
  backendUserAdd: {
    username: 'Username',
    password: 'Password',
    confirmPassword: 'Confirm Password ',
    pleaseConfirmPassword: 'Please Confirm Password',
    passwordLength: 'Password Length Should Between 8-16 Digits',
    notSamePassword: 'Password not matched!',
    moreOneRole: 'Please Select At Least One role'
  },
  roleAdd: {
    pleaseEnterRoleName: 'Please Enter Role\'s Name',
    authorization: 'Authorization',
    pleaseConfigRoleAuth: 'Please Configure Role\'s Authority'
  },
  visitDetail: {
    allImage: 'All Pictures',
    incompleteShelf: 'Incomplete Shelf',
    splicedPicture: 'Result Image',
    storeRuleResults: 'Outlet Rule Result',
    sceneRuleResults: 'Scene Rule Result',
    ourDistributionRate: 'Our SKU Distribution \n Rate',
    ourShortageRate: 'Our SKU Shortage \n Rate',
    ourFacingRate: 'Our SKU Facing Rate',
    otherFacingRate: 'Other SKU Facing \n Rate',
    storeStatisticalAnalysis: 'Outlet Statistical Analysis',
    sceneStatisticalAnalysis: 'Scene Statistical Analysis',
    visitMes: 'Visit Info',
    salesOver: 'Panorama of outlet',
    checkTime: 'Inspection Time',
    storeData: 'Outlet Data',
    storeRule: 'Outlet Rule',
    storeQuestionnaire: 'Outlet Questionnaire',
    allScene: 'All Scenes',
    identifyScene: 'Recognition Result',
    totalCount: 'Altogether',
    accordBrand: 'Brand',
    accordCategory: 'Category',
    accordSku: 'SKU',
    accordPOSM: 'POSM',
    countTitle: 'Number',
    ownCountTitle: '',
    layer: 'Layer',
    priceTag: 'Price Tag',
    showAll: 'Show All',
    sceneQuestionnaire: 'Scene Questionnaire',
    questionnaireResult: 'Questionnaire Result',
    photoArtwork: 'Original Image',
    totalPieces: 'Images Altogether',
    ruleSet: 'Rule Name',
    ruleResult: 'Rule Result',
    allSkuCount: 'Recognition Result',
    total: 'Total ',
    item: '',
    answer: 'Answer',
    withPriceTag: 'With Price Tag',
    haveTag: 'Yes',
    identifyReportTime: 'Recognition Report Time',
    sceneCreateTime: 'Scene Creation Time',
  },
  api: {
    all: 'All',
    cooler: 'Cooler',
    shelf: 'Shelf',
    stack: 'Stack',
    stepInit: 'Init',
    stepImageToCloud: 'Image To Cloud',
    stepIrApiSend: 'Ir Api Send',
    stepIrApiComplete: 'Ir Api Complete',
    stepComplete: 'Completed',
    stepEndClean: 'Cleanup Completed'
  },
  home: {
    proportionOfAbnormalOutlets: 'Proportion Of Abnormal Outlets',
    visitCompletionRate: 'Visit Completion Rate',
    saleVisitedOutlets: 'Visited Outlets',
    plannedVisitOutlets: 'Plan To Visit Outlets',
    abnormalOutlets: 'Abnormal Outlets',
    normalOutlets: 'Normal Outlets',
    averageDailyVisits: 'Average Daily Visits',
    averageSingleStoreVisitTime: 'Average Store Visit Time (Minute)',
    channelType: 'Channel Type',
    regionalOverview: 'Region (Province) Overview',
    visit: 'Visit Detail',
    visited: 'Visited',
    visitPlan: 'Visit Plan',
    hello: 'Hello',
    happyEveryDay: 'I Wish You Happy Every Day!',
    installedDeviceCount: 'Number of Installed Devices',
    storeCount: 'Number of Sales Spots Insights',
    sceneCount: 'Number of Scenes Insights',
    salesTop10: 'Top 10 Sales',
    categorySales: 'Sales Proportion for Category ',
    brandSales: 'Sales Proportion for Brand',
    storeSalesRanking: 'Sales Ranking of SalesSpots',
    storePeopleRanking: 'Foot Traffic Ranking of SalesSpots'
  },
  store: {
    livePreview: 'Live Preview',
    loading: 'Loading',
    noImage: 'There are currently no images available',
    noData: 'Currently No Data',
    storeDetail: 'Outlet Detail',
    store3D: '3D Modeling Of Outlet',
    salesStatistics: 'Sales Statistics',
    deepAnalysis: 'Deep Analysis',
    categorySales: 'Category',
    brandSales: 'Brand',
    selectTime: 'Select Date',
    timeInterval: 'Please select a time interval',
    globalMonitoring: 'Global Monitoring',
    storeName: 'Outlet Name',
    storeAddress: 'Outlet Address',
    perceptionScenarios : 'Detected Scenearios',
    legend: 'Legend',
    goodsShelves: 'Goods Shelves',
    threeDoorFreezer: 'Three Door Freezer',
    colderAir: 'Cold Air Cabinet',
    freezer: 'Freezer',
    horizontalFreezer: 'Chest Freezer',
    cashier: 'Cashier',
    barCounter: 'Bar Counter',
    stackingBox: 'Stacking Box',
    combinedShelves: 'Combined Shelves',
    coldAirCabinet: 'Cold Air Cabinet',
    sales: 'Sales',
    trafficVolume: 'Traffic Volume',
    categorySalesSituation: 'Category',
    brandSalesSituation: 'Brand',
    allCategories: 'All Categories',
    allBrands: 'All Brands',
    jointSalesAnalysis: 'Joint Sales Analysis',
    timesOfAssociation: '',
    countUnit: 'times of association',
    purchaseBottles: 'Purchase',
    bottleUnit: 'bottles',
    realTimeSales: 'Real Time Sales',
    pointHeating: 'Heat Map',
    againstTheWall: 'Against the wall',
    numberOfPhotos: 'Number Of Photos',
    shootingTime: 'Shooting Time',
    behaviorDetails: 'Transaction',
    multipleScenePerspectives: 'Multiple Scene Perspectives',
    sold: 'Sold',
    Replenishment: 'Replenishment',
    Misalignment: 'Misalignment',
    firstOne: 'It\'s already the first one',
    lastOne: 'It\'s already the last one',
    lastOneWeek: 'Last Week',
    lastOneMonth: 'Last Month',
    lastThreeMonth: 'Last Three Months',
    sceneLook: 'Scene Detection',
    timeNoChange: 'There has been no change during this time period',
    selectTimeRange: 'Select a time period',
    basicInfo: 'Basic Information',
    useTool: 'Using Tools',
    powerOn: 'Power Switch',
    temperature: 'Temperature',
    doorOpenCount: 'Door Open/Close',
    position: 'Location',
    trafficSales: 'Traffic/Sales',
    ownCountTitle: '',
    fillColor: 'Fill Color',
    ranking: 'Ranking',
    sku_name: 'SKU Name',
    counts: 'Quantity',
  },
  orthodontic: {
    choseImage: 'Select an image for correction (selected image:',
    correctiveImage: 'Click to generate corrected image',
    correctionMap: 'Generate Image',
    sendReport: 'Click to send recognition',
    imageReport: 'Image Recognition',
  },
  sceneAnnotate: {
    boxSelectAll: 'Box Select All',
  },
  device: {
    addDevice: 'Add New Device',
    editDevice:'Editing Device',
  },
}
