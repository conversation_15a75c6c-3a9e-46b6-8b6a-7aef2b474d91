export default {
  route: {
    home: '首页',
    add: '新增',
    view: '查看',
    edit: '编辑',
    update: '修改',
    enable: '禁用/启用',
    delete: '删除',
    dataImport: '数据上传',
    dataExport: '数据下载',
    dataReset: '数据还原',
    copy: '复制',
    retailEyeSystemName: '运营平台',
    skuManagement: 'SKU管理',
    tenantManagement: '品牌方管理',
    storeWarehouse: '售点管理',
    ownStoreWarehouse: '零眸自有售点库',
    tenantStoreWarehouse: '品牌方售点库',
    storeMap: '售点库映射关系',
    skuWarehouse: 'SKU库',
    ownMasterData: '自有主数据',
    tenantMasterData: '品牌方主数据',
    masterDataMap: '主数据映射关系',
    questionnaireManagement: '问卷管理',
    checkProject: '检查项目',
    checkPlan: '检查计划',
    accountManagement: '账号管理',
    frontAccountManagement: '前台账号管理',
    backendAccountManagement: '后台账号',
    replaceIndex: '更换列表',
    questionIndex: '问题件列表',
    roleManagement: '角色管理',
    visitManagement: '拜访管理',
    visitPlan: '拜访计划',
    cycleManagement: '周期管理',
    visitedEnquiry: '已拜访查询',
    abnormalStoreQuery: '异常门店查询',
    apiResult: 'API',
    appLog: 'APP日志',
    systemConfiguration: '系统配置',
    gateway: '网关',
    checkPlanAdd: '检查计划新增',
    tenantStoreAdd: '品牌方售点新增',
    tenantAdd: '品牌方新增',
    storeReport: '售点报告',
    tenantToView: '品牌方查看',
    tenantEdit: '品牌方编辑',
    checkPlanToView: '检查计划查看',
    checkPlanToEdit: '检查计划编辑',
    checkPlanToAdd: '检查计划新增',
    storeToEdit: '售点编辑',
    storeToAdd: '售点新增',
    apiReport: '识别详情',
    installCheck: '验收详情',
    installCheckDetail: '任务详情',
    deviceList: '登录设备列表',
    deviceCheckM: '设备巡检-月份',
    deviceCheckD: '设备巡检-日期',
    deviceCheckT: '设备巡检-时间',
    operationLog: '操作日志',
    lockAndUnlock: '锁定/解锁',
    sceneReportTime: '场景报告时间',
    gatherCooler: '冰柜情况处理',
    gatherRack: 'QC',
    gatherDiscriminate: '识别处理',
    gatherSales: '销量处理',
    gatherRackSales: '货架销量处理',
    storeInventory: '售点列表',
    taskList: '任务列表',
    sceneList: '场景列表',
    sceneDetail: '售点详情',
    QC: 'QC',
    qcRack: '货架摄像头',
    qcCooler: '冰柜摄像头',
    rackQcDetail: '货架QC详情',
    coolerQcDetail: '冰柜QC详情',
    sceneAnnotate: '场景标注',
    sceneData: '场景数据',
    sceneConfig: '场景配置',
    storeConfig: '售点配置',
    versionConfig: '版本配置',
    storeSceneList: '场景列表',
    coolerQcDiscriminate: '冰柜识别处理',
    gatherImage: '图片处理',
    deviceIndex: '设备列表',
    dashboard: '数据看板',
    installIndex: '安装列表',
    installIndexTask: '安装任务',
    checkIndex: 'QC列表',
    installAppAccount: '设备安装APP帐号',
    metaStoreAccount: 'MetaStore账号',
    qcIndexTask: 'QC列表',
    qcIndexTaskDetail: 'QC详情',
  },
  login: {
    welcomeToUse: '欢迎使用',
    retailEyeSystemName: 'QC平台',
    pleaseEnterUserName: '请输入用户名',
    pleaseEnterPassword: '请输入密码',
    login: '登录'
  },
  navBar: {
    changePassword: '修改密码',
    logout: '退出',
    confirmExit: '确定进行[退出]操作?'

  },
  tagsView: {
    closeCurrentTab: '关闭当前标签页',
    closeOthersTab: '关闭其他标签页',
    closeAllTab: '关闭全部标签页',
    refreshCurrentTab: '刷新当前标签页'
  },
  reaCommon: {
    hasCamera: '是否安装设备',
    fullScene: '是否100%覆盖',
    bindDevType: '安装设备类型',
    sceneLocation: '场景位置',
    sceneDisplayPosition: '场景陈列位置',
    displayPosition: '陈列位置与竞品更靠近人流走向',
    haveGateway: '是否有网关',
    openDirection: '开门方向',
    uploadF: '上传',
    pleaseUploadF: '请上传文件',
    versionInformation: '版本信息',
    configList: '配置列表',
    today: '今天',
    yesterday: '昨天',
    thisWeek: '本周',
    thisMonth: '本月',
    thisQuarter: '本季',
    threeDays: '3天',
    sevenDays: '7天',
    thirtyDays: '30天',
    ninetyDays: '90天',
    subtotal: '小计',
    hotspotInsights: '热点洞察',
    assetNumber: '资产编号',
    versionCodeNumber: '版本编号',
    versionCode: '版本名称',
    currentVersion: '当前使用版本',
    effectiveTime: '数据生效时间',
    effectiveStart: '生效开始时间',
    effectiveStop: '生效结束时间',
    versionDesc: '版本描述',
    publishPersion: '发布人',
    publishTime: '创建时间',
    longTerm: '长期有效',

    versionConfig: '版本配置',
    aveFacing: '平均排面',
    skuTotal: '总销量',
    hotZone: '热区分类',
    numberCount: '热区个数',
    sortName: '排名',
    hotspot: '热点名称',
    sales: '销量',
    sovi: 'SOVI',
    skuID: 'SKU ID',
    selectTime: '选择时间',
    sureDel: '是否确认删除？',
    success: '操作成功',
    passwordNotChange: '请修改密码，否则将无法登录',
    confirmPassword: '确认密码',
    pleaseConfirmPassword: '请确认密码',
    notSamePassword: '两次输入的密码不一致',
    passwordLength: '密码长度在8-16位之间',
    phonePlaceholder: '请输入手机号码',
    isLockOrNot: '是否锁定',
    locked: '已锁定',
    unlocked: '未锁定',
    unlockSuccess: '解锁成功',
    lockSuccess: '锁定成功',
    lockAndUnlock: '锁定/解锁',
    sceneReportTime: '场景报告时间',
    unlock: '解锁',
    lock: '锁定',
    updatePassword: '更新密码',
    passwordSpecialVerify: '密码需包含字母、数字、特殊字符',
    verificationCode: '验证码',
    verificationCodeError: '验证码有误，请重新输入',
    pleaseConfirmNewPassword: '请确认新密码',
    pleaseEnterNewPassword: '请输入新密码',
    passwordRequirements: '请输入新密码（密码长度8-16位，需包含字母、数字、特殊字符）',
    pleaseEnterPhoneReceiveCode: '请输入登录手机号，接收验证码',
    pleaseEnterVerificationCode: '请输入验证码',
    forgotPassword: '忘记密码',
    lastLoginTime: '最后登录时间',
    // grade protection
    operationTime: '操作时间',
    operationAccount: '操作账号',
    operationAuthority: '操作权限',
    operationDate: '日期',
    operationTimeSecond: '时间',
    apiLog: 'API日志',
    systemLogManage: '系统日志管理',
    logConfig: '日志配置',
    clearLog: '清理日志',
    deleteLog: '删除日志',
    operationNode: '操作节点',
    operationBehavior: '操作行为',
    operationDescribe: '操作描述',
    locationAddress: '位置地址',
    locationPosition: '地理位置',
    selectExportCheckProject: '选择导出检查项目',
    taskName: '任务名称',
    dateRange: '日期范围',
    startUseTime: '开始使用时间',
    deviceId: '设备ID',
    deviceLang: '应用设置的语言',
    devicePixelRatio: '设备像素比',
    lastUseTime: '最后使用时间',
    sdkVersion: 'SDK版本',
    appVersion: 'APP版本',
    systemNameAndOSVersion: '系统名称及OS版本',
    switchMap: '切换地图模式',
    switchList: '切换列表模式',
    addGateway: '新增网关配置',
    editGateway: '编辑网关配置',
    gatewayName: '名称',
    projectName: '模型',
    recognitionUrl: '识别接口地址',
    stitchingQueue: '拼接队列名',
    originImages: '拍摄原图',
    skuFacing: 'SKU排面数',
    engineResults: '规则结果',
    videoFailed: '视频出错，无法播放',
    imageFailed: '照片出错',
    sku_rate: 'SKU铺货率',
    sku_face: 'SKU排面占比',
    allRevivificationTxt: '此操作将还原所有SKU数据，是否继续？',
    skuLogo: 'SKU Logo',
    phoneError: '手机号码格式有误，请重新输入',
    batchDeleteWarningPrevTxt: '此操作将删除已勾选的',
    batchDeleteWarningNextTxt: '条数据，是否继续？',
    changeSkuName: '修改SKU名称',
    pleaseEnterSkuName: '请输入SKU名称',
    pleaseLeastOne: '请至少勾选一项',
    checkedItems: '已勾选',
    item: '项',
    revivification: '数据还原',
    skuName: 'SKU名称',
    importDate: '导入日期',
    deviceModel: '设备型号',
    clickPlay: '点击播放',
    export: '导出',
    selectExportField: '选择导出字段',
    pleaseSelectField: '请选择导出字段',
    taskId: '任务ID',
    visitDuration: '拜访时长',
    pleaseSelectCycle: '请选择周期',
    pleaseEnterSelectStore: '请选择售点',
    pleaseEnterToSearch: '请输入售点名称进行搜索',
    visitStatus: '拜访状态',
    visiting: '拜访中',
    completed: '已完成',
    password: '密码',
    loginFail: '登录失效或您的账号已在其它地点登录，请重新登录',
    loginAgain: '重新登录',
    visitPlan: '拜访计划',
    storeCollection: '售点采集',
    imageRecognition: '图像识别',
    ruleEngine: '规则引擎',
    set: '设置',
    download: '下载',
    buildingExcel: '正在生成Excel，请耐心等待。',
    buildSuccess: '生成成功',
    downloadTemplate: '下载模板',
    downloadErrorData: '下载失败数据',
    downloadRepeatData: '下载重复数据',
    note: '注',
    account: '账号',
    originalPassword: '原密码',
    newPassword: '新密码',
    welcomeToUseRE: '欢迎使用REA管理后台',
    back: '返回',
    nextStep: '下一步',
    previousStep: '上一步',
    complete: '完成',
    cancel: '取消',
    confirm: '确认',
    operationSuccess: '操作成功',
    confirmDelete: '确定删除',
    warning: '提示',
    confirmDeleteThisOne: '确定删除这条数据',
    dataImport: '数据上传',
    dataExport: '数据下载',
    setTableTitle: '表头设置',
    allFields: '全部字段',
    addStore: '新增售点',
    spread: '展开',
    fold: '折叠',
    yes: '是',
    no: '否',
    save: '保存',
    submit: '提交',
    templateDownload: '模板下载',
    addUser: '新增用户',
    rapidRetrieval: '快速检索',
    startDate: '开始日期',
    endDate: '结束日期',
    to: '至',
    query: '查询',
    reset: '重置',
    advancedSearch: '高级搜索',
    packUp: '收起',
    status: '状态',
    operation: '操作',
    toView: '查看',
    disable: '禁用',
    normal: '正常',
    enable: '启用',
    edit: '编辑',
    delete: '删除',
    add: '新增',
    disableSuccess: '禁用成功',
    enableSuccess: '启用成功',
    visitorNumber: '走访员编号',
    visitorName: '走访员姓名',
    cycleNumber: '周期编号',
    planStartDate: '计划开始时间',
    planEndDate: '计划结束时间',
    createDate: '创建日期',
    updateDate: '更新时间',
    tenantNameOrId: '品牌名称/ID',
    ConfigModule: '配置模块',
    tenant: '品牌方',
    BU: '所属BU',
    organization: '组织架构',
    storeNameCode: '售点编号/名称',
    provinces: '省份',
    city: '城市',
    county: '区县',
    channelType: '渠道类型',
    storeStatus: '售点状态',
    checkDate: '检查日期',
    checkProject: '检查项目',
    userId: '用户ID',
    phone: '手机号码',
    supplier: '供应商',
    name: '姓名',
    openTime: '创建日期',
    roleName: '角色名称',
    serialNumber: '序号',
    position: '岗位',
    tenantId: '品牌ID',
    tenantName: '品牌名称',
    createTime: '创建日期',
    storeId: '售点ID',
    storeCode: '售点编号',
    storeName: '售点名称',
    storeAddress: '售点地址',
    detailAddress: '详细地址',
    ownerName: '店主姓名',
    ownerTel: '店主手机',
    theContact: '联系人',
    contact: '联系方式',
    scottLong: '高德经度',
    scottLat: '高德纬度',
    baiduLong: '百度经度',
    baiduLat: '百度纬度',
    shopArea: '门店面积',
    hasShopCar: '是否有购物车/购物篮',
    remark: '备注',
    department: '部门',
    checkPlanId: '检查计划ID',
    checkProjectName: '检查项目名称',
    visitedId: '走访ID',
    startTime: '开始时间',
    endTime: '结束时间',
    visitType: '拜访类型',
    visitDate: '拜访日期',
    remainingDays: '剩余天数',
    creator: '创建人',
    uploadFile: '上传文件',
    selectFile: '选择文件',
    uploadAttention: '注：最大支持 8 MB 的文件',
    dataMustSameTemplate: '导入的数据文件需要同模板文件保持一致。',
    clickUpload: '点击上传',
    uploadAgain: '重新上传',
    uploading: '上传中',
    fileSize512M: '文件大小不能超过512M',
    fileFormat: '文件格式不正确',
    effectData: '条有效数据',
    repeatData: '条重复数据',
    errorData: '条失败数据',
    close: '关闭',
    addPlan: '新增计划',
    copy: '复制',
    copyVisitPlan: '复制拜访计划',
    editVisitPlan: '编辑拜访计划',
    unfixedDate: '不固定日期',
    fixedDate: '固定日期',
    cycleName: '周期名称',
    cycleTime: '周期时间',
    addCycle: '新增周期',
    wheneverWhereverVisit: '随店随访',
    inTheShopTime: '在店时长',
    less5Minutes: '5分钟以下',
    to10Minutes: '5-10分钟',
    to40Minutes: '10-40分钟',
    moreThan40Minutes: '40分钟以上',
    visitTime: '走访时间',
    intoStoreTime: '进店时间',
    leaveStoreTime: '离店时间',
    doorImage: '门头照',
    video: '视频',
    sceneData: '场景数据',
    errorType: '异常类型',
    errorReason: '异常原因',
    picture: '照片',
    errorDescription: '异常点说明',
    pleaseEnter: '请输入',
    pleaseSelect: '请选择',
    cashNumber: '收银机台数',
    scannerNumber: '扫描机台数',
    isEnabled: '已启用',
    isNotEnabled: '未启用',
    isDisabled: '已禁用',
    noData: '暂无数据',
    deleteSuccess: '删除成功',
    pcc: '省/市/区',
    completeSet: '完成设置',
    editUser: '编辑用户',
    resetPassword: '重置密码',
    resetPasswordSuccess: '密码重置成功',
    confirmCopyData: '确定复制这条数据',
    selectDate: '选择日期',
    copySuccess: '复制成功',
    editCycle: '编辑周期',
    cycle: '周期',
    questionnaireDimension: '问卷维度',
    isIrQuestionnaire: '是否IR问卷',
    questionnaireIdOrName: '问卷ID/题干',
    questionnaireName: '问卷题干',
    questionnaireType: '问卷题型',
    completion: '填空题',
    choice: '选择题',
    yesNoQuestion: '是非题',
    questionnaireScene: '问卷场景',
    questionnaireId: '问卷ID',
    questionnaireNamePlaceholder: '请输入问卷题干',
    questionnaireTypePlaceholder: '请选择问卷题型',
    questionnaireDimensionPlaceholder: '请选择问卷维度',
    isIrQuestionnairePlaceholder: '请选择是否IR问卷',
    pleaseEnterAnswerType: '请勾选答案类型',
    answerType: '答案类型',
    anyValue: '任意值',
    integer: '整数',
    percent: '两位小数',
    pleaseSelectQuestionnaireScene: '请选择问卷场景',
    saveUse: '保存并启用',
    checkProjectIdName: '检查项目ID/名称',
    checkProjectId: '检查项目ID',
    storeQuestionnaire: '售点问卷',
    sceneQuestionnaire: '场景问卷',
    option: '选项',
    store: '售点',
    scene: '场景',
    scenePlaceholder: '请选择场景',
    oops: '哎呀',
    backHome: '返回首页',
    notFindView: '找不到这个页面...',
    copyright: '版权所有',
    reaName: '零眸智能',
    checkUrlRight: '请检查您输入的网址是否正确，或单击下面的按钮返回主页',
    username: '用户名',
    role: '角色',
    priceTag: '价签',
    showAll: '显示所有',
    deviceTotCount: '设备总数',
    // TODO
    taskStatus: '任务状态',
    issueStatus: '问题件状态',
    methodStatus: '处理方式',
    installUser: '安装人员',
    checkUser: '验收人员',
    handelUser: '处理人员',
    checkView: '验收',
    handelTime: '处理时间',

    sceneTotCount: '场景总数',
    storeConfig: '售点配置',
    config: '配置',
    sceneImage: '场景图片',
    sceneType: '场景类型',
    isBindDev: '是否绑定设备',
    isBindPlan: '是否绑定平面图',
    deviceCode: '设备编号',
    takePhotos: '拍照时间',
    deviceStatus: '设备状态',
    deviceInStatus: '设备安装状态',
    belongStoreNameCode: '所属售点编号',
    qcStatus: 'QC状态',
    storeInventory: '售点列表',
    taskList: '任务列表',
    channel: '所属渠道',
    sort: '排序',
    sceneName: '场景名称',
    sceneSort: '场景排序',
    uploadSceneImage: '上传场景图片',
    pleaseUploadSceneImage: '请上传场景图片',
    sceneConfig: '场景配置',
    deviceGroup: '设备组',
    storeImage: '售点平面图',
    storeImageConfig: '平面图配置',
    noSceneInfo: '暂无此设备绑定场景信息',
    alsoSceneConfig: '已配置场景',
    thisDeviceGroup: '此设备组',
    otherDeviceGroup: '其他设备组',
    sureUnlinkScene: '是否需要解绑此场景，重新关联?',
    alsoLinkDevice: '已关联设备',
    pleaseBindData: '请提交绑定数据',
    number: '编号',
    deviceName: '设备名称',
    QCStartTime: 'QC开始时间',
    QCFinishTime: 'QC完成时间',
    gatherSales: '销量处理',
    deviceInfo: '设备信息',
    SKUList: 'SKU列表',
    count: '数量',
    outList: '拿取列表',
    inList: '放回列表',
    pleaseSelectSKU: '请选择SKU',
    extraBox: '超出边界',
    openAtTime: '开门时间',
    closeAtTime: '关门时间',
    gatherDiscriminate: '识别处理',
    gatherImage: '图片处理',
    updateSales: '修改销量',
    discriminateReport: '识别结果',
    downloadVideo: '下载视频',
    imageFrame: '抽帧图',
    correctImage: '矫正图',
    generateCurrentImage: '生成矫正图',
    uploadCurrentImage: '上传矫正图',
    belongStore: '所属售点',
    storeSearchKey: '售点编号或名称',
    deviceNumber: '设备编号',
    createdTime: '创建时间',
    installFinishTime: '安装完成时间',
    taskFinishTime: '任务完成时间',
    deviceType: '设备类型',
    voltage: '电压值',
    // 任务列表
    taskEndTime: '任务截止时间',
    timeAskDetail: '具体时间要求',
    storeName2: '企业名称',
    taskDes: '任务描述',
    isdisGroup: '是否分组',
    taskDisGroup: '任务分组',
    auditStatus: '审核状态',
    finishTime: '完成时间',
    checkUser: '审核人',
  },
  tenantAdd: {
    photoModePlaceholder: '请至少选择一种拍照模式',
    frontCameraMode: '自拍模式',
    videoAnalysis: '视频分析',
    VRMode: 'VR模式',
    ARMode: 'AR模式',
    flowMode: '光流模式',
    rearCameraMode: '普通模式',
    photoMode: '拍照模式',
    openInspectionPlan: '开启检查计划',
    modelMonitor: '置信度监控',
    withDisplay: '地堆背景过滤',
    withShelfBackgroundDisplay: '货架背景过滤',
    withBottles: '单瓶抑制',
    chooseLeastOne: '至少选择一种拜访类型',
    checkPlanEffect: '检查计划配置生效',
    meters: '米',
    tenantAdd: '品牌新增',
    smsCodeEnabled: '开启短信验证码',
    scenereportTimeEnabled: '开启场景报告时间',
    pleaseSelectModule: '请选择品牌方需要的模块并设置',
    storeCollection: '售点采集',
    imageRecognition: '图像识别',
    ruleEngine: '规则引擎',
    completeConfiguration: '完成配置',
    tenantName: '品牌名称',
    tenantNamePlaceholder: '请输入品牌名称',
    collectWay: '采集方式',
    regionType: '地域',
    regionOverseas: '海外',
    regionChina: '中国',
    cameraGroup: '开启SDK分组',
    scene: '场景',
    scenePlaceholder: '请选择场景',
    cloudStorage: '云存储',
    cloudStoragePlaceholder: '请选择云存储方式',
    skuMasterDataIndustry: 'SKU主数据行业',
    IRMasterDataSource: 'SKU主数据来源',
    IRMasterData: 'IR主数据',
    REAMappingTable: 'REA映射表',
    skuMasterDataIndustryPlaceholder: '请选择SKU主数据行业',
    identifyConfig: '识别配置',
    priceTag: '价签',
    recognitionModel: '识别模型',
    recognitionModelPlaceholder: '请选择识别模型',
    successfulImage: '成功图像',
    acquisitionMode: '采集模式',
    acquisitionModePlaceholder: '至少选择一项采集模式',
    manualMode: '手动模式',
    irMode: 'IR模式',
    checkProjectTackEffect: '检查项目配置生效',
    instant: '即时',
    planToVisit: '计划拜访',
    wheneverWhereverVisit: '随店随访',
    doorImage: '门头照',
    noPictures: '不拍',
    takePictures: '必拍',
    selectPictures: '选拍',
    doorVideo: '门头视频',
    signIn: '签到',
    SignInEffectiveDistance: '签到有效距离',
    SignInEffectiveDistancePlaceholder: '请输入签到有效距离',
    organization: '组织架构',
    organizationConfig: '配置组织架构',
    checkOrganizationConfig: '查看组织架构',
    editOrganizationConfig: '编辑组织架构',
    levelOneDepartment: '一级部门',
    addLevelOneDepartment: '添加一级部门',
    addSameDepartment: '添加同级部门',
    addChildDepartment: '添加下级部门',
    departmentName: '部门名称',
    deleteDepartment: '删除部门',
    changeDepartmentName: '修改部门名称',
    departmentNamePlaceholder: '请输入部门名称',
    sameNameLevelOneDepartment: '已存在相同名称的一级部门',
    sameNameSameLevelDepartment: '已存在相同名称的同级部门',
    sameNameChildDepartment: '已存在相同名称的子部门',
    collectModuleWarning: '采集模块必选',
    planModuleWarning: '拜访计划模块必选',
    changeName: '修改名称',
    haveChild: '当前部门存在子部门，请先删除子部门',
    department: '部门',
    retailEye: '零眸',
    cos: '腾讯云',
    blob: '微软云',
    pleaseEnter: '请输入',
    pleaseConfigOrganization: '请配置组织架构',
    detectPlatform: '选择配置平台',
    pleasedetectPlatform: '请选择配置平台',
    basicDetectPlatform: '基础配置',
    omniDetectPlatform: 'OMNI配置',
    flowId: '流程编号',
    pleaseFlowId: '请输入流程编号',
    apiUrl: '识别接口地址',
    pleaseApiUrl: '请输入识别接口地址',
    apiSecret: '签名密钥',
    pleaseApiSecret: '请输入签名密钥',
    linkHelp: '请联系算法部提供'

  },
  checkPlanAdd: {
    article: '条',
    checkPlanAdd: '检查计划新增',
    configProjectTime: '配置项目及时间',
    configStore: '配置售点',
    tenant: '品牌',
    tenantPlaceholder: '请选择品牌',
    checkProject: '检查项目',
    checkProjectPlaceholder: '请选择检查项目',
    checkDate: '检查日期',
    checkDatePlaceholder: '请选择检查日期',
    addStore: '添加售点',
    addDepartment: '请选择部门',
    importStore: '导入售点',
    alreadyImportStore: '已导入售点',
    organizationStore: '组织架构售点',
    deleteStore: '删除售点',
    batchDeleteStore: '批量删除售点',
    checkCount: '查看数量',
    downloadStore: '下载已导入售点',
    screenStore: '您已筛选',
    storeCount: '个售点'
    // TODO
  },
  frontUserAdd: {
    belongTenant: '所属品牌',
    belongTenantPlaceholder: '请选择所属品牌',
    belongStore: '所属售点',
    belongStorePlaceholder: '请选择所属售点',
    namePlaceholder: '请输入姓名',
    phonePlaceholder: '请输入手机号码',
    positionPlaceholder: '请输入岗位',
    normal: '正常',
    disabled: '禁用'
  },
  backendUserAdd: {
    username: '用户名',
    password: '密码',
    confirmPassword: '确认密码',
    pleaseConfirmPassword: '请确认密码',
    passwordLength: '密码长度在8-16位之间',
    notSamePassword: '两次输入的密码不一致',
    moreOneRole: '请至少选择一个角色'
  },
  roleAdd: {
    pleaseEnterRoleName: '请输入角色名称',
    authorization: '授权',
    pleaseConfigRoleAuth: '请配置角色权限'
  },
  visitDetail: {

    incompleteShelf: '不完整层',
    splicedPicture: '拼接结果图',
    storeRuleResults: '售点规则结果',
    sceneRuleResults: '场景规则结果',
    ourDistributionRate: '我司SKU铺货率',
    ourShortageRate: '我司SKU缺货率',
    ourFacingRate: '我司SKU排面占比',
    otherFacingRate: '其他SKU排面占比',
    storeStatisticalAnalysis: '售点统计分析',
    sceneStatisticalAnalysis: '场景统计分析',
    visitMes: '拜访信息',
    salesOver: '售点全景',
    checkTime: '检查时间',
    storeData: '售点数据',
    storeRule: '售点规则',
    storeQuestionnaire: '售点问卷',
    allScene: '所有场景',
    identifyScene: '识别场景',
    totalCount: '共',
    accordBrand: '按品牌',
    accordCategory: '按品类',
    accordSku: '按SKU',
    accordPOSM: '按POSM',
    countTitle: '数量（个）',
    ownCountTitle: '个',
    layer: '所在层',
    priceTag: '价签',
    showAll: '显示所有',
    sceneQuestionnaire: '场景问卷',
    questionnaireResult: '问卷结果',
    photoArtwork: '拍照原图',
    totalPieces: '共',
    ruleSet: '规则名称',
    ruleResult: '规则结果',
    allSkuCount: '识别结果',
    total: '共',
    item: '项',
    answer: '答',
    withPriceTag: '有价签',
    haveTag: '有',
    identifyReportTime: '识别报告时间',
    sceneCreateTime: '场景创建时间',
    allImage: '所有图片'
  },
  api: {
    all: '全部',
    cooler: '冰柜',
    shelf: '货架',
    stack: '地堆',
    stepInit: '初始状态',
    stepImageToCloud: '图片同步到云',
    stepIrApiSend: '已送识别',
    stepIrApiComplete: '识别出结果',
    stepComplete: '已完成',
    stepEndClean: '已完成，且完成了内部数据清理'
  },
  home: {
    proportionOfAbnormalOutlets: '异常售点占比',
    visitCompletionRate: '走访完成率',
    saleVisitedOutlets: '已走访售点 (家)',
    plannedVisitOutlets: '计划走访售点 (家)',
    abnormalOutlets: '异常售点 (家)',
    normalOutlets: '正常售点 (家)',
    averageDailyVisits: '日均走访数 (家)',
    averageSingleStoreVisitTime: '平均单店走访时长 (分钟)',
    channelType: '渠道类型',
    regionalOverview: '地区 (省) 概况',
    visit: '走访情况',
    visited: '已走访',
    visitPlan: '走访计划',
    hello: '你好',
    happyEveryDay: '祝你开心每一天！',
    installedDeviceCount: '已安装设备数',
    storeCount: '洞察售点数',
    sceneCount: '洞察场景数',
    salesTop10: '销量 TOP 10',
    categorySales: '品类销量占比',
    brandSales: '品牌销量占比',
    storeSalesRanking: '售点销售排名（个）',
    storePeopleRanking: '售点人流量排名（位）'
  },
  store: {
    livePreview: '实时预览',
    loading: '加载中',
    noImage: '暂无图片',
    noData: '暂无数据',
    storeDetail: '售点详情',
    store3D: '售点3D建模',
    salesStatistics: '销售统计',
    deepAnalysis: '深度分析',
    categorySales: '品类销量',
    brandSales: '品牌销量',
    selectTime: '选择时间',
    timeInterval: '请选择时间区间',
    globalMonitoring: '全域监测',
    storeName: '售点名称',
    storeAddress: '售点地址',
    perceptionScenarios: '感知场景数',
    legend: '图例',
    goodsShelves: '货架',
    freezer: '冰柜',
    threeDoorFreezer: '三门冰柜',
    colderAir: '冷风柜',
    horizontalFreezer: '卧式冰柜',
    cashier: '收银台',
    barCounter: '吧台',
    stackingBox: '堆箱',
    combinedShelves: '组合货架',
    coldAirCabinet: '冷风柜',
    sales: '销量',
    trafficVolume: '人流量',
    categorySalesSituation: '品类销售情况',
    brandSalesSituation: '品牌销售情况',
    allCategories: '全品类',
    allBrands: '全品牌',
    jointSalesAnalysis: '连带销售分析',
    timesOfAssociation: '连带次数',
    countUnit: '次',
    purchaseBottles: '购买',
    bottleUnit: '瓶',
    realTimeSales: '实时销售',
    pointHeating: '点位热力',
    againstTheWall: '靠墙',
    numberOfPhotos: '照片张数',
    shootingTime: '拍摄时间',
    behaviorDetails: '行为明细',
    multipleScenePerspectives: '多场景视角',
    sold: '售出',
    Replenishment: '补货',
    Misalignment: '错位',
    firstOne: '已经是第一张了',
    lastOne: '已经是最后一张了',
    lastOneWeek: '最近一周',
    lastOneMonth: '最近一个月',
    lastThreeMonth: '最近三个月',
    sceneLook: '场景检测',
    timeNoChange: '此时间段没有变化',
    selectTimeRange: '选择时间段',
    basicInfo: '基础信息',
    useTool: '运用工具',
    powerOn: '电源开关',
    temperature: '温度',
    doorOpenCount: '开关门次数',
    position: '坐标',
    trafficSales: '人流量/销量',
    ownCountTitle: '个',
    fillColor: '填充颜色',
    ranking: '排行',
    sku_name: 'SKU 名称',
    counts: '数量'
  },
  orthodontic: {
    choseImage: '选择一张需要矫正的图片（已选中图',
    correctiveImage: '点击按钮，生成矫正图片',
    correctionMap: '生成矫正图',
    sendReport: '点击按钮，送识别',
    imageReport: '图片识别'
  },
  sceneAnnotate: {
    boxSelectAll: '框选全部'
  },
  device: {
    addDevice: '新建设备',
    editDevice: '编辑设备'
  }

}
