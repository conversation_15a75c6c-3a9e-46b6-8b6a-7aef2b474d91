import Vue from 'vue'
import VueI18n from 'vue-i18n'
import Storage from 'storejs'
import ElementLocale from 'element-ui/lib/locale'
import elementEnLocale from 'element-ui/lib/locale/lang/en.js' // element-ui lang
import elementZhLocale from 'element-ui/lib/locale/lang/zh-CN.js'// element-ui lang
import elementHkLocale from 'element-ui/lib/locale/lang/zh-TW.js'// element-ui lang
import enLocale from './en'
import zhLocale from './zh'
import hkLocale from './hk'

Vue.use(VueI18n)

const messages = {
  'en': {
    ...enLocale,
    ...elementEnLocale
  },
  'zh-Hans': {
    ...zhLocale,
    ...elementZhLocale
  },
  'zh-Hant': {
    ...hkLocale,
    ...elementHkLocale
  }
}

export function getLanguage() {
  const chooseLanguage = Storage.get('language')
  if (chooseLanguage) return chooseLanguage

  // if has not choose language
  const language = (navigator.language || navigator.browserLanguage).toLowerCase()
  const locales = Object.keys(messages)
  for (const locale of locales) {
    if (language.indexOf(locale) > -1) {
      return locale
    }
  }
  return 'zh-Hans'
}

ElementLocale.i18n((key, value) => i18n.t(key, value))
const i18n = new VueI18n({
  // set locale
  // options: en | zh | es
  locale: getLanguage(),
  // set locale messages
  messages
})

export default i18n
