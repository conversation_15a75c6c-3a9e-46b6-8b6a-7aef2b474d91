export default {
  route: {
    home: '首頁',
    add: '新增',
    view: '查看',
    edit: '編輯',
    update: '修改',
    enable: '禁用/啟用',
    delete: '删除',
    dataImport: '數據上傳',
    dataExport: '數據下載',
    dataReset: '數據還原',
    copy: '複製',
    retailEyeSystemName: '运营平台',
    skuManagement: 'SKU管理',
    tenantManagement: '品牌方管理',
    storeWarehouse: '售點管理',
    ownStoreWarehouse: '零眸自有售點庫',
    tenantStoreWarehouse: '品牌方售點庫',
    storeMap: '售點庫映射關係',
    skuWarehouse: 'SKU庫',
    ownMasterData: '自有主數據',
    tenantMasterData: '品牌方主數據',
    masterDataMap: '主數據映射關係',
    questionnaireManagement: '問卷管理',
    checkProject: '檢查項目',
    checkPlan: '檢查計畫',
    accountManagement: '帳號管理',
    frontAccountManagement: '前臺帳號管理',
    backendAccountManagement: '後臺帳號管理',
    replaceIndex: '更换列表',
    questionIndex: '问题件列表',
    roleManagement: '角色管理',
    visitManagement: '拜訪管理',
    visitPlan: '拜訪計畫',
    cycleManagement: '週期管理',
    visitedEnquiry: '已拜訪査詢',
    abnormalStoreQuery: '异常門店査詢',
    apiResult: 'API',
    appLog: 'APP日誌',
    systemConfiguration: '系統配寘',
    gateway: '閘道',

    checkPlanAdd: '檢查計畫新增',
    tenantStoreAdd: '品牌方售點新增',
    tenantAdd: '品牌方新增',
    storeReport: '售點報告',
    tenantToView: '品牌方查看',
    tenantEdit: '品牌方編輯',
    checkPlanToView: '檢查計畫查看',
    checkPlanToEdit: '檢查計畫編輯',
    checkPlanToAdd: '檢查計畫新增',
    storeToEdit: '售點編輯',
    storeToAdd: '售點新增',
    apiReport: '識別詳情',
    deviceList: '登入設備清單',
    operationLog: '操作日誌',
    lockAndUnlock: '鎖定/解鎖',
    sceneReportTime: '場景報告時間',
    gatherCooler: '冰櫃情况處理',
    gatherRack: 'QC',
    gatherDiscriminate: '識別處理',
    gatherSales: '銷量處理',
    gatherRackSales: '貨架銷量處理',
    storeInventory: '售點清單',
    sceneList: '場景清單',
    sceneDetail: '售點詳情',
    QC: 'QC',
    qcRack: '貨架監視器',
    qcCooler: '冰櫃監視器',
    rackQcDetail: '貨架QC詳情',
    coolerQcDetail: '冰櫃QC詳情',
    sceneAnnotate: '場景標注',
    sceneData: '場景數據',
    sceneConfig: '場景配寘',
    storeConfig: '售點配寘',
    versionConfig: '版本配置',
    storeSceneList: '場景清單',
    coolerQcDiscriminate: '冰櫃識別處理',
    gatherImage: '圖片處理',
    deviceIndex: '設備列表',
    dashboard: '数据看板',
  },
  login: {
    welcomeToUse: '歡迎使用',
    retailEyeSystemName: '运营平台',
    pleaseEnterUserName: '請輸入用戶名',
    pleaseEnterPassword: '請輸入密碼',
    login: '登錄'
  },
  navBar: {
    changePassword: '修改密碼',
    logout: '退出',
    confirmExit: '確定進行[退出]操作？'

  },
  tagsView: {
    closeCurrentTab: '關閉當前標籤頁',
    closeOthersTab: '關閉其他標籤頁',
    closeAllTab: '關閉全部標籤頁',
    refreshCurrentTab: '重繪當前標籤頁'
  },
  reaCommon: {
    hasCamera: '是否安装设备',
    fullScene: '是否全店覆盖',
    versionConfig: '版本配置',
    aveFacing: '平均排面',
    numberCount: '个数',
    skuTotal: 'SKU总数',
    hotZone: '热区分类',
    sortName: '排名',
    hotspot: '热点名称',
    sales: '销量',
    sovi: 'SOVI',
    skuID: 'SKU ID',
    selectTime: '選擇時間',
    sureDel: '是否確認删除？',
    success: '操作成功',
    passwordNotChange: '請修改密碼，否則將無法登入',
    confirmPassword: '確認密碼',
    pleaseConfirmPassword: '請確認密碼',
    notSamePassword: '兩次輸入的密碼不一致',
    passwordLength: '密碼長度在8-16比特之間',
    phonePlaceholder: '请输入手机号码',
    isLockOrNot: '是否鎖定',
    locked: '已鎖定',
    unlocked: '未鎖定',
    unlockSuccess: '解鎖成功',
    lockSuccess: '鎖定成功',
    lockAndUnlock: '鎖定/解鎖',
    sceneReportTime: '場景報告時間',
    unlock: '解鎖',
    lock: '鎖定',
    updatePassword: '更新密碼',
    passwordSpecialVerify: '密碼需包含字母、數位、特殊字元',
    verificationCode: '驗證碼',
    verificationCodeError: '驗證碼有誤，請重新輸入',
    pleaseConfirmNewPassword: '請確認新密碼',
    pleaseEnterNewPassword: '請輸入新密碼',
    passwordRequirements: '請輸入新密碼（密碼長度8-16比特，需包含字母、數位、特殊字元）',
    pleaseEnterPhoneReceiveCode: '請輸入登入手機號，接收驗證碼',
    pleaseEnterVerificationCode: '請輸入驗證碼',
    forgotPassword: '忘記密碼',
    lastLoginTime: '最後登錄時間',
    // grade protection
    operationTime: '操作時間',
    operationAccount: '操作帳號',
    operationAuthority: '操作許可權',
    operationDate: '日期',
    operationTimeSecond: '時間',
    apiLog: 'API日誌',
    systemLogManage: '系統日誌管理',
    logConfig: '日誌配寘',
    clearLog: '清理日誌',
    deleteLog: '删除日誌',
    operationNode: '操作節點',
    operationBehavior: '操作行為',
    operationDescribe: '操作描述',
    locationAddress: '位置地址',
    locationPosition: '地理位置',
    selectExportCheckProject: '選擇匯出檢查項目',
    taskName: '任务名称',
    dateRange: '日期範圍',
    startUseTime: '開始使用時間',
    deviceId: '設備ID',
    deviceLang: '應用設定的語言',
    devicePixelRatio: '設備點數比',
    lastUseTime: '最後使用時間',
    sdkVersion: 'SDK版本',
    appVersion: 'APP版本',
    systemNameAndOSVersion: '系統名稱及OS版本',
    switchMap: '切換地圖模式',
    switchList: '切換清單模式',
    addGateway: '新增閘道配寘',
    editGateway: '編輯閘道配寘',
    gatewayName: '名稱',
    projectName: '模型',
    recognitionUrl: '識別介面地址',
    stitchingQueue: '拼接隊列名',
    originImages: '拍攝原圖',
    skuFacing: 'SKU排面數',
    engineResults: '規則結果',
    videoFailed: '視頻出錯，無法播放',
    imageFailed: '照片出錯',
    sku_rate: 'SKU鋪貨率',
    sku_face: 'SKU排面占比',
    allRevivificationTxt: '此操作將還原所有SKU數據，是否繼續？',
    skuLogo: 'SKU Logo',
    phoneError: '手機號碼格式有誤，請重新輸入',
    batchDeleteWarningPrevTxt: '此操作將删除已勾選的',
    batchDeleteWarningNextTxt: '條數據，是否繼續？',
    changeSkuName: '修改SKU名稱',
    pleaseEnterSkuName: '請輸入SKU名稱',
    pleaseLeastOne: '請至少勾選一項',
    checkedItems: '已勾選',
    item: '項',
    revivification: '數據還原',
    skuName: 'SKU名稱',
    importDate: '導入日期',
    deviceModel: '設備型號',
    clickPlay: '點擊播放',
    export: '匯出',
    selectExportField: '選擇匯出欄位',
    pleaseSelectField: '請選擇匯出欄位',
    taskId: '任務ID',
    visitDuration: '拜訪時長',
    pleaseSelectCycle: '請選擇週期',
    pleaseEnterSelectStore: '請選擇售點',
    pleaseEnterToSearch: '請輸入售點名稱進行蒐索',
    visitStatus: '拜訪狀態',
    visiting: '拜訪中',
    completed: '已完成',
    password: '密碼',
    loginFail: '登入失效或您的帳號已在其它地點登入，請重新登入',
    loginAgain: '重新登入',
    visitPlan: '拜訪計畫',
    storeCollection: '售點採集',
    imageRecognition: '圖像識別',
    ruleEngine: '規則引擎',
    set: '設定',
    download: '下載',
    buildingExcel: '正在生成Excel，請耐心等待。',
    buildSuccess: '生成成功',
    downloadTemplate: '下載範本',
    downloadErrorData: '下載失敗數據',
    downloadRepeatData: '下載重複數據',
    note: '注',
    account: '帳號',
    originalPassword: '原密碼',
    newPassword: '新密碼',
    welcomeToUseRE: '歡迎使用REA管理後臺',
    back: '返回',
    nextStep: '下一步',
    previousStep: '上一步',
    complete: '完成',
    cancel: '取消',
    confirm: '確認',
    operationSuccess: '操作成功',
    confirmDelete: '確定删除',
    warning: '提示',
    confirmDeleteThisOne: '確定删除這條數據',
    dataImport: '數據上傳',
    dataExport: '數據下載',
    setTableTitle: '表頭設定',
    allFields: '全部欄位',
    addStore: '新增售點',
    spread: '展開',
    fold: '折疊',
    yes: '是',
    no: '否',
    save: '保存',
    submit: '提交',
    templateDownload: '範本下載',
    addUser: '新增用戶',
    rapidRetrieval: '快速檢索',
    startDate: '開始日期',
    endDate: '結束日期',
    to: '至',
    query: '査詢',
    reset: '重置',
    advancedSearch: '高級搜索',
    packUp: '收起',
    status: '狀態',
    operation: '操作',
    toView: '查看',
    disable: '禁用',
    normal: '正常',
    enable: '啟用',
    edit: '編輯',
    delete: '删除',
    add: '新增',
    disableSuccess: '禁用成功',
    enableSuccess: '啟用成功',
    visitorNumber: '走訪員編號',
    visitorName: '走訪員姓名',
    cycleNumber: '週期編號',
    planStartDate: '計畫開始時間',
    planEndDate: '計畫結束時間',
    createDate: '創建日期',
    tenantNameOrId: '品牌名稱/ID',
    ConfigModule: '配寘模塊',
    tenant: '品牌方',
    organization: '組織架構',
    storeNameCode: '售點編號/名稱',
    provinces: '省份',
    city: '都市',
    county: '區縣',
    channelType: '通路類型',
    storeStatus: '售點狀態',
    checkDate: '檢查日期',
    checkProject: '檢查項目',
    userId: '用戶ID',
    phone: '手機號碼',
    name: '姓名',
    openTime: '創建日期',
    roleName: '角色名稱',
    serialNumber: '序號',
    position: '崗位',
    tenantId: '品牌ID',
    tenantName: '品牌名稱',
    createTime: '創建日期',
    storeId: '售點ID',
    storeCode: '售點編號',
    storeName: '售點名稱',
    storeAddress: '售點地址',
    ownerName: '店主姓名',
    ownerTel: '店主手機',
    theContact: '連絡人',
    contact: '聯繫方式',
    scottLong: '高德經度',
    scottLat: '高德緯度',
    baiduLong: '百度經度',
    baiduLat: '百度緯度',
    shopArea: '門店面積',
    hasShopCar: '是否有購物車/購物籃',
    remark: '備註',
    updateDate: '更新日期',
    department: '部門',
    checkPlanId: '檢查計畫ID',
    checkProjectName: '檢查項目名稱',
    visitedId: '走訪ID',
    startTime: '開始時間',
    endTime: '結束時間',
    visitType: '拜訪類型',
    visitDate: '拜訪日期',
    remainingDays: '剩餘天數',
    creator: '創建人',
    uploadFile: '上傳文件',
    selectFile: '選擇檔案',
    uploadAttention: '注：最大支持8 MB 的檔案',
    dataMustSameTemplate: '導入的資料檔案需要同範本檔案保持一致。',
    uploadAgain: '重新上傳',
    uploading: '上傳中',
    fileSize512M: '文件大小不能超过512M',
    fileFormat: '文件格式不正确',
    effectData: '條有效數據',
    repeatData: '條重複數據',
    errorData: '條失敗數據',
    close: '關閉',
    addPlan: '新增計畫',
    copy: '複製',
    copyVisitPlan: '複製拜訪計畫',
    editVisitPlan: '編輯拜訪計畫',
    unfixedDate: '不固定日期',
    fixedDate: '固定日期',
    cycleName: '週期名稱',
    cycleTime: '週期時間',
    addCycle: '新增週期',
    wheneverWhereverVisit: '隨店隨訪',
    inTheShopTime: '在店時長',
    less5Minutes: '5分鐘以下',
    to10Minutes: '5-10分鐘',
    to40Minutes: '10-40分鐘',
    moreThan40Minutes: '40分鐘以上',
    visitTime: '走訪時間',
    intoStoreTime: '進店時間',
    leaveStoreTime: '離店時間',
    doorImage: '門頭照',
    video: '視頻',
    sceneData: '場景數據',
    errorType: '异常類型',
    errorReason: '异常原因',
    picture: '照片',
    errorDescription: '异常點說明',
    pleaseEnter: '請輸入',
    pleaseSelect: '請選擇',
    cashNumber: '收銀機台數',
    scannerNumber: '掃描機台數',
    isEnabled: '已啟用',
    isNotEnabled: '未啟用',
    isDisabled: '已禁用',
    noData: '暫無數據',
    deleteSuccess: '删除成功',
    pcc: '省/市/區',
    completeSet: '完成設定',
    editUser: '編輯用戶',
    resetPassword: '重置密码',
    resetPasswordSuccess: '密碼重置成功',
    confirmCopyData: '確定複製這條數據',
    selectDate: '選擇日期',
    copySuccess: '複製成功',
    editCycle: '編輯週期',
    cycle: '週期',
    questionnaireDimension: '問卷維度',
    isIrQuestionnaire: '是否IR問卷',
    questionnaireIdOrName: '問卷ID/題幹',
    questionnaireName: '问卷題幹',
    questionnaireType: '問卷題型',
    completion: '填空題',
    choice: '選擇題',
    yesNoQuestion: '是非題',
    questionnaireScene: '問卷場景',
    questionnaireId: '問卷ID',
    questionnaireNamePlaceholder: '請輸入問卷題幹',
    questionnaireTypePlaceholder: '請選擇問卷題型',
    questionnaireDimensionPlaceholder: '請選擇問卷維度',
    isIrQuestionnairePlaceholder: '請選擇是否IR問卷',
    pleaseEnterAnswerType: '請勾選答案類型',
    answerType: '答案類型',
    anyValue: '任意值',
    integer: '整數',
    percent: '兩位小數',
    pleaseSelectQuestionnaireScene: '請選擇問卷場景',
    saveUse: '保存並啟用',
    checkProjectIdName: '檢查項目ID/名稱',
    checkProjectId: '檢查項目ID',
    storeQuestionnaire: '售點問卷',
    sceneQuestionnaire: '場景問卷',
    option: '選項',
    store: '售點',
    scene: '場景',
    scenePlaceholder: '請選擇場景',
    oops: '哎呀',
    backHome: '返回首頁',
    notFindView: '找不到這個頁面...',
    copyright: '版權所有',
    reaName: '零眸智能',
    checkUrlRight: '請檢查您輸入的網址是否正確，或按一下下麵的按鈕返回主頁',
    username: '用戶名',
    role: '角色',
    priceTag: '價簽',
    showAll: '顯示所有',
    deviceTotCount: '設備總數',
    sceneTotCount: '場景總數',
    storeConfig: '售點配寘',
    config: '配寘',
    sceneImage: '場景圖片',
    sceneType: '場景類型',
    isBindDev: '是否綁定設備',
    isBindPlan: '是否綁定平面圖',
    deviceCode: '設備編號',
    takePhotos: '拍照時間',
    deviceStatus: '設備狀態',
    qcStatus: 'QC狀態',
    storeInventory: '售點清單',
    channel: '所屬通路',
    sort: '排序',
    sceneName: '場景名稱',
    sceneSort: '場景排序',
    uploadSceneImage: '上傳場景圖片',
    pleaseUploadSceneImage: '請上傳場景圖片',
    sceneConfig: '場景配寘',
    deviceGroup: '設備組',
    storeImage: '售點平面圖',
    storeImageConfig: '平面圖配寘',
    noSceneInfo: '暫無此設備綁定場景資訊',
    alsoSceneConfig: '已配寘場景',
    thisDeviceGroup: '此設備組',
    otherDeviceGroup: '其他設備組',
    sureUnlinkScene: '是否需要解綁此場景，重新關聯？',
    alsoLinkDevice: '已關聯設備',
    pleaseBindData: '請提交綁定數據',
    number: '編號',
    deviceName: '設備名稱',
    QCStartTime: 'QC開始時間',
    QCFinishTime: 'QC完成時間',
    gatherSales: '銷量處理',
    deviceInfo: '設備資訊',
    SKUList: 'SKU清單',
    count: '數量',
    outList: '拿取清單',
    inList: '放回清單',
    pleaseSelectSKU: '請選擇SKU',
    extraBox: '超出邊界',
    openAtTime: '開門時間',
    closeAtTime: '關門時間',
    gatherDiscriminate: '識別處理',
    gatherImage: '圖片處理',
    updateSales: '修改銷量',
    discriminateReport: '識別結果',
    downloadVideo: '下載視頻',
    imageFrame: '抽幀圖',
    correctImage: '矯正圖',
    generateCurrentImage: '生成矯正圖',
    uploadCurrentImage: '上傳矯正圖',
    belongStore: '所屬售點',
    deviceNumber: '設備編號',
    createdTime: '創建時間',
    ranking: '排行',
    sku_name: 'SKU 名称',
    counts: '数量',
    installFinishTime: '安装完成时间'
  },
  tenantAdd: {
    photoModePlaceholder: '請至少選擇一種拍照模式',
    frontCameraMode: '自拍模式',
    videoAnalysis: '視頻分析',
    VRMode: 'VR模式',
    ARMode: 'AR模式',
    flowMode: '光流模式',
    rearCameraMode: '普通模式',
    photoMode: '拍照模式',
    openInspectionPlan: '開啟檢查計畫',
    modelMonitor: '置信度監控',
    withDisplay: '地堆背景過濾',
    withShelfBackgroundDisplay: '貨架背景過濾',
    withBottles: '單瓶抑制',
    chooseLeastOne: '至少選擇一種拜訪類型',
    checkPlanEffect: '檢查計畫配寘生效',
    meters: '米',
    tenantAdd: '品牌新增',
    smsCodeEnabled: '開啟簡訊驗證碼',
    scenereportTimeEnabled: '開啟場景報告時間',
    pleaseSelectModule: '請選擇品牌方需要的模塊並設定',
    storeCollection: '售點採集',
    imageRecognition: '圖像識別',
    ruleEngine: '規則引擎',
    completeConfiguration: '完成配寘',
    tenantName: '品牌名稱',
    tenantNamePlaceholder: '請輸入品牌名稱',
    collectWay: '採集管道',
    regionType: '地域',
    regionOverseas: '海外',
    cameraGroup: '開啟SDK分組',
    regionChina: '中國',
    scene: '場景',
    scenePlaceholder: '請選擇場景',
    cloudStorage: '雲存儲',
    cloudStoragePlaceholder: '請選擇雲存儲管道',
    skuMasterDataIndustry: 'SKU主數據行業',
    IRMasterDataSource: 'SKU主數據來源',
    IRMasterData: 'IR主數據',
    REAMappingTable: 'REA映射錶',
    skuMasterDataIndustryPlaceholder: '請選擇SKU主數據行業',
    identifyConfig: '識別配寘',
    priceTag: '價簽',
    recognitionModel: '識別模型',
    recognitionModelPlaceholder: '請選擇識別模型',
    successfulImage: '成功影像',
    acquisitionMode: '採集模式',
    acquisitionModePlaceholder: '至少選擇一項採集模式',
    manualMode: '手動模式',
    irMode: 'IR模式',
    checkProjectTackEffect: '檢查項目配寘生效',
    instant: '即時',
    planToVisit: '計畫拜訪',
    wheneverWhereverVisit: '隨店隨訪',
    doorImage: '門頭照',
    noPictures: '不拍',
    takePictures: '必拍',
    selectPictures: '選拍',
    doorVideo: '門頭視頻',
    signIn: '簽到',
    SignInEffectiveDistance: '簽到有效距離',
    SignInEffectiveDistancePlaceholder: '請輸入簽到有效距離',
    organization: '組織架構',
    organizationConfig: '配寘組織架構',
    checkOrganizationConfig: '查看組織架構',
    editOrganizationConfig: '編輯組織架構',
    levelOneDepartment: '一級部門',
    addLevelOneDepartment: '添加一級部門',
    addSameDepartment: '添加同級部門',
    addChildDepartment: '添加下級部門',
    departmentName: '部門名稱',
    deleteDepartment: '删除部門',
    changeDepartmentName: '修改部門名稱',
    departmentNamePlaceholder: '請輸入部門名稱',
    sameNameLevelOneDepartment: '已存在相同名稱的一級部門',
    sameNameSameLevelDepartment: '已存在相同名稱的同級部門',
    sameNameChildDepartment: '已存在相同名稱的子部門',
    collectModuleWarning: '採集模塊必選',
    planModuleWarning: '拜訪計畫模塊必選',
    changeName: '修改名稱',
    haveChild: '當前部門存在子部門，請先删除子部門',
    department: '部門',
    retailEye: '零眸',
    cos: '騰訊雲',
    blob: '微軟雲',
    pleaseEnter: '請輸入',
    pleaseConfigOrganization: '請配寘組織架構',
    detectPlatform: '選擇配寘平臺',
    pleasedetectPlatform: '請選擇配寘平臺',
    basicDetectPlatform: '基礎配寘',
    omniDetectPlatform: 'OMNI配寘',
    flowId: '流程編號',
    pleaseFlowId: '請輸入流程編號',
    apiUrl: '識別介面地址',
    pleaseApiUrl: '請輸入識別介面地址',
    apiSecret: '簽名金鑰',
    pleaseApiSecret: '請輸入簽名金鑰',
    linkHelp: '請聯系算灋部提供'
  },
  checkPlanAdd: {
    article: '條',
    checkPlanAdd: '檢查計畫新增',
    configProjectTime: '配寘項目及時間',
    configStore: '配寘售點',
    tenant: '品牌',
    tenantPlaceholder: '請選擇品牌',
    checkProject: '檢查項目',
    checkProjectPlaceholder: '请选择检查项目',
    checkDate: '檢查日期',
    checkDatePlaceholder: '請選擇檢查日期',
    addStore: '添加售點',
    addDepartment: '請選擇部門',
    importStore: '導入售點',
    alreadyImportStore: '已導入售點',
    organizationStore: '組織架構售點',
    deleteStore: '删除售點',
    batchDeleteStore: '批量删除售點',
    checkCount: '查看數量',
    downloadStore: '下載已導入售點',
    screenStore: '您已篩選',
    storeCount: '個售點',
  },
  frontUserAdd: {
    belongTenant: '所屬品牌',
    belongTenantPlaceholder: '請選擇所屬品牌',
    belongStore: '所属售点',
    belongStorePlaceholder: '请选择所属售点',
    namePlaceholder: '請輸入姓名',
    phonePlaceholder: '請輸入手機號碼',
    positionPlaceholder: '請輸入崗位',
    normal: '正常',
    disabled: '禁用'
  },
  backendUserAdd: {
    username: '用戶名',
    password: '密碼',
    confirmPassword: '確認密碼',
    pleaseConfirmPassword: '請確認密碼',
    passwordLength: '密碼長度在8-16比特之間',
    notSamePassword: '兩次輸入的密碼不一致',
    moreOneRole: '請至少選擇一個角色'
  },
  roleAdd: {
    pleaseEnterRoleName: '請輸入角色名稱',
    authorization: '授權',
    pleaseConfigRoleAuth: '請配寘角色許可權'
  },
  visitDetail: {
    allImage: '所有图片',
    incompleteShelf: '不完整層',
    splicedPicture: '拼接結果圖',
    storeRuleResults: '售點規則結果',
    sceneRuleResults: '場景規則結果',
    ourDistributionRate: '我司SKU鋪貨率',
    ourShortageRate: '我司SKU缺貨率',
    ourFacingRate: '我司SKU排面占比',
    otherFacingRate: '其他SKU排面占比',
    storeStatisticalAnalysis: '售點統計分析',
    sceneStatisticalAnalysis: '場景統計分析',
    visitMes: '拜訪資訊',
    salesOver: '售點全景',
    checkTime: '檢查時間',
    storeData: '售點數據',
    storeRule: '售點規則',
    storeQuestionnaire: '售點問卷',
    allScene: '所有場景',
    identifyScene: '識別場景',
    totalCount: '共',
    accordBrand: '按品牌',
    accordCategory: '按品類',
    accordPOSM: '按POSM',
    accordSku: '按SKU',
    countTitle: '數量（個）',
    ownCountTitle: '個',
    layer: '所在層',
    priceTag: '價簽',
    showAll: '顯示所有',
    sceneQuestionnaire: '場景問卷',
    questionnaireResult: '問卷結果',
    photoArtwork: '拍照原圖',
    totalPieces: '共',
    ruleSet: '規則名稱',
    ruleResult: '規則結果',
    allSkuCount: '識別結果',
    total: '共',
    item: '項',
    answer: '答',
    withPriceTag: '有價簽',
    haveTag: '有',
    identifyReportTime: '識別報告時間',
    sceneCreateTime: '場景創建時間',
  },
  api: {
    all: '全部',
    cooler: '冰櫃',
    shelf: '貨架',
    stack: '地堆',
    stepInit: '初始狀態',
    stepImageToCloud: '圖片同步到雲',
    stepIrApiSend: '已送識別',
    stepIrApiComplete: '識別出結果',
    stepComplete: '已完成',
    stepEndClean: '已完成，且完成了內部數據清理'
  },
  home: {
    proportionOfAbnormalOutlets: '异常售點占比',
    visitCompletionRate: '走訪完成率',
    saleVisitedOutlets: '已走訪售點 (家)',
    plannedVisitOutlets: '計畫走訪售點 (家)',
    abnormalOutlets: '异常售點 (家)',
    normalOutlets: '正常售點 (家)',
    averageDailyVisits: '日均走訪數 (家)',
    averageSingleStoreVisitTime: '平均單店走訪時長 (分鐘)',
    channelType: '通路類型',
    regionalOverview: '地區 (省) 概况',
    visit: '走訪情况',
    visited: '已走訪',
    visitPlan: '走訪計畫',
    hello: '你好',
    happyEveryDay: '祝你開心每一天！',
    installedDeviceCount: '已安裝設備數',
    storeCount: '見解店數',
    sceneCount: '見解場景數',
    salesTop10: '銷售 TOP 10',
    categorySales: '品類銷售佔比',
    brandSales: '品牌銷售佔比',
    storeSalesRanking: '店鋪銷售排名（個）',
    storePeopleRanking: '店鋪客流排名（位）'
  },
  store: {
    livePreview: '实时预览',
    loading: '加載中',
    noImage: '暫無圖片',
    noData: '暫無數據',
    storeDetail: '售點詳情',
    store3D: '售點3D建模',
    salesStatistics: '銷售統計',
    deepAnalysis: '深度分析',
    categorySales: '品類銷量',
    brandSales: '品牌銷量',
    selectTime: '選擇時間',
    timeInterval: '請選擇時間區間',
    globalMonitoring: '全域監測',
    storeName: '售點名稱',
    storeAddress: '售點地址',
    perceptionScenarios: '感知場景數',
    legend: '圖例',
    goodsShelves: '貨架',
    threeDoorFreezer: '三門冰櫃',
    colderAir: '冷風櫃',
    horizontalFreezer: '臥式冰櫃',
    cashier: '收銀台',
    barCounter: '吧台',
    stackingBox: '堆箱',
    combinedShelves: '組合貨架',
    coldAirCabinet: '冷風櫃',
    sales: '銷量',
    trafficVolume: '人流量',
    categorySalesSituation: '品類銷售情况',
    brandSalesSituation: '品牌銷售情况',
    allCategories: '全品類',
    allBrands: '全品牌',
    jointSalesAnalysis: '連帶銷售分析',
    timesOfAssociation: '連帶次數',
    countUnit: '次',
    purchaseBottles: '購買',
    bottleUnit: '瓶',
    realTimeSales: '實时銷售',
    pointHeating: '點比特熱力',
    againstTheWall: '靠牆',
    numberOfPhotos: '照片張數',
    shootingTime: '拍攝時間',
    behaviorDetails: '行為明細',
    multipleScenePerspectives: '多場景視角',
    sold: '售出',
    Replenishment: '補貨',
    Misalignment: '錯位',
    firstOne: '已經是第一張了',
    lastOne: '已經是最後一張了',
    lastOneWeek: '最近一周',
    lastOneMonth: '最近一個月',
    lastThreeMonth: '最近三個月',
    sceneLook: '場景檢測',
    timeNoChange: '此時間段沒有變化',
    selectTimeRange: '選擇時間段',
    basicInfo: '基礎資訊',
    useTool: '運用工具',
    powerOn: '電源開關',
    temperature: '溫度',
    doorOpenCount: '開關門次數',
    position: '座標',
    trafficSales: '人流量/銷量',
    ownCountTitle: '個',
    fillColor: '填充顏色',
  },
  orthodontic: {
    choseImage: '選擇一張需要矯正的圖片（已選中圖',
    correctiveImage: '點擊按鈕，生成校正圖片',
    correctionMap: "生成校正地圖",
    sendReport: "點擊按鈕，送達識別",
    imageReport: "圖片識別報告"
  },
  sceneAnnotate: {
    boxSelectAll: '框选全部',
  },
  device: {
    addDevice: '新增設備',
    editDevice:'編輯設備',
  },
}
