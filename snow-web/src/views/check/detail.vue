<template>
  <div class="checkdetail__container page_container account_frontend_view" v-loading="isLoading">
    <div class="flex-y-center mb10">
      <div class="head__back" style="margin-bottom: 0" @click="backCloseViewFuns"><span
          class="el-icon-arrow-left"></span>返回</div>
      <div class="flex-1 flex-x-end">
        <el-button type="danger" :disabled="!isEditFinish" size="small" @click="submitQc(false)"
          :loading="isSubmitFalse">不合格</el-button>
        <el-button type="primary" :disabled="!isEditFinish" size="small" @click="submitQc(true)"
          :loading="isSubmitSure">合格</el-button>
        <el-button type="danger" v-if="isEditDeny" :disabled="!isEditProcess" size="small" @click="submitQcDeny(false)"
          :loading="isSubmitFalse">驳回审核</el-button>
        <el-button type="primary" :disabled="!isEditProcess" size="small" @click="submitQcPass(true)"
          :loading="isSubmitSure">提交审核</el-button>
      </div>
    </div>
    <div class="store_mes wbg wbg_shadow">
      <div class="flex store_mes_col flex-center">
        <div class="flex-1">
          <span>售点名称：</span>{{ storeDetail.store_name || '-' }}
        </div>
        <div class="flex-1">
          <span>任务ID：</span>{{ storeDetail.task_id || '-' }}
        </div>
        <div class="flex-1">
          <span>执行人：</span>{{ storeDetail.executor || '-' }}
        </div>
        <div class="flex-1">
          <span>联系方式<i class="phone">后4位</i>：</span>{{ storeDetail.visitor_phone_suffix || '-' }}
        </div>
        <div class="flex-1 thm" v-if="![1, 2].includes(storeDetail.audit_status)">
          <span>审核状态：</span>{{ storeDetail.audit_status_msg || '-' }}
        </div>
        <!-- <div class="flex-1 thm" v-if="![1,2].includes(storeDetail.audit_status)||true">
          <span>审核状态：</span>{{ !storeDetail.audit_status}}
        </div> -->
        <!-- <div class="flex-1 thm flex flex-row justify-center items-center">
          <span style="display: inline-block;width: 132px;line-height: 32px;">审核备注：</span>
          <el-input size="small" v-model="storeDetail.audit_desc"></el-input>
        </div> -->
      </div>
    </div>
    <div class="rec__warp--box material__square">
      <div class="flex-y-center justify-between" style="justify-content: space-between;">
        <div class="title">审核日志</div>
        <div class="flex-y-center">
          <div class="mr10 view-all-btn">查看全部</div>
          <div>
            <el-switch v-model="isExpendAll" active-color="#0471d4">
            </el-switch>
          </div>
        </div>
      </div>
      <div class="table_box wbg">
        <template v-if="auditLogs && auditLogs.length">
          <div class="audit-log-item" v-for="(item, index) in auditLogs" :key="index">
            <div class="audit-log-header" @click="toggleAuditLog(index)">
              <div class="audit-header-left">
                <span class="audit-user">{{ item.admin_position }} {{ item.admin_name }} 审核于 {{
                  formatDate(item.audit_time) }}</span>
                <div class="audit-log-content">
                  {{ item.audit_desc }}
                </div>
              </div>
              <div class="toggle-btn">
                <i :class="expandedLogs.includes(index) ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
              </div>
              <span class="audit-result" :class="item.audit_value ? 'audit-pass' : 'audit-reject'">{{ item.audit_value ?
                '审核通过' : '审核驳回' }}</span>
            </div>
            <transition name="fade">
              <div v-show="expandedLogs.includes(index)" class="audit-log-details">
                <!-- SKU信息展示区域 -->
                <div class="sku-info-container">
                  <template v-for="(scene, index) in item.op_log.long_shot">
                    <div class="sku-category-row mb-4">
                      <div class="flex items-center justify-center w-40 border-r p-4">
                        <p class="scene-txt">全景图 - {{ scene.scene_name }}</p>
                      </div>

                      <div class="flex-1 flex flex-row sku-box">
                        <!-- 新增SKU -->
                        <div v-if="scene.added && scene.added.length" class="flex flex-row add-sku">
                          <div class="tit">新增SKU</div>
                          <div class="grid grid-cols-3 gap-4">
                            <div v-for="(sku, index) in scene.added" :key="index" class="">
                              <div class="sku-item">SKU名称：<span class="mt-1">{{ sku.sku_name || '无' }}</span></div>
                            </div>
                          </div>
                        </div>

                        <!-- 删除SKU -->
                        <div v-if="scene.deleted && scene.deleted.length" class="flex flex-row detele-sku">
                          <div class="tit">删除SKU</div>
                          <div class="grid grid-cols-3 gap-4">
                            <div v-for="(sku, index) in scene.deleted" :key="index">
                              <div class="sku-item">SKU名称：<span class="mt-1">{{ sku.sku_name || '无' }}</span></div>
                            </div>
                          </div>
                        </div>

                        <!-- 修改SKU -->
                        <div v-if="scene.modified && scene.modified.length" class="flex flex-row edit-sku pl30">
                          <div class="tit">编辑SKU</div>
                          <div class="grid grid-cols-3 gap-4">
                            <div v-for="(sku, index) in scene.modified" :key="index" class="">
                              <div class="flex items-center sku-item" v-if="sku.before_sku_name">
                                <div class="text-gray-500">{{ sku.sku_name }}：</div>
                                <div class="line-through text-gray-400 ml-2">{{ sku.before_sku_name || sku.sku_name }}</div>
                                <div class="text-green-500 ml-4 mr-4"> &nbsp;&nbsp;→&nbsp;&nbsp;</div>
                                <div class="ml-2">{{ sku.before_sku_name || sku.sku_name }}</div>
                              </div>
                              <div class="flex items-center sku-item">
                                <div>{{ sku.sku_name }}：</div>
                                <div class="text-gray-500 mr-1">数量&nbsp;</div>
                                <div class="line-through text-gray-400 ml-2">{{ sku.before_num || '无' }}&nbsp;</div>
                                <div class="text-green-500 ml-4 mr-4"> &nbsp;&nbsp;→&nbsp;&nbsp; </div>
                                <div class="ml-2">&nbsp;{{ sku.after_num || '无' }}</div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <!-- 空状态修改SKU占位 -->
                        <div v-else-if="scene.modified !== undefined" class="flex flex-row edit-sku pl30"
                          style="min-height: 40px;">
                          <!-- <div class="tit">编辑SKU</div> -->
                          <div class="grid grid-cols-3 gap-4">
                            <div class="empty-modified-placeholder"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </template>

                  <!-- 特写图修改信息 -->
                  <template v-if="item.op_log.close_up && item.op_log.close_up.length">
                    <div v-for="(closeup, closeupIndex) in item.op_log.close_up" :key="closeupIndex"
                      class="sku-category-row" style="padding:0">
                      <div class="flex items-center justify-center" style="padding-left:24px">
                        <p class="scene-txt">特写图 - 组{{ closeup.item_index + 1 }}</p>
                      </div>

                      <div class="flex-1 p-6">
                        <div v-if="closeup.modified && closeup.modified.length">
                          <div class="grid grid-cols-1 gap-4">
                            <div v-for="(mod, index) in closeup.modified" :key="index" class="flex flex-row"
                              style="padding-left:32px;padding-top:16px">
                              <div v-if="mod.before_sku_name || mod.sku_name" class="flex flex-row items-center sku-item">
                                <div class="text-gray-500 mr-4">SKU名称：</div>
                                <div class="line-through text-gray-400">{{ mod.before_sku_name || mod.sku_name  }}</div>
                                <div class="text-green-500 ml-4 mr-4">→</div>
                                <div class="ml-2">{{ mod.after_sku_name || mod.sku_name }}</div>
                              </div>
                              <div v-if="mod.before_date || mod.after_date" class="flex flex-row items-center sku-item">
                                <div class="text-gray-500 mr-4">生产日期：</div>
                                <div class="line-through text-gray-400 ml-2">{{ mod.before_date || '无' }}</div>
                                <div class="text-green-500 ml-4 mr-4">&nbsp;&nbsp;→&nbsp;&nbsp;</div>
                                <div class="ml-2">{{ mod.after_date || '无' }}</div>
                              </div>
                              <div v-if="mod.before_price !== undefined || mod.after_price !== undefined"
                                class="flex flex-row items-center sku-item">
                                <div class="text-gray-500 mr-4">价格：</div>
                                <div class="line-through text-gray-400 ml-2">{{ mod.before_price || '无' }}</div>
                                <div class="text-green-500 ml-4 mr-4">&nbsp;→&nbsp;</div>
                                <div class="ml-2">{{ mod.after_price || '无' }}</div>
                              </div>
                              <div v-if="mod.before_inventory !== undefined || mod.after_inventory !== undefined"
                                class="flex flex-row items-center sku-item">
                                <div class="tmr-4gray-500 mr-4">库存：</div>
                                <div class="line-through text-gray-400">{{ mod.before_inventory || '无' }}</div>
                                <div class="text-green-500 ml-4 mr-4">&nbsp;→&nbsp;</div>
                                <div class="ml-2">{{ mod.after_inventory || '无' }}</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </template>

                  <!-- 菜单图修改信息 -->
                  <template v-if="item.op_log.menu && item.op_log.menu.length">
                    <div v-for="(menu, menuIndex) in item.op_log.menu" :key="menuIndex" class="sku-category-row mb-4">
                      <div class="flex items-center justify-center w-40 border-r p-4">
                        <p class="scene-txt">菜单图</p>
                      </div>

                      <div class="flex-1 flex flex-row sku-box" style="margin-left: 32px;">
                        <!-- 新增SKU -->
                        <div v-if="menu.added && menu.added.length" class="flex flex-row add-sku">
                          <div class="tit">新增SKU</div>
                          <div class="grid grid-cols-3 gap-4">
                            <div v-for="(sku, index) in menu.added" :key="index" class="">
                              <div class="sku-item">SKU名称：<span class="mt-1">{{ sku.sku_name }}</span></div>
                            </div>
                          </div>
                        </div>

                        <!-- 删除SKU -->
                        <div v-if="menu.deleted && menu.deleted.length" class="flex flex-row detele-sku">
                          <div class="tit">删除SKU</div>
                          <div class="grid grid-cols-3 gap-4">
                            <div v-for="(sku, index) in menu.deleted" :key="index">
                              <div class="sku-item">SKU名称：<span class="mt-1">{{ sku.sku_name || '无' }}</span></div>
                            </div>
                          </div>
                        </div>

                        <!-- 修改SKU -->
                        <div v-if="menu.modified && menu.modified.length" class="flex flex-row edit-sku pl30">
                          <div class="tit">编辑SKU</div>
                          <div class="grid grid-cols-3 gap-4">
                            <div v-for="(sku, index) in menu.modified" :key="index" class="">
                              <div class="flex items-center sku-item">
                                <div class="text-gray-500">{{ sku.before_name }}：</div>
                                <div class="line-through text-gray-400 ml-2">{{ sku.before_name }}</div>
                                <div class="text-green-500 ml-4 mr-4"> &nbsp;→&nbsp; </div>
                                <div class="ml-2">{{ sku.after_name }}</div>
                              </div>
                              <div class="flex items-center sku-item">
                                <div>{{ sku.before_name }}：</div>
                                <div class="text-gray-500 mr-4">数量&nbsp;</div>
                                <div class="line-through text-gray-400">{{ sku.before_num || '无'}}&nbsp;</div>
                                <div class="text-green-500 ml-4 mr-4"> &nbsp;&nbsp;→&nbsp;&nbsp; </div>
                                <div class="ml-2">&nbsp;{{ sku.after_num || '无' }}</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
            </transition>
            <!-- <div class="audit-log-divider"></div> -->
          </div>
        </template>
        <el-empty v-else description="暂无数据"></el-empty>
      </div>
    </div>
    <div class="rec__warp--box">
      <div class="title">全景图<span>识别结果</span></div>
      <div class="table_box wbg ">
        <template v-if="sceneList.length">
          <div class="flex relative">
            <el-tabs class="flex-1" v-model="activeName" @tab-click="handleClick">
              <el-tab-pane :label="item.scene_name" :name="index + ''" v-for="(item, index) in sceneList"
                :key="'scene_' + index"></el-tab-pane>
            </el-tabs>
            <div class="opt__all flex-center">
              <div class="flex-y-center mr10">
                <div class="mr10">查看原图</div>
                <div><el-switch v-model="isOriginal" active-color="#0471d4">
                  </el-switch></div>
              </div>
              <div class="flex-y-center">
                <div class="mr10">显示所有</div>
                <div><el-switch @change="showChange" v-model="isShowAll" active-color="#0471d4">
                  </el-switch></div>
              </div>
            </div>
          </div>
          <template v-if="sceneList[activeName]">
            <div class="flex rec__warp">
              <!--          <div class="flex-1 ir__image"><el-image :src="sceneList[activeName].image" alt=""></el-image> </div>-->
              <div class="flex-1 ir__image" ref="imageBox" v-loading="imageLoading"
                :element-loading-text="$t('store.loading')" element-loading-spinner="el-icon-loading"
                element-loading-background="transparent">
                <HotFigure :canvasW="imageBoxW" :canvasH="imageBoxH" @imageFinshed="imageFinshed" ref="planeFigure">
                </HotFigure>
              </div>
              <div class="ir__skulist">
                <el-form ref="form" :disabled="!isEditProcess" label-width="100px" size="small">
                  <el-table max-height="500" :data="sceneList[activeName].sku_list" @row-click="highlightSku"
                    highlight-current-row>
                    <el-table-column fixed prop="sku_name" label="SKU名称">
                      <template slot-scope="scope">
                        <!-- 非编辑状态下显示SKU名称 -->
                        <div v-if="!isEditProcess">
                          <span v-if="scope.row.sku_id">
                            {{ getSkuNameById(scope.row.sku_id) || scope.row.sku_id }}
                          </span>
                          <span v-else>
                            {{ scope.row.out_sku_name || '-' }}
                          </span>
                        </div>
                        <!-- 编辑状态下显示选择器 -->
                        <div v-else>
                          <div v-if="!scope.row.sku_id" style="cursor: pointer;">
                            <scroll-select v-model="scope.row.out_sku_name" :list="enumSkuList" label="name" value="code"
                              :placeholder="'请选择或输入自定义SKU'" :keeps-params="10" :is-concat="true" :concat-symbol="' || '"
                              :is-multiple="false" :disabled="false" :allow-create="true" @change="(val) => handleSkuChange(val, scope.row)" />
                          </div>
                          <div v-else style="cursor: pointer;">
                            <scroll-select v-model="scope.row.sku_id" :list="enumSkuList" label="name" value="code"
                              :placeholder="'请选择或输入自定义SKU'" :keeps-params="10" :is-concat="true" :concat-symbol="' || '"
                              :is-multiple="false" :disabled="false" :allow-create="true" @change="(val) => handleSkuChange(val, scope.row)" />
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="sku_num" label="数量" width="180">
                      <template slot-scope="scope">
                        <el-input-number placeholder="请输入" size="small" v-model="scope.row.sku_num"
                          controls-position="right" @change="handleChange" :min="0" :max="999"></el-input-number>
                      </template>
                    </el-table-column>
                    <el-table-column prop="name" label="" width="80">
                      <template slot-scope="scope">
                        <el-link type="primary" :disabled="!isEditProcess" :underline="false"
                          @click="deleteSku(100, scope.$index)">删除</el-link>
                      </template>
                    </el-table-column>
                  </el-table>
                  <div class="add__sku" v-if="isEditProcess" @click="addSku(100)"><span
                      class="el-icon-plus"></span>添加SKU</div>
                </el-form>
              </div>
            </div>
            <div class="original__image--box" v-if="isOriginal && sceneList[activeName].images_cos">
              <el-image :preview-src-list="getPrivewImages(sceneList[activeName].images_cos, index)"
                :key="'original_' + index" v-for="(item, index) in sceneList[activeName].images_cos"
                class="original__image" fit="contain" :src="item + '?imageMogr2/thumbnail/!50p'"></el-image>
            </div>
          </template>
        </template>
        <template v-else>
          <el-empty description="暂无数据"></el-empty>
        </template>
      </div>
    </div>
    <div class="rec__warp--box material__square">
      <div class="title">特写图</div>
      <div class="table_box wbg">
        <template v-if="skuImageData.length">
          <el-tabs v-model="activeNameSmall" @tab-click="handleClick">
            <el-tab-pane :label="'组'+(index+1)" :name="index+''" v-for="(item, index) in skuImageData" :key="index"></el-tab-pane>
          </el-tabs>
          <div class="flex rec__warp" v-if="skuImageData[activeNameSmall] && skuImageData[activeNameSmall].images_group">
          <div class="flex-1 ir__image">
            <el-image fit="contain" :preview-src-list="getPrivewImages(skuImageData[activeNameSmall].images_group, index)" class="img" v-for="(item, index) in skuImageData[activeNameSmall].images_group" :key="index" :src="item+'?imageMogr2/thumbnail/!50p'"></el-image>
          </div>
          <div class="ir__skulist">
            <el-form ref="form"  label-width="100px" size="small" :disabled="!isEditProcess">
              <el-form-item label="SKU名称：">
                <scroll-select
                    v-model="skuImageData[activeNameSmall].sku_id"
                    :list="enumSkuList"
                    label="name"
                    value="code"
                    :placeholder="'请选择'"
                    :keeps-params="10"
                    :is-concat="true"
                    :concat-symbol="' || '"
                    :is-multiple="false"
                    :disabled="false"
                    :allow-create="false"
                />
              </el-form-item>
              <el-form-item label="生产日期：">
                <div class="original__box" >
                  <el-date-picker  class="w100p"  type="date" value-format="yyyy-MM-dd" placeholder="选择日期" v-model="skuImageData[activeNameSmall].date" style="width: 100%;"></el-date-picker>
                  <div class="flex-center mt5">
<!--                    <div class="c66">来源：</div>-->
                    <el-select class="flex-1" filterable clearable v-model="skuImageData[activeNameSmall].date_source" placeholder="请选择生产日期来源">
                      <el-option
                          v-for="item in dateSource"
                          :key="item.code"
                          :label="item.name"
                          :value="item.code">
                      </el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="价格：">
                <div class="original__box" >
                  <el-input-number placeholder="请输入" class="w100p" v-model="skuImageData[activeNameSmall].price" controls-position="right" @change="handleChange" :min="0" :max="999"></el-input-number>
                  <div class="flex-center mt5">
                    <el-select class="flex-1" filterable clearable v-model="skuImageData[activeNameSmall].price_source" placeholder="请选择价格来源">
                      <el-option
                          v-for="item in priceSource"
                          :key="item.code"
                          :label="item.name"
                          :value="item.code">
                      </el-option>
                    </el-select>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="库存：">
                <el-input-number placeholder="请输入" class="w100p" v-model="skuImageData[activeNameSmall].inventory" controls-position="right" @change="handleChange" :min="0" :max="999"></el-input-number>
              </el-form-item>
            </el-form>
          </div>
        </div>
        </template>
        <el-empty v-else description="暂无数据"></el-empty>
      </div>
    </div>
    <div class="rec__warp--box material__square">
      <div class="title">菜单图</div>
      <div class="table_box wbg ">
        <div class="flex rec__warp" v-if="skuMenuData.menu_images && skuMenuData.menu_images.length">
          <div class="flex-1 ir__image">
            <template  v-if="skuMenuData.menu_images">
              <el-image  fit="contain" :preview-src-list="getPrivewImages(skuMenuData.menu_images, index)"  class="img" v-for="(item, index) in skuMenuData.menu_images" :key="index" :src="item+'?imageMogr2/thumbnail/!50p'"></el-image>
            </template>
          </div>
          <div class="ir__skulist">
            <el-form ref="form"  label-width="100px" size="small" :disabled="!isEditProcess">
              <el-table
                  max-height="300"
                  :data="skuMenuData.menu_skulist"
              >
                <el-table-column
                    fixed
                    prop="sku_name"
                    label="SKU名称"
                >
                  <template slot-scope="scope">
                    <div v-if="!scope.row.sku_id" style="cursor: pointer;">
                        <scroll-select v-model="scope.row.out_sku_name" :list="enumSkuList" label="name" value="code"
                          :placeholder="'请选择或输入自定义SKU'" :keeps-params="10" :is-concat="true" :concat-symbol="' || '"
                          :is-multiple="false" :disabled="false" :allow-create="true" />
                    </div>
                    <div v-else style="cursor: pointer;">
                        <scroll-select v-model="scope.row.sku_id" :list="enumSkuList" label="name" value="code"
                          :placeholder="'请选择或输入自定义SKU'" :keeps-params="10" :is-concat="true" :concat-symbol="' || '"
                          :is-multiple="false" :disabled="false" :allow-create="true" />
                    </div>
                    <!-- <scroll-select
                        v-model="scope.row.sku_id"
                        :list="enumSkuList"
                        label="name"
                        value="code"
                        :placeholder="'请选择'"
                        :keeps-params="10"
                        :is-concat="true"
                        :concat-symbol="' || '"
                        :is-multiple="false"
                        :disabled="false"
                        :allow-create="false"
                    /> -->
                  </template>
                </el-table-column>
                <el-table-column
                    prop="price"
                    label="价格"
                    width="180">
                  <template slot-scope="scope">
                    <el-input-number placeholder="请输入" size="small" v-model="scope.row.price" controls-position="right" @change="handleChange" :min="0" :max="999"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column
                    prop="name"
                    label=""
                    width="80"
                >
                  <template slot-scope="scope">
                    <el-link type="primary" :disabled="!isEditProcess" :underline="false"
                      @click="deleteSku(300, scope.$index)">删除</el-link>
                  </template>
                </el-table-column>
              </el-table>
              <div class="add__sku" v-if="isEditProcess" @click="addSku(300)"><span class="el-icon-plus"></span>添加SKU
              </div>
            </el-form>
          </div>
        </div>
        <el-empty v-else description="暂无数据"></el-empty>
      </div>
    </div>
    <div class="rec__warp--box material__square">
      <div class="title">录音</div>
      <div class="table_box wbg flex-wrap--div">
        <!-- 循环渲染每个音频文件 -->
        <div class="file__item flex-center" v-for="(item, index) in audioFiles" :key="index">
          <audio controls>
            <source :src="item" type="audio/mpeg">
            您的浏览器不支持 audio 元素。
          </audio>
        </div>
      </div>
    </div>
    <el-backtop></el-backtop>
  </div>
</template>
<script>
import ScrollSelect from '@/components/SelectMore';
import { mapGetters } from 'vuex'
import HotFigure from '@/components/HotFigure'
import { recognitionInfo, recognitionQcFinish, recognitionQcReject, recognitionQcPass, recognitionQcStart, recognitionQcInfoHistory, recognitionQcCancel } from '@/api/check'
import Event from '@/mixins/Event'
import { deepJSON } from '@/utils/tools'
import Demo1 from './1.json'
import Demo2 from './2.json'
import { enumList } from '@/api/common'
import submitForm from '@/components/SubmitForm/index.vue'

export default {
  name: 'check-day',
  components: {
    ScrollSelect,
    HotFigure
  },
  mixins: [Event],
  data() {
    return {
      priceSource: [],
      dateSource: [],
      isOriginal: true,
      isLoading: false,
      isExpendAll: false,
      isEdit: false,
      isEditProcess: false,
      isEditDeny: true,
      isEditFinish: false,
      isSubmitFalse: false,
      isSubmitSure: false,
      enumSkuList: [],
      isShowAll: true,
      imageBoxW: 0,
      imageBoxH: 0,
      imageLoading: true,
      activeName: '0',
      sceneList: [],
      audioFiles: [],
      storeDetail: {},
      skuImageData: [],
      skuMenuData: {},
      activeNameSmall: '0',
      expandedLogs: [],
      auditLogs: [],
      form: {
        name: '',
        region: ''
      },

      sceneValue: 0,
      monthData: '',
      baseInfo: {},
      dataList: [],
      versionIndex: 0,
      searchLabelWidth: '100px',
      curAdmin: {}
    }
  },
  computed: {
    submitForm() {
      return submitForm
    },
    imageList() {
      let relation_list = this.dataList[this.sceneValue] && this.dataList[this.sceneValue].relation_list
      if (relation_list && relation_list.length) {
        const imgs = relation_list[this.versionIndex] && relation_list[this.versionIndex].images
        if (imgs) {
          return imgs
        }
      }
      return []
    },
    ...mapGetters([
      'userInfo',
      'language'
    ])
  },
  watch: {
    '$i18n.locale'() {
      this.searchLabelWidth = this.language === 'en' ? '140px' : '140px'
      this.dialogLabelWidth = this.language === 'en' ? '180px' : '95px'
    },
    'isExpendAll': {
      handler(newVal, oldVal) {
        if (newVal) {
          this.expandedLogs = this.auditLogs.map((item, index) => index)
        } else {
          this.expandedLogs = []
        }
      },
      immediate: true // 立即执行
    }
  },
  async created() {
    await this.getEnumList()
    await this.getQCInfoHistory()
    // const { month } = this.$route.query
    // this.monthData = month
    // this.versionIndex = localStorage.routeDayQuery || 0
    // this.searchLabelWidth = this.language === 'en' ? '140px' : '140px'
    // this.dialogLabelWidth = this.language === 'en' ? '180px' : '95px'
    // this.getList()
  },
  methods: {
    backCloseViewFuns() {
      recognitionQcCancel({ id: this.$route.query.id }).then(res => {
        // console.log(res)
      })
      this.closeView()
      this.$router.go(-1)

    },
    formatDate(timestamp) {
      const date = new Date(timestamp * 1000);
      return date.toLocaleString(); // 或者使用自定义格式
    },
    /**
     * 切换审核日志的展开/收起状态
     */
    toggleAuditLog(index) {
      const position = this.expandedLogs.indexOf(index)
      if (position > -1) {
        this.expandedLogs.splice(position, 1)
      } else {
        this.expandedLogs.push(index)
      }
    },
    expandAllLogs() {
      if (this.auditLogs && this.auditLogs.length) {
        // 如果所有日志都已展开，则收起全部
        if (this.expandedLogs.length === this.auditLogs.length) {
          this.expandedLogs = []
        } else {
          // 否则展开全部
          this.expandedLogs = this.auditLogs.map((_, index) => index)
        }
      }
    },

    /**
     *  调整图片顺序，把当前图片放在第一位
     */
    getPrivewImages(imgList, index) {
      if (!imgList) {
        return
      }
      const tempImgList = [...imgList]// 所有图片地址
      if (index == 0) return tempImgList
      // 调整图片顺序，把当前图片放在第一位
      const start = tempImgList.splice(index)
      const remain = tempImgList.splice(0, index)
      return start.concat(remain)// 将当前图片调整成点击缩略图的那张图片
    },
    /**
     * 提交QC结果
     * */
    submitQc(isPass) {
      let params = {
        is_pass: isPass,
        // long_shot: [], // 全景图
        // close_up: [], // 特写
        // menu: {} // 菜单
      }
      // let longEmpty = false
      // this.sceneList.filter(val => {
      //   const { item_id, sku_list } = val
      //   let obj = {
      //     item_id,
      //     response: {
      //       sku_list: sku_list || []
      //     }
      //   }
      //   if (sku_list) {
      //     const findItem = sku_list.find(v => !v.sku_id || !v.sku_num)
      //     if (findItem) {
      //       longEmpty = true
      //     }
      //   }
      //   params.long_shot.push(obj)
      // })
      // if (longEmpty) {
      //   this.$message.error('全景图-sku名称或者数量不能为空')
      //   return
      // }
      // let closeEmpty = false
      // this.skuImageData.filter(val => {
      //   const { item_id, date, sku_id, price, inventory, date_source, price_source } = val
      //   let obj = {
      //     item_id,
      //     response: {
      //       sku: {
      //         price_source,
      //         date_source,
      //         inventory,
      //         sku_id,
      //         date,
      //         price
      //       }
      //     }
      //   }
      //   if (!sku_id) {
      //     obj.response.sku = {}
      //   }
      //   if (!sku_id && (price || date || price_source || date_source || inventory)) {
      //     closeEmpty = true
      //   }
      //   params.close_up.push(obj)
      // })
      // if (closeEmpty) {
      //   this.$message.error('特写图-sku信息不能为空')
      //   return
      // }
      // let menuEmpty = false
      // const { item_id, menu_skulist, menu_images } = this.skuMenuData
      // menuEmpty = menu_skulist.find(v => !v.sku_id || !v.price)
      // if (menuEmpty) {
      //   this.$message.error('菜单图-sku名称或价格不能为空')
      //   return
      // }
      // let menuObj = {
      //   item_id,
      //   response: {
      //     sku_list: menu_skulist || []
      //   }
      // }
      // params.menu = menu_images && menu_images.length ? menuObj : null
      // params.audit_desc = this.storeDetail.audit_desc
      this.$confirm('是否确认此操作？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (isPass) {
          this.isSubmitSure = true
        } else {
          this.isSubmitFalse = true
        }
        recognitionQcFinish({ id: this.$route.query.id, qc_result: params }).then(res => {
          this.$message.success('操作成功')
          setTimeout(() => {
            this.isSubmitSure = false
            this.isSubmitFalse = false
            this.$router.go(-1)
          }, 1000)
        }).catch(e => {
          this.isSubmitSure = false
          this.isSubmitFalse = false
        })
      })
    },
    /**
     * QC 审核驳回
     * */
    submitQcDeny(isPass) {
      this.$prompt('', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '请填写审核备注',
      }).then(({ value }) => {
        this.isSubmitSure = true;
        recognitionQcReject({ id: this.$route.query.id, qc_result: { is_pass: isPass, audit_desc: value } }).then(res => {
          this.$message.success('操作成功');
          setTimeout(() => {
            this.isSubmitSure = false;
            this.$router.go(-1);
          }, 1000);
        }).catch(e => {
          this.isSubmitSure = false;
          console.error('审核驳回操作失败:', e);
          this.$message.error(e.message || '操作失败，请稍后重试');
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消操作'
        });
      })
    },
    /**
     * QC 审核通过
     * */
    submitQcPass(isPass) {

      let params = {
        is_pass: isPass,
        long_shot: [], // 全景图 - 绑定页面数据
        close_up: [], // 特写 - 绑定页面数据
        menu: {} // 菜单 - 绑定页面数据
      }
      let longEmpty = false;
      let closeEmpty = false;
      let menuEmpty = false;

      // 获取预定义SKU ID列表 (确保enumSkuList已加载)
      const predefinedSkuCodes = this.enumSkuList ? this.enumSkuList.map(item => item.code.toString()) : [];
      // 处理全景图数据
      this.sceneList.forEach(val => {
        const { item_id, sku_list } = val;
        let processedSkuList = [];
        if (sku_list) {
          processedSkuList = sku_list.map(sku => {
            let submittedSku = { sku_id: sku.sku_id, sku_num: sku.sku_num };
            let isCustomSku = false;

            if (sku.sku_id) {
              if (!predefinedSkuCodes.includes(sku.sku_id.toString())) {
                // 如果输入的sku_id不在预定义列表中，将其作为自定义SKU处理
                this.enumSkuList.filter(item => item.code === sku.sku_id).forEach(item => {
                  submittedSku.sku_id = item.code;
                  submittedSku.out_sku_name = item.name;
                })
                isCustomSku = true;
              } else {
                // 如果输入的sku_id在预定义列表中，保留sku_id，不作为自定义SKU处理
                submittedSku.sku_id = sku.sku_id;
                isCustomSku = false;
              }
            } else if (sku.out_sku_name) {
              // 检查out_sku_name是否是预定义SKU的code
              const matchedSku = this.enumSkuList.find(item => item.code === sku.out_sku_name);
              if(matchedSku){
                  // 如果是预定义SKU的code，则设置sku_id为该code，并清空out_sku_name
                  submittedSku.sku_id = matchedSku.code;
                  delete submittedSku.out_sku_name;
                  isCustomSku = false;
              } else {
                // 如果不是预定义SKU的code，则作为自定义SKU处理
                submittedSku.out_sku_name = sku.out_sku_name;
                submittedSku.sku_id = "";
                isCustomSku = true;
              }
            }

            if (!isCustomSku) {
              delete submittedSku.out_sku_name;
            }

            if (submittedSku.sku_id || submittedSku.out_sku_name) {
              return submittedSku;
            } else {
              longEmpty = true;
            }
            return null;
          }).filter(Boolean);
        } else {
          processedSkuList = []; // 确保空列表时也是空数组
        }
        params.long_shot.push({
          item_id,
          response: {
            sku_list: processedSkuList
          }
        });
      });

      if (longEmpty) {
        this.$message.error('全景图-请选择/输入SKU并填写数量');
        return;
      }

      this.skuImageData.filter(val => {
        const { item_id, date, sku_id, price, inventory, date_source, price_source } = val
        let obj = {
          item_id,
          response: {
            sku: {
              price_source,
              date_source,
              inventory,
              sku_id,
              date,
              price
            }
          }
        }
        if (!sku_id) {
          obj.response.sku = {}
        }
        if (!sku_id && (price || date || price_source || date_source || inventory)) {
          closeEmpty = true
        }
        params.close_up.push(obj)
      })
      if (closeEmpty) {
        this.$message.error('特写图-sku信息不能为空')
        return
      }

      // 处理菜单图数据
      const { item_id, menu_skulist, menu_images } = this.skuMenuData;
      let processedMenuSkuList = [];
      if (menu_skulist) {
        processedMenuSkuList = menu_skulist.map(sku => {
          let submittedSku = { sku_id: sku.sku_id, price: sku.price };
          let isCustomSku = false;

          // 处理有sku_id的情况
          if (sku.sku_id) {
            // 检查是否是预定义的SKU
            if (predefinedSkuCodes.includes(sku.sku_id.toString())) {
              // 预定义SKU：保留sku_id，不需要out_sku_name
              isCustomSku = false;
            } else {
              // 自定义SKU：使用out_sku_name，清除sku_id
              submittedSku.out_sku_name = sku.sku_id;
              submittedSku.sku_id = "";
              isCustomSku = true;
            }
          } else if (sku.out_sku_name) {
              // 检查out_sku_name是否匹配预定义SKU的code
              const matchedSku = this.enumSkuList.find(item => item.code === sku.out_sku_name);
              if (matchedSku) {
                  // 如果匹配到预定义SKU，应该使用sku_id而不是out_sku_name
                  submittedSku.sku_id = sku.out_sku_name;
                  delete submittedSku.out_sku_name;
                  isCustomSku = false;
              } else {
                // 真正的自定义SKU
                submittedSku.out_sku_name = sku.out_sku_name;
                submittedSku.sku_id = "";
                isCustomSku = true;
              }
            }

          if (((!isCustomSku && !submittedSku.sku_id) || (isCustomSku && !submittedSku.out_sku_name))
            || (submittedSku.price === undefined || submittedSku.price === null)) {
            if (submittedSku.sku_id || submittedSku.out_sku_name || submittedSku.price !== undefined) {
              menuEmpty = true;
            }
          }
          if (!isCustomSku) {
            delete submittedSku.out_sku_name;
          }

          if (submittedSku.sku_id || submittedSku.out_sku_name) {
            return submittedSku;
          }
          return null;
        }).filter(Boolean);
      } else {
        processedMenuSkuList = []; // 确保空列表时也是空数组
      }

      if (menuEmpty) {
        this.$message.error('菜单图-请选择/输入SKU并填写价格');
        return;
      }

      let menuObj = {
        item_id,
        response: {
          sku_list: processedMenuSkuList
        }
      };
      // 只有当有菜单图图片时才提交菜单数据
      params.menu = menu_images && menu_images.length ? menuObj : null;
      // console.log('params', JSON.stringify(params))
      // return
      // 提交审核
      this.$prompt('', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '请填写审核备注',
      }).then(({ value }) => {
        this.isSubmitSure = true;
        params.audit_desc = value;
        // 发送审核通过请求
        recognitionQcPass({ id: this.$route.query.id, qc_result: params }).then(res => {
          this.$message.success('操作成功');
          setTimeout(() => {
            this.isSubmitSure = false;
            if (this.curAdmin.audit_finish_permission === 0) {
              this.$router.go(-1)
            } else {
              // 重新获取数据以更新页面状态
              this.getDetail()
              this.getQCInfoHistory()
            }
          }, 1000);
        }).catch(e => {
          this.isSubmitSure = false;
          console.error('审核通过操作失败:', e);
          this.$message.error(e.message || '操作失败，请稍后重试');
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消操作'
        });
      });
    },
    /**
     * 获取sku下拉选项
     * */
    getEnumList() {
      enumList({ fields: ['sku', 'price_source', 'date_source'] }).then(res => {
        const { price_source, date_source, sku } = res
        const fun = (arr) => {
          // 修复这里的赋值操作，应该使用===比较而不是=赋值
          // 使用map确保每个项的code属性被正确转换为字符串
          return Object.values(arr).map(v => {
            v.code = v.code.toString();
            return v;
          })
        }
        this.enumSkuList = fun(sku)
        this.priceSource = fun(price_source)
        this.dateSource = fun(date_source)
        this.getDetail()
      })
    },
    /**
     * 提交QC
     * */
    startQc(row) {
      recognitionQcStart({ id: row.id }).then(res => {
        this.openItem('/check/detail', { id: row.id })
      })
    },
    /**
     * 删除sku
     * type 100=全景图 200=特写图 300=菜单图
     * */
    deleteSku(type, index) {
      if (type == 100) {
        let sku_list = this.sceneList[this.activeName].sku_list
        sku_list.splice(index, 1)
      }
      if (type == 300) {
        if (this.skuMenuData && this.skuMenuData.menu_skulist) {
          this.skuMenuData.menu_skulist.splice(index, 1);
        } else {
          console.error("Cannot delete SKU: skuMenuData or menu_skulist is missing.");
          // Optionally show an error message
          // this.$message.error('无法删除菜单图SKU，数据错误。');
        }
      }
      if (type == 200) {
        // Corrected logic for deleting SKU from close-up images (特写图)
        if (this.skuImageData && this.skuImageData[this.activeNameSmall] && this.skuImageData[this.activeNameSmall].sku_list) {
          this.skuImageData[this.activeNameSmall].sku_list.splice(index, 1);
        } else {
          console.error("Cannot delete SKU: skuImageData or active tab data or sku_list is missing.");
          // Optionally show an error message
          // this.$message.error('无法删除SKU，数据错误。');
        }
      }
    },
    /**
     * 添加sku
     * type 100=全景图 200=菜单图
     * */
    addSku(type) {
      if (type == 100) {
        let sku_list = this.sceneList[this.activeName].sku_list
        let obj = {
          "sku_id": "",
          "sku_num": 0,
          "out_sku_name": ""
        }
        sku_list.push(obj)
      }
      if (type == 200) {
        // 特写图添加SKU逻辑
        if (this.skuImageData && this.skuImageData[this.activeNameSmall]) {
          let sku_list = this.skuImageData[this.activeNameSmall].sku_list;
          let obj = {
            "sku_id": "",
            "out_sku_name": "",
            "price": null,
            "production_date": null
          };
          // Ensure sku_list exists before pushing
          if (!sku_list) {
            this.$set(this.skuImageData[this.activeNameSmall], 'sku_list', []);
            sku_list = this.skuImageData[this.activeNameSmall].sku_list;
          }
          sku_list.push(obj);
        } else {
          console.error("Cannot add SKU: skuImageData or active tab data is missing.");
          // Optionally show an error message to the user
          // this.$message.error('无法添加SKU，数据加载错误。');
        }
      }
      if (type == 300) {
        // 菜单图添加SKU逻辑
        if (this.skuMenuData) {
          // 确保menu_skulist存在
          if (!this.skuMenuData.menu_skulist) {
            this.$set(this.skuMenuData, 'menu_skulist', []);
          }

          let obj = {
            "sku_id": "",
            "out_sku_name": "",
            "price": null
          };
          this.skuMenuData.menu_skulist.push(obj);
        } else {
          console.error("Cannot add SKU: skuMenuData is missing.");
          // 初始化skuMenuData以防止错误
          this.skuMenuData = {
            menu_images: [],
            item_id: null,
            menu_skulist: []
          };
          // 添加新的SKU
          let obj = {
            "sku_id": "",
            "out_sku_name": "",
            "price": null
          };
          this.skuMenuData.menu_skulist.push(obj);
          this.$message.warning('菜单图数据已初始化，请确认添加的SKU信息。');
        }
      }
    },
    // 处理特写图 SKU 选择/创建
    handleCloseupSkuChange(value, row) {
      // 检查 value 是否是已知的 sku_id (通常是数字或特定格式的字符串)
      // 假设 enumSkuList 包含所有预定义的 SKU 对象 { code: 'xxx', name: 'yyy' }
      const isExistingSku = this.enumSkuList.some(sku => sku.code === value);

      if (isExistingSku) {
        // 用户选择了预定义的 SKU
        row.sku_id = value;
        row.out_sku_name = ''; // 清除自定义名称
        // 查找对应的 name 并设置，如果 scroll-select 没有自动处理
        const selectedSku = this.enumSkuList.find(sku => sku.code === value);
        // row.sku_name = selectedSku ? selectedSku.name : ''; // 如果需要显式设置名称
      } else {
        // 用户输入了自定义 SKU 名称
        row.out_sku_name = value; // 将输入值设为自定义名称
        // 将 v-model (sku_id) 也设置为自定义值，以便 scroll-select 显示它
        // 提交逻辑会检查 sku_id 是否在 enumSkuList 中来区分
        row.sku_id = value;
      }
      // 强制更新视图，如果需要
      // this.$forceUpdate();
    },

    // 处理菜单图 SKU 选择/创建
    handleMenuSkuChange(value, row) {
      // 检查 value 是否是已知的 sku_id
      const isExistingSku = this.enumSkuList.some(sku => sku.code === value);

      if (isExistingSku) {
        // 用户选择了预定义的 SKU
        row.sku_id = value;
        row.out_sku_name = ''; // 清除自定义名称
        // 查找对应的 name 并设置，如果需要
        const selectedSku = this.enumSkuList.find(sku => sku.code === value);
      } else {
        // 用户输入了自定义 SKU 名称
        row.out_sku_name = value; // 将输入值设为自定义名称
        row.sku_id = value; // 保持 sku_id 与输入值一致
      }
    },
    /**
     * 图片打点-查看全部
     * */
    showChange() {
      this.drawResult(this.isShowAll)
    },
    /**
     * 图片是否加载完成
     * */
    imageFinshed(value) {
      setTimeout(() => {
        this.imageLoading = value
      }, 800)
    },
    /**
     * 获取初始化尺寸
     * */
    initDomSize() {
      this.$nextTick(() => {
        const imageBox = this.$refs.imageBox
        if (imageBox) {
          this.imageBoxW = imageBox.offsetWidth
          this.imageBoxH = 540
        }
      })
    },
    /**
     * 绘制识别结果
     * @param {Boolean} isDraw - 是否显示所有标记点
     * @param {String} highlightSkuId - 需要高亮显示的SKU ID
     * */
    drawResult(isDraw, highlightSkuId, highlightSkuName) {
      if (!this.sceneList.length) {
        return
      }
      const { image, sku_list, bounding_box_list } = this.sceneList[this.activeName]
      // 处理高亮显示
      let filteredBoxList = bounding_box_list
      if (isDraw) {
        filteredBoxList = bounding_box_list.map(item => {
          // 创建一个新对象，避免修改原始数据
          const newItem = { ...item }
          // 默认设置highlight为false
          newItem.highlight = false
          // 如果指定了高亮SKU ID，且当前项匹配，则设置为高亮
          if (highlightSkuId && newItem.sku_id && newItem.sku_id === highlightSkuId) {
            newItem.highlight = true
          }
          // 如果指定了高亮SKU名称，且当前项匹配，则设置为高亮
          else if (highlightSkuName && newItem.sku_name && newItem.sku_name === highlightSkuName) {
            newItem.highlight = true
          }
          return newItem
        })
      }

      let reportObj = {
        isHeatMap: false,
        drawType: 1,
        image_url: image,
        sale_result: isDraw ? filteredBoxList : []
      }
      // console.log('reportObj', reportObj)

      this.$refs.planeFigure && this.$refs.planeFigure.getDetail(reportObj, {})
    },

    /**
     * 根据SKU ID获取SKU名称
     * @param {String|Number} skuId - SKU ID
     * @return {String} - SKU名称
     */
    getSkuNameById(skuId) {
      if (!skuId) return '';
      const sku = this.enumSkuList.find(item => item.code === skuId);
      return sku ? sku.name : '';
    },

    /**
     * 高亮显示特定SKU
     * @param {Object} skuItem - SKU项
     * */
    highlightSku(skuItem) {
      // console.log('skuItem', skuItem)
      if (!skuItem) return

      this.enumSkuList.forEach(item => {
        if (item.code === skuItem.sku_id) {
          skuItem.sku_name = item.lm_sku_name
        }
      })
      // 根据isShowAll的状态决定是否显示所有SKU
      // 当isShowAll为false时，只显示被点击的SKU
      // 当isShowAll为true时，显示所有SKU，并高亮显示选中的SKU
      this.drawResult(this.isShowAll, skuItem.sku_id, skuItem.sku_name);

      // 5秒后恢复显示所有SKU（取消高亮）
      // setTimeout(() => {
      //   this.drawResult(this.isShowAll)
      // }, 5000)
    },
    /**
     * 获取审核日志
     * */
    getQCInfoHistory() {
      recognitionQcInfoHistory({ id: this.$route.query.id }).then(res => {
        if (res.detail.length) {
          this.auditLogs = res.detail
          this.expandedLogs = []
          this.isExpendAll = false
        }
      })
    },
    /**
     * 获取详情
     * */
    getDetail() {
      this.isEdit = false
      this.imageLoading = true
      this.isLoading = true
      recognitionInfo({ id: this.$route.query.id }).then(res => {
        res.menu_data = res.menu_data || {}
        const {
          visitor_phone_suffix,
          is_owner,
          audit_status_msg,
          audit_status,
          store_name,
          admin_name,
          task_id,
          long_shot_data,
          close_up_data,
          menu_data,
          record_data,
          audit_desc,
          cur_qc_permission
        } = res
        if (['21', '22', '23'].includes(audit_status)) {
          this.isEdit = is_owner && ['21', '22', '23'].includes(audit_status) ? true : false
        }
        this.isEditProcess = cur_qc_permission.audit_process
        this.isEditFinish = cur_qc_permission.audit_finish
        const { cur_admin } = cur_qc_permission
        this.curAdmin = cur_admin
        if(this.curAdmin.position === '一审') {
          this.isEditDeny = false
        }
        // 售点详情
        this.storeDetail = {
          visitor_phone_suffix,
          audit_status,
          audit_status_msg,
          store_name,
          task_id,
          audit_desc,
          executor: admin_name,
        }
        // 全景图
        // 先将long_shot_data转为数组，以便排序
        const sortedLongShotData = [...long_shot_data];
        // 对数据进行排序，将冰柜与货架的位置对调
        sortedLongShotData.sort((a, b) => {
          // 如果a是冰柜(scene_code=2)，b是货架(scene_code=1)，则a排在b后面
          if (a.scene_code === 2 && b.scene_code === 1) return 1;
          // 如果a是货架(scene_code=1)，b是冰柜(scene_code=2)，则a排在b前面
          if (a.scene_code === 1 && b.scene_code === 2) return -1;
          // 其他情况保持原顺序
          return 0;
        });

        sortedLongShotData.forEach(v => {
          let obj = {
            scene_name: v.scene_name,
            image: v.stitched_image,
            item_id: v.id,
            images_cos: v.images_cos || [],
            bounding_box_list: v.bounding_box_list,
            sku_list: v.response && v.response.sku_list || [],
          }
          this.sceneList.push(obj)
        })
        this.initDomSize()
        setTimeout(() => {
          this.drawResult(true)
        }, 100)
        // 特写图--- response如果为null则取外面的，否则取response里的
        close_up_data.filter(v => {
          let response = null
          if (v.response === null) {
            response = v
          } else {
            const sku = v.response && v.response.sku
            if (Array.isArray(sku)) {
              response = {}
            } else {
              response = sku || {}
            }
          }
          let obj = {
            // sku_name: response.sku_name,
            sku_id: response.sku_id,
            date: response.date,
            item_id: v.id,
            date_source: response.date_source,
            price_source: response.price_source,
            price: response.price,
            inventory: response.inventory,
            images_group: v.images_cos,
          }
          this.skuImageData.push(obj)
        })
        // 菜单
        this.skuMenuData = {
          menu_images: menu_data.images_cos || [],
          item_id: menu_data.id,
          menu_skulist: menu_data.response && menu_data.response.sku_list || []
        }
        // 录音
        if (record_data && record_data.records_cos) {
          this.audioFiles = record_data.records_cos || []
        }
      }).finally(e => {
        setTimeout(() => {
          this.isLoading = false
        }, 500)
      })
    },
    handleClick(tab, event) {
      this.imageLoading = true
      this.isShowAll = true
      this.drawResult(true)
    },
    /**
     * 切换场景
     * */
    changeScene() {
      this.versionIndex = 0
    },
    choseItem(item) {
      // console.log(this.$route.meta)
      const { device_id, store_code } = this.$route.query
      localStorage.routeDayQuery = this.versionIndex
      localStorage.removeItem('routeTimeQuery')
      this.openItem('/check/time', {
        day: item.time,
        scene_unit_photo_id: item.id,
        device_id,
        store_code
      })
    },
    handleChange() {

    },
    /**
     * 处理SKU选择/创建
     * @param {String} value - 选择或输入的值
     * @param {Object} row - 当前行数据
     */
    handleSkuChange(value, row) {
      // 检查value是否是预定义的SKU ID
      const isExistingSku = this.enumSkuList.some(sku => sku.code === value);

      if (isExistingSku) {
        // 用户选择了预定义的SKU
        row.sku_id = value;
        row.out_sku_name = ''; // 清除自定义名称
        // 查找并设置对应的SKU名称，确保显示名称而不是ID
        const selectedSku = this.enumSkuList.find(sku => sku.code === value);
        if (selectedSku) {
          // 强制更新视图，确保显示名称
          this.$nextTick(() => {
            // 这里不需要额外操作，因为getSkuNameById函数会在模板中被调用
          });
        }
      } else {
        // 用户输入了自定义SKU名称
        row.out_sku_name = value; // 将输入值设为自定义名称

        // 如果当前是在sku_id字段上操作，需要清空它
        if (row.sku_id) {
          row.sku_id = '';
        }
      }
    },
    /**
     * 获取列表数据
     * */
    getList() {
      const query = this.$route.query
      inspectionMonthDay(query).then(res => {
        this.baseInfo = res
        const { scene_unit_list, other_images } = res
        let otherObj = {}
        let arr = scene_unit_list
        if (Object.keys(other_images).length) {
          otherObj = {
            relation_list: [other_images],
            relation_id: 0,
            scene_unit_name: '其他',
            scene_unit_code: '-1'
          }
          arr.unshift(otherObj)
        }
        arr = arr.map(v => {
          v.relation_list = v.relation_list.map((val, index) => {
            val.index = v.relation_list.length - index
            return val
          })
          // v.relation_list = v.relation_list.reverse()
          return v
        })
        this.dataList = arr
        if (query.scene_unit_code) {
          this.sceneValue = this.dataList.findIndex(v => v.scene_unit_code == query.scene_unit_code)
        }
        // const zeroIdIndex = res.list.findIndex(item => item.relation_id === 0)
        // // 如果找到 relation_id=0 的元素，将其移动到数组末尾
        // if (zeroIdIndex !== -1) {
        //   const [itemToMove] = res.list.splice(zeroIdIndex, 1)
        //   res.list.unshift(itemToMove)
        // }
        // res.list.map((v, index) => {
        //   v.index = (index + 1)
        //   return v
        // })
        // this.dataList = res.list.reverse()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.table_box {
  padding-top: 25px !important;
}

.checkdetail__container {
  .head__back {
    font-size: 14px;
    font-weight: 500;
    color: #0471d4;
    line-height: 22px;
    cursor: pointer;
    margin-bottom: 20px;

    span {
      margin-right: 5px;
    }
  }

  .view-all-btn {
    color: #0471d4;
    cursor: pointer;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }

  .audit-log-item {
    margin-bottom: 15px;
    border: 1px solid #EBEEF5;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.03);

    .audit-log-header {
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 15px;
      background-color: #F5F5F5;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        background-color: #eef1f6;
      }

      .audit-header-left {
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 5px;
        flex: 1;
      }

      .toggle-btn {
        position: absolute;
        left: 50%;
        top: 60%;
        font-size: 16px;
        color: #909399;
        transition: transform 0.3s;
        width: 64px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fff;
        border-radius: 8px;
        border: 1px solid #EBEEF5;

        i {
          transition: transform 0.3s;
        }
      }

      .audit-log-header:has(.el-icon-arrow-up) .toggle-btn i {
        transform: rotate(180deg);
      }

      .audit-user {
        color: #8C8C8C;
        font-family: 思源黑体;
        font-size: 12px;
        font-weight: 400;
        line-height: 17px;
        letter-spacing: 0px;
        text-align: left;
      }

      .audit-result {
        font-weight: 500;
        padding: 2px 8px;
        border-radius: 4px;

        &.audit-pass {
          color: #67C23A;
          // background-color: rgba(103, 194, 58, 0.1);
        }

        &.audit-reject {
          color: #F56C6C;
          // background-color: rgba(245, 108, 108, 0.1);
        }
      }
    }

    .audit-log-details {
      padding: 0 15px;
      overflow: hidden;
      background: #F5F5F5;
    }

    .audit-log-content {
      font-size: 14px;
      font-weight: 400;
      color: #000;
      line-height: 1.5;
      margin: 4px 0;
      padding: 8px 0px;
      border-radius: 4px;
    }

    .audit-log-divider {
      height: 1px;
      background-color: #EBEEF5;
      margin: 10px 0;
    }

    /* 过渡动画 */
    .fade-enter-active,
    .fade-leave-active {
      transition: all 0.3s ease;
      max-height: 1000px;
      opacity: 1;
      overflow: hidden;
    }

    .fade-enter,
    .fade-leave-to {
      max-height: 0;
      opacity: 0;
      overflow: hidden;
    }

    /* SKU信息展示区域样式 */
    .sku-info-container {
      width: 100%;
    }

    .sku-category-row {
      display: flex;
      align-items: flex-start;
      border-top: 1px dashed #D9D9D9;
      padding: 24px;
    }
    .ml-4 {
      margin-left: 4px;
    }
    .mr-4 {
      margin-right: 4px;
    }
    .scene-txt {
      font-size: 14px;
      font-weight: 500;
      color: #1F1F1F;
      line-height: 20px;
    }

    .pl30 {
      padding-left: 30px;
    }

    .detele-sku {
      padding-left: 30px;

      .tit {
        color: #FF4D4F;
      }
    }

    .edit-sku {
      .tit {
        color: #FAAD14;
      }
    }

    .tit {
      font-size: 14px;
      color: #52C41A;
    }

    .delete-sku {
      color: #FF4D4F;
    }

    .sku-category-title {
      min-width: 120px;
      font-weight: 500;
      color: #333;
      margin-right: 16px;
    }

    .sku-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      flex: 1;
    }

    .sku-tag {
      display: inline-flex;
      align-items: center;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 14px;
    }

    .sku-tag.new-sku {
      background-color: #e6f7ff;
      border: 1px solid #91d5ff;
      color: #1890ff;
    }

    .sku-tag.delete-sku {
      background-color: #fff1f0;
      border: 1px solid #ffa39e;
      color: #ff4d4f;
    }

    .sku-tag.edit-sku {
      background-color: #f6ffed;
      border: 1px solid #b7eb8f;
      color: #52c41a;
    }

    .sku-name {
      margin-right: 4px;
    }

    .sku-box {
      padding-left: 30px;
      justify-content: space-around;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
    }

    .sku-count {
      color: #666;
    }

    .sku-item {
      margin-left: 24px;
      color: #000;
      font-size: 14px;
      line-height: 17px;
      letter-spacing: 0px;
      text-align: left;
      margin-bottom: 12px;
    }
  }

  .original__box {
    background: #fafafa;
    padding: 10px;
    border-radius: 4px;
  }

  .original__image--box {
    margin-top: 10px;

    .original__image {
      background: rgba(25, 87, 210, 0.05);
      width: 100px;
      display: inline-block;
      height: 100px;
      border-radius: 4px;
      margin-right: 10px;
    }
  }

  .opt__all {
    right: 0;
    top: 10px;
    position: absolute;
    color: #232323;
    font-size: 14px;
    font-weight: 500;
  }

  .file__item {
    //background: #EEEEEE;
    //border-radius: 3px;
    //width: 170px;
    //padding: 10px 0;
    margin-right: 10px;
    margin-top: 10px;
    cursor: pointer;

    img {
      display: block;
      width: 32px;
      height: 32px;
      margin-right: 10px;
    }
  }

  .store_mes {
    padding: 20px;

    .phone {
      font-size: 12px;
      margin-left: 5px;
      color: #666666;
    }

    .store_mes_col {
      font-size: 14px;
      font-weight: 400;
      color: #737373;
      line-height: 22px;

      &>div {
        margin-right: 4%;
      }

      span {
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #000;
        font-weight: 500;
        line-height: 22px;
      }

      .thm {
        color: #0A5FAC;
      }
    }
  }

  .rec__warp--box {

    .title {
      position: relative;
      padding-left: 18px;
      margin: 24px 0 8px;
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #0471d4;
      line-height: 25px;
      margin-top: 20px;

      //font-weight: 500;
      //margin-bottom: 10px;
      &::before {
        content: "";
        display: block;
        position: absolute;
        left: 0;
        width: 5px;
        height: 18px;
        background-color: #0471d4;
        top: 3px;
      }

      span {
        color: #666666;
        font-size: 13px;
        margin-left: 5px;
      }
    }

    .add__sku {
      font-size: 15px;
      color: #0471D4;
      font-weight: 500;
      text-align: center;
      margin-top: 15px;
      cursor: pointer;

      span {
        margin-right: 5px;
      }
    }

    .table_box {
      //min-height: 60vh;
      padding: 18px 25px 0;
      padding-bottom: 20px;
      padding-top: 10px;

      .rec__warp {
        position: relative;

        .ir__image {
          width: 40%;
          height: 540px;

          .img {
            display: block;
            width: 100%;
            height: 100%;
            background: #fff;
          }
        }

        .ir__skulist {
          width: 40vw;
          padding: 0 15px;

          .tit {
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
    }

    &.material__square {
      .ir__image {
        overflow-y: scroll;
        max-height: 340px !important;
        width: auto !important;
        padding: 16px;
        border-radius: 8px;
        background: rgba(25, 87, 210, .05);

        .img {
          width: 120px !important;
          height: 150px !important;
          border-radius: 6px;
          display: inline-block !important;
          ;
          margin-right: 5px;
          margin-bottom: 3px;
        }
      }
    }
  }

}
</style>
