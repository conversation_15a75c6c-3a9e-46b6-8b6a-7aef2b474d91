<template>
  <div class="page_container account_frontend_view">
    <div class="mb10">
      <el-link type="primary" @click="backCloseViewFun()" :underline="false" class="el-icon-d-arrow-left">{{$t('reaCommon.back')}}</el-link>
      <span class="head__title">{{ $route.query.name }}</span>
    </div>
    <div class="form_box">
      <search-form :value="queryParam" :form-data="formData" :label-width="searchLabelWidth"
                   @nodeConfigHandler="formHandler" @selectChangeHandler="selectChangeHandler"
                   @resetQueryParam="resetQueryParam">
        <template v-slot:scene_type_code="{row}">
          <el-select filterable v-model="queryParam.scene_type_code" placeholder="请选择" size="small" clearable>
            <el-option-group
              v-for="(group, index) in sceneList"
              :key="index"
              :label="group.scene_type_name">
              <el-option
                v-for="(item, ind) in group.sub"
                :key="ind"
                :label="item.scene_type_name"
                :value="item.scene_type_code">
              </el-option>
            </el-option-group>
          </el-select>
        </template>

      </search-form>
    </div>
    <div class="table_box wbg">
      <div class="control_box flex flex-y-center flex-x-start">
        <div class="flex-1"></div>
        <div>
          <el-button size="medium" v-if="$havePermission('0101',$route.meta.functionList)" icon="el-icon-plus"
                     type="primary" @click.native="addScene(100)"> {{ $t('reaCommon.add') }}
          </el-button>
        </div>
      </div>
      <new-table
        ref="newTable"
        :outerHeight="150"
        :loading="loading"
        :table-header="sceneIndexHeader"
        :table-data="dataSource"
        :pagination="pagination"
        @pageConfigHandler="pageHandler"
      >
        <template v-slot:scene_images="{row}">
          <el-image
            :preview-src-list="row.scene_images"
            style="width: 50px;height: 50px"
            :src="row.scene_images[0]+'?imageView2/1/w/50/h/50'"
            fit="cover"
          />
        </template>
        <template v-slot:is_bind_device="{row}">
          <el-tag v-if="row.is_bind_device == 1" size="small">{{$t('reaCommon.yes')}}</el-tag>
          <el-tag type="danger" size="small" v-else>{{$t('reaCommon.no')}}</el-tag>
        </template>
        <template v-slot:is_bind_plan="{row}">
          <el-tag v-if="row.is_bind_plan == 1" size="small">{{$t('reaCommon.yes')}}</el-tag>
          <el-tag type="danger" size="small" v-else>{{$t('reaCommon.no')}}</el-tag>
        </template>
        <template v-slot:edit="{row}">
          <span class="table__oprate--item" v-if="$havePermission('0301',$route.meta.functionList)"
                @click="editFun(row)">{{ $t('reaCommon.edit') }}</span>
          <span class="table__oprate--item" v-if="$havePermission('0501',$route.meta.functionList)"
                @click="deleteFun(row)">{{ $t('reaCommon.delete') }}</span>
        </template>
      </new-table>
    </div>

    <!--新增/编辑售点-->
    <div class="dialog_container">
      <ele-dialog :title="dialogForm.store_id ? $t('reaCommon.edit') : $t('reaCommon.add')" @dialogClose="dialogCancel"
                  :visible.sync="dialogVisible"
                  center @dialogCancel="dialogCancel" @dialogConfirm="sbumitFun()">

        <template slot="content">
          <div class="dialog_form">
            <el-form ref="dialogForm" size="medium" :model="dialogForm" :rules="dialogRules" label-width="175px"
                     class="demo-ruleForm">
              <el-form-item :label="$t('reaCommon.sceneType')" prop="scene_type_code">
                <el-select v-model="dialogForm.scene_type_code" filterable  :placeholder="$t('reaCommon.pleaseEnter')" size="small">
                  <el-option-group
                    v-for="(group, index) in sceneList"
                    :key="index"
                    :label="group.scene_type_name">
                    <el-option
                      v-for="(item, ind) in group.sub"
                      :key="ind"
                      :label="item.scene_type_name"
                      :value="item.scene_type_code">
                    </el-option>
                  </el-option-group>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('reaCommon.sceneName')" prop="scene_name">
                <el-input v-model="dialogForm.scene_name" clearable
                          :placeholder="$t('reaCommon.pleaseEnter')"
                          @blur="dialogForm.scene_name = $event.target.value.trim()"/>
              </el-form-item>

              <!--新增3个字段-->
              <el-form-item :label="$t('reaCommon.sceneLocation')" prop="store_scene_location">
                <el-select v-model="dialogForm.store_scene_location" filterable :placeholder="$t('reaCommon.pleaseEnter')">
                  <el-option
                      v-for="item in downListObj.store_scene_location"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('reaCommon.sceneDisplayPosition')" prop="display_location">
                <el-select v-model="dialogForm.display_location" filterable :placeholder="$t('reaCommon.pleaseEnter')">
                  <el-option
                      v-for="item in downListObj.display_location"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('reaCommon.displayPosition')" prop="competitive_display_location">
                <el-select v-model="dialogForm.competitive_display_location" filterable :placeholder="$t('reaCommon.pleaseEnter')">
                  <el-option
                      v-for="item in downListObj.competitive_display_location"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('reaCommon.haveGateway')" >
                <el-select v-model="dialogForm.is_gateway" filterable clearable :placeholder="$t('reaCommon.pleaseEnter')">
                  <el-option
                      v-for="item in downListObj.is_gateway"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code">
                  </el-option>
                </el-select>
              </el-form-item>
              <!--冰柜场景-->
              <template v-if="isAssetFreezer">
                <el-form-item :label="$t('reaCommon.openDirection')" >
                  <el-select v-model="dialogForm.door_direction" filterable :placeholder="$t('reaCommon.pleaseEnter')">
                    <el-option
                        v-for="item in downListObj.door_direction"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item :label="$t('reaCommon.assetNumber')">
                  <el-input v-model="dialogForm.asset_number" clearable
                            :placeholder="$t('reaCommon.pleaseEnter')"
                            @blur="dialogForm.asset_number = $event.target.value.trim()"/>
                </el-form-item>
              </template>
              <el-form-item  :label="$t('reaCommon.sceneSort')">
                <el-input v-model="dialogForm.sort" type="number" clearable
                          :placeholder="$t('reaCommon.pleaseEnter')"
                          @blur="dialogForm.sort = $event.target.value.trim()"/>
              </el-form-item>

              <el-form-item :label="$t('reaCommon.uploadSceneImage')" prop="scene_images">
                <Upload ref="uploadImage1" class="flex-1" fileRoutePrefix="store_scene"
                        @uploadSuccess="uploadSuccess"></Upload>
                <el-image
                  v-if="dialogForm.url"
                  :preview-src-list="[dialogForm.url]"
                  class="scene__face"
                  :src="dialogForm.url+'?imageView2/1/w/80/h/80'"
                  fit="contain"></el-image>
              </el-form-item>
            </el-form>
          </div>
        </template>
      </ele-dialog>
    </div>

  </div>
</template>
<script>
import EleDialog from '@/components/Dialog'
import Upload from '@/components/Upload'
import SearchForm from '@/components/SearchForm'
import NewTable from '@/components/Table'
import {sceneIndexHeader} from '@/utils/header'
import ListMixin from '@/mixins/List'
import EventMixin from '@/mixins/Event'
import {mapGetters} from 'vuex'
import {storeSceneDelete, storeSceneCreate, storeSceneEdit} from "@/api/scene"
import {storeList} from "@/api/list"
import {backendMenu, sceneTypeTree} from "@/api/common"
import Link from "@/layout/components/Sidebar/Link";

export default {
  name: 'gather-cooler',
  components: {
    Link,
    SearchForm,
    NewTable,
    Upload,
    EleDialog
  },
  mixins: [ListMixin, EventMixin],
  data() {
    const validElement = (rule, value, callback) => {
      if (!value.length) {
        callback(this.$t('reaCommon.pleaseEnter') + ' ' + this.$t('reaCommon.uploadSceneImage'))
      } else {
        callback()
      }
    }
    return {
      dialogForm: {
        is_gateway: '',
        id: '',
        scene_name: '',
        scene_type_code: '',
        scene_images: [],
        sort: 100,
        url: '',
        asset_number: '',
        store_scene_location: '',
        display_location: '',
        competitive_display_location: '',
        door_direction: ''
      },
      dialogRules: {
        scene_name: [
          {required: true, message: this.$t('reaCommon.pleaseEnter'), trigger: 'blur'}
        ],
        scene_type_code: [
          {required: true, message: this.$t('reaCommon.pleaseSelect'), trigger: 'blur'}
        ],
        scene_images: [
          {type: 'array', required: true, message: this.$t('reaCommon.pleaseUploadSceneImage'), trigger: 'blur'}
        ],
        sort: [
          {required: true, message: this.$t('reaCommon.pleaseSelect'), trigger: 'blur'}
        ],
        store_scene_location: [
          {required: true, message: this.$t('reaCommon.pleaseSelect'), trigger: 'blur'}
        ],
        display_location: [
          {required: true, message: this.$t('reaCommon.pleaseSelect'), trigger: 'blur'}
        ],
        competitive_display_location: [
          {required: true, message: this.$t('reaCommon.pleaseSelect'), trigger: 'blur'}
        ],
      },
      sceneIndexHeader,
      formData: [
        // {
        //   name: '售点列表',
        //   type: 'select',
        //   nameKey: 'storeInventory',
        //   field: 'store_id',
        //   placeholder: '请选择',
        //   attrs: {
        //     clearable: true,
        //     filterable: true
        //   },
        //   options: []
        // },
        {
          name: '场景类型',
          nameKey: 'sceneType',
          type: 'tree',
          slot: 'scene_type_code',
          field: 'scene_type_code',
          placeholder: '请选择场景类型',
          attrs: {
            clearable: true,
            filterable: true
          },
          options: []
        },
        {
          name: '是否绑定设备',
          nameKey: 'isBindDev',
          type: 'select',
          field: 'is_bind_device',
          attrs: {
            clearable: true
          },
          options: [
            {label: this.$t('reaCommon.yes'), value: 1},
            {label: this.$t('reaCommon.no'), value: 0},
          ]
        },
        {
          name: '是否绑定平面图',
          nameKey: 'isBindPlan',
          type: 'select',
          field: 'is_bind_plan',
          attrs: {
            clearable: true
          },
          options: [
            {label: this.$t('reaCommon.yes'), value: 1},
            {label: this.$t('reaCommon.no'), value: 0},
          ]
        },
      ],
      searchLabelWidth: '140px',
      queryParam: {
        store_id: '',
        scene_type_code: '',
        is_bind_device: '',
        is_bind_plan: '',
      },
      initQueryParam: {},
      dataSource: [],
      /* mixin里接口名称*/
      apiUrl: {
        list: 'storeSceneList'
      },
      dialogVisible: false,
      sceneList: [],
      downListObj: [],
      isAssetFreezer: false,
      version_code: '',
    }
  },
  watch: {
    '$i18n.locale'() {
      this.searchLabelWidth = this.language === 'en' ? '160px' : '140px'
    },
    'dialogForm.scene_type_code': {
      handler(newName, oldName) {
        this.isAssetFreezer = false
        this.sceneList.filter(v => {
          const findItem = v.sub.find(val => this.dialogForm.scene_type_code == val.scene_type_code)
          // 选中冰柜场景
          if(findItem && findItem.parent_code == 1){
            this.isAssetFreezer = true
          }
        })
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    visitedViews() {
      return this.$store.state.tagsView.visitedViews
    },
    ...mapGetters([
      'userInfo',
      'language'
    ])
  },
  created() {
    this.searchLabelWidth = this.language === 'en' ? '160px' : '140px'
    const {version, id} = this.$route.query
    this.version_code = version
    this.queryParam.store_id = id
    this.queryParam.version_code = version
    this.initQueryParam = this.queryParam
  },
  mounted() {
    this.getBackendMenu()
    // this.getStoreList()
    this.getSceneList()
  },
  methods: {
    /**
     * 获取下拉列表数据
     * */
    getBackendMenu(){
      backendMenu({fields: ['store_scene_location','display_location','competitive_display_location','door_direction', 'is_gateway'], tenant_id: this.userInfo.tenant_id}).then(res => {
        this.downListObj = res
      })
    },
    /**
     * 上传场景图片
     * */
    uploadSuccess(e) {
      this.dialogForm.url = e.imageSrc
      this.$refs.dialogForm.clearValidate('scene_images');
    },
    /**
     * 获取售点列表
     * */
    getStoreList() {
      storeList().then(res => {
      })
    },
    /**
     * 获取场景列表
     * */
    getSceneList() {
      sceneTypeTree().then(res => {
        let arr = []
        res.filter((v) => {
          arr.push({value: v.id, label: v.tenant_name})
        })
        this.brandList = res
        const itemIndex = this.formData.findIndex(v => v.field == 'scene_type_code')
        this.formData[itemIndex].options = arr
        this.sceneList = res
      })
    },
    /**
     * 编辑场景
     * */
    editFun(item) {
      Object.keys(this.dialogForm).filter(v => {
        this.dialogForm[v] = item[v]
      })
      // TODO 1个场景1张图
      const scene_images = item.scene_images
      if (scene_images && scene_images.length) {
        let url = scene_images[0]
        // this.dialogForm.scene_images = [url.substring(url.lastIndexOf("/") + 1)]
        this.dialogForm.scene_images = [url]
        this.dialogForm.url = url
      }
      this.addScene()
    },
    /**
     * 添加场景
     * */
    addScene(type) {
      if (type == 100) {
        this.dialogForm = {
          id: '',
          scene_name: '',
          scene_type_code: '',
          scene_images: [],
          sort: 100,
          url: ''
        }
        if (this.$refs.uploadImage1) {
          this.$refs.uploadImage1.clearFun()
        }
      }
      if (this.$refs['dialogForm']) {
        this.$refs['dialogForm'].clearValidate()
      }
      this.dialogVisible = true
    },
    /**
     * 取消编辑
     * */
    dialogCancel() {
      this.dialogVisible = false
    },
    /**
     * 场景编辑/新增
     * */
    sbumitFun() {
      const imageSrc = this.$refs.uploadImage1.imageSrc
      if (imageSrc) {
        this.dialogForm.scene_images = [imageSrc]
      }
      // this.$refs.usernameItem.validateField('username');
      //
      this.$refs['dialogForm'].validate((valid) => {
        if (valid) {
          if (this.isSubmit) {
            return
          }
          this.isSubmit = true
          let call = null
          let params = this.dialogForm
          params.version_code = this.version_code
          params.store_id = this.$route.query.id
          if (this.dialogForm.id) {
            call = storeSceneEdit(this.dialogForm)
          } else {
            call = storeSceneCreate(this.dialogForm)
          }
          call.then(res => {
            this.dialogVisible = false
            this.$message.success(this.$t('reaCommon.operationSuccess'))
            this.formHandler()
          }).finally(() => {
            setTimeout(() => {
              this.isSubmit = false
            }, 800)
          })
        }
      })
    },
    /**
     * 删除场景
     * */
    deleteFun(row) {
      this.$confirm(this.$t('reaCommon.confirmDeleteThisOne') + '?', this.$t('reaCommon.warning'), {
        confirmButtonText:  this.$t('reaCommon.confirm'),
        cancelButtonText:  this.$t('reaCommon.cancel'),
        type: 'warning'
      }).then(() => {
        if (row.loading) {
          return
        }
        row.loading = true
        storeSceneDelete({id: row.id, version_code: this.version_code}).then((res) => {
          this.$message.success(this.$t('reaCommon.deleteSuccess'))
          this.formHandler()
        }).finally(() => {
          row.loading = false
        })
      })
    },
    formHandler() {
      if (Array.isArray(this.queryParam.date) && this.queryParam.date.length) {
        this.queryParam.open_start_time = this.queryParam.date[0]
        this.queryParam.open_end_time = this.queryParam.date[1]
      } else {
        this.queryParam.open_start_time = ''
        this.queryParam.open_end_time = ''
      }
      this.searchQuery()
    },
    selectChangeHandler(selectObj) {
      const {name, value} = selectObj
    },
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.scene__face {
  width: 100px;
  height: 100px;
  border: 1px solid #f2f2f2;
  margin-top: 10px;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 60px;
  height: 60px;
  line-height: 60px;
  text-align: center;
}

.table_box.mt0 {
  margin-top: 0;
}

.table_box {
  margin-top: 23px;
  padding: 18px 25px 0;
}

.control_box {
  padding: 0 0 17px;
  padding-bottom: 18px;
}

.reset_password ::v-deep .el-input__inner {
  border: none;
}

.frontend_control_box ::v-deep .el-button--default {
  border-color: $themeColor;
  color: $themeColor;
}

.dot {
  margin-right: 8px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #909399;
}

.dot.primary {
  background-color: $themeColor;
}

.dot.success {
  background-color: #36C08E;
}

.dot.warnning {
  background-color: #E6A23C;
}


.dot.danger {
  background-color: #E52828;
}

.header_scroll_box {
  height: 270px;
  padding: 5px 0;
  overflow-y: auto;

  &::-webkit-scrollbar-track-piece {
    background: #D3E6E6;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #99a9bf;
    border-radius: 20px;
  }

}
</style>
<style lang="scss">
@import "~@/styles/variables.scss";

.table_header_set_box {
  padding: 0 10px !important;

  .el-checkbox__label {
    color: #333;
  }
}

.cursor {
  cursor: pointer;
}

.text__align--center {
  text-align: center;
}

.pointer {
  cursor: pointer;
}


</style>
