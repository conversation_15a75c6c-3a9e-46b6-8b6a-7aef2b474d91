<template>
  <div class="page_container">
    <submit-form :confirm-button-loading="submitting" :show-input-count="9" :value="form" :form-rule="formRule"
                 :form-data="formData" :label-width="searchLabelWidth" @cancelHandler="cancel" @saveHandler="save"
                 @selectChangeHandler="selectChangeHandler">
      <template slot="organization">
        <el-tree-select ref="treeSelect" v-model="form.organize_id" collapse-tags :styles="styles"
                        :select-params="selectParams" :tree-params="treeParams"/>
      </template>
    </submit-form>
  </div>
</template>
<script>
import SubmitForm from '@/components/SubmitForm/index'
import {formCheckPhone} from '@/utils/validate'

import {tenantAll, organizeTenant, storeChannelList, cityList, storeStatus} from '@/api/common'
import {storeEdit, storeAdd, storeDetail} from '@/api/store-warehouse/index'
import {mapGetters} from 'vuex'

export default {
  name: 'store-warehouse-add',
  components: {
    SubmitForm
  },
  data() {
    return {
      searchLabelWidth: '160px',
      submitting: false,
      form: {
        id: '',
        tenant_id: '',
        organize_id: '',
        store_name: '',
        store_code: '',
        province_id: '',
        city_id: '',
        county_id: '',
        province_city_county: [],
        channel_id: '',
        status_id: '',
        address: '',
        shopkeeper_name: '',
        mobile: '',
        contacts: '',
        contact_way: '',
        gaode_lng: '',
        gaode_lat: '',
        baidu_lng: '',
        baidu_lat: '',
        store_area: '',
        cash_registers_count: '',
        scanner_count: '',
        summary: '',
        has_shopping_cart: ''
      },
      formRule: {
        tenant_id: [
          {
            required: true,
            message: this.$t('reaCommon.pleaseSelect') + ' ' + this.$t('reaCommon.tenant'),
            trigger: 'blur'
          }
        ],
        organize_id: [
          {
            required: true,
            message: this.$t('reaCommon.pleaseSelect') + ' ' + this.$t('reaCommon.organization'),
            trigger: 'blur'
          }
        ],
        store_name: [
          {
            required: true,
            message: this.$t('reaCommon.pleaseEnter') + ' ' + this.$t('reaCommon.storeName'),
            trigger: 'blur'
          }
        ],
        store_code: [
          {
            required: true,
            message: this.$t('reaCommon.pleaseEnter') + ' ' + this.$t('reaCommon.storeCode'),
            trigger: 'blur'
          }
        ],
        // province_city_county: [
        //   { required: true, message: this.$t('reaCommon.pleaseSelect') + ' ' + this.$t('reaCommon.pcc'), trigger: 'blur' }
        // ],
        channel_id: [
          {
            required: true,
            message: this.$t('reaCommon.pleaseSelect') + ' ' + this.$t('reaCommon.channelType'),
            trigger: 'blur'
          }
        ],
        status_id: [
          {
            required: true,
            message: this.$t('reaCommon.pleaseSelect') + ' ' + this.$t('reaCommon.storeStatus'),
            trigger: 'blur'
          }
        ],
        address: [
          {
            required: true,
            message: this.$t('reaCommon.pleaseEnter') + ' ' + this.$t('reaCommon.storeAddress'),
            trigger: 'blur'
          }
        ],
        mobile: [
          {
            required: true,
            message: this.$t('reaCommon.pleaseEnter') + ' ' + this.$t('reaCommon.phone'),
            trigger: 'blur'
          },
          {validator: formCheckPhone, trigger: 'blur'}
        ]
      },
      formData: [{
        name: '品牌方',
        nameKey: 'tenant',
        type: 'select',
        field: 'tenant_id',
        placeholder: '请选择品牌方',
        attrs: {
          clearable: true
        },
        options: []
      }, {
        name: '组织架构信息',
        nameKey: 'organization',
        type: 'tree',
        field: 'organize_id',
        slot: 'organization',
        options: []
      }, {
        name: '售点名称',
        nameKey: 'storeName',
        type: 'input',
        field: 'store_name',
        placeholder: '请选择',
        attrs: {
          'clearable': true
        }
      }, {
        name: '售点编号',
        nameKey: 'storeCode',
        type: 'input',
        field: 'store_code',
        placeholder: '请选择',
        attrs: {
          'clearable': true
        }
      }, {
        name: '省/市/区',
        nameKey: 'pcc',
        type: 'cascader',
        field: 'province_city_county',
        props: {
          children: 'sub'
        },
        attrs: {
          clearable: true
        },
        options: []
      }, {
        name: '渠道类型',
        nameKey: 'channelType',
        type: 'select',
        field: 'channel_id',
        placeholder: '请选择渠道类型',
        attrs: {
          clearable: true
        },
        options: []
      }, {
        name: '售点地址',
        nameKey: 'storeAddress',
        type: 'input',
        field: 'address',
        attrs: {
          clearable: true
        }
      }, {
        name: '售点状态',
        nameKey: 'storeStatus',
        type: 'select',
        field: 'status_id',
        placeholder: '请选择售点状态',
        attrs: {
          clearable: true
        },
        options: []
      }, {
        name: '店主手机',
        nameKey: 'ownerTel',
        type: 'mobile',
        field: 'mobile',
        attrs: {
          clearable: true
        }
      }, {
        name: '店主姓名',
        nameKey: 'ownerName',
        type: 'input',
        field: 'shopkeeper_name',
        attrs: {
          clearable: true
        }
      }, {
        name: '联系人',
        nameKey: 'theContact',
        type: 'input',
        field: 'contacts',
        attrs: {
          clearable: true
        }
      }, {
        name: '联系方式',
        nameKey: 'contact',
        type: 'input',
        field: 'contact_way',
        attrs: {
          clearable: true
        }
      }, {
        name: '高德经度',
        nameKey: 'scottLong',
        type: 'input',
        field: 'gaode_lng',
        attrs: {
          clearable: true
        }
      }, {
        name: '高德纬度',
        nameKey: 'scottLat',
        type: 'input',
        field: 'gaode_lat',
        attrs: {
          clearable: true
        }
      }, {
        name: '百度经度',
        nameKey: 'baiduLong',
        type: 'input',
        field: 'baidu_lng',
        attrs: {
          clearable: true
        }
      }, {
        name: '百度纬度',
        nameKey: 'baiduLat',
        type: 'input',
        field: 'baidu_lat',
        attrs: {
          clearable: true
        }
      }, {
        name: '门店面积',
        nameKey: 'shopArea',
        type: 'input',
        field: 'store_area',
        attrs: {
          clearable: true
        }
      }, {
        name: '收银机台数',
        nameKey: 'cashNumber',
        type: 'input',
        field: 'cash_registers_count',
        attrs: {
          clearable: true
        }
      }, {
        name: '扫描机台数',
        nameKey: 'scannerNumber',
        type: 'input',
        field: 'scanner_count',
        attrs: {
          clearable: true
        }
      }, {
        name: '备注',
        nameKey: 'remark',
        type: 'input',
        field: 'summary',
        attrs: {
          clearable: true
        }
      }, {
        name: '是否有购物车/购物篮',
        nameKey: 'hasShopCar',
        type: 'radio',
        field: 'has_shopping_cart',
        attrs: {
          clearable: true
        },
        options: [{
          name: '是',
          labelKey: 'yes',
          label: 1
        }, {
          name: '否',
          labelKey: 'no',
          label: 0
        }]
      }],
      styles: {
        width: '100%'
      },
      test: '',
      values: ['11111'],
      value: '',
      selectParams: {
        clearable: true,
        placeholder: this.$t('reaCommon.pleaseEnter'),
        'collapse-tags': true
      },
      treeParams: {
        filterable: false,
        'default-expand-all': true,
        'expand-on-click-node': false,
        'node-key': 'id',
        'show-checkbox': true,
        data: [],
        props: {
          children: 'children',
          label: 'label',
          disabled: 'disabled',
          value: 'id'
        }
      },
      optionsIndexObj: {},
      id: '',
      storeType: 0
    }
  },
  computed: {
    visitedViews() {
      return this.$store.state.tagsView.visitedViews
    },
    ...mapGetters([
      'userInfo',
      'language'
    ])
  },
  watch: {
    '$i18n.locale'() {
      this.searchLabelWidth = this.language === 'en' ? '210px' : '160px'
      this.selectParams = {
        clearable: true,
        placeholder: this.$t('reaCommon.pleaseEnter'),
        'collapse-tags': true
      }
      this.setFormRule()
    }
  },
  created() {
    this.searchLabelWidth = this.language === 'en' ? '210px' : '160px'
    this.form.id = this.$route.query.id
    if (this.form.id) {
      this.getStoreDetail()
    }
    if (!this.userInfo.is_super) {
      this.formData.splice(0, 1)
      this.formData.map((v, index) => {
        this.optionsIndexObj[v.field] = index
      })
      if (!this.form.id) {
        this.getOrganizeTenant()
      }
    } else {
      this.formData.map((v, index) => {
        this.optionsIndexObj[v.field] = index
      })
      this.getTenantAll()
    }
    this.getStoreChannelList()
    this.getCityList()
    this.getStoreStatusList()
  },
  mounted() {
  },
  methods: {
    setFormRule() {
      if (this.storeType) {
        this.formRule = {}
      } else {
        this.formRule = {
          tenant_id: [
            {
              required: true,
              message: this.$t('reaCommon.pleaseSelect') + ' ' + this.$t('reaCommon.tenant'),
              trigger: 'blur'
            }
          ],
          organize_id: [
            {
              required: true,
              message: this.$t('reaCommon.pleaseSelect') + ' ' + this.$t('reaCommon.organization'),
              trigger: 'blur'
            }
          ],
          store_name: [
            {
              required: true,
              message: this.$t('reaCommon.pleaseEnter') + ' ' + this.$t('reaCommon.storeName'),
              trigger: 'blur'
            }
          ],
          store_code: [
            {
              required: true,
              message: this.$t('reaCommon.pleaseEnter') + ' ' + this.$t('reaCommon.storeCode'),
              trigger: 'blur'
            }
          ],
          // province_city_county: [
          //   { required: true, message: this.$t('reaCommon.pleaseSelect') + ' ' + this.$t('reaCommon.pcc'), trigger: 'blur' }
          // ],
          channel_id: [
            {
              required: true,
              message: this.$t('reaCommon.pleaseSelect') + ' ' + this.$t('reaCommon.channelType'),
              trigger: 'blur'
            }
          ],
          status_id: [
            {
              required: true,
              message: this.$t('reaCommon.pleaseSelect') + ' ' + this.$t('reaCommon.storeStatus'),
              trigger: 'blur'
            }
          ],
          address: [
            {
              required: true,
              message: this.$t('reaCommon.pleaseEnter') + ' ' + this.$t('reaCommon.storeAddress'),
              trigger: 'blur'
            }
          ],
          mobile: [
            {
              required: true,
              message: this.$t('reaCommon.pleaseEnter') + ' ' + this.$t('reaCommon.phone'),
              trigger: 'blur'
            },
            {validator: formCheckPhone, trigger: 'blur'}
          ]
        }
      }
    },
    getStoreDetail() {
      storeDetail({id: this.form.id}).then((res) => {
        if (res.code == 1) {
          Object.keys(res.data).map((key) => {
            this.form[key] = res.data[key] || ''
          })
          if (res.data.store_type == 1) {
            this.storeType = 1
            this.formRule = {}
          }
          this.form.province_city_county = [this.form.province_id, this.form.city_id, this.form.county_id]
          this.getOrganizeTenant()
        }
      })
    },
    closeView() {
      const {path} = this.$route
      this.visitedViews.map((v) => {
        if (v.path === path) {
          this.$store.dispatch('tagsView/delView', v)
        }
      })
    },
    refreshSelectedTag() {
      let hasTag = false
      this.visitedViews.map((v) => {
        if (v.path === '/store-warehouse/index') {
          hasTag = true
          this.$store.dispatch('tagsView/delCachedView', v).then(() => {
            const {fullPath} = v
            this.$nextTick(() => {
              this.$router.replace({
                path: '/redirect' + fullPath
              })
            })
          })
        }
      })
      if (!hasTag) {
        this.$router.push({
          path: '/store-warehouse/index'
        })
      }
    },
    cancel() {
      this.closeView()
      this.$router.push({
        path: '/store-warehouse/index'
      })
    },
    save() {
      if (Array.isArray(this.form.province_city_county) && this.form.province_city_county.length === 3) {
        this.form.province_id = this.form.province_city_county[0]
        this.form.city_id = this.form.province_city_county[1]
        this.form.county_id = this.form.province_city_county[2]
      }
      this.submitting = true
      const ajax = this.form.id ? storeEdit : storeAdd
      ajax(this.form).then((res) => {
        if (res.code == 1) {
          this.$message.success(this.$t('reaCommon.operationSuccess'))
          this.closeView()
          this.refreshSelectedTag()
          // this.$router.push({
          //   path: '/store-warehouse/index'
          // })
        }
      }).finally(() => {
        this.submitting = false
      })
    },
    getTenantAll() {
      tenantAll().then((res) => {
        if (res.code == 1) {
          if (Array.isArray(res.data)) {
            res.data.map((v) => {
              v.value = v.id
              v.label = v.name
            })
            this.formData[this.optionsIndexObj['tenant_id']].options = res.data
          }
        }
      })
    },
    getStoreStatusList() {
      storeStatus().then((res) => {
        if (res.code == 1) {
          if (Array.isArray(res.data)) {
            res.data.map((v) => {
              v.label = v.name
              v.value = v.id
            })
            this.formData[this.optionsIndexObj['status_id']].options = res.data
          }
        }
      })
    },
    getOrganizeTenant() {
      organizeTenant({tenant_id: this.form.tenant_id}).then((res) => {
        if (res.code == 1 && Array.isArray(res.data)) {
          this.treeParams.data = res.data
          this.$nextTick(() => {
            this.$refs.treeSelect.treeDataUpdateFun(res.data)
          })
        }
      })
    },
    selectChangeHandler(val) {
      const {name} = val
      if (name == 'tenant_id') {
        this.form.organize_id = ''
        this.getOrganizeTenant()
      }
    },
    getCityList() {
      cityList().then((res) => {
        if (res.code == 1) {
          if (Array.isArray(res.data)) {
            this.formData[this.optionsIndexObj['province_city_county']].options = res.data
          }
        }
      })
    },
    getStoreChannelList() {
      storeChannelList().then((res) => {
        if (res.code == 1) {
          if (Array.isArray(res.data)) {
            res.data.map((v) => {
              v.label = v.name
              v.value = v.id
            })
            this.formData[this.optionsIndexObj['channel_id']].options = res.data
          }
        }
      })
    }
  }
}
</script>
<style lang="scss">
@import "~@/styles/variables.scss";

.is-current {
  color: $themeColor;
  font-weight: bold;
  background-color: $themeBgColor;
}
</style>
