<template>
  <div class="page_container account_frontend_view store__detail">
    <div class="store_mes wbg wbg_shadow">
      <div class="store_big_title flex flex-x-start flex-y-center">
        <div>{{ $t('reaCommon.storeName') }}：{{ storeDetail.store_name }}</div>
      </div>
      <div class="flex store_mes_col flex-x-start flex-y-start">
        <div>
          <span>{{ $t('reaCommon.storeId') }}：</span>{{ storeDetail.id || '-' }}
        </div>
        <div>
          <span>{{ $t('reaCommon.storeCode') }}：</span>{{ storeDetail.store_code || '-' }}
        </div>
        <div>
          <span>{{ $t('reaCommon.storeAddress') }}：</span>{{ storeDetail.address || '-' }}
        </div>
        <div>
          <span>{{ $t('reaCommon.fullScene') }}：</span>{{ storeDetail.full_scene ? '是' : '否' }}
        </div>
        <!--        <div>-->
        <!--          <span>{{ $t('reaCommon.fullScene') }}：</span>{{ sceneList.length || '0' }}-->
        <!--        </div>-->

        <!--        <div>-->
        <!--          <span>{{-->
        <!--              $t('reaCommon.versionInformation')-->
        <!--            }}：</span>{{ storeDetail.version_name }}-->
        <!--        </div>-->

      </div>
    </div>
    <div class="flex mt12 store__devlist--box">
      <div class="store_mes wbg wbg_shadow store__devlist">
        <div class="dev__item--tit flex-y-center">
          <div class="flex-1">{{ $t('reaCommon.configList') }}</div>
          <div class="flex-y-center">
            <div class="flex device__count">
              <div class="mr10">
                <span>{{ $t('reaCommon.deviceTotCount') }}：</span>{{ deviceCount || '0' }}
              </div>
              <div>
                <span>{{ $t('reaCommon.sceneTotCount') }}：</span>{{ sceneTotCount || '0' }}
              </div>
            </div>
            <div class="flex">
              <div class="flex-y-center flex-x-end" style="width: 30px;text-align: center">
                <span class="el-icon-loading mr10" v-show="isDeviceIng" ></span>
              </div>
              <el-select clearable @change="changeVersion"  v-model="versionCode" placeholder="请选择" size="small">
                <el-option
                    v-for="item in versionList"
                    :label="item.relation_id == -1 ? '全部版本' : (`${item.start_time} ~ ${item.end_time}`)"
                    :key="item.start_time+''"
                    :value="item.start_time+'_'+item.end_time"
                >
                  <div class="flex">
                    <div class="flex-1" v-if="item.relation_id == -1">全部版本</div>
                    <div class="flex-1" v-else>{{ `${item.start_time} ~ ${item.end_time}` }}</div>
                    <!--                <div class="version__text" v-if="versionList.length > 1">-->
                    <!--                  <span class="" v-if="storeDetail.version_code == item.relation_id">已启用版本</span>-->
                    <!--                  <span v-else-if="storeDetail.version_code_selected == item.relation_id">当前版本</span>-->
                    <!--                  <span class="" v-if="storeDetail.version_code == item.relation_id">当前版本</span>-->
                    <!--                  <span v-else-if="storeDetail.version_code_selected == item.relation_id">当前版本</span>-->
                    <!--                </div>-->
                  </div>
                </el-option>
              </el-select>
            </div>
          </div>
        </div>
        <el-table
            :data="deviceList"
            style="width: 100%"
        >
          <el-table-column
              type="index"
              :label="$t('reaCommon.serialNumber')"
              width="70"
          >
          </el-table-column>
          <el-table-column
              :label="$t('reaCommon.deviceCode')"
              prop="device_code"
          >
          </el-table-column>

          <el-table-column
              :label="$t('reaCommon.deviceName')"
              prop="device_name"
          >
          </el-table-column>
          <el-table-column
              :label="$t('reaCommon.sceneConfig')"
              prop="name"
          >
            <template slot-scope="{row}">
              {{ row.scene_unit_list && row.scene_unit_list.length || 0 }}
            </template>
          </el-table-column>
          <el-table-column
              label="设备状态"
              prop="status"
          >
            <template slot-scope="{row}">
              <el-tag :type="row.status == 0 ? 'info' : 'success'">{{ row.status == 0 ? '已禁用' : '已启用' }}</el-tag>
            </template>
          </el-table-column>
          <!--          <el-table-column-->
          <!--              :label="$t('reaCommon.operation')"-->
          <!--              width="125"-->
          <!--              prop="desc"-->
          <!--          >-->
          <!--            <template slot-scope="{row}">-->
          <!--              <el-link type="primary" :underline="false" v-if="$havePermission('1601',$route.meta.functionList)"-->
          <!--                       @click="bindScene(200, {group_id: row.id, name: row.device_group_name, index: row.device_group_code})"-->
          <!--              >-->
          <!--                {{ $t('reaCommon.config') }}-->
          <!--              </el-link>-->
          <!--            </template>-->
          <!--          </el-table-column>-->
          <el-table-column type="expand" width="30">
            <template slot-scope="{row}">
              <!--              <div class="overflow">-->
              <template v-if="row.scene_unit_list.length">
                <div v-for="(it, ind) in row.scene_unit_list" :key="ind" class="dev__scene fl"
                     style="background: rgba(50, 157, 255, 0.10)"
                >
                  <div class="name">{{ it.scene_unit_name }}</div>
                  <el-image
                      :preview-src-list="[it.scene_unit_image]"
                      class="face"
                      :src="it.scene_unit_image+'?imageView2/1/w/300/h/300'"
                      fit="contain"
                  ></el-image>
                </div>
              </template>
              <el-empty v-else :description="$t('reaCommon.noData')"></el-empty>
              <!--              </div>-->
            </template>
          </el-table-column>

        </el-table>
        <!--        <el-collapse v-model="activeNames" v-if="storeScene.length">-->
        <!--          <el-collapse-item v-for="(item, index) in storeScene" :name="index" :key="index">-->
        <!--            <template slot="title">-->
        <!--              <div class="dev__item&#45;&#45;tit flex overflow">-->
        <!--                <div class="flex-1">-->
        <!--                  <span>{{$t('reaCommon.deviceGroup')}}{{ index + 1 }}：{{ item.device_group_name }}</span>-->
        <!--                  <span>{{$t('reaCommon.sceneConfig')}}：{{ item.store_scene_list.length || 0 }}</span>-->
        <!--                </div>-->
        <!--                <div class="mr12">-->
        <!--                  <el-button size="small" v-if="$havePermission('1601',$route.meta.functionList)" type="primary"-->
        <!--                             @click="bindScene(200, {group_id: item.id, name: item.device_group_name, index: index+1})">-->
        <!--                    {{$t('reaCommon.config')}}-->
        <!--                  </el-button>-->
        <!--                </div>-->
        <!--              </div>-->
        <!--            </template>-->
        <!--            <div class="overflow">-->
        <!--              <template v-if="item.store_scene_list.length">-->
        <!--                <div v-for="(it, ind) in item.store_scene_list" :key="ind" class="dev__scene fl"-->
        <!--                     style="background: rgba(50, 157, 255, 0.10)">-->
        <!--                  <div class="name">{{ it.scene_name }}</div>-->
        <!--                  <el-image-->
        <!--                    :preview-src-list="[it.scene_images && it.scene_images[0]]"-->
        <!--                    class="face"-->
        <!--                    :src="it.scene_images && it.scene_images[0]+'?imageView2/1/w/300/h/300'"-->
        <!--                    fit="cover"></el-image>-->
        <!--                </div>-->
        <!--              </template>-->
        <!--              <el-empty v-else :description="$t('reaCommon.noData')"></el-empty>-->
        <!--            </div>-->
        <!--          </el-collapse-item>-->
        <!--        </el-collapse>-->
        <!--        <div v-else class="flex-center flex-y-center" style="height: 100%">-->
        <!--          <el-empty :description="$t('reaCommon.noData')" :image-size="120"></el-empty>-->
        <!--        </div>-->
      </div>
      <div class="flex-1 flex__direction wbg wbg_shadow store_mes plan__content" ref="viewerBox">
        <div class="plan__tabs">
          <el-tabs v-model="planActiveName" class="flex-1 mb0">
            <el-tab-pane :label="$t('reaCommon.storeImage')" name="store2d"></el-tab-pane>
            <el-tab-pane :label="$t('store.store3D')" name="store3d"></el-tab-pane>
          </el-tabs>
          <!--有编辑权限可上传-->
          <div class="btn flex-y-center" v-if="$havePermission('0301',$route.meta.functionList)">
            <!--            <el-button size="small" plain type="primary" @click="uplaodFile(200)">{{ $t('reaCommon.uploadF') }}-->
            <!--            </el-button>-->
            <!--              <el-button size="small" type="primary"-->
            <!--                         v-if="storeDetail.store_plan_url && $havePermission('1601',$route.meta.functionList)"-->
            <!--                         @click="bindScene(100)"-->
            <!--              > {{ $t('reaCommon.config') }}-->
            <!--              </el-button>-->
            <div class="flex-y-center flex-1">
              <span class="f13"><i class="el-icon-loading" v-if="isStoreIng"></i> 版本：</span>
              <el-select  @change="changeStoreVersion" v-model="versionStoreCode" placeholder="请选择" size="small"
                         style="margin-right: 10px;width: 125px"
              >
                <el-option
                    v-for="item in versionStoreList"
                    :label="(`${item.start_time} ~ ${item.end_time}`)"
                    :key="item.id"
                    :value="item.id"
                >
                  <div class="flex">
<!--                    <div class="flex-1" v-if="item.relation_id == -1">全部版本</div>-->
                    <div class="flex-1">{{ `${item.start_time} ~ ${item.end_time}` }}</div>
                  </div>
                </el-option>
              </el-select>
            </div>
            <div>
              <el-button size="small" type="primary"
                         @click="$router.push({path: '/store/version', query: {name: storeDetail.store_name, store_code: storeDetail.store_code}})"
              > {{ $t('reaCommon.config') }}
              </el-button>
            </div>

          </div>

        </div>
        <div v-show="planActiveName == 'store2d'" class="wbg model__drawing direction__flex">
          <div class="dev__item--tit flex-center">
            <div class="flex-1 scene__count">
              <!--              <span>{{ $t('reaCommon.storeImage') }}</span>-->
              <span> {{ $t('reaCommon.sceneConfig') }}：{{ planCount }}</span>
            </div>

          </div>
          <div class="flex__direction" ref="imageBox">
            <SceneAnnotate
                :isPopup="true"
                v-if="storeDetail.store_plan_url"
                :src="storeDetail.store_plan_url"
                :canvasW="imageBoxW" :canvasH="imageBoxH"
                ref="planeFigure"
            ></SceneAnnotate>
            <el-empty v-else :description="$t('reaCommon.noData')" :image-size="70"></el-empty>
          </div>
        </div>
        <div v-show="planActiveName == 'store3d'" class="wbg model__viewer--box direction__flex">
          <!--          <div class="dev__item&#45;&#45;tit flex-center">-->
          <!--            <div class="flex-1">{{ $t('store.store3D') }}</div>-->
          <!--            &lt;!&ndash;有编辑权限可上传&ndash;&gt;-->
          <!--            <div v-if="$havePermission('0301',$route.meta.functionList)">-->
          <!--              <el-button size="small" plain type="primary" @click="uplaodFile(100)">{{ $t('reaCommon.uploadF') }}-->
          <!--              </el-button>-->
          <!--            </div>-->
          <!--          </div>-->
<!--          <model-viewer v-if="storeDetail.store_model_url" class="model" ref="viewer" style="width: 100%;height: 90%"-->
<!--                        :src="storeDetail.store_model_url"-->
<!--                        alt="A 3D model of an astronaut"-->
<!--                        ar camera-controls-->
<!--                        camera-target="2.1m 0.5m -0.1278m"-->
<!--                        shadow-intensity="1" camera-orbit="-29.02deg 62.75deg 11.57m"-->
<!--                        field-of-view="13.41deg"-->
<!--                        min-field-of-view="2deg"-->
<!--          ></model-viewer>-->
<!--          <el-empty v-else :description="$t('reaCommon.noData')" :image-size="70"></el-empty>-->
        </div>
      </div>
    </div>

    <!--上传文件-->
    <ele-dialog width="50%" :title="$t('reaCommon.uploadFile')" @dialogClose="dialogCancel"
                :visible.sync="dialogVisible"
                center @dialogCancel="dialogCancel" @dialogConfirm="sbumitFun()"
    >

      <template slot="content">
        <div class="dialog_form">
          <el-form ref="dialogForm" size="medium" :model="uploadDialogForm" :rules="dialogRules" label-width="0"
                   class="demo-ruleForm"
          >
            <el-form-item label="" prop="file_url">
              <Upload ref="uploadImage1"
                      :accept="uploadDialogForm.type == 100 ? 'glb' : 'image/png, image/jpeg, image/jpg'" class="flex-1"
                      fileRoutePrefix="store_scene"
                      @uploadSuccess="uploadSuccess" :isProgress="uploadDialogForm.type == 100 ? true : false"
              ></Upload>
              <!--              <el-image-->
              <!--                  v-if="uploadDialogForm.url"-->
              <!--                  :preview-src-list="[uploadDialogForm.url]"-->
              <!--                  class="scene__face"-->
              <!--                  :src="dialogForm.url+'?imageView2/1/w/80/h/80'"-->
              <!--                  fit="contain"></el-image>-->
            </el-form-item>
          </el-form>
        </div>
      </template>
    </ele-dialog>

  </div>
</template>
<script>
import Upload from '@/components/Upload'
import SceneAnnotate from '@/components/SceneAnnotate'
import { storeModelEdit, storeOne } from '@/api/store'
import { devWithScene } from '@/api/device'
import { storeSceneList, storeVersionList, storeVesList } from '@/api/list'
import { deepJSON, doRectanglesIntersect } from '@/utils/tools'
import { sceneTypeTree } from '@/api/common'
import EleDialog from '@/components/Dialog/index.vue'
import Config from '@/config'

export default {
  name: '',
  components: {
    Upload,
    EleDialog,
    SceneAnnotate
  },
  mixins: [],
  data() {
    return {
      isDeviceIng: false,
      isStoreIng: true,
      planActiveName: 'store2d',
      planBoundingConfig: deepJSON(Config).planBoundingConfig,
      versionCode: '_',
      versionList: [],
      deviceList: [],
      query: {},
      storeDetail: {},
      activeNames: '1',
      imageBoxW: 0,
      imageBoxH: 0,
      sceneList: [],
      deviceCount: 0,
      planCount: 0,
      loading: false,
      uploadDialogForm: {
        file_url: '',
        type: 100 // 100=3d文件 200=2d图片
      },
      dialogRules: {
        file_url: [
          { required: true, message: '请上传文件', trigger: 'blur' }
        ]
      },
      dialogVisible: false,
      isSubmit: false,
      versionStoreList: [],
      versionStoreCode: '',
      sceneTotCount: 0
    }
  },
  computed: {},
  created() {
    this.query = this.$route.query
    // this.versionCode = this.query.version || ''
    this.getStoreVersion()
  },
  mounted() {
    this.initDomSize()
    this.$nextTick(() => {
      this.getDetail()
      this.getDeviceList()
    })
  },
  methods: {
    /**
     * 初始化平面图配置
     * */
    initPlanBoundingConfig(plan_url){
      const { emptyImageSuffix } = this.planBoundingConfig
      this.planBoundingConfig.isDefImage = false
      if (plan_url && plan_url.indexOf(emptyImageSuffix) != -1) {
        this.makeEmptyScenePossess()
        this.planBoundingConfig.isDefImage = true
      }
      this.getSceneList()
    },
    /**
     * 切换3D/2D版本
     * */
    changeStoreVersion(){
      this.isStoreIng = true
      const item = this.versionStoreList.find(v => v.id == this.versionStoreCode)
      if(item){
        this.storeDetail.store_model_url = item.model_url || ''
        this.storeDetail.store_plan_url = item.plan_url || ''
        if(item.plan_url){
          this.planBoundingConfig = deepJSON(Config).planBoundingConfig
          this.initPlanBoundingConfig(item.plan_url)
          setTimeout(() => {
            this.$refs.planeFigure && this.$refs.planeFigure.updateBackground({ url: item.plan_url })
          }, 600)
        }
      }
    },
    /**
     * 售点版本列表
     * */
    getVersionList() {
      storeVesList({store_code: this.storeDetail.store_code,page_num: 1, page_size: 9999}).then(res => {
        if(res.list && res.list.length){
          const {start_time, plan_url, model_url, id} = res.list[0]
          this.storeDetail.store_plan_url = plan_url
          this.storeDetail.store_model_url = model_url
          this.versionStoreCode = id
          this.initPlanBoundingConfig(plan_url)
        }
        this.versionStoreList = res.list
        this.$forceUpdate()
      })
    },
    /**
     * 点击上传文件按钮
     * */
    uplaodFile(type) {
      this.dialogVisible = true
      this.uploadDialogForm.type = type
      this.uploadDialogForm.file_url = ''
      // this.$refs.dialogForm && this.$refs.dialogForm.clearValidate('file_url');
      if (this.$refs.uploadImage1) {
        this.$refs.uploadImage1.clearFun()
      }
      if (this.$refs['dialogForm']) {
        this.$refs['dialogForm'].clearValidate()
      }
      this.$forceUpdate()
    },
    /**
     * 上传文件成功
     * */
    uploadSuccess(e) {
      // 临时url
      // this.uploadDialogForm.url = e.imageSrc
      this.$refs.dialogForm.clearValidate('file_url')
    },
    /**
     * 文件上传提交
     * */
    sbumitFun() {
      const { type } = this.uploadDialogForm
      const imageSrc = this.$refs.uploadImage1.imageSrc
      if (imageSrc) {
        this.uploadDialogForm.file_url = imageSrc
        this.$forceUpdate()
      }
      // this.$refs.usernameItem.validateField('username');
      //
      this.$refs['dialogForm'].validate((valid) => {
        if (valid) {
          if (this.isSubmit) {
            return
          }
          this.isSubmit = true
          let params = {
            start_time: this.versionCode && this.versionCode.split('_')[0],
            store_code: this.query.code,
            model_url: '',
            plan_url: ''
          }
          if (type == 100) {
            delete params.plan_url
            params.model_url = imageSrc
          } else {
            delete params.model_url
            params.plan_url = imageSrc
          }
          storeModelEdit(params).then(res => {
            this.dialogVisible = false
            this.$message.success(this.$t('reaCommon.operationSuccess'))
            if (type == 100) {
              this.storeDetail.store_model_url = imageSrc
            } else {
              this.storeDetail.store_plan_url = imageSrc
              setTimeout(() => {
                this.$refs.planeFigure && this.$refs.planeFigure.updateBackground({ url: imageSrc })
              }, 600)
            }
            this.$forceUpdate()
          }).finally(() => {
            setTimeout(() => {
              this.isSubmit = false
            }, 800)
          })
        }
      })
    },
    /**
     * 取消编辑
     * */
    dialogCancel() {
      this.dialogVisible = false
    },
    /**
     * 切换售点版本
     * */
    changeVersion(e) {
      this.isDeviceIng = true
      this.getDeviceList()
      // let str = `${this.query.id}_${this.versionCode}`
      // localStorage.versionCode = str
      // let url = location.href
      // const versionIndex = location.href.indexOf('&version')
      // if (versionIndex !== -1) {
      //   url = url.substring(0, versionIndex)
      // }
      // location.href = url + '&version=' + this.versionCode
    },
    /**
     * 设置默认版本号
     * */
    setVersion() {
      if (this.versionList.length && this.storeDetail.store_code) {
        let versionItem = this.versionList.length && this.versionList[0] || ''
        if (versionItem) {
          this.storeDetail.version_name = (versionItem.relation_id == -1) ? '全部版本' : `${versionItem.start_time} ~ ${versionItem.end_time}`
          this.storeDetail.version_code = `${versionItem.start_time}_${versionItem.end_time}`
        }
      }
    },
    /**
     * 获取售点版本号
     * */
    getStoreVersion() {
      // if(localStorage.versionCode){
      //   const localVersionCode = localStorage.versionCode
      //   const arr = localVersionCode.split('_')
      //   if(arr.length && arr[0] == this.query.id){
      //     this.versionCode = arr[1] * 1
      //   } else {
      //     localStorage.removeItem('versionCode')
      //   }
      // }
      storeVersionList({ store_code: this.query.code }).then(res => {
        // const { id } = this.storeDetail
        // if (id) {
        //   const item = res.list.find(v => v.version_code == version_code)
        //   item && (this.storeDetail.version_name = item.version_name)
        // }
        res.unshift({
          end_time: '',
          relation_id: -1,
          start_time: ''
        })
        this.versionList = res
        this.setVersion()
        this.$forceUpdate()
      })
    },
    /**
     * 获取初始化尺寸
     * */
    initDomSize() {
      const imageBox = this.$refs.imageBox
      if (imageBox) {
        this.imageBoxW = imageBox.offsetWidth
        this.imageBoxH = imageBox.offsetHeight
      }
    },
    /**
     * 生成空场景占位
     * */
    makeEmptyScenePossess() {
      let { PLAN_BOUNDING_BOX_DEFAULT, PLAN_BOUNDING_BOX_SPACE_H, PLANO_BOUNDING_BOX_COUNT_H } = this.planBoundingConfig
      for (let i = 0; i < PLANO_BOUNDING_BOX_COUNT_H; i++) {
        Object.keys(PLAN_BOUNDING_BOX_DEFAULT).filter(v => {
          let { pos } = PLAN_BOUNDING_BOX_DEFAULT[v]
          const { tl, tr, bl, br } = pos
          let disW = (PLAN_BOUNDING_BOX_SPACE_H + (tr[0] - tl[0])) * i
          let plan_bounding_box = {
            tl: [tl[0] + disW, tl[1]],
            tr: [tr[0] + disW, tr[1]],
            bl: [bl[0] + disW, bl[1]],
            br: [br[0] + disW, br[1]]
          }
          if (!PLAN_BOUNDING_BOX_DEFAULT[v].boundingBoxArr) {
            PLAN_BOUNDING_BOX_DEFAULT[v].boundingBoxArr = []
          }
          PLAN_BOUNDING_BOX_DEFAULT[v].boundingBoxArr.push({ pos: plan_bounding_box, isOccupy: false })
        })
      }
    },
    /**
     * 计算平面图打点
     * */
    setPlaneFigure() {
      // if(!this.sceneList.length){
      //   return
      // }
      let isFirst = true
      const sceneList = this.sceneList
      let planCount = 0
      let list = []
      // is_delete需要过滤，如果没有坐标则自动计算位置，有坐标正常打上去
      let { PLAN_BOUNDING_BOX_DEFAULT, isDefImage } = this.planBoundingConfig
      const sceneFun = (call) => {
        sceneList.filter(v => {
          if(!v.is_delete && v.device_code){
            if(isDefImage){
              const containsAny = Object.keys(PLAN_BOUNDING_BOX_DEFAULT).find(item => v.scene_type_code.includes(item))
              if(containsAny){
                if(call){
                  call(containsAny, v)
                } else {
                  let { boundingBoxArr } = PLAN_BOUNDING_BOX_DEFAULT[containsAny]
                  // 没坐标的去掉被占的位置
                  if (v.plan_bounding_box && !Object.keys(v.plan_bounding_box).length || !v.plan_bounding_box) {
                    let item = boundingBoxArr.find(val => !val.isOccupy)
                    if (item) {
                      v.plan_bounding_box = item.pos
                      item.isOccupy = true
                    }
                  }
                  if (!v.device_code) {
                    v.plan_bounding_box = {}
                  }
                }
              }
            }
            if(!call){
              list.push(v)
              if(v.plan_bounding_box && Object.keys(v.plan_bounding_box).length){
                planCount++
              }
            }
          }
        })
      }
      sceneFun((containsAny, v) => {
        let { boundingBoxArr } = PLAN_BOUNDING_BOX_DEFAULT[containsAny]
        // 有坐标算好占位
        if(v.plan_bounding_box && Object.keys(v.plan_bounding_box).length){
          boundingBoxArr = boundingBoxArr.map(val => {
            let haveInterwoven = doRectanglesIntersect(val.pos, v.plan_bounding_box)
            if(haveInterwoven){
              val.isOccupy = true
            }
            return val
          })
        }
        PLAN_BOUNDING_BOX_DEFAULT[containsAny].boundingBoxArr = boundingBoxArr
      })
      sceneFun()
      // sceneList.filter(v => {
      //   if(!v.is_delete && v.device_code){
      //     if(isDefImage){
      //       const containsAny = Object.keys(PLAN_BOUNDING_BOX_DEFAULT).find(item => v.scene_type_code.includes(item))
      //       if(containsAny){
      //         let { boundingBoxArr } = PLAN_BOUNDING_BOX_DEFAULT[containsAny]
      //         // 有坐标算好占位
      //         if(v.plan_bounding_box && Object.keys(v.plan_bounding_box).length){
      //           boundingBoxArr = boundingBoxArr.map(val => {
      //             let haveInterwoven = doRectanglesIntersect(val.pos, v.plan_bounding_box)
      //             if(haveInterwoven){
      //               val.isOccupy = true
      //             }
      //             return val
      //           })
      //         }
      //         // 没坐标的去掉被占的位置
      //         if(v.plan_bounding_box && !Object.keys(v.plan_bounding_box).length || !v.plan_bounding_box){
      //           let item = boundingBoxArr.find(val => !val.isOccupy)
      //           if (item) {
      //             v.plan_bounding_box = item.pos
      //             item.isOccupy = true
      //           }
      //         }
      //         if (!v.device_code) {
      //           v.plan_bounding_box = {}
      //         }
      //       }
      //     }
      //     list.push(v)
      //     if(v.plan_bounding_box && Object.keys(v.plan_bounding_box).length){
      //       planCount++
      //     }
      //   }
      // })
      this.planCount = planCount
      setTimeout(() => {
        this.$refs.planeFigure && this.$refs.planeFigure.initParams({ allScene: list, isLock: true })
      }, 600)
      // }
    },
    /**
     * 获取版本号时间
     * */
    // getVersionTime(){
    //   const item = this.versionList.find(v => v.start_time == this.versionCode)
    //   if(item){
    //     return `${item.start_time}`
    //   }
    //   return ''
    // },
    /**
     * 获取场景列表
     * */
    getSceneList() {
      storeSceneList({ store_code: this.query.code, store_model_id: this.versionStoreCode}).then(res => {
        this.sceneList = res
        // this.planCount = res.length
        this.setPlaneFigure()
        // this.bindDeviceWithScene()
        setTimeout(() => {
          this.isStoreIng = false
        }, 200)
      })
    },
    getChoseTime(){
      if(this.versionCode){
        const [start_time, end_time] = this.versionCode.split('_')
        return {start_time, end_time}
      }
      return ''
    },
    /**
     * 获取售点详情
     * */
    getDetail() {
      this.loading = true
      let params = {
        store_code: this.query.code,
        start_time: '',
        end_time: ''
      }
      params = Object.assign(params, this.getChoseTime())
      storeOne(params).then(res => {
        // const {id, version_code} = res
        // if(id && this.versionList.length){
        //   const item = this.versionList.find(v => v.version_code == version_code)
        //   item && (res.version_name = item.version_name)
        // }
        this.storeDetail = res
        this.setVersion()
        this.setPlaneFigure()
        this.getVersionList()
        // this.versionCode = res.version_code_selected || ''
      }).finally(e => {
        setTimeout(e => {
          this.loading = false
        }, 500)
      })
    },
    /**
     * 设备和场景互相匹配
     * */
    bindDeviceWithScene() {
      // if (this.deviceList.length && this.sceneList.length) {
      //   this.deviceList = this.deviceList.map(v => {
      //     // if (!v.store_scene_list) {
      //       v.store_scene_list = []
      //     // }
      //     this.sceneList.filter(val => {
      //       if (val.device_code == v.device_code && val.plan_bounding_box && Object.keys(val.plan_bounding_box).length) {
      //         v.store_scene_list.push(val)
      //       }
      //     })
      //     return v
      //   })
      // }
    },
    /**
     * 设备列表
     * */
    getDeviceList() {
      let params = {
        store_code: this.query.code,
        start_time: '',
        end_time: ''
      }
      params = Object.assign(params, this.getChoseTime())
      devWithScene(params).then(res => {
        res = res || []
        this.deviceCount = res.length
        let sceneTotCount = 0
        res.filter(v => {
          const count = v.scene_unit_list && v.scene_unit_list.length
          if(count){
            sceneTotCount += count
          }
        })
        this.sceneTotCount = sceneTotCount
        this.deviceList = res
        // this.bindDeviceWithScene()
        setTimeout(()=> {
          this.isDeviceIng = false
        }, 200)
      })
    },
    /**
     * 去绑定场景
     * type 100=2d图 200=场景
     * */
    bindScene(type, param) {
      let query = {
        type,
        index: type == 200 && param.index,
        name: type == 100 ? this.$t('reaCommon.storeImageConfig') : param.name,
        group_id: param && param.group_id,
        store_id: this.query.id,
        version: this.query.version
      }
      this.$router.push({
        path: '/store/scene-annotate',
        query
      })
    }
  }
}
</script>

<style lang="scss" scoped>
$sceneBoxH: calc(100vh - 264px);
$drawingH: calc((100vh - 264px) / 2 - 70px);
.plan__tabs {
  position: relative;

  .btn {
    position: absolute;
    right: 0;
    top: 0px;
  }
}

.version__text {
  font-size: 13px;
  color: #8492a6;
  margin-left: 10px;
}
.device__count{
  font-size: 14px;
  //margin-right: 10px;
}

.store__detail {
  .store_mes {
    padding: 15px;


    .store_big_title {
      font-size: 18px;
      font-weight: 500;
      color: #262626;
      line-height: 28px;
      padding-bottom: 5px;
    }

    .store_mes_col {
      font-size: 14px;
      font-weight: 400;
      color: #737373;
      line-height: 22px;

      & > div {
        margin-right: 2%;
      }

      span {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #000;
        line-height: 22px;
      }
    }
  }

  .store__devlist--box {
    height: $sceneBoxH;
  }

  .store__devlist {
    width: 55%;
    //padding-top: 0;
    overflow-y: scroll;
    height: 100%;
    margin-right: 12px;

    .el-collapse {
      border-top: none !important;
    }

    .dev__item--tit {
      margin-bottom: 10px;
    }
  }

  .dev__item--tit {

    color: #000000;
    width: 100%;
    font-weight: 500;

    span {
      //margin-right: 5%;
    }
  }

  .scene__count {
    text-align: right;
    font-size: 13px;
    margin-bottom: 5px;
  }

  .dev__scene {
    width: 23%;
    border-radius: 4px;
    padding: 14px;
    margin-right: 2%;
    margin-bottom: 2%;

    .name {
      font-size: 14px;
      color: #000000;
      font-weight: 600;
      line-height: 19px;
      margin-bottom: 10px;
    }

    .face {
      width: 100%;
      height: 180px;
      display: block;
      background: #fff;
    }

    &:nth-child(4n) {
      margin-right: 0;
    }
  }

  .model__viewer--box {
    margin-top: 5px;
    height: calc(50% - 7px);

    .model {
      background: #000;
      margin-top: 10px;
    }
  }

  .model__drawing {
    /*height: calc(50% - 7px);*/
    box-sizing: content-box;

    img {
      width: 100%;
      height: $drawingH;
      display: block;
      object-fit: contain;
    }
  }

}
</style>
<style lang="scss">
.store__devlist {
  .el-table th.el-table__cell {
    background-color: #FAFAFA;
    color: #000000;
    font-size: 14px;
    border-bottom: none;
  }

  .el-table__expand-icon > .el-icon {
    color: #0055B5;
  }

}
</style>
