<template>
  <div class="page_container account_frontend_view">
    <div class="mb10">
      <el-link type="primary" @click="backCloseViewFun()" :underline="false" class="el-icon-d-arrow-left">{{$t('reaCommon.back')}}</el-link>
      <span class="head__title">{{ $route.query.name }}^{{ $route.query.code }}</span>
    </div>
    <div class="form_box">
      <search-form :value="queryParam" :form-data="formData" :label-width="searchLabelWidth"
                   @nodeConfigHandler="formHandler" @selectChangeHandler="selectChangeHandler"
                   @resetQueryParam="resetQueryParam">
        <template v-slot:scene_type_code="{row}">
          <el-select v-model="queryParam.scene_type_code" :placeholder="$t('reaCommon.pleaseEnter')" size="small" clearable>
            <el-option-group
                v-for="(group, index) in sceneList"
                :key="index"
                :label="group.scene_type_name">
              <el-option
                  v-for="(item, ind) in group.sub"
                  :key="ind"
                  :label="item.scene_type_name"
                  :value="item.scene_type_code">
              </el-option>
            </el-option-group>
          </el-select>
        </template>

      </search-form>
    </div>
    <div class="table_box wbg">
      <div class="control_box flex flex-y-center flex-x-start">
        <div class="flex-1"></div>
        <div>
          <el-button size="medium" v-if="$havePermission('0101',$route.meta.functionList)" icon="el-icon-plus"
                     type="primary" @click.native="addScene(100)"> {{ $t('reaCommon.add') }}
          </el-button>
        </div>
      </div>
      <new-table
          ref="newTable"
          :outerHeight="150"
          :loading="loading"
          :table-header="versionHeader"
          :table-data="dataSource"
          :pagination="pagination"
          @pageConfigHandler="pageHandler"
      >
        <template v-slot:effectiveStop="{row}">
          {{row.end_time.indexOf('9999') != -1 ? '长期有效' : row.end_time}}
        </template>
        <template v-slot:scene_images="{row}">
          <el-image
              :preview-src-list="row.scene_images"
              style="width: 50px;height: 50px"
              :src="row.scene_images[0]+'?imageView2/1/w/50/h/50'"
              fit="cover"
          />
        </template>
        <template v-slot:edit="{row}">
<!--          <el-dropdown>-->
            <span class="table__oprate--item" @click="openItem('/store/scene-annotate', {version_id: row.id, store_code: row.store_code, type: 100})">
              查看
            </span>
<!--            <el-dropdown-menu slot="dropdown">-->
<!--              <el-dropdown-item command="2" >-->
<!--                <div  @click="openItem('/store/scene', {id: row.store_id, name: row.store_name, version: row.version_code})"><i-->
<!--                    class="el-icon-document"></i>{{-->
<!--                    $t('reaCommon.sceneData')-->
<!--                  }}-->
<!--                </div>-->
<!--              </el-dropdown-item>-->
<!--              <el-dropdown-item command="3">-->
<!--                <div @click="openItem('/store/config-detail', {id: row.store_id, version: row.version_code})"><i class="el-icon-setting"></i>{{ $t('route.sceneDetail')}}-->
<!--                </div>-->
<!--              </el-dropdown-item>-->
<!--            </el-dropdown-menu>-->

<!--          </el-dropdown>-->
          <span class="table__oprate--item"
                @click="editFun(row)">{{ $t('reaCommon.edit') }}</span>
          <span class="table__oprate--item"
                @click="deleteFun(row)">{{ $t('reaCommon.delete') }}</span>
<!--            <span class="table__oprate&#45;&#45;item" v-if="row.is_switc"-->
<!--                  @click="changeVersion(row)">切换版本</span>-->
        </template>
        <template v-slot:currentVersion="{row}">
          <el-tag v-if="row.is_current_version">是</el-tag>
          <el-tag v-else type="info">否</el-tag>
        </template>

      </new-table>
    </div>

    <!--新增/编辑售点-->
    <div class="dialog_container">
      <ele-dialog :title="dialogForm.id ? $t('reaCommon.edit') : $t('reaCommon.add')" @dialogClose="dialogCancel"
                  :visible.sync="dialogVisible"
                  center @dialogCancel="dialogCancel" @dialogConfirm="sbumitFun()">

        <template slot="content">
          <div class="dialog_form">
            <el-form ref="dialogForm" size="medium" :model="dialogForm" :rules="dialogRules" label-width="175px"
                     class="demo-ruleForm">
<!--              <el-form-item :label="$t('reaCommon.versionCode')" prop="version_name">-->
<!--                <el-input v-model="dialogForm.version_name" clearable-->
<!--                          :placeholder="$t('reaCommon.pleaseEnter')"-->
<!--                          />-->
<!--              </el-form-item>-->
              <el-form-item label="是否继承" prop="extend">
                <el-checkbox v-model="dialogForm.extend">继承上个版本配置
                  <el-popover
                      placement="top-start"
                      title="提示"
                      width="200"
                      trigger="hover"
                      content="1、继承后，上个版本的平面图及配置、3D模型会默认添加到当前版本；2、继承且上传平面图和3D模型，则以最新上传的数据为准">
                    <span slot="reference" class="el-icon-info"></span>
                  </el-popover>
                </el-checkbox>
              </el-form-item>
              <el-form-item :label="$t('reaCommon.versionDesc')" prop="note">
                <el-input v-model="dialogForm.note" clearable
                          :placeholder="$t('reaCommon.pleaseEnter')"
                          />
              </el-form-item>
              <el-form-item label="上传平面图" prop="note">
                <Upload ref="uploadImage1" accept="image/png, image/jpeg, image/jpg" class="flex-1" fileRoutePrefix="store_plan"
                        @uploadSuccess="uploadSuccess"></Upload>
                <el-image
                    v-if="dialogForm.plan_url"
                    :preview-src-list="[dialogForm.plan_url]"
                    class="scene__face"

                    :src="dialogForm.plan_url+'?imageView2/1/w/80/h/80'"
                    fit="contain"></el-image>
<!--                <div class="chose__file flex-center" @click="uplaodFile(100)"><span class="el-icon-plus"></span></div>-->
              </el-form-item>
              <el-form-item label="上传3D模型" prop="note">
                <Upload ref="uploadImage2" accept="glbg" class="flex-1" fileRoutePrefix="store_model"
                        @uploadSuccess="uploadSuccessModel"></Upload>
                {{urlSplit(dialogForm.model_url)}}
<!--                <div class="chose__file flex-center"  @click="uplaodFile(200)"><span class="el-icon-plus"></span></div>-->
              </el-form-item>
              <el-form-item :label="$t('reaCommon.effectiveTime')" prop="start_time">
                <el-date-picker
                    v-model="dialogForm.start_time"
                    type="datetime"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :picker-options="pickerOptions"
                    :placeholder="$t('reaCommon.pleaseEnter')"
                    align="right">
                </el-date-picker>
                <div>{{$t('reaCommon.to')}}</div>
                <el-date-picker
                    :disabled="dialogForm.checked"
                    v-model="dialogForm.end_time"
                    type="datetime"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :picker-options="pickerOptions"
                    :placeholder="$t('reaCommon.pleaseEnter')"
                    align="right">
                </el-date-picker>
                <el-checkbox v-model="dialogForm.checked" @change="changeCheck" style="margin-left: 30px">{{$t('reaCommon.longTerm')}}</el-checkbox>
              </el-form-item>

            </el-form>
          </div>
        </template>
      </ele-dialog>
    </div>

  </div>
</template>
<script>

import EleDialog from '@/components/Dialog'
import Upload from '@/components/Upload'
import SearchForm from '@/components/SearchForm'
import NewTable from '@/components/Table'
import { versionHeader} from '@/utils/header'
import ListMixin from '@/mixins/List'
import EventMixin from '@/mixins/Event'
import {mapGetters} from 'vuex'
import {storeSceneDelete, storeSceneCreate, storeSceneEdit} from "@/api/scene"
import { storeList, storeVesList } from '@/api/list'
import {backendMenu, sceneTypeTree} from "@/api/common"
import Link from "@/layout/components/Sidebar/Link";
import {storeDeleteVersion, storeSwitchVersion, storeVersionCreate, storeVersionEdit} from "@/api/store/version";
import {deepJSON} from '@/utils/tools'
import {compareDates} from "@/utils/validate";
export default {
  name: 'gather-cooler',
  components: {
    Link,
    SearchForm,
    NewTable,
    Upload,
    EleDialog
  },
  mixins: [ListMixin, EventMixin],
  data() {
    const validElement = (rule, value, callback) => {
      if (!value.length) {
        callback(this.$t('reaCommon.pleaseEnter') + ' ' + this.$t('reaCommon.uploadSceneImage'))
      } else {
        callback()
      }
    }
    return {
      pickerOptions: {
        shortcuts: [{
          text: this.$t('reaCommon.today'),
          onClick(picker) {
            picker.$emit('pick', new Date());
          }
        }, {
          text: this.$t('reaCommon.yesterday'),
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() - 3600 * 1000 * 24);
            picker.$emit('pick', date);
          }
        }, {
          text: this.$t('reaCommon.thisWeek'),
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', date);
          }
        }]
      },
      visible: false,
      uploadDialogForm: {
        file_url: '',
        type: 100 // 100=3d文件 200=2d图片
      },
      uploadDialogRules: {
        file_url: [
          { required: true, message: '请上传文件', trigger: 'blur' }
        ]
      },
      dialogForm: {
        plan_url: '',
        model_url: '',
        extend: true,
        id: '',
        store_code: '',
        start_time: '',
        end_time: '',
        note: '',
        checked: false,
        // store_id: '',
        // version_name: '',
        // version_desc: '',
        // start_at: '',
        // stop_at: '',
        // id: '',
        // checked: false,
      },
      dialogRules: {
        // extend: [
        //   {required: true, message: this.$t('reaCommon.pleaseSelect'), trigger: 'blur'}
        // ],
        start_time: [
          {required: true, message: this.$t('reaCommon.pleaseSelect'), trigger: 'blur'}
        ],
      },
      versionHeader,
      formData: [
        {
            name: '版本名称',
            nameKey: 'versionCode',
            type: 'input',
            field: 'version_name',
            attrs: {
                clearable: true
            }
        },{
              name: '选择时间',
              nameKey: 'selectTime',
              type: 'daterange',
              field: 'date',
          },],
      searchLabelWidth: '140px',
      queryParam: {
          store_code: '',
          tenant_id: '',
          store_id: '',
          start: '',
          end: '',
          id: '',
          version_code: '',
          version_name: '',
          status: ''
      },
      initQueryParam: { },
      dataSource: [],
      /* mixin里接口名称*/
      apiUrl: {
        list: 'storeVesList'
      },
      dialogVisible: false,
      sceneList: [],
      downListObj: [],
      isAssetFreezer: false,
      isSubmit: false,
    }
  },
  watch: {
    'dialogForm.end_time': {
      handler(newName, oldName) {
        if(newName){
          this.dialogForm.checked = false
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    visitedViews() {
      return this.$store.state.tagsView.visitedViews
    },
    ...mapGetters([
      'userInfo',
      'language'
    ])
  },
  created() {
    // this.queryParam.store_id = this.$route.query.id
    this.queryParam.store_code = this.$route.query.store_code
    this.initQueryParam = this.queryParam
  },
  mounted() {
    this.getBackendMenu()
  },
  methods: {
    urlSplit(url){
      let strArr = []
      let str = url
      if(url){
        strArr = url.split('/')
        if(strArr.length){
          str = strArr[strArr.length - 1]
        }
      }
      return str
    },
    /**
     * 上传平面图片
     * */
    uploadSuccess(e) {
      this.dialogForm.plan_url = e.imageSrc
      // this.$refs.dialogForm.clearValidate('scene_images');
    },
    /**
     * 上传3D模型
     * */
    uploadSuccessModel(e){
      this.dialogForm.model_url = e.imageSrc
    },
    /**
     * 点击上传文件按钮
     * */
    uplaodFile(type) {
      this.dialogVisible = true
      this.uploadDialogForm.type = type
      this.uploadDialogForm.file_url = ''
      // this.$refs.dialogForm && this.$refs.dialogForm.clearValidate('file_url');
      if (this.$refs.uploadImage1) {
        this.$refs.uploadImage1.clearFun()
      }
      if (this.$refs['uploadDialogForm']) {
        this.$refs['uploadDialogForm'].clearValidate()
      }
      this.$forceUpdate()
    },
    /**
     * 跳转
     * */
    openItem(path, query){
      this.$router.push({
        path,
        query
      })
    },
    /**
     * 勾选
     * */
    changeCheck(){
      const {checked} = this.dialogForm
      if(checked){
        this.dialogForm.end_time = ''
      }
    },
    /**
     * 切换版本
     * */
    changeVersion(row){
      this.$confirm('切换后无法切换更低版本，是否确认切换该版本？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      }).then(() => {
        storeSwitchVersion({store_id: this.$route.query.id, id: row.id, version_code: row.version_code}).then(res => {
          this.$message.success('版本切换成功')
          this.formHandler()
        })
      })
    },
    /**
     * 获取下拉列表数据
     * */
    getBackendMenu(){
      backendMenu({fields: ['store_scene_location','display_location','competitive_display_location','door_direction'], tenant_id: this.userInfo.tenant_id}).then(res => {
        this.downListObj = res
      })
    },
    /**
     * 获取售点列表
     * */
    getStoreList() {
      storeList().then(res => {
      })
    },
    /**
     * 编辑场景
     * */
    editFun(item) {
      Object.keys(this.dialogForm).filter(v => {
        this.dialogForm[v] = item[v]
      })
      if(item.end_time && item.end_time.indexOf('9999') != -1){
        this.dialogForm.checked = true
        this.dialogForm.end_time = ''
      }
      this.$forceUpdate()
      this.addScene()
    },
    /**
     * 添加场景
     * */
    addScene(type) {
      if (type == 100) {
        this.dialogForm = {
          plan_url: '',
          model_url: '',
          extend: true,
          id: '',
          store_code: '',
          start_time: '',
          end_time: '',
          note: '',
          checked: false,
        }
      }
      if (this.$refs['dialogForm']) {
        this.$refs['dialogForm'].clearValidate()
      }
      this.dialogVisible = true
    },
    /**
     * 取消编辑
     * */
    dialogCancel() {
      this.dialogVisible = false
    },
    /**
     * 场景编辑/新增
     * */
    sbumitFun() {
      this.$refs['dialogForm'].validate((valid) => {
        if (valid) {
          let params = deepJSON(this.dialogForm)
          if(!params.checked && !params.end_time){
            this.$message.error('请选择生效结束时间')
            return
          }
          if(!params.checked){
            if(compareDates(params.start_time, params.end_time)){
              this.$message.error('生效开始时间不能大于结束时间')
              return
            }
          }
          if (this.isSubmit) {
            return
          }
          this.isSubmit = true
          if(params.checked){
            params.end_time = '9999-12-31 23:59:59'
          }
          params.store_code = this.$route.query.store_code
          storeVersionEdit(params).then(res => {
            this.dialogVisible = false
            this.formHandler()
            // 创建新版本直接跳转
            if(!this.dialogForm.id){
              this.$router.push({
                path: '/store/scene-annotate',
                query: {version_id: res.id, store_code: params.store_code, type: 100}
              })
            } else {
              this.$message.success(this.$t('reaCommon.operationSuccess'))
            }
          }).finally(() => {
            setTimeout(() => {
              this.isSubmit = false
            }, 800)
          })
        }
      })
    },
    /**
     * 删除场景
     * */
    deleteFun(row) {
      this.$confirm(this.$t('reaCommon.confirmDeleteThisOne') + '?', this.$t('reaCommon.warning'), {
        confirmButtonText:  this.$t('reaCommon.confirm'),
        cancelButtonText:  this.$t('reaCommon.cancel'),
        type: 'warning'
      }).then(() => {
        if (row.loading) {
          return
        }
        row.loading = true
        storeDeleteVersion({id: row.id}).then((res) => {
          this.$message.success(this.$t('reaCommon.deleteSuccess'))
          this.formHandler()
        }).finally(() => {
          row.loading = false
        })
      })
    },
    formHandler() {
      if (Array.isArray(this.queryParam.date) && this.queryParam.date.length) {
        this.queryParam.start = this.queryParam.date[0]
        this.queryParam.end = this.queryParam.date[1]
      } else {
        this.queryParam.start = ''
        this.queryParam.end = ''
      }
      this.searchQuery()
    },
    selectChangeHandler(selectObj) {
      const {name, value} = selectObj
    },
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";
.chose__file{
  position: relative;
  padding: 0 24px;
  cursor: pointer;
  height: 35px;
  line-height: 35px;
  background: #EFF7FF;
  border-radius: 4px;
  border: 1px dashed #0471D4;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #1890FF;
}
.scene__face {
  width: 100px;
  height: 100px;
  border: 1px solid #f2f2f2;
  margin-top: 10px;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 60px;
  height: 60px;
  line-height: 60px;
  text-align: center;
}

.table_box.mt0 {
  margin-top: 0;
}

.table_box {
  margin-top: 23px;
  padding: 18px 25px 0;
}

.control_box {
  padding: 0 0 17px;
  padding-bottom: 18px;
}

.reset_password ::v-deep .el-input__inner {
  border: none;
}

.frontend_control_box ::v-deep .el-button--default {
  border-color: $themeColor;
  color: $themeColor;
}

.dot {
  margin-right: 8px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #909399;
}

.dot.primary {
  background-color: $themeColor;
}

.dot.success {
  background-color: #36C08E;
}

.dot.warnning {
  background-color: #E6A23C;
}


.dot.danger {
  background-color: #E52828;
}

.header_scroll_box {
  height: 270px;
  padding: 5px 0;
  overflow-y: auto;

  &::-webkit-scrollbar-track-piece {
    background: #D3E6E6;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #99a9bf;
    border-radius: 20px;
  }

}
</style>
<style lang="scss">
@import "~@/styles/variables.scss";

.table_header_set_box {
  padding: 0 10px !important;

  .el-checkbox__label {
    color: #333;
  }
}

.cursor {
  cursor: pointer;
}

.text__align--center {
  text-align: center;
}

.pointer {
  cursor: pointer;
}


</style>
