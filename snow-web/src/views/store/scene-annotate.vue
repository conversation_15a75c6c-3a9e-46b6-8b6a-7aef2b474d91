<template>
  <div class="account_frontend_view" @keydown.delete="keydownFun">
    <div class="head__annotate">
      <!--      <el-link type="primary" @click="backCloseViewFun()" :underline="false" class="el-icon-d-arrow-left">{{$t('reaCommon.back')}}</el-link>-->
      <div class="flex-y-center" style="width: calc(72%)">
        <div class="head__title flex-1" v-if="query.type == 200">
          <i>{{ $t('reaCommon.deviceCode') }}：{{ query.index }}</i>
          <i>{{ $t('reaCommon.deviceName') }}：{{ query.name }}</i>
        </div>
        <div class="head__title flex-1" v-else>
          <i>{{ query.name }}</i>
        </div>
      </div>
    </div>
    <div class="scene__annotate flex wbg">
      <div class="flex-center scene__fabric flex-y-center flex-1" ref="imageBox">
        <el-button class="button__bor whole__all" size="small" @click="boxSelectAll" v-if="query && query.type == 200">
          {{ $t('sceneAnnotate.boxSelectAll') }}
        </el-button>
        <div class="chose__color flex-y-center" v-if="query && query.type == 100">
          <div>{{ $t('store.fillColor') }}：</div>
          <div>
            <span @click="choseColor(index)" v-for="(item, index) in fillColor" :key="index"
                  :style="{'background': item.color}" class="def__bg" :class="{'active': item.active}"
            ></span>
          </div>
        </div>
        <SceneAnnotate
            v-if="storeDetail.plan_url"
            @updateCoords="updateCoords"
            @delCoords="delCoords"
            :src="storeDetail.plan_url"
            :canvasW="imageBoxW" :canvasH="imageBoxH"
            ref="planeFigure"
        ></SceneAnnotate>
        <el-empty v-else :description="$t('store.noImage')"></el-empty>
      </div>
      <div class="scene__img flex__direction">
        <div class="dev__scene" style="background: #EAEAEA" v-if="choseItem.name || allBindOther">
          <div class="scene__empty--des flex-y-center flex-x-center" v-if="allBindOther">
            <div>
              <i class="el-icon-picture"></i>
              <div class="text">{{ $t('reaCommon.noSceneInfo') }}</div>
            </div>
          </div>
          <template v-else>
            <div class="name">{{ choseItem.name }}</div>
            <el-image
                :preview-src-list="[choseItem.image]"
                class="face"
                :src="choseItem.image"
                fit="contain"
            ></el-image>
          </template>
        </div>
        <div class="scene__list direction__flex">
          <div class="flex-y-center">
            <div class="count flex-1">{{ $t('reaCommon.alsoSceneConfig') }}：<span>{{ finishedAmount }}/{{
                amount
              }}</span></div>
<!--            <div class=" flex-y-center legend__group">-->
<!--              <div>{{ $t('reaCommon.thisDeviceGroup') }}</div>-->
<!--              <div class="ot">{{ $t('reaCommon.otherDeviceGroup') }}</div>-->
<!--            </div>-->
          </div>
          <div>
            <div v-for="(item, index) in sceneList" :key="index" class="scene__list--item">
              <div class="type">{{ item.name }}：</div>
              <div class="face__item--box">
                <div v-for="(it, index) in item.items" :key="index" class="face__item"
                     :class="{'active': it.active, 'other_group_active': it.active && it.is_other, 'opt4': !it.device_code}"
                     @click="choseScene(it)"
                >
                  <div class="tost flex-center" :class="{'other_group_active': it.is_other}"
                       v-if="it.bounding_box && Object.keys(it.bounding_box).length || it.is_all"
                  >
                    <span class="el-icon-circle-check"></span>
                  </div>
                  <el-image
                      class="face"
                      :src="it.image+'?imageView2/1/w/200/h/200'"
                      fit="cover"
                  ></el-image>
                </div>
              </div>
            </div>
            <el-empty v-if="!sceneList.length" :image-size="200"></el-empty>
          </div>
        </div>
        <div class="flex flex-center submit" v-if="sceneList.length">
          <el-button size="small" @click="backCloseViewFun()" class="button__bor">{{ $t('reaCommon.back') }}</el-button>
          <el-button size="small" type="primary" @click.native="submitFun" :loading="isSubmit">
            {{ $t('reaCommon.submit') }}
          </el-button>
        </div>
      </div>
    </div>
    <!--展示图片弹出框-->
    <el-dialog
        title="提示"
        center
        :visible.sync="dialogVisible"
        width="30%"
    >
      <div class="scene__dialog" v-if="sceneOtherGroup">
        <div class="tit">{{ $t('reaCommon.sureUnlinkScene') }}</div>
        <div class="scene__dialog--info">
          <el-image
              class="scene__dialog--image"
              :src="sceneOtherGroup.image"
              fit="contain"
          ></el-image>
          <div class="name">{{ $t('reaCommon.sceneName') }}：{{ sceneOtherGroup.name }}</div>
          <div class="dev">{{ $t('reaCommon.alsoLinkDevice') }}：{{ query.name }}</div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">{{ $t('reaCommon.cancel') }}</el-button>
          <el-button type="primary" @click="unbindScene">{{ $t('reaCommon.confirm') }}</el-button>
        </span>
    </el-dialog>

  </div>
</template>

<script>
import SceneAnnotate from '@/components/SceneAnnotate'
// import { fabric } from 'fabric'
import { uuid } from 'vue-uuid'
import { deepJSON, calculatePostion, calculateRectangleCorners, doRectanglesIntersect } from '@/utils/tools'
import { storeSceneTree, storeSceneBind } from '@/api/scene'
import { devWithSceneBind, deviceOne } from '@/api/device'
import { storeOne } from '@/api/store'
import EventMixin from '@/mixins/Event'
import { storeSceneList } from '@/api/list'
import { storeVersionDetail } from '@/api/store/version'
import Config from '@/config'

export default {
  components: {
    SceneAnnotate
  },
  mixins: [EventMixin],
  data() {
    return {
      planBoundingConfig: deepJSON(Config).planBoundingConfig,
      fillColor: [
        {
          name: '默认颜色',
          color: '#00FCFF',
          active: true
        },
        {
          name: '可乐颜色',
          color: 'rgba(246,24,24,.7)',
          active: false
        }
      ],
      dialogVisible: false,
      choseItem: {},
      sceneList: [],
      contextmenuVisible: true,
      imageBoxW: 0,
      imageBoxH: 0,
      amount: 0,
      finishedAmount: 0,
      storeDetail: {},
      isSubmit: false,
      sceneOtherGroup: null,
      allBindOther: false,
      titleName: '',
      query: {},
      haveSubmitBoundingBox: false
    }
  },
  // watch: {
  //     sceneList: {
  //         handler(newValue) {
  //
  //             console.log(newValue, 'ruleObj')
  //         },
  //         deep: true
  //     },
  // },
  // computed:{
  //     // 已完成配置数量
  //     finishedCount(){
  //     }
  // },
  mounted() {
    this.initDomSize()
    const { type, name, index, version } = this.query = this.$route.query
    // this.titleName = name
    // if (type == 200) {
    //   // this.titleName = `${this.$t('reaCommon.deviceGroup')}${index}：${name}`
    //   this.titleName = `${this.$t('reaCommon.deviceCode')}：${index}${name}`
    // }
    this.getDetail()

    if (type == 200) {
    }
    document.onkeydown = (e) => {
      let key = window.event.keyCode
      console.log('查看键盘数88888', key)
      if (key == 8) {
      }
    }
  },
  methods: {
    /**
     * 监听键盘按下事件
     * */
    keydownFun(e) {
      console.log('查看键盘数99999', e)
    },
    /**
     * 框选全部
     * */
    boxSelectAll() {
      if (this.$refs.planeFigure) {
        this.$refs.planeFigure.drawCover()
      }
    },
    /**
     * 选择颜色
     * */
    choseColor(index) {
      let color = ''
      this.fillColor = this.fillColor.map((v, ind) => {
        v.active = false
        if (index == ind) {
          v.active = true
          color = v.color
        }
        return v
      })
      this.sceneList = this.sceneList.map(v => {
        v.items = v.items.map(val => {
          if (val.active) {
            val.fill_color = color
            val.is_update = true
            this.choseItem = val
          }
          return val
        })
        return v
      })
      if (this.$refs.planeFigure) {
        this.$refs.planeFigure.initParams({ oneScene: this.choseItem })
      }
    },
    /**
     * 获取初始化尺寸
     * */
    initDomSize() {
      const imageBox = this.$refs.imageBox
      if (imageBox) {
        this.imageBoxW = imageBox.offsetWidth
        this.imageBoxH = imageBox.offsetHeight
      }
    },
    /**
     * 获取售点详情
     * */
    getDetail() {
      const { version_id } = this.query
      storeVersionDetail({ id: version_id }).then(res => {
        const { emptyImageSuffix } = this.planBoundingConfig
        this.planBoundingConfig.isDefImage = false
        if (res.plan_url && res.plan_url.indexOf(emptyImageSuffix) != -1) {
          this.makeEmptyScenePossess()
          this.planBoundingConfig.isDefImage = true
        }
        this.storeDetail = res
        this.getStoreScene()
      })
    },
    /**
     * 生成空场景占位
     * */
    makeEmptyScenePossess() {
      let { PLAN_BOUNDING_BOX_DEFAULT, PLAN_BOUNDING_BOX_SPACE_H, PLANO_BOUNDING_BOX_COUNT_H } = this.planBoundingConfig
      for (let i = 0; i < PLANO_BOUNDING_BOX_COUNT_H; i++) {
        Object.keys(PLAN_BOUNDING_BOX_DEFAULT).filter(v => {
          let { pos } = PLAN_BOUNDING_BOX_DEFAULT[v]
          const { tl, tr, bl, br } = pos
          let disW = (PLAN_BOUNDING_BOX_SPACE_H + (tr[0] - tl[0])) * i
          let plan_bounding_box = {
            tl: [tl[0] + disW, tl[1]],
            tr: [tr[0] + disW, tr[1]],
            bl: [bl[0] + disW, bl[1]],
            br: [br[0] + disW, br[1]]
          }
          if (!PLAN_BOUNDING_BOX_DEFAULT[v].boundingBoxArr) {
            PLAN_BOUNDING_BOX_DEFAULT[v].boundingBoxArr = []
          }
          PLAN_BOUNDING_BOX_DEFAULT[v].boundingBoxArr.push({ pos: plan_bounding_box, isOccupy: false })
        })
      }
    },
    /**
     * 获取场景列表
     * */
    getStoreScene() {
      const { store_code, version_id } = this.query
      const { plan_url } = this.storeDetail
      storeSceneList({ store_code, store_model_id: version_id }).then(res => {
        // // 删除数据
        // let demo1 = deepJSON(res[0])
        // demo1.is_delete = true
        // demo1.plan_bounding_box = {}
        // demo1.scene_unit_code = '2222'
        // demo1.id = '22'
        // // 新增数据
        // let demo = deepJSON(res[1])
        // demo.id = '11'
        // demo.scene_unit_code = '1111'
        // demo.plan_bounding_box = {}
        // res = res.concat(demo).concat(demo1)
        // res = res.map(v => {
        //   v.plan_bounding_box = []
        //   return v
        // })
        // console.log(res, 22222)
        const { isDefImage, PLAN_BOUNDING_BOX_DEFAULT } = this.planBoundingConfig
        let sceneObj = {}
        // res[3].device_code = ''
        // 是否存在已经提交过数据
        const haveBoundingBox = res.find(v => v.plan_bounding_box && Object.keys(v.plan_bounding_box).length && v.device_code)
        this.haveSubmitBoundingBox = haveBoundingBox ? true : false
        res.filter(v => {
          const containsAny = Object.keys(PLAN_BOUNDING_BOX_DEFAULT).find(item => v.scene_type_code.includes(item))
          if (containsAny) {
            let { type, name, index, pos, boundingBoxArr } = PLAN_BOUNDING_BOX_DEFAULT[containsAny]
            // 没被删除的数据
            if (!sceneObj[type]) {
              sceneObj[type] = {
                sceneType: containsAny,
                scene_type_code: type,
                scene_type_name: name,
                index,
                pos,
                sub: []
              }
            }
            // 当数据是is_delete的时候不自动补位
            if (isDefImage && !v.is_delete) {
              if(v.device_code){
                // 记录提交过的数据的占位
                if(v.plan_bounding_box && Object.keys(v.plan_bounding_box).length){
                  this.planBoundingConfig.PLAN_BOUNDING_BOX_DEFAULT[containsAny].boundingBoxArr = boundingBoxArr.map(val => {
                    let haveInterwoven = doRectanglesIntersect(val.pos, v.plan_bounding_box)
                    if(haveInterwoven){
                      val.isOccupy = true
                    }
                    return val
                  })
                }
                // // 如果一次都没提交过
                // if(!haveBoundingBox){
                //   let item = boundingBoxArr.find(val => !val.isOccupy)
                //   if (item) {
                //     v.plan_bounding_box = item.pos
                //     item.isOccupy = true
                //   }
                // // 曾经提交过且有些坐标是新增的数据
                // } else {
                //
                // }
              } else {
                v.plan_bounding_box = {}
              }
              // 如果有提交过
              // // 不自动
              // if (!haveBoundingBox && v.device_code) {
              //   let item = boundingBoxArr.find(val => !val.isOccupy)
              //   if (item) {
              //     v.plan_bounding_box = item.pos
              //     item.isOccupy = true
              //   }
              // }
              // 自动补位-is_delete数据除外、另外保存的时候需要给is_delete数据
              // if(haveBoundingBox && boundingBoxArr){
              // 如果有坐标则标记位置已经被占用
              // if(Object.keys(v.plan_bounding_box).length){
              //   this.planBoundingConfig.PLAN_BOUNDING_BOX_DEFAULT[containsAny].boundingBoxArr = boundingBoxArr.map(val => {
              //     // 如果面积交织标识好已经被占用了
              //     let haveInterwoven = this.doRectanglesIntersect(val.pos, v.plan_bounding_box)
              //     if(haveInterwoven){
              //       val.isOccupy = true
              //     }
              //     return val
              //   })
              // }
              // }

            }
            sceneObj[type].sub.push(v)
          }
        })
        let arr = []
        Object.values(sceneObj).filter(v => {
          let obj = {
            'id': v.scene_type_code,
            'name': v.scene_type_name,
            items: []
          }
          v.sub.filter(val => {
            const {
              device_code,
              scene_unit_image,
              plan_bounding_box,
              scene_type_code,
              scene_unit_name,
              fill_color,
              device_group_id,
              id,
              scene_unit_code,
              is_delete
            } = val
            let def = {
              device_code,
              scene_unit_code,
              device_group_id,
              is_other: false,
              bounding_box: plan_bounding_box || {},
              image: scene_unit_image,
              id,
              is_delete: is_delete || false,
              scene_code: id,
              scene_type: scene_type_code,
              name: scene_unit_name,
              fill_color: fill_color || this.fillColor[0].color
            }
            // 没有坐标则生成坐标点
            if (isDefImage && !is_delete && device_code && (def.bounding_box && !Object.keys(def.bounding_box).length || !def.bounding_box)) {
              let boundingBoxArr = this.planBoundingConfig.PLAN_BOUNDING_BOX_DEFAULT[v.sceneType].boundingBoxArr
              let item = boundingBoxArr.find(val => !val.isOccupy)
              if (item) {
                def.bounding_box = item.pos
                item.isOccupy = true
              }
              // 提交过数据且又有新数据/从未提交过数据
              // 自动补位-有设备编号并且没有坐标信息并且没有被占位
              // if(device_code && (def.bounding_box && !Object.keys(def.bounding_box).length || !def.bounding_box)){
              //   let boundingBoxArr = this.planBoundingConfig.PLAN_BOUNDING_BOX_DEFAULT[v.sceneType].boundingBoxArr
              //   let item = boundingBoxArr.find(val => !val.isOccupy)
              //   if (item) {
              //     def.bounding_box = item.pos
              //     item.isOccupy = true
              //   }
              // }
            }
            if (fill_color) {
              this.fillColor = this.fillColor.map(cov => {
                cov.active = false
                if (cov.color == fill_color) {
                  cov.active = true
                }
                return cov
              })
            }
            obj.items.push(def)
          })
          arr.push(obj)
        })
        this.sceneList = arr
        setTimeout(() => {
          this.alculationReady(true)
        }, 200)
      })
    },
    /**
     * 选择场景
     * */
    choseScene(item) {
      console.log(item.id, '@@@@')
      if (!item.device_code) {
        return
      }
      const isBind = item.bounding_box && Object.keys(item.bounding_box).length
      // 如果是其他设备组并且已绑定数据
      if (item.is_other && isBind) {
        this.sceneOtherGroup = item
        this.dialogVisible = true
        return
      }
      this.sceneList = this.sceneList.map(v => {
        v.items = v.items.map(val => {
          val.active = false
          if (item.scene_code == val.scene_code) {
            val.active = true
            // 改变对应的颜色
            if (val.fill_color) {
              this.fillColor = this.fillColor.map(cov => {
                cov.active = false
                if (cov.color == val.fill_color) {
                  cov.active = true
                }
                return cov
              })
            }
          }
          return val
        })
        return v
      })
      this.choseItem = item
      if (this.$refs.planeFigure) {
        this.$refs.planeFigure.initParams({ oneScene: this.choseItem })
      }
      // this.getLineCoords()
    },
    /**
     * 计算已完成
     * */
    alculationReady(isFirst) {
      let amount = 0
      let finishedAmount = 0
      let isOpen = true
      let allBindOther = true // 是否所有设备都绑定了其他组设备
      const { group_id, type } = this.query
      let sceneList = this.sceneList.map((v, index) => {
        v.items = v.items.map((val, ind) => {
          if (isFirst) {
            val.random = `${uuid.v1()}_${index}_${ind}` // 随机字符串
            val.active = false
            if (Object.keys(val.bounding_box).length) {
              finishedAmount++
              if (!val.is_other) {
                allBindOther = false
              }
            } else {
              allBindOther = false
            }
            // 默认选中当前组下的第一绑定的场景数据
            if (type == 200) {
              if (val.device_group_id == group_id) {
                if (isOpen) {
                  val.active = true
                  this.choseItem = val
                  isOpen = false
                }
                // 没有发现绑定的默认取未框过的
              }
              // else {
              // }
            } else {
              if (ind == 0 && index == 0) {
                val.active = true
                this.choseItem = val
              }
            }

          }
          return val
        })
        if (isFirst) {
          amount += v.items.length
        }
        return v
      })
      if (isFirst) {
        this.finishedAmount = finishedAmount
        this.amount = amount
        this.allBindOther = allBindOther
      }
      this.sceneList = sceneList
      // 初始化参数
      if (this.$refs.planeFigure) {
        this.$refs.planeFigure.initParams({ oneScene: this.choseItem })
      }
    },
    /**
     * 删除场景框
     * */
    delCoords(rectId) {
      let finishedAmount = 0
      this.sceneList.map(v => {
        v.items.map(v => {
          if (v.random == rectId) {
            v.bounding_box = {}
            v.is_delete = true
            v.is_all = false
            v.is_update = true
          }
          if (Object.keys(v.bounding_box).length) {
            finishedAmount++
          }
          return v
        })
        return v
      })
      this.finishedAmount = finishedAmount
    },
    /**
     * 框选后更新数据
     * */
    updateCoords(e) {
      let finishedAmount = 0
      const scale = this.$refs.planeFigure && this.$refs.planeFigure.getScale() || 0
      const { random, postion, is_all } = e
      const { group_id } = this.query
      this.sceneList = this.sceneList.map(v => {
        v.items = v.items.map(val => {
          if (val.random == e.random) {
            val.is_update = true // 是否操作过数据
            val.is_all = is_all ? true : false
            val.is_other = false
            val.is_delete = false
            val.device_group_id = group_id
            val.bounding_box = calculateRectangleCorners(postion, scale)
          }
          if (Object.keys(val.bounding_box).length) {
            finishedAmount++
          }
          return val
        })
        return v
      })
      this.finishedAmount = finishedAmount
    },
    /**
     * 解绑其他设备组绑定关系
     * */
    unbindScene() {
      const { random } = this.sceneOtherGroup
      let finishedAmount = 0
      this.sceneList = this.sceneList.map(v => {
        v.items = v.items.map(val => {
          if (val.random == random) {
            val.bounding_box = {}
            val.is_other = false
            this.allBindOther = false
            this.choseScene(val)
          }
          return val
        })
        return v
      })
      this.dialogVisible = false
    },
    /**
     * 提交数据
     * */
    submitFun() {
      const scale = this.$refs.planeFigure && this.$refs.planeFigure.getScale() || 0
      const store_device_group_id = this.query.group_id
      const { type } = this.query
      let storeSceneArr = []
      let isUpdate = false
      this.sceneList.filter(v => {
        v.items.filter(v => {
          let { bounding_box, id, device_group_id, fill_color, is_all, is_delete, is_update, scene_unit_code } = v
          // 坐标取整数
          Object.keys(bounding_box).filter(v => {
            const posArr = bounding_box[v]
            bounding_box[v] = [parseInt(posArr[0]), parseInt(posArr[1])]
          })
          if (is_update) {
            isUpdate = true
          }
          let position = bounding_box || {}
          // if (Object.keys(position).length) {
            storeSceneArr.push({ scene_unit_code: scene_unit_code, bounding_box: position, fill_color, is_delete: is_delete || false})
          // }
        })
      })
      if (type == 100 && !storeSceneArr.length || type == 200 && !devSceneArr.length) {
        this.$message.error(this.$t('reaCommon.pleaseBindData'))
        return
      }
      // 没更新过且之前没有提交过则传空 相当于全部
      if (!isUpdate && !this.haveSubmitBoundingBox) {
        // storeSceneArr = []
        return
      }
      this.isSubmit = true
      storeSceneBind({ scene_unit_area: storeSceneArr, id: this.query.version_id }).then(res => {
        this.$message.success(this.$t('reaCommon.operationSuccess'))
      }).finally(e => {
        setTimeout(() => {
          this.isSubmit = false
        }, 800)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.legend__group {
  font-size: 12px;

  & > div {
    &:before {
      content: '';
      box-sizing: border-box;
      border: 1px solid #33d9eb;
      display: inline-block;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: #A3ECF4;
      margin-right: 5px;
      position: relative;
      top: 1px;
    }

    margin-left: 15px;
  }

  .ot:before {
    content: '';
    border-color: #ef903b;
    background: #F5AE6F;
  }
}

.head__annotate {
  //padding-left: 24px;
  //padding-top: 20px;
  background: #fff;
  margin: 10px 24px;
  margin-bottom: 0;
  //padding-bottom: 10px;
  border-radius: 8px 8px 0 0;
  padding-top: 15px;

  .head__title {
    color: #333;
    margin-left: 15px;

    i {
      margin-right: 40px;
    }
  }

}

.scene__dialog {
  color: #333;
  text-align: center;

  .tit {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 15px;
  }

  .scene__dialog--info {
    text-align: left;
    margin: 0 auto;
    width: 200px;

    .name {
      font-size: 15px;
      font-weight: 500;
      margin-bottom: 5px;
    }

    .scene__dialog--image {
      margin: 0 auto;
      width: 100%;
      height: 300px;
      border-radius: 4px;
      border: 1px solid #DCDFE6;
      margin-bottom: 10px;
    }
  }


}

.scene__empty--des {
  text-align: center;
  height: 250px;

  i {
    color: #c8c8c8;
    font-size: 35px;
    display: inline-block;
    border-radius: 4px;
  }

  .text {
    font-size: 12px;
    color: #AEAEAE;
    margin-top: 10px;
  }
}

.scene__annotate {
  height: calc(100vh - 120px - 20px - 40px);
  margin: 10px 24px;
  margin-top: 0;
  border-radius: 0 0 8px 8px;
  padding: 15px;

  .scene__fabric {
    position: relative;
    /*width: 70%;*/
    background: #D9D9D9;
    height: 100%;
    box-sizing: content-box;

    img {
      width: 40%;
      margin: 0 auto;
    }

    .chose__color {
      position: absolute;
      left: 10px;
      top: 10px;
      font-size: 13px;
      z-index: 22;

      span {
        width: 30px;
        height: 30px;
        display: inline-block;
        background: #00FCFF;
        margin-left: 10px;
        cursor: pointer;

        &.active {
          border: 2px solid #03A9F4;;
        }

        box-sizing: border-box;
      }
    }
  }

  .scene__img {
    width: 28%;
    min-width: 330px;

    .dev__scene {
      border-radius: 4px;
      padding: 14px;
      width: 52%;
      margin: 0 auto;
      margin-top: 15px;

      .name {
        font-size: 14px;
        color: #000000;
        font-weight: 600;
        line-height: 19px;
        margin-bottom: 10px;
      }

      .face {
        width: 100%;
        height: 250px;
        display: block;
        /*background: #fff;*/
      }
    }
  }

  .face__item--box {
    overflow-x: scroll;
    width: 100%;

    .face__item {
      cursor: pointer;
      position: relative;
      width: 61px;
      height: 73px;
      float: left;
      margin-right: 7px;
      margin-bottom: 7px;

      &.active {
        border: 5px solid #03A9F4;
      }

      &.opt4 {
        opacity: .4;
      }

      .tost {
        background: rgba(0, 0, 0, .6);
        color: #33d9eb;
        font-size: 24px;
        text-align: center;
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 1;

        &.other_group_active {
          /*background: rgba(84, 173, 255, .5);*/
          color: #F5AE6F;
        }
      }
    }

    .face {
      width: 100%;
      height: 100%;
    }

  }

  .scene__list {
    .count {
      font-size: 14px;
      color: #333333;

      span {
        color: #06539B;
      }
    }

    width: 90%;
    margin: 0 auto;
    margin-top: 20px;
    /*height: calc(100% - 370px);*/
    overflow-y: scroll;

    .type {
      font-size: 12px;
      margin-bottom: 5px;
    }

    .scene__list--item {
      margin-top: 10px;
    }
  }

  .submit {
    padding: 10px 0;
  }


}

.whole__all {
  position: absolute;
  right: 0;
  top: -40px;
}
</style>
<style lang="scss">
.button__bor {
  border-color: #0E55A9;
  color: #0E55A9;
}
</style>
