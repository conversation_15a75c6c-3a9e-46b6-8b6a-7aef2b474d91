<template>
  <div class="page_container account_frontend_view">
    <!--    <iframe src="https://bpo.yykj.com/attachmentView?id=%40encrypt%40uQfP91l1bu4ii7xOHpwErwPpTFtsz8%2Fe" width="100%" height="600px" frameborder="0" style="border: 1px solid red"></iframe>-->
    <div class="form_box">
      <search-form ref="searchForm" :value="queryParam" :form-data="formData" :label-width="searchLabelWidth"
                   @nodeConfigHandler="formHandler" @selectChangeHandler="selectChangeHandler"
                   @resetQueryParam="resetQueryParam"
      >
<!--        <template slot="organization">-->
<!--          <el-tree-select ref="treeSelect" v-model="queryParam.organize_id" collapse-tags :styles="{width: '100%'}" :select-params="selectParams" :tree-params="treeParams" />-->
<!--        </template>-->
<!--        <template slot="organization">-->
<!--          <el-tree-select ref="treeSelect" v-model="queryParam.organize_ids" collapse-tags :styles="styles"-->
<!--                          :select-params="selectParams" :tree-params="treeParams"-->
<!--          />-->
<!--        </template>-->
      </search-form>
      <!--      <search-form ref="searchForm" :value="queryParam" :form-data="formData" :label-width="searchLabelWidth"-->
      <!--                   @nodeConfigHandler="formHandler" @selectChangeHandler="selectChangeHandler"-->
      <!--                   @resetQueryParam="resetQueryParam"/>-->
    </div>
    <div class="table_box wbg">
      <div class="control_box flex flex-y-center flex-x-end">
        <div class="frontend_control_box">
          <template v-if="!isMap">
<!--            v-if="$havePermission('0601',$route.meta.functionList)"-->
            <el-button  size="medium"
                       icon="el-icon-upload2" @click.native="openUploadPop"
            >{{ $t('reaCommon.dataImport') }}
            </el-button>
<!--            v-if="$havePermission('0701',$route.meta.functionList)"-->
            <el-button
                       icon="el-icon-download" size="medium" @click.native="openFilterPop">{{ $t('reaCommon.dataExport')
              }}
            </el-button>
<!--            v-if="$havePermission('0101',$route.meta.functionList)"-->
<!--            <el-button  size="medium"-->
<!--                       icon="el-icon-plus" type="primary" @click.native="addStore(100)">{{ $t('reaCommon.add') }}-->
<!--            </el-button>-->
            <!--            <el-button size="medium" icon="el-icon-plus" type="primary"-->
            <!--                       v-if="$havePermission('0101',$route.meta.functionList)" @click.native="addStore(100)">-->
            <!--              {{ $t('reaCommon.add') }}-->
            <!--            </el-button>-->
          </template>
          <!--          <el-button v-if="isMap" style="margin-left:15px;" size="medium" icon="el-icon-tickets" type="primary"-->
          <!--                     @click="isMap = false">{{ $t('reaCommon.switchList') }}-->
          <!--          </el-button>-->
          <!--          <el-button v-else style="margin-left:15px;" size="medium" icon="el-icon-position" type="primary"-->
          <!--                     @click="isMap = true">{{ $t('reaCommon.switchMap') }}-->
          <!--          </el-button>-->
        </div>

      </div>
      <!-- 地图模式 -->
<!--      <Map v-if="isMap" :count="initListCount" :query-param="queryParam"/>-->
      <div v-show="!isMap">
        <div v-if="showTable">
          <new-table
              ref="newTable"
              :loading="loading"
              :table-header="storeWarehouseHeader"
              :table-data="dataSource"
              :pagination="pagination"
              @pageConfigHandler="pageHandler"
          >
            <template v-slot:isGroup="{row}">
              {{row.task_group_name ? '是' : '否'}}
            </template>
<!--            <template v-slot:BU="{row}">-->
<!--              <template v-if="row.bu_list && row.bu_list.length">-->
<!--                {{ row.bu_list.map(item => item.bu_name).join('、') }}-->
<!--              </template>-->
<!--              <template v-else>-</template>-->
<!--            </template>-->
<!--              <template v-slot:check="{row}">-->
<!--                <span @click="openItem('/store/config-detail', {id: row.id, name: row.store_name, code: row.store_code})"-->
<!--                      v-if="$havePermission('1501',$route.meta.functionList) || $havePermission('1601',$route.meta.functionList) || $havePermission('0201',$route.meta.functionList) "-->
<!--                      class="table__oprate&#45;&#45;item"-->
<!--                >-->
<!--                   {{ $t('reaCommon.toView') }}-->
<!--                </span>-->
<!--              </template>-->
<!--            <template v-slot:edit="{row}">-->
<!--              <span class="table__oprate&#45;&#45;item" v-if="$havePermission('0301',$route.meta.functionList)"-->
<!--                    @click.stop="editFun(row)"-->
<!--              >{{ $t('reaCommon.edit') }}</span>-->
<!--            </template>-->
<!--            <template v-slot:delete="{row}">-->
<!--              <span class="table__oprate&#45;&#45;item" v-if="$havePermission('0501',$route.meta.functionList)"-->
<!--                    @click.stop="deleteThis(row)"-->
<!--              >{{ $t('reaCommon.delete') }}</span>-->
<!--            </template>-->
          </new-table>
        </div>
        <div v-else class="no_table_data_box">{{ $t('reaCommon.noData') }}</div>
      </div>
    </div>
    <div v-if="uploadVisible">
      <!--      :brand-list="brandList"-->
      <import
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          :action="'import_task_and_push_yy'"
          :visible.sync="uploadVisible"
          center
          @refresh="formHandler"
          @dialogCancel="uploadCancel"
          @dialogConfirm="uploadConfirm"
          @dialogClose="uploadClose"
      />
    </div>
    <div v-if="downloadFilterPop">
      <filter-field :field-list="fieldList" :filter-visible.sync="downloadFilterPop" @dialogCancel="filterCancel"
                    @dialogConfirm="filterConfirm" @dialogClose="filterClose"
      />
    </div>
    <download-progress :progress="progress" :download-visible.sync="downloadVisible"
                       @downloadDialogCancel="processCancel" @downloadDialogConfirm="processConfirm"
                       @downloadDialogClose="processClose"
    />
    <!--新增/编辑售点-->
    <div class="dialog_container">
      <ele-dialog :title="dialogForm.id ? $t('reaCommon.edit') : $t('reaCommon.add')"
                  @dialogClose="dialogCancel"
                  :visible.sync="dialogVisible"
                  center @dialogCancel="dialogCancel" @dialogConfirm="sbumitFun()"
      >
        <template slot="content">
          <div class="dialog_form">
            <el-form ref="dialogForm" size="medium" :model="dialogForm" :rules="dialogRules" label-width="155px"
                     class="demo-ruleForm"
            >
              <template v-if="userInfo.is_super">
                <el-form-item  :label="$t('reaCommon.tenant')" prop="tenant_id">
                  <el-select filterable v-model="dialogForm.tenant_id" @change="changeTenantFun"
                             :placeholder="$t('reaCommon.pleaseSelect')"
                             clearable
                  >
                    <el-option v-for="brand in brandList" :key="brand.id" :label="brand.tenant_name" :value="brand.id"/>
                  </el-select>
                </el-form-item>
                <el-form-item v-if="buList.length && (dialogForm.tenant_id || dialogForm.id)" label="BU" prop="bu_list">
                  <el-checkbox-group v-model="dialogForm.bu_list">
                    <el-checkbox v-for="role in buList" :key="role.value" :label="role.value">{{ role.label }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </template>
              <!--bu-->
              <!--              <el-form-item v-if="userInfo.is_super && dialogForm.tenant_id && buList.length || !userInfo.is_super"-->
              <!--                            label="BU" prop="bu_ids"-->
              <!--              >-->
              <!--                <el-checkbox-group v-model="dialogForm.bu_ids">-->
              <!--                  <el-checkbox v-for="role in buList" :key="role.value" :label="role.value">{{ role.label }}-->
              <!--                  </el-checkbox>-->
              <!--                </el-checkbox-group>-->
              <!--              </el-form-item>-->
              <el-form-item :label="$t('reaCommon.channel')" prop="channel_sub_id">
                <el-select filterable v-model="dialogForm.channel_sub_id" :placeholder="$t('reaCommon.pleaseSelect')"
                           clearable
                >
                  <el-option v-for="brand in channelList" :key="brand.value" :label="brand.label" :value="brand.value"/>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('reaCommon.storeName')" prop="store_name">
                <el-input v-model="dialogForm.store_name" clearable
                          :placeholder="$t('reaCommon.pleaseEnter')"
                          @blur="dialogForm.store_name = $event.target.value.trim()"
                />
              </el-form-item>
              <el-form-item :label="$t('reaCommon.storeCode')" prop="store_code" v-if="!dialogForm.id">
                <el-input v-model="dialogForm.store_code" clearable
                          :placeholder="$t('reaCommon.pleaseEnter')"
                          @blur="dialogForm.store_code = $event.target.value.trim()"
                />
              </el-form-item>
              <el-form-item :label="$t('reaCommon.storeAddress')" prop="address">
                <el-input :placeholder="$t('reaCommon.pleaseEnter')" v-model="dialogForm.address" clearable
                          @blur="dialogForm.address = $event.target.value.trim()"
                />
              </el-form-item>
              <el-form-item :label="$t('reaCommon.fullScene')">
                <el-radio-group v-model="dialogForm.full_scene">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </div>
        </template>
      </ele-dialog>
    </div>
  </div>
</template>
<script>

import EleDialog from '@/components/Dialog'
import Import from '@/components/Import/tenantSelectImport'
import FilterField from '@/components/FilterField/index'
import SearchForm from '@/components/SearchForm'
import NewTable from '@/components/Table'
import DownloadProgress from '@/components/DownloadProgress'
import Map from '@/components/Map'
import { storeWarehouseHeader } from '@/utils/header'
import ListMixin from '@/mixins/List'
import { enumList, organizeTenant, storeChannelList, storeStatus, buListAll, configDevTypeDropdown, tenantAll } from '@/api/common'
import { storeCreate, storeEdit } from '@/api/store'

import { storeDelete } from '@/api/store-warehouse/index'

import { downloadTemplate } from '@/utils/index'
import { deepJSON, openWin } from '@/utils/tools'
import { transformData } from '@/utils/validate'

import { mapGetters } from 'vuex'
import { taskList, test } from '@/api/list'

const optionsSelect = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '是',
    value: 1
  },
  {
    label: '否',
    value: 0
  }
]
export default {
  name: 'store-warehouse-index',
  components: {
    SearchForm,
    NewTable,
    Import,
    FilterField,
    DownloadProgress,
    Map,
    EleDialog
  },
  mixins: [ListMixin],
  data() {
    return {
      openLocalSearch: true, // 是否打开本地存储查询条件
      dialogVisible: false,
      type: 'create',
      dialogForm: {
        full_scene: '',
        bu_list: [],
        id: '',
        store_code: '',
        tenant_id: '',
        store_name: '',
        address: '',
        channel_sub_id: '',
        // channel_id: ''
      },
      dialogRules: {
        bu_list: [
          { required: true, message: this.$t('reaCommon.pleaseSelect'), trigger: 'blur' }
        ],
        store_name: [
          { required: true, message: this.$t('reaCommon.pleaseEnter'), trigger: 'blur' }
        ],
        store_code: [
          { required: true, message: this.$t('reaCommon.pleaseEnter'), trigger: 'blur' }
        ],
        address: [
          { required: true, message: this.$t('reaCommon.pleaseEnter'), trigger: 'blur' }
        ],
        tenant_id: [
          { required: true, message: this.$t('reaCommon.pleaseSelect'), trigger: 'blur' }
        ],
        channel_sub_id: [
          { required: true, message: this.$t('reaCommon.pleaseSelect'), trigger: 'blur' }
        ]
      },
      isMap: false,
      initListCount: 0,
      searchLabelWidth: '120px',
      uploadVisible: false,
      styles: {
        width: '100%'
      },
      selectParams: {
        multiple: true,
        clearable: true,
        placeholder: this.$t('reaCommon.pleaseSelect'),
        'collapse-tags': true
      },
      treeParams: {
        filterable: false,
        'default-expand-all': true,
        'expand-on-click-node': false,
        'node-key': 'id',
        'show-checkbox': true,
        data: [],
        props: {
          children: 'children',
          label: 'label',
          disabled: 'disabled',
          value: 'id'
        }
      },
      channelList: [],
      brandList: [],
      storeWarehouseHeader,
      formData: [
        {
          name: '任务ID',
          nameKey: 'taskId',
          type: 'input',
          field: 'task_id',
          attrs: {
            clearable: true
          }
        },
        {
          name: '任务名称',
          nameKey: 'taskName',
          type: 'input',
          field: 'task_name',
          attrs: {
            clearable: true
          }
        },
        {
          name: '企业名称',
          nameKey: 'storeName2',
          type: 'input',
          field: 'store_name',
          attrs: {
            clearable: true
          }
        },
        {
          name: '省',
          nameKey: 'provinces',
          type: 'select',
          field: 'store_province',
          attrs: {
            filterable: true,
            clearable: true
          },
          options: []
        },
        {
          name: '市',
          nameKey: 'city',
          type: 'select',
          field: 'store_city',
          attrs: {
            filterable: true,
            clearable: true
          },
          options: []
        },
        {
          name: '区',
          nameKey: 'county',
          type: 'select',
          field: 'store_county',
          attrs: {
            filterable: true,
            clearable: true
          },
          options: []
        },
        {
          name: '任务截止时间',
          nameKey: 'taskEndTime',
          'value-format': 'yyyy-MM-dd',
          type: 'daterange',
          field: 'createTime'
        },
        {
          name: '任务描述',
          nameKey: 'taskDes',
          type: 'input',
          field: 'task_desc',
          attrs: {
            clearable: true
          }
        },
        {
          name: '任务分组',
          nameKey: 'taskDisGroup',
          type: 'input',
          field: 'task_group_name',
          attrs: {
            clearable: true
          }
        }],
      queryParam: {
        createTime: [],
        task_group_name: '',
        task_desc: '',
        store_county: '',
        store_city: '',
        store_province: '',
        store_name: '',
        task_name: '',
        task_id: '',
        start: '',
        end: ''
      },
      initQueryParam: {},
      dataSource: [],
      apiUrl: {
        list: 'taskList',
        export: 'exportStart',
      },
      downloadType: 'export_task_list',
      tableOperates: [
        {
          label: '配置',
          labelKey: 'config',
          slot: 'set',
          method: (index, row) => {
          }
        },
        // {
        //   label: '配置',
        //   labelKey: 'config',
        //   slot: 'set',
        //   method: (index, row) => {
        //   }
        // },
        {
          label: '查看',
          labelKey: 'toView',
          slot: 'check',
          method: (index, row) => {
          }
        },
        {
          label: '编辑',
          labelKey: 'edit',
          slot: 'edit',
          method: (index, row) => {
          }
        },
        {
          label: '删除',
          labelKey: 'delete',
          slot: 'delete',
          method: (index, row) => {
          }
        }
      ],
      operatesProps: {
        width: '150px',
        label: '操作',
        labelKey: 'operation',
        fixed: 'right'
      },
      showTable: true,
      isSubmit: false,
      buList: [],
      enumSkuList: []
    }
  },
  computed: {
    visitedViews() {
      return this.$store.state.tagsView.visitedViews
    },
    ...mapGetters([
      'userInfo',
      'language'
    ])
  },
  watch: {
    '$i18n.locale'() {
      this.searchLabelWidth = this.language === 'en' ? '160px' : '120px'
    }
  },
  created() {
    this.getEnumList()
    this.initQueryParam = deepJSON(this.queryParam)
    // 记录筛选条件
    this.setLocalQuery(100)
    this.searchLabelWidth = this.language === 'en' ? '160px' : '120px'
    // 非超级管理员不能看到品牌方筛选
    // const { is_super, tenant_id } = this.userInfo
    // if(is_super){
    //   this.getTenantAll()
    // }
    // this.fieldList = deepJSON(storeWarehouseHeader)
  },
  mounted() {
  },
  methods: {
    /**
     * 获取sku下拉选项
     * */
    getEnumList(){
      enumList({fields: ['city', 'county', 'province']}).then(res => {
        const {city, county, province, audit_status} = res
        this.formData.map(v => {
          if(v.field == 'store_province'){
            v.options = transformData(province)
          }
          if(v.field == 'store_county'){
            v.options = transformData(county)
          }
          if(v.field == 'store_city'){
            v.options = transformData(city)
          }
          return v
        })
      })
    },
    /**
     * 获取渠道-下拉
     * */
    getDownList() {
      // storeChannelList().then(res => {
      //   let arr = []
      //   res.map((v) => {
      //     arr.push({ value: v.id, label: v.name })
      //   })
      //   this.channelList = arr
      //   const index = this.formData.findIndex(v => v.field == 'channel_sub_id')
      //   this.formData[index].options = arr
      // })
    },
    changeTenantFun() {
      this.dialogForm.bu_list = []
      this.buList = []
      this.getBuList(this.dialogForm.tenant_id)
    },
    /**
     * 添加售点
     * */
    addStore(type) {
      if (type == 100) {
        this.dialogForm = {
          full_scene: '',
          bu_list: [],
          id: '',
          store_code: '',
          tenant_id: '',
          store_name: '',
          address: '',
          channel_sub_id: ''
        }
        this.userInfo.is_super && (this.buList = [])
      }
      if (this.$refs['dialogForm']) {
        this.$refs['dialogForm'].clearValidate()
      }
      this.dialogVisible = true
    },
    /**
     * 取消编辑
     * */
    dialogCancel() {
      this.dialogVisible = false
    },
    /**
     * 售点编辑/新增
     * */
    sbumitFun() {
      this.$refs['dialogForm'].validate((valid) => {
        if (valid) {
          if (this.isSubmit) {
            return
          }
          this.isSubmit = true
          let call = null
          if (this.dialogForm.id) {
            call = storeEdit(this.dialogForm)
          } else {
            // this.dialogForm.tenant_id = ''
            // this.dialogForm.id = ''
            call = storeCreate(this.dialogForm)
          }
          call.then(res => {
            this.dialogVisible = false
            this.$message.success(this.$t('reaCommon.operationSuccess'))
            this.formHandler()
          }).finally(() => {
            setTimeout(() => {
              this.isSubmit = false
            }, 800)
          })
        }
      })
    },
    openWinFun(url, string) {
      openWin(url, string)
    },
    /**
     * 获取组织架构
     * */
    getOrganizeTenant() {
      // organizeTenant({tenant_id: this.queryParam.tenant_id}).then((res) => {
      //   if (Array.isArray(res)) {
      //     this.treeParams.data = res
      //     this.$nextTick(() => {
      //       this.$refs.treeSelect.treeDataUpdateFun(res)
      //     })
      //   }
      // })
    },
    /**
     * 删除售点
     * */
    deleteThis(row) {
      this.$confirm(this.$t('reaCommon.confirmDeleteThisOne') + '?', this.$t('reaCommon.warning'), {
        confirmButtonText: this.$t('reaCommon.confirm'),
        cancelButtonText: this.$t('reaCommon.cancel'),
        type: 'warning'
      }).then(() => {
        if (row.loading) {
          return
        }
        row.loading = true
        storeDelete({ id: row.id }).then((res) => {
          this.$message.success(this.$t('reaCommon.deleteSuccess'))
          this.formHandler()
        }).finally(() => {
          row.loading = false
        })
      })
    },
    /**
     * 编辑售点
     * */
    editFun(store) {
      let { bu_list, tenant_list } = store
      let bu_ids = []
      bu_list.map(v => {
        bu_ids.push(v.id)
        return v
      })
      this.dialogForm.bu_list = []
      Object.keys(this.dialogForm).filter(v => {
        this.dialogForm[v] = store[v]
      })
      this.dialogForm.full_scene = store.full_scene
      if (bu_ids && bu_ids.length) {
        this.dialogForm.bu_list = bu_ids
      }
      if (tenant_list && tenant_list.length) {
        const tenant_id = tenant_list[0].id
        this.dialogForm.tenant_id = tenant_id
        this.getBuList(tenant_id)
      }
      this.addStore()
    },
    /**
     * 搜索回调
     * */
    selectChangeHandler(selectObj) {
    },
    /**
     * 获取bu列表
     * */
    getBuList(tenant_id, type) {
      const searchFormRef = this.$refs.searchForm
      const index = this.formData.findIndex(v => v.field == 'bu_list')
      let queryItem = this.formData[index]
      if (index != -1 && type) {
        queryItem.attrs.loading = true
        if (searchFormRef) {
          searchFormRef.updateFormData(this.formData)
        }
      }
      buListAll({ tenant_id }).then((res) => {
        if (Array.isArray(res)) {
          res.map((v) => {
            v.value = v.id
            v.label = v.bu_name
            return v
          })
          this.buList = res
        }
        if (type) {
          queryItem.options = this.buList
          if (searchFormRef) {
            setTimeout(() => {
              queryItem.attrs.loading = false
              searchFormRef.updateFormData(this.formData)
            }, 800)
          }
          this.$forceUpdate()
        }
      })
    },
    openUploadPop() {
      this.uploadVisible = true
    },
    uploadCancel() {
      this.uploadVisible = false
    },
    uploadConfirm() {
      this.uploadVisible = false
    },
    uploadClose() {
      this.uploadVisible = false
    },
    closeView() {
      this.visitedViews.map((v) => {
        if (v.path === '/store-warehouse/add') {
          this.$store.dispatch('tagsView/delView', v)
        }
      })
    },
    /**
     * 下载
     * */
    downloadTemplateFile() {
      downloadTemplate()
    },
    formHandler() {
      const {createTime} = this.queryParam
      if(createTime && createTime.length){
        this.queryParam.start = createTime[0]
        this.queryParam.end = createTime[1]
      } else {
        this.queryParam.start = ''
        this.queryParam.end = ''
      }
      this.searchQuery()
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.table_box.mt0 {
  margin-top: 0;
}

.table_box {
  margin-top: 23px;
  padding: 18px 25px 0;
}

.control_box {
  padding: 0 0 17px;
}

.reset_password ::v-deep .el-input__inner {
  border: none;
}

.frontend_control_box ::v-deep .el-button--default {
  border-color: $themeColor;
  color: $themeColor;
}

.dot {
  margin-right: 8px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #36C08E;
}

.dot.red {
  background-color: #E52828;
}

.header_scroll_box {
  height: 270px;
  padding: 5px 0;
  overflow-y: auto;

  &::-webkit-scrollbar-track-piece {
    background: #D3E6E6;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #99a9bf;
    border-radius: 20px;
  }
}
</style>
<style lang="scss">
@import "~@/styles/variables.scss";

.table_header_set_box {
  padding: 0 10px !important;

  .el-checkbox__label {
    color: #333;
  }
}

.cursor {
  cursor: pointer;
}

.text__align--center {
  text-align: center;
}

</style>
