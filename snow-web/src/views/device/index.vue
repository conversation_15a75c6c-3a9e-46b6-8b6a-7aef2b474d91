<template>
  <div class="page_container account_frontend_view">
    <div class="form_box">
      <search-form :value="queryParam" :form-data="formData" :label-width="searchLabelWidth"
                   @nodeConfigHandler="formHandler" @selectChangeHandler="selectChange"  @resetQueryParam="resetQueryParam"
      />
    </div>
    <div class="table_box wbg">
      <div class="control_box flex flex-y-center flex-x-end">
        <div class="frontend_control_box">
          <!--          v-if="$havePermission('0101',$route.meta.functionList)"-->
          <!--          <el-button  style="margin-left:15px;" size="medium"-->
          <!--                     icon="el-icon-plus" type="primary" @click.native="openDialog({})"-->
          <!--          >{{ $t('reaCommon.add') }}-->
          <!--          </el-button>-->
          <el-button  v-if="$havePermission('0601',$route.meta.functionList)" size="medium"
                     icon="el-icon-upload2" @click.native="openUploadPop"
          >{{ $t('reaCommon.dataImport') }}
          </el-button>
          <el-button
              v-if="$havePermission('0701',$route.meta.functionList)"
              icon="el-icon-download" size="medium" @click.native="openExport"
          >{{ $t('reaCommon.dataExport') }}
          </el-button>
        </div>
      </div>
      <div>
        <new-table
            :loading="loading"
            :table-header="deviceIndexHeader"
            :table-data="dataSource"
            :operates="tableOperates"
            :operates-props="operatesProps"
            :pagination="pagination"
            @pageConfigHandler="pageHandler"
        >
          <template v-slot:deviceStatus="{items, index, row}">
            <el-tag :type="row.status == 0 ? 'info' : 'success'">{{ row.status == 0 ? '已禁用' : '已启用' }}</el-tag>
          </template>
          <template v-slot:deviceType="{items, index, row}">
            {{matchDeviceType(row.type)}}
          </template>

          <template v-slot:edit="{items, index, row}">
            <span  v-if="$havePermission('0301',$route.meta.functionList) && row.type == 4 && row.store_code" slot="reference" class="table__oprate--item" @click="openDialog(row)">{{
                $t('reaCommon.edit')
              }}</span>
          </template>
          <template v-slot:enable="{row}">
            <span v-if="$havePermission('0401',$route.meta.functionList)" class="table__oprate--item"
                  @click="enableFn(row)"
            >{{ row.status == 1 ? $t('reaCommon.disable') : $t('reaCommon.enable') }}</span>
          </template>
<!--          <template v-slot:delete="{items, index, row}">-->
<!--            <el-popconfirm-->
<!--                :confirm-button-text="$t('reaCommon.confirm')"-->
<!--                :cancel-button-text="$t('reaCommon.cancel')"-->
<!--                :title="`${$t('reaCommon.confirmDeleteThisOne')}？`"-->
<!--                icon="el-icon-warning"-->
<!--                @confirm="deleteThis(row)"-->
<!--            >-->
<!--              <span slot="reference" class="table__oprate&#45;&#45;item">{{ $t('reaCommon.delete') }}</span>-->
<!--            </el-popconfirm>-->
<!--          </template>-->
        </new-table>
      </div>
    </div>
    <!--    新增-->
    <div class="dialog_container">
      <ele-dialog :title="type == 'add' ?  $t('device.addDevice') : $t('device.editDevice') "
                  :visible.sync="dialogVisible" center @dialogCancel="dialogClose" @dialogConfirm="dialogConfirm"
                  @dialogClose="dialogClose"
      >
        <template slot="content">
          <div class="dialog_form">
            <el-form ref="dialogForm" size="medium" :model="dialogForm" :rules="dialogRules"
                     label-width="140px" class="demo-ruleForm"
            >
<!--              <div v-for="(item, index) in assetCodeList" :key="index" class="flex">-->
<!--                <el-form-item label="品牌方：">-->
<!--                  <el-input v-model.trim="item.tenant_name"-->
<!--                            :disabled="true"-->
<!--                            :placeholder="$t('reaCommon.pleaseEnter')" clearable-->
<!--                  />-->
<!--                </el-form-item>-->
              <el-form-item label="品牌方资产编号：" >
              </el-form-item>
              <el-empty description="暂无数据" v-if="!assetCodeList.length" ></el-empty>
              <el-form-item :label="item.tenant_name+'：'" v-for="(item, index) in assetCodeList" :key="index">
                <el-input v-model.trim="item.asset_code"
                          :placeholder="$t('reaCommon.pleaseEnter')" clearable
                />
              </el-form-item>
<!--              </div>-->
            </el-form>
          </div>
        </template>
      </ele-dialog>
    </div>

    <div v-if="uploadVisible">
      <!--      :brand-list="brandList"-->
      <import
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          :action="'import_device'"
          :visible.sync="uploadVisible"
          center
          @refresh="formHandler"
          @dialogCancel="uploadCancel"
          @dialogConfirm="uploadCancel"
          @dialogClose="uploadCancel"
      />
    </div>
    <!--下载-->
    <template>
<!--      <div v-if="downloadFilterPop">-->
<!--        &lt;!&ndash;        field-list="fieldList"&ndash;&gt;-->
<!--        <filter-field :filter-visible.sync="downloadFilterPop" @dialogCancel="filterCancel"-->
<!--                      @dialogConfirm="filterConfirm" @dialogClose="filterClose"-->
<!--        />-->
<!--      </div>-->
      <download-progress :progress="progress" :download-visible.sync="downloadVisible"
                         @downloadDialogCancel="processCancel" @downloadDialogConfirm="processConfirm"
                         @downloadDialogClose="processClose"
      />
    </template>
  </div>
</template>
<script>
import SearchForm from '@/components/SearchForm'
import NewTable from '@/components/Table'
import EleDialog from '@/components/Dialog'
import { deviceIndexHeader } from '@/utils/header'
import ListMixin from '@/mixins/List'
import { configDevTypeDropdown, configStatusDropdown, tenantAll } from '@/api/common'
import { mapGetters } from 'vuex'
import { addDevice, delDeviceGroup, editDevice, getAssetCode, switchStatus } from '@/api/device'
import { getStoreListSelect } from '@/api/common'
import Import from '@/components/Import/tenantSelectImport.vue'
import FilterField from '@/components/FilterField/index.vue'
import DownloadProgress from '@/components/DownloadProgress/index.vue'

const voltageType = [
  {
    value: '3500',
    label: '低于3500' // 货架
  },
  {
    value: '3600',
    label: '低于3600' // 冰柜
  }
]
export default {
  name: 'device-index',
  components: {
    DownloadProgress, FilterField,
    Import,
    SearchForm,
    NewTable,
    EleDialog
  },
  mixins: [ListMixin],
  data() {
    return {
      openLocalSearch: false, // 是否打开本地存储查询条件
      dialogVisible: false,
      dialogForm: {
        // device_group_code: '',
        // device_group_name: '',
        device_id: '',
        tenant_id: '',
        asset_code: ''
      },
      editStoreId: '',
      dialogRules: {
        asset_code: [
          {
            required: true,
            message: this.$t('reaCommon.pleaseEnter'),
            trigger: 'change'
          }
        ],
        tenant_id: [
          {
            required: true,
            message: this.$t('reaCommon.pleaseSelect'),
            trigger: 'change'
          }
        ]
      },
      searchLabelWidth: '100px',
      dialogLabelWidth: '95px',
      storeList: [],
      deviceTypeList: [],
      deviceIndexHeader,
      formRule: {},
      formData: [
        {
          name: '售点编号或名称',
          nameKey: 'storeSearchKey',
          type: 'input',
          field: 'store_search_key',
          attrs: {
            clearable: true
          }
        }, {
          name: '设备编号',
          nameKey: 'deviceNumber',
          type: 'input',
          field: 'device_code',
          attrs: {
            clearable: true
          }
        },
        {
          name: '设备类型',
          nameKey: 'deviceType',
          type: 'select',
          field: 'type',
          placeholder: '请选择设备类型',
          attrs: {
            clearable: true,
            filterable: true
          },
          options: []
        },{
          name: '电压值',
          nameKey: 'voltage',
          type: 'select',
          field: 'adc',
          placeholder: '请选择',
          attrs: {
            clearable: true,
            filterable: true
          },
          options: voltageType
        }, {
          name: '设备状态',
          nameKey: 'deviceStatus',
          type: 'select',
          field: 'device_status',
          placeholder: '请选择设备状态',
          attrs: {
            clearable: true,
            filterable: true
          },
          options: []
        }, {
          name: '设备安装状态',
          nameKey: 'deviceInStatus',
          type: 'select',
          field: 'device_install_status',
          placeholder: '请选择设备安装状态',
          attrs: {
            clearable: true,
            filterable: true
          },
          options: []
        }
      ],
      queryParam: {
        adc: '',
        store_search_key: '',
        store_code: '',
        type: '',
        device_install_status: '',
        device_status: '',
        device_code: '', // 8886660000009
        tenant_id: ''
      },
      initQueryParam: {
        adc: '',
        store_search_key: '',
        store_code: '',
        type: '',
        device_install_status: '',
        device_status: '',
        device_code: '',
        tenant_id: ''
      },
      dataSource: [],
      /* mixin里接口名称*/
      apiUrl: {
        export: 'deviceDownload',
        list: 'allDeviceList'
      },
      uploadVisible: false,
      /* 表格操作按钮*/
      tableOperates: [
        {
          label: '查看',
          labelKey: 'toView',
          slot: 'view'
        },
        {
          label: '禁用',
          labelKey: 'enable',
          slot: 'enable'
        },
        {
          label: '编辑',
          labelKey: 'edit',
          slot: 'edit'
        },
        {
          label: '删除',
          labelKey: 'delete',
          slot: 'delete'
        }
      ],
      operatesProps: {
        width: '120px',
        label: '操作',
        labelKey: 'operation'
      },
      type: 'add',
      assetCodeList: []
    }
  },
  computed: {
    ...mapGetters([
      'userInfo',
      'language'
    ])
  },
  watch: {
    '$i18n.locale'() {
      this.searchLabelWidth = this.language === 'en' ? '140px' : '140px'
      this.dialogLabelWidth = this.language === 'en' ? '180px' : '95px'
    }
  },
  created() {
    // 如果只有查看权限则隐藏当前这一列
    if (this.$route.meta.functionList.length == 1 && this.$havePermission('0201', this.$route.meta.functionList)) {
      this.tableOperates = []
    }
    this.getDeviceType()
    this.getDeviceInstallStatus()
    this.getDeviceStatus()
    this.searchLabelWidth = this.language === 'en' ? '140px' : '140px'
    this.dialogLabelWidth = this.language === 'en' ? '180px' : '95px'
    // 记录筛选条件
    this.setLocalQuery(100)
    // this.getStoreListSelect()
  },
  methods: {
    resetQueryParam() {
      const index = this.formData.findIndex(v => v.field == 'adc')
      this.formData[index].options = voltageType
    },
    /**
     * 改变选择品牌资产编号
     * */
    changeAssetFun(){

    },
    uploadCancel() {
      this.uploadVisible = false
    },
    openUploadPop() {
      this.uploadVisible = true
    },
    /**
     * 导出
     * */
    openExport(){
      this.$confirm('是否确认导出数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.filterConfirm([])
      })
    },
    /**
     * 搜索筛选
     * */
    selectChange(val){
      const {name, value} = val
      if (name == 'type') {
        if(!value){
          this.resetQueryParam()
          this.queryParam.adc = ''
          return
        }
        const index = this.formData.findIndex(v => v.field == 'adc')
        // 货架摄像头
        let obj = {}
        if(value == 3){
          obj = voltageType[0]
        }
        // 冰柜摄像头
        if(value == 4){
          obj = voltageType[1]
        }
        this.formData[index].options = [obj]
        this.queryParam.adc = ''
        this.$forceUpdate()
      }
    },
    matchDeviceType(type){
      const item = this.deviceTypeList.find(v => v.value == type)
      return item ? item.label : ''
    },
    /**
     * 设备类型
     * */
    getDeviceType() {
      configDevTypeDropdown().then((res) => {
        if (Array.isArray(res.list)) {
          let arr = []
          res.list.map((v) => {
            arr.push({ value: v.key + '', label: v.key_label })
          })
          const index = this.formData.findIndex(v => v.field == 'type')
          this.formData[index].options = arr
          this.deviceTypeList = arr
        }
      })
    },
    /**
     * 设备安装状态
     * */
    getDeviceInstallStatus() {
      configStatusDropdown().then((res) => {
        if (Array.isArray(res.list)) {
          let arr = []
          res.list.map((v) => {
            arr.push({ value: v.key + '', label: v.key_label })
          })
          const index = this.formData.findIndex(v => v.field == 'device_install_status')
          this.formData[index].options = arr
        }
      })
    },
    /**
     * 设备状态
     * */
    getDeviceStatus() {
      let arr = [
        {
          value: '0',
          label: '禁用'
        },
        {
          value: '1',
          label: '正常'
        }
      ]
      const index = this.formData.findIndex(v => v.field == 'device_status')
      this.formData[index].options = arr
    },
    /**
     * 启用/禁用
     * */
    enableFn(row) {
      if (row.loading) {
        return
      }
      row.loading = true
      switchStatus({ device_code: row.device_code, status: row.status == 1 ? 0 : 1 }).then((res) => {
        row.status = row.status == 1 ? 0 : 1
        this.$message.success(row.status == 1 ? this.$t('reaCommon.enableSuccess') : this.$t('reaCommon.disableSuccess'))
      }).finally(() => {
        row.loading = false
      })
    },
    dialogClose() {
      this.dialogVisible = false
    },
    dialogConfirm() {
      if(!this.assetCodeList.length){
        this.dialogVisible = false
        return
      }
      if (this.submitting) {
        return
      }
      this.$refs['dialogForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          // if (this.type == 'add') {
          //   addDevice(this.dialogForm).then((res) => {
          //     this.$message.success(this.$t('reaCommon.operationSuccess'))
          //     this.dialogVisible = false
          //     this.formHandler()
          //   }).finally(() => {
          //     setTimeout(() => {
          //       this.submitting = false
          //     }, 200)
          //   })
          // } else {
          //   this.dialogForm.device_id = ''
          const findEmpty = this.assetCodeList.find(v => !v.asset_code)
          if(findEmpty){
            this.$message.error('资产编号不能为空')
            return
          }
          editDevice({asset_code_list: this.assetCodeList}).then((res) => {
            this.$message.success(this.$t('reaCommon.operationSuccess'))
            this.dialogVisible = false
            // this.formHandler()
          }).finally(() => {
            setTimeout(() => {
              this.submitting = false
            }, 200)
          })
          // }

        }
      })
    },
    openDialog(row) {
      this.type = row.id ? 'edit' : 'add'
      this.dialogForm = {
        device_id: row.id
      }
      getAssetCode({device_id: row.id}).then(res => {
        res = res.filter(v => v.device_id = row.id)
        this.assetCodeList = res
        // this.assetCodeList = res.concat({asset_code: 111, tenant_id: 33, tenant_name: 44, device_id: 2396})
        this.dialogVisible = true
        this.$nextTick(() => {
          if (this.$refs['dialogForm']) {
            this.$refs['dialogForm'].clearValidate()
          }
        })
      })
    },
    deleteThis(row) {
      if (row.loading) {
        return
      }
      row.loading = true
      delDeviceGroup({ id: row.id }).then((res) => {
        this.formHandler()
        this.$message.success(this.$t('reaCommon.deleteSuccess'))
      }).finally(() => {
        row.loading = false
      })
    },
    /**
     * 品牌商
     */
    getTenantAll() {
      tenantAll().then((res) => {
        if (res.code == 1) {
          if (Array.isArray(res.data)) {
            res.data.map((v) => {
              v.value = v.id
              v.label = v.name
            })
            this.brandList = res.data
            this.formData[0].options = this.brandList
          }
        }
      })
    },
    /**
     * 设备列表
     */
    getStoreListSelect() {
      getStoreListSelect().then((res) => {
        if (Array.isArray(res)) {
          res.map((v) => {
            v.value = v.code
            v.label = v.name
          })
          this.formData[0].options = res
        }
        this.storeList = res
      })
    },
    formHandler() {
      const {type, adc} = this.queryParam
      if(adc && !type){
        this.$message.error('筛选电压值需要先选择设备类型')
        return
      }
      this.searchQuery()
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.table_box {
  margin-top: 16px;
  padding: 18px 25px 0;
}

.control_box {
  padding: 0 0 17px;
}

.reset_password ::v-deep .el-input__inner {
  border: none;
}

.frontend_control_box ::v-deep .el-button--default {
  border-color: $themeColor;
  color: $themeColor;
}

.dot {
  margin-right: 8px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #36C08E;
}

.dot.red {
  background-color: #E52828;
}

.danger {
  color: #dc3545;
}

.freezer_code {
  color: $themeColor;
  cursor: pointer;
}

.freezer_code:hover {
  color: #262626;
}

.target_dialog_container ::v-deep .el-dialog__body {
  padding-bottom: 0 !important;
  padding-top: 0 !important;
}
</style>
