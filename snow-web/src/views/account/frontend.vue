<template>
  <div class="page_container account_frontend_view">
    <div class="form_box">
      <search-form :value="queryParam" :form-data="formData" :label-width="searchLabelWidth"
                   @nodeConfigHandler="formHandler" @resetQueryParam="resetQueryParam"
      />
    </div>
    <div class="table_box wbg">
      <div class="control_box flex flex-y-center flex-x-end">
        <div class="frontend_control_box">
          <!--          <el-button v-if="$havePermission('0601',$route.meta.functionList)" size="medium" icon="el-icon-download " @click.native="downloadTemplateFile">{{ $t('reaCommon.templateDownload') }}</el-button>-->
          <el-button v-if="$havePermission('0601',$route.meta.functionList)" style="margin-left:15px;"
                     icon="el-icon-upload2" size="medium" @click.native="openUploadPop"
          >{{ $t('reaCommon.dataImport') }}
          </el-button>
<!--          <el-button v-if="$havePermission('0101',$route.meta.functionList)" style="margin-left:15px;" size="medium"-->
<!--                     icon="el-icon-plus" type="primary" @click.native="openDialog"-->
<!--          >{{ $t('reaCommon.add') }}-->
<!--          </el-button>-->
        </div>
      </div>
      <div>
        <new-table
            :loading="loading"
            :table-header="accountFrontendHeader"
            :table-data="dataSource"
            :operates="tableOperates"
            :operates-props="operatesProps"
            :pagination="pagination"
            @pageConfigHandler="pageHandler"
        >
          <template v-slot:accountStatus="{items, index, row}">
            <el-tag :type="row.status == 0 ? 'info' : 'success'">{{ row.status == 0 ? '已禁用' : '已启用' }}</el-tag>
          </template>
          <template v-slot:unlock="{row}">
            <span v-if="$havePermission('0401',$route.meta.functionList)" class="table__oprate--item"
                  @click="unlockFn(row)"
            >{{
                row.freeze_status == 1 ? $t('reaCommon.unlock') : $t('reaCommon.lock')
              }}</span>
          </template>
          <template v-slot:edit="{row}">
            <span v-if="$havePermission('0301',$route.meta.functionList)" class="table__oprate--item"
                  @click="editFn(row)"
            >{{ $t('reaCommon.edit') }}</span>
          </template>
          <template v-slot:enable="{row}">
            <span v-if="$havePermission('0401',$route.meta.functionList)" class="table__oprate--item"
                  @click="enableFn(row)"
            >{{ row.status == 1 ? $t('reaCommon.disable') : $t('reaCommon.enable') }}</span>
          </template>
          <template v-slot:delete="{items, index, row}">
            <el-popconfirm
                v-if="row.status != 1 && $havePermission('0501',$route.meta.functionList)"
                :confirm-button-text="$t('reaCommon.confirm')"
                :cancel-button-text="$t('reaCommon.cancel')"
                :title="`${$t('reaCommon.confirmDeleteThisOne')}？`"
                icon="el-icon-warning"
                @confirm="deleteThis(row)"
            >
              <span slot="reference" class="table__oprate--item">{{ $t('reaCommon.delete') }}</span>
            </el-popconfirm>
          </template>
        </new-table>
      </div>
    </div>
    <div class="dialog_container">
      <ele-dialog :title="type == 'create' ? $t('reaCommon.addUser') : $t('reaCommon.editUser')"
                  :visible.sync="dialogVisible" center @dialogCancel="dialogCancel" @dialogConfirm="dialogConfirm"
                  @dialogClose="dialogClose"
      >
        <template slot="content">
          <div class="dialog_form">
            <el-form ref="dialogForm" size="medium" :model="dialogForm" :rules="dialogRules" label-width="135px"
                     class="demo-ruleForm"
            >
              <el-form-item :label="$t('reaCommon.phone')" prop="phone">
                <el-input v-model.trim="dialogForm.phone" oninput="if(value.length>30)value=value.slice(0,30)"
                          clearable
                />
              </el-form-item>
              <el-form-item :label="$t('reaCommon.name')" prop="name">
                <el-input v-model="dialogForm.username" clearable
                          @blur="dialogForm.username=$event.target.value.trim()"
                />
              </el-form-item>
              <!--              <el-form-item v-if="userInfo.is_super" :label="$t('frontUserAdd.belongTenant')" prop="tenant_id">-->
              <!--                <el-select v-model="dialogForm.tenant_id" :placeholder="$t('frontUserAdd.belongTenantPlaceholder')"-->
              <!--                           :disabled="type == 'edit'"-->
              <!--                >-->
              <!--                  <el-option v-for="brand in brandList" :key="brand.id" :label="brand.label" :value="brand.value"/>-->
              <!--                </el-select>-->
              <!--              </el-form-item>-->
              <!--              <el-form-item :label="$t('reaCommon.position')" prop="position">-->
              <!--                <el-input v-model="dialogForm.position" clearable-->
              <!--                          @blur="dialogForm.position=$event.target.value.trim()"-->
              <!--                />-->
              <!--              </el-form-item>-->
              <!--              <el-form-item :label="$t('reaCommon.status')" prop="status">-->
              <!--                <el-radio-group v-model="dialogForm.status">-->
              <!--                  <el-radio :label="1">{{ $t('frontUserAdd.normal') }}</el-radio>-->
              <!--                  <el-radio :label="0">{{ $t('frontUserAdd.disabled') }}</el-radio>-->
              <!--                </el-radio-group>-->
              <!--              </el-form-item>-->
              <!--              <el-form-item v-if="type == 'edit'" :label="$t('reaCommon.password')" prop="password">-->
              <!--                <div class="flex flex-x-start flex-y-center reset_password">-->
              <!--                  <div style="padding-right:20px;">******</div>-->
              <!--                  <el-button type="primary" @click.native="initPassword">{{ $t('reaCommon.resetPassword') }}</el-button>-->
              <!--                </div>-->
              <!--              </el-form-item>-->
            </el-form>
          </div>
        </template>
      </ele-dialog>
    </div>
    <div v-if="uploadVisible">
      <import
          :brand-list="brandList"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          width="50%"
          :action="'import_app_user'"
          :visible.sync="uploadVisible"
          center
          @refresh="formHandler"
          @dialogCancel="uploadCancel"
          @dialogConfirm="uploadConfirm"
          @dialogClose="uploadClose"
      />
    </div>
  </div>
</template>
<script>
import Import from '@/components/Import/tenantSelectImport'
import { formCheckPhone } from '@/utils/validate'
// import { SearchForm, NewTable, EleDialog } from 'ele-form'

import SearchForm from '@/components/SearchForm'
import NewTable from '@/components/Table'
import EleDialog from '@/components/Dialog'
import { accountFrontendHeader } from '@/utils/header'
import ListMixin from '@/mixins/List'
import { userCreate, userEdit, userChangeStatus, userDelete, userReset } from '@/api/account/frontend'
import { supplierList, tenantAll } from '@/api/common'
import { mapGetters } from 'vuex'
import { downloadTemplate } from '@/utils/index'
import { importTemplate } from '@/api/download-api'
import { userLock, userUnlock } from '@/api/account/frontend'

export default {
  name: 'account-frontend',
  components: {
    SearchForm,
    NewTable,
    EleDialog,
    Import
  },
  mixins: [ListMixin],
  data() {
    return {
      searchLabelWidth: '100px',
      uploadVisible: false,
      type: 'create',
      dialogForm: {
        phone: '',
        username: '',
        tenant_id: '',
        // status: 1,
        position: '',
        password: '********'
      },
      brandList: [],
      dialogRules: {
        phone: [
          { required: true, message: this.$t('frontUserAdd.phonePlaceholder'), trigger: 'blur' },
          { validator: formCheckPhone, trigger: 'blur' }
        ],
        username: [
          { required: true, message: this.$t('frontUserAdd.namePlaceholder'), trigger: 'blur' }
        ],
        tenant_id: [
          { required: true, message: this.$t('frontUserAdd.belongTenantPlaceholder'), trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'blur' }
        ],
        position: [
          { required: true, message: this.$t('frontUserAdd.positionPlaceholder'), trigger: 'blur' }
        ]
      },
      dialogVisible: false,
      accountFrontendHeader,
      formRule: {},
      formData: [{
        name: '手机号码',
        nameKey: 'phone',
        type: 'input',
        field: 'phone',
        attrs: {
          clearable: true
        }
      }, {
        name: '姓名',
        nameKey: 'name',
        type: 'input',
        field: 'username',
        attrs: {
          clearable: true
        }
      }, {
        name: '供应商',
        nameKey: 'supplier',
        type: 'select',
        field: 'supplier_id',
        placeholder: '请选择供应商',
        attrs: {
          clearable: true
        },
        options: []
      }, {
        name: '状态',
        nameKey: 'status',
        type: 'select',
        field: 'status',
        placeholder: '请选择状态',
        options: [{
          label: '正常',
          labelKey: 'normal',
          value: 1
        }, {
          label: '禁用',
          labelKey: 'disable',
          value: 0
        }],
        attrs: {
          clearable: true
        }
      }],
      queryParam: {
        phone: '',
        username: '',
        supplier_id: '',
        tenant_id: '',
        status: ''
      },
      initQueryParam: {
        phone: '',
        username: '',
        supplier_id: '',
        tenant_id: '',
        status: ''
      },
      dataSource: [],
      /* mixin里接口名称*/
      apiUrl: {
        list: 'userList'
      },
      /* 表格操作按钮*/
      tableOperates: [
        // {
        //   label: '解锁',
        //   labelKey: 'unlock',
        //   slot: 'unlock'
        // },
        {
          label: '编辑',
          labelKey: 'edit',
          slot: 'edit',
          method: (index, row) => {
            this.editFn(row)
          }
        },
        {
          label: '禁用',
          labelKey: 'enable',
          slot: 'enable'
        },
        {
          label: '删除',
          labelKey: 'delete',
          slot: 'delete'
        }

      ],
      operatesProps: {
        width: '200px',
        label: '操作',
        labelKey: 'operation',
        fixed: 'right'
      },
      selectAccount: {},
      optionsIndexObj: {}

    }
  },
  computed: {
    ...mapGetters([
      'userInfo',
      'language'
    ])
  },
  watch: {
    '$i18n.locale'() {
      this.searchLabelWidth = this.language === 'en' ? '130px' : '100px'

      this.dialogRules = {
        phone: [
          { required: true, message: this.$t('frontUserAdd.phonePlaceholder'), trigger: 'blur' },
          { validator: formCheckPhone, trigger: 'blur' }
        ],
        username: [
          { required: true, message: this.$t('frontUserAdd.namePlaceholder'), trigger: 'blur' }
        ],
        tenant_id: [
          { required: true, message: this.$t('frontUserAdd.belongTenantPlaceholder'), trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'blur' }
        ],
        position: [
          { required: true, message: this.$t('frontUserAdd.positionPlaceholder'), trigger: 'blur' }
        ]
      }
      setTimeout(() => {
        if (this.$refs['dialogForm']) {
          this.$refs['dialogForm'].clearValidate()
        }
      })
    }
  },
  created() {
    // 如果只有查看权限则隐藏当前这一列
    if (this.$route.meta.functionList.length == 1 && this.$havePermission('0201', this.$route.meta.functionList)) {
      this.tableOperates = []
    }
    this.getSupplierList()
    this.searchLabelWidth = this.language === 'en' ? '130px' : '100px'
    this.operatesProps.width = this.language === 'en' ? '250px' : '200px'
    // 非超级管理员不能看到品牌方筛选
    if (!this.userInfo.is_super) {
      this.formData.splice(3, 1)
      // this.accountFrontendHeader.splice(3, 1)
      this.accountFrontendHeader.map((v, index) => {
        if (v.labelKey === 'tenant') {
          this.accountFrontendHeader.splice(index, 1)
        }
      })
      this.formData.map((v, index) => {
        this.optionsIndexObj[v.field] = index
      })
    } else {
      this.formData.map((v, index) => {
        this.optionsIndexObj[v.field] = index
      })
      // this.getTenantAll()
    }
  },
  methods: {
    unlockFn(row) {
      if (row.loading) {
        return
      }
      {
        row.loading = true
      }
      (row.freeze_status ? userUnlock : userLock)({ user_id: row.id, status: row.status == 1 ? 0 : 1 }).then((res) => {
        row.freeze_status = row.freeze_status == 1 ? 0 : 1
        this.$message.success(row.freeze_status == 1 ? this.$t('reaCommon.lockSuccess') : this.$t('reaCommon.unlockSuccess'))
      }).finally(() => {
        row.loading = false
      })
    },
    toDeviceView(row) {
      this.$router.push({
        path: '/account/frontend-device-list',
        query: {
          id: row.id
        }
      })
    },
    openUploadPop() {
      this.uploadVisible = true
    },
    uploadCancel() {
      this.uploadVisible = false
    },
    uploadConfirm() {
      this.uploadVisible = false
    },
    uploadClose() {
      this.uploadVisible = false
    },
    downloadTemplateFile() {
      downloadTemplate(`${importTemplate}?action=user`)
    },
    initPassword() {
      userReset({ id: this.selectAccount.id }).then((res) => {
        if (res.code == 1) {
          this.$message.success(this.$t('reaCommon.resetPasswordSuccess'))
        }
      })
    },
    deleteThis(row) {
      if (row.loading) {
        return
      }
      row.loading = true
      userDelete({ id: row.id }).then((res) => {
        this.$message.success(this.$t('reaCommon.deleteSuccess'))
        this.formHandler()
      }).finally(() => {
        row.loading = false
      })
    },
    enableFn(row) {
      if (row.loading) {
        return
      }
      row.loading = true
      userChangeStatus({ id: row.id, status: row.status == 1 ? 0 : 1 }).then((res) => {
        row.status = row.status == 1 ? 0 : 1
        this.$message.success(row.status == 1 ? this.$t('reaCommon.enableSuccess') : this.$t('reaCommon.disableSuccess'))
      }).finally(() => {
        row.loading = false
      })
    },
    editFn(selectAccount) {
      this.selectAccount = selectAccount
      this.type = 'edit'
      Object.keys(this.dialogForm).map((key) => {
        this.dialogForm[key] = selectAccount[key]
      })
      this.dialogVisible = true
      if (this.$refs['dialogForm']) {
        this.$refs['dialogForm'].clearValidate()
      }
    },
    getSupplierList() {
      supplierList({ page_size: 10000, page_num: 1 }).then(res => {
        let arr = []
        res.list.map((v) => {
          arr.push({ value: v.id, label: v.supplier_name })
        })
        const index = this.formData.findIndex(v => v.field == 'supplier_id')
        this.formData[index].options = arr
      })
    },
    dialogClose() {
      this.dialogVisible = false
    },
    dialogConfirm() {
      if (this.type == 'edit') {
        this.dialogForm.id = this.selectAccount.id
      }
      this.$refs['dialogForm'].validate((valid) => {
        if (valid) {
          (this.type === 'create' ? userCreate : userEdit)(this.dialogForm).then((res) => {
            this.$message.success(this.$t('reaCommon.operationSuccess'))
            delete this.dialogForm.id
            this.dialogVisible = false
            this.formHandler()
          })
        }
      })
    },
    dialogCancel() {
      this.dialogVisible = false
    },
    openDialog() {
      this.type = 'create'
      this.dialogForm = {
        phone: '',
        name: '',
        tenant_id: '',
        status: 1,
        position: '',
        password: '********'
      }
      this.dialogVisible = true
      if (this.$refs['dialogForm']) {
        this.$refs['dialogForm'].resetFields()
        this.$refs['dialogForm'].clearValidate()
      }
    },
    formHandler() {
      if (Array.isArray(this.queryParam.openTime) && this.queryParam.openTime.length) {
        this.queryParam.start = this.queryParam.openTime[0]
        this.queryParam.end = this.queryParam.openTime[1]
      } else {
        this.queryParam.start = ''
        this.queryParam.end = ''
      }
      this.searchQuery()
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.table_box {
  margin-top: 23px;
  padding: 18px 25px 0;
}

.control_box {
  padding: 0 0 17px;
}

.reset_password ::v-deep .el-input__inner {
  border: none;
}

.frontend_control_box ::v-deep .el-button--default {
  border-color: $themeColor;
  color: $themeColor;
}

.dot {
  margin-right: 8px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #36C08E;
}

.dot.red {
  background-color: #E52828;
}
</style>
