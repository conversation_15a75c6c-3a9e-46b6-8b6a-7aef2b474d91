<template>
  <div class="page_container account_frontend_view">
    <div class="form_box">
      <search-form :value="queryParam" :form-data="formData" :label-width="searchLabelWidth"
                   @nodeConfigHandler="formHandler" @resetQueryParam="resetQueryParam"
      />
    </div>
    <div class="table_box wbg">
      <div class="control_box flex flex-y-center flex-x-start">
        <div class="flex-1"/>
        <div v-if="$havePermission('0101',$route.meta.functionList)">
          <el-button size="medium" icon="el-icon-plus" type="primary" @click.native="openDialog">{{
              $t('reaCommon.add')
            }}
          </el-button>
        </div>
      </div>
      <new-table
          :loading="loading"
          :table-header="accountRoleHeader"
          :table-data="dataSource"
          :operates="tableOperates"
          :operates-props="operatesProps"
          :pagination="pagination"
          @pageConfigHandler="pageHandler"
      >
        <template v-slot:edit="{row}">
          <span v-if="$havePermission('0301',$route.meta.functionList)" class="table__oprate--item"
                @click="editFn(row.id)"
          >{{ $t('reaCommon.edit') }}</span>
        </template>
        <template v-slot:delete="{items, index, row}">
          <el-popconfirm
              v-if="$havePermission('0501',$route.meta.functionList)"
              :confirm-button-text="$t('reaCommon.confirm')"
              :cancel-button-text="$t('reaCommon.cancel')"
              :title="`${$t('reaCommon.confirmDeleteThisOne')}？`"
              icon="el-icon-warning"
              @confirm="deleteThis(row)"
          >
            <span slot="reference" class="table__oprate--item">{{ $t('reaCommon.delete') }}</span>
          </el-popconfirm>
        </template>
      </new-table>
    </div>
    <div class="dialog_container">
      <ele-dialog :title="type == 'create' ? $t('reaCommon.add') : $t('reaCommon.edit')" :visible.sync="dialogVisible"
                  center @dialogCancel="dialogCancel" @dialogConfirm="dialogConfirm" @dialogClose="dialogClose"
      >
        <template slot="content">
          <div class="dialog_form">
            <el-form ref="dialogForm" size="medium" :model="dialogForm" :rules="dialogRules" label-width="120px"
                     class="demo-ruleForm"
            >
              <el-form-item :label="$t('reaCommon.roleName')" prop="role_name">
                <el-input v-model="dialogForm.role_name" clearable
                          @blur="dialogForm.role_name=$event.target.value.trim()"
                />
              </el-form-item>
              <el-form-item :label="$t('reaCommon.remark')" prop="remark">
                <el-input v-model="dialogForm.remark" clearable @blur="dialogForm.remark=$event.target.value.trim()"/>
              </el-form-item>
              <el-form-item v-if="userInfo.is_super" :label="$t('reaCommon.tenant')" prop="tenant_id">
                <el-select v-model="dialogForm.tenant_id" :placeholder="$t('checkPlanAdd.tenantPlaceholder')"
                           :disabled="type == 'edit'" @change="getMenuList"
                >
                  <el-option v-for="brand in brandList" :key="brand.id" :label="brand.label" :value="brand.value"/>
                </el-select>
              </el-form-item>
              <el-form-item v-if="(userInfo.is_super && dialogForm.tenant_id) || !userInfo.is_super"
                            :label="$t('roleAdd.authorization')" prop="module_id" class="authorization_tree_box"
              >
                <el-tree
                    ref="tree"
                    :data="menuList"
                    show-checkbox
                    node-key="module_id"
                    :default-checked-keys="ownCheckedNodeList"
                    :props="defaultProps"
                    default-expand-all
                    @check-change="checkChange"
                />

              </el-form-item>
            </el-form>
          </div>
        </template>
      </ele-dialog>
    </div>
  </div>
</template>
<script>
// import { SearchForm, NewTable, EleDialog } from 'ele-form'
import SearchForm from '@/components/SearchForm'
import NewTable from '@/components/Table'
import EleDialog from '@/components/Dialog'
import { accountRoleHeader } from '@/utils/header'
import ListMixin from '@/mixins/List'
import { mapGetters } from 'vuex'
import { tenantAll } from '@/api/common'
import {
  adminRoleOne,
  adminRoleCreate,
  adminRoleEdit,
  adminRoleTenantMenu,
  adminRoleDelete
} from '@/api/account/backend'
import { deepJSON } from '@/utils/tools'
import { convertRoute } from '@/utils/index'

export default {
  name: 'account-role',
  components: {
    SearchForm,
    NewTable,
    EleDialog
  },
  computed: {
    ...mapGetters([
      'userInfo',
      'language'
    ])
  },
  mixins: [ListMixin],
  data() {
    return {
      searchLabelWidth: '100px',
      ownCheckedNodeList: [],
      type: 'create',
      defaultProps: {
        children: 'items',
        label: (data) => {
          return this.$t(`route.${data.index_name}`)
        }
      },
      dialogForm: {
        role_name: '',
        remark: '',
        tenant_id: '',
        module_id: []
      },
      dialogRules: {
        role_name: [
          { required: true, message: this.$t('roleAdd.pleaseEnterRoleName'), trigger: 'blur' }
        ],
        tenant_id: [
          { required: true, message: this.$t('checkPlanAdd.tenantPlaceholder'), trigger: 'blur' }
        ],
        module_id: [
          { required: true, message: this.$t('roleAdd.pleaseConfigRoleAuth'), trigger: 'blur', type: 'array' }
        ]
      },
      dialogTitle: '新增',
      dialogVisible: false,
      accountRoleHeader,
      formRule: {},
      formData: [{
        name: '角色名称',
        nameKey: 'roleName',
        type: 'input',
        field: 'role_name',
        attrs: {
          clearable: true
        }
      }, {
        name: '品牌方',
        nameKey: 'tenant',
        type: 'select',
        field: 'tenant_id',
        placeholder: '请选择品牌方',
        attrs: {
          clearable: true
        },
        options: []
      }],
      queryParam: {
        role_name: '',
        tenant_id: ''
      },
      brandList: [],
      dataSource: [],
      /* mixin里接口名称*/
      apiUrl: {
        list: 'adminRoleList'
      },
      /* 表格操作按钮*/
      tableOperates: [
        {
          label: '编辑',
          labelKey: 'edit',
          slot: 'edit',
          method: (index, row) => {
            this.editFn(row.id)
          }
        },
        {
          label: '删除',
          labelKey: 'delete',
          slot: 'delete'
        }
      ],
      operatesProps: {
        width: '145px',
        label: '操作',
        labelKey: 'operation',
        fixed: 'right'
      },
      menuList: [],
      selectAccount: {}

    }
  },
  watch: {
    '$i18n.locale'() {
      this.searchLabelWidth = this.language === 'en' ? '110px' : '100px'

      this.dialogRules = {
        name: [
          { required: true, message: this.$t('roleAdd.pleaseEnterRoleName'), trigger: 'blur' }
        ],
        tenant_id: [
          { required: true, message: this.$t('checkPlanAdd.tenantPlaceholder'), trigger: 'blur' }
        ],
        module_id: [
          { required: true, message: this.$t('roleAdd.pleaseConfigRoleAuth'), trigger: 'blur', type: 'array' }
        ]
      }
      setTimeout(() => {
        if (this.$refs['dialogForm']) {
          this.$refs['dialogForm'].clearValidate()
        }
      })
    }
  },
  created() {
    // 如果只有查看权限则隐藏当前这一列
    if (this.$route.meta.functionList.length == 1 && this.$havePermission('0201', this.$route.meta.functionList)) {
      this.tableOperates = []
    }
    this.initQueryParam = deepJSON(this.queryParam)
    this.searchLabelWidth = this.language === 'en' ? '110px' : '100px'
    if (!this.userInfo.is_super) {
      this.formData.splice(1, 1)
      this.getTenantMenu()
    } else {
      this.getTenantAll()
    }
  },
  methods: {
    /**
     * 获取角色权限列表
     * */
    getMenuList(val) {
      this.getTenantMenu(val)
    },
    /**
     * 获取角色权限列表
     * */
    getTenantMenu(tenant_id) {
      adminRoleTenantMenu({ tenant_id }).then((res) => {
        res = res && convertRoute(res)
        const arr = []
        Object.values(res).map((route) => {
          if (Array.isArray(route.items) && route.items.length) {
            route.items.map((item) => {
              if (Array.isArray(item.function_list) && item.function_list.length) {
                item.items = []
                item.function_list.map((button) => {
                  item.items.push({
                    module_id: button.function_id,
                    index_name: button.index_name
                  })
                })
              }
            })
          }
          //售点列表也有权兼容售点列表下面的场景数据权限
          let items = []
          if (Array.isArray(route.function_list) && route.function_list.length) {
            route.function_list.map((item) => {
              items.push({
                module_id: item.function_id,
                index_name: item.index_name
              })
            })
          }
          route.items = items.concat(route.items)
        })
        this.menuList = Object.values(res)
        const parentNodeList = []
        this.menuList.map((menu) => {
          if (menu.items && menu.items.length) {
            parentNodeList.push(menu.module_id)
            menu.items.map((button) => {
              if (button.items && button.items.length) {
                parentNodeList.push(button.module_id)
              }
            })
          }
        })
        // 设置默认参数
        const defaultNodeList = []
        if (Array.isArray(this.dialogForm.module_id)) {
          this.dialogForm.module_id.map((id) => {
            if (!parentNodeList.includes(id)) {
              defaultNodeList.push(id)
            }
          })
          this.ownCheckedNodeList = defaultNodeList
        }
      })
    },
    /**
     * 删除角色
     * */
    deleteThis(row) {
      if (row.loading) {
        return
      }
      row.loading = true
      adminRoleDelete({ id: row.id }).then((res) => {
        this.$message.success(this.$t('reaCommon.deleteSuccess'))
        this.formHandler()
      }).finally(() => {
        row.loading = false
      })
    },
    /**
     * 编辑角色
     * */
    editFn(id) {
      adminRoleOne({ id }).then((res) => {
        res.module_id = res.module_codes
        res.function_id = res.function_codes
        this.selectAccount = res
        this.type = 'edit'
        Object.keys(this.dialogForm).map((key) => {
          this.dialogForm[key] = this.selectAccount[key]
        })
        if (Array.isArray(this.selectAccount.function_id)) {
          this.dialogForm.module_id.push(...this.selectAccount.function_id)
        }
        this.dialogVisible = true
        this.getMenuList(this.dialogForm.tenant_id)
        if (this.$refs['dialogForm']) {
          this.$refs['dialogForm'].clearValidate()
        }
      })
    },
    /**
     * 保存 编辑/创建
     * */
    dialogConfirm() {
      if (this.type == 'edit') {
        this.dialogForm.id = this.selectAccount.id
      }
      if (this.$refs.tree) {
        const checkBox = this.$refs.tree.getCheckedNodes(false, true)
        const nodeList = []
        const btnList = []
        checkBox.map((v) => {
          if (v.module_id.includes('-')) {
            btnList.push(v.module_id)
          } else {
            nodeList.push(v.module_id)
          }
        })
        this.dialogForm.module_codes = nodeList
        this.dialogForm.function_codes = btnList
      }
      this.$refs['dialogForm'].validate((valid) => {
        if (valid) {
          (this.type === 'create' ? adminRoleCreate : adminRoleEdit)(this.dialogForm).then((res) => {
            if (res) {
              this.$message.success(this.$t('reaCommon.operationSuccess'))
              delete this.dialogForm.id
              this.dialogVisible = false
              this.formHandler()
            }
          })
        }
      })
    },
    checkChange() {
      const checkBox = this.$refs.tree.getCheckedNodes(false, true)
      const nodeList = []
      checkBox.map((v) => {
        nodeList.push(v.module_id)
      })
      this.dialogForm.module_id = nodeList
    },

    dialogClose() {
      this.dialogVisible = false
    },

    dialogCancel() {
      this.dialogVisible = false
    },
    openDialog() {
      this.type = 'create'
      this.ownCheckedNodeList = []
      this.dialogVisible = true
      this.dialogForm = {
        role_name: '',
        remark: '',
        tenant_id: '',
        module_id: []
      }
      if (this.$refs['dialogForm']) {
        this.$refs['dialogForm'].resetFields()
        this.$refs['dialogForm'].clearValidate()
      }
      if (this.$refs.tree) {
        this.$refs.tree.setCheckedKeys([])
      }
    },
    formHandler() {
      this.searchQuery()
    }
  }
}
</script>

<style lang="scss" scoped>
.table_box {
  margin-top: 23px;
  padding: 18px 25px 0;
}

.control_box {
  padding: 0 0 17px;
}

.authorization_tree_box ::v-deep .el-form-item__label {
  line-height: 26px;
}
</style>
<style lang="scss">
.account_frontend_view {
  .el-button--primary.is-plain {
    background-color: white;
  }
}
</style>
