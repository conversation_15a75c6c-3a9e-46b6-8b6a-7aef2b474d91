<template>
  <div class="page_container account_frontend_view">
    <div class="form_box">
      <search-form ref="searchForm" :value="queryParam" :form-data="formData" :label-width="searchLabelWidth"
                   @nodeConfigHandler="formHandler" @selectChangeHandler="selectChangeHandler"
                   @resetQueryParam="resetQueryParam"
      />
    </div>
    <div class="table_box wbg">
      <div class="control_box flex flex-y-center flex-x-start">
        <div class="flex-1"/>
        <div v-if="$havePermission('0101',$route.meta.functionList)">
          <el-button size="medium" icon="el-icon-plus" type="primary" @click.native="openDialog">{{
              $t('reaCommon.add')
            }}
          </el-button>
        </div>
      </div>
      <new-table
          :loading="loading"
          :table-header="accountBackendHeader"
          :table-data="dataSource"
          :operates="tableOperates"
          :pagination="pagination"
          :operates-props="operatesProps"
          @pageConfigHandler="pageHandler"
          @sort-change="sortChange"
      >
        <template v-slot:BU="{row}">
          <template v-if="row.bu_list && row.bu_list.length">
            {{ row.bu_list.map(item => item.bu_name).join('、') }}
          </template>
          <template v-else>-</template>
        </template>
        <template v-slot:freeze_status="{row}">
          <div class="flex flex-y-center"><span class="dot" :class="{'red':row.freeze_status == 1}"/>{{
              row.freeze_status == 1 ? $t('reaCommon.locked') : $t('reaCommon.unlocked')
            }}
          </div>
        </template>
        <template v-slot:status="{row}">
          <div class="flex flex-y-center"><span class="dot" :class="{'red':row.status == 0}"/>{{
              row.status == 1 ?
                  $t('reaCommon.normal') : $t('reaCommon.disable')
            }}
          </div>
        </template>
        <template v-slot:edit="{row}">
          <span v-if="$havePermission('0301',$route.meta.functionList)" class="table__oprate--item"
                @click="editFn(row)"
          >{{ $t('reaCommon.edit') }}</span>
        </template>
        <!--        <template v-slot:unlock="{row}">-->
        <!--          <span v-if="$havePermission('1801',$route.meta.functionList)" class="table__oprate&#45;&#45;item"-->
        <!--                @click="unlockFn(row)">{{-->
        <!--              row.freeze_status == 1 ? $t('reaCommon.unlock') : $t('reaCommon.lock')-->
        <!--            }}</span>-->
        <!--        </template>-->
        <template v-slot:enable="{row}">
          <span v-if="$havePermission('0401',$route.meta.functionList)" class="table__oprate--item"
                @click="enableFn(row)"
          >{{ row.status == 1 ? $t('reaCommon.disable') : $t('reaCommon.enable') }}</span>
        </template>
        <template v-slot:delete="{items, index, row}">
          <el-popconfirm
              v-if="$havePermission('0501',$route.meta.functionList)"
              :confirm-button-text="$t('reaCommon.confirm')"
              :cancel-button-text="$t('reaCommon.cancel')"
              :title="`${$t('reaCommon.confirmDeleteThisOne')}？`"
              icon="el-icon-warning"
              @confirm="deleteThis(row)"
          >
            <span slot="reference" class="table__oprate--item">{{ $t('reaCommon.delete') }}</span>
          </el-popconfirm>
        </template>
      </new-table>
    </div>
    <div class="dialog_container">
      <ele-dialog :title="type == 'create' ? $t('reaCommon.add') : $t('reaCommon.edit')" :visible.sync="dialogVisible"
                  center @dialogCancel="dialogCancel" @dialogConfirm="dialogConfirm" @dialogClose="dialogClose"
      >
        <template slot="content">
          <div class="dialog_form">
            <el-form ref="dialogForm" size="medium" :model="dialogForm" :rules="dialogRules" label-width="155px"
                     class="demo-ruleForm"
            >
              <el-form-item :label="$t('backendUserAdd.username')" prop="admin_name">
                <el-input v-model="dialogForm.admin_name" clearable
                          @blur="dialogForm.admin_name=$event.target.value.trim()"
                />
              </el-form-item>
              <el-form-item v-if="type == 'create'" :label="$t('backendUserAdd.password')" prop="password">
                <el-input v-model="dialogForm.password" type="password" show-password clearable
                          @blur="dialogForm.password=$event.target.value.trim()"
                />
              </el-form-item>
              <el-form-item v-if="type == 'create'" :label="$t('backendUserAdd.confirmPassword')"
                            prop="confirmPassword"
              >
                <el-input v-model="dialogForm.confirmPassword" type="password" show-password clearable
                          @blur="dialogForm.confirmPassword=$event.target.value.trim()"
                />
              </el-form-item>
              <el-form-item :label="$t('reaCommon.phone')" prop="phone">
                <el-input v-model="dialogForm.phone" oninput="if(value.length>30)value=value.slice(0,30)" clearable
                          @blur="dialogForm.phone=$event.target.value.trim()"
                />
              </el-form-item>
              <el-form-item v-if="userInfo.is_super" :label="$t('reaCommon.tenant')" prop="tenant_id">
                <el-select filterable v-model="dialogForm.tenant_id" :placeholder="$t('checkPlanAdd.tenantPlaceholder')"
                           :disabled="type == 'edit'" clearable @change="getRoleList"
                >
                  <el-option v-for="brand in brandList" :key="brand.id" :label="brand.label" :value="brand.value"/>
                </el-select>
              </el-form-item>
              <!--bu-->
              <el-form-item v-if="dialogForm.tenant_id && buList.length" label="BU" prop="bu_ids">
                <el-checkbox-group v-model="dialogForm.bu_ids">
                  <el-checkbox v-for="role in buList" :key="role.value" :label="role.value">{{ role.label }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item :label="$t('reaCommon.position')" prop="position">
                <el-input v-model="dialogForm.position" clearable
                          @blur="dialogForm.position=$event.target.value.trim()"
                />
              </el-form-item>

              <el-form-item v-if="dialogForm.tenant_id" :label="$t('reaCommon.role')" prop="role_ids">
                <el-checkbox-group v-model="dialogForm.role_ids">
                  <el-checkbox v-for="role in roleList" :key="role.value" :label="role.value">{{ role.label }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>

              <el-form-item :label="$t('reaCommon.status')" prop="status">
                <el-radio-group v-model="dialogForm.status">
                  <el-radio :label="1">{{ $t('reaCommon.normal') }}</el-radio>
                  <el-radio :label="0">{{ $t('reaCommon.disable') }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item v-if="type == 'edit'" :label="$t('reaCommon.password')">
                <div class="flex flex-x-start flex-y-center reset_password">
                  <div style="padding-right:20px;">******</div>
                  <el-button type="primary" @click.native="initPassword" :disabled="disabledResetPassword">
                    {{ $t('reaCommon.resetPassword') }}
                  </el-button>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </template>
      </ele-dialog>
    </div>
  </div>
</template>
<script>
import { formCheckPhone } from '@/utils/validate'
// import { SearchForm, NewTable, EleDialog } from 'ele-form'
import SearchForm from '@/components/SearchForm'
import NewTable from '@/components/Table'
import EleDialog from '@/components/Dialog'

import { accountBackendHeader } from '@/utils/header'
import ListMixin from '@/mixins/List'
import { adminCreate, adminDelete, adminEdit, adminReset, adminRoleAll } from '@/api/account/backend'
import { tenantAll, buListAll } from '@/api/common'
import { mapGetters } from 'vuex'
import { adminDisable, adminUnlock, adminLock } from '@/api/account/backend'

export default {
  name: 'account-backend',
  components: {
    SearchForm,
    NewTable,
    EleDialog
  },
  mixins: [ListMixin],
  data() {
    const verifyLength = (rule, value, callback) => {
      if (value.length < 8 || value.length > 16) {
        callback(new Error(this.$t('reaCommon.passwordLength')))
      } else {
        callback()
      }
    }
    const verifySpecialCode = (rule, value, callback) => {
      // 1.全部包含：大写、小写、数字、特殊字符；
      const regex1 = '(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[\\W_])^.*$'
      // 2.无大写：小写、数字、特殊字符；
      const regex2 = '(?=.*[a-z])(?=.*[0-9])(?=.*[\\W_])^.*$'
      // 3.无小写：大写、数字、特殊字符；
      const regex3 = '(?=.*[A-Z])(?=.*[0-9])(?=.*[\\W_])^.*$'
      // 4.无数字：大写、小写、特殊字符；
      const regex4 = '(?=.*[A-Z])(?=.*[a-z])(?=.*[\\W_])^.*$'
      // 5.无特殊字符：大写、小写、数字；
      const regex5 = '(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])^.*$'
      const reg = '(' + regex1 + ')|(' + regex2 + ')|(' + regex3 + ')|(' + regex4 + ')|(' + regex5 + ')'
      const pwdRegex = new RegExp(reg)
      if (pwdRegex.test(value)) {
        callback()
      } else {
        callback(new Error(this.$t('reaCommon.passwordSpecialVerify')))
      }
    }
    const equalToPassword = (rule, value, callback) => {
      if (this.dialogForm.password !== value) {
        callback(new Error(this.$t('backendUserAdd.notSamePassword')))
      } else {
        callback()
      }
    }
    return {
      searchLabelWidth: '100px',
      type: 'create',
      brandList: [],
      buList: [],
      dialogForm: {
        bu_ids: [],
        phone: '',
        admin_name: '',
        tenant_id: '',
        status: 1,
        position: '',
        role_ids: [],
        password: 'Lm12345678',
        confirmPassword: 'Lm12345678'
      },
      dialogRules: {
        phone: [
          { required: true, message: this.$t('frontUserAdd.phonePlaceholder'), trigger: 'blur' },
          { validator: formCheckPhone, trigger: 'blur' }
        ],
        admin_name: [
          { required: true, message: this.$t('frontUserAdd.namePlaceholder'), trigger: 'blur' }
        ],
        tenant_id: [
          { required: true, message: this.$t('frontUserAdd.belongTenantPlaceholder'), trigger: 'blur' }
        ],
        bu_ids: [
          { required: true, message: this.$t('reaCommon.pleaseSelect'), trigger: 'blur' }
        ],
        status: [
          { required: true, message: this.$t('reaCommon.pleaseSelect'), trigger: 'blur' }
        ],
        position: [
          { required: true, message: this.$t('frontUserAdd.positionPlaceholder'), trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: this.$t('backendUserAdd.pleaseConfirmPassword'), trigger: 'blur' },
          { required: true, validator: verifyLength, trigger: 'blur' },
          { required: true, validator: equalToPassword, trigger: 'blur' },
          { required: true, validator: verifySpecialCode, trigger: 'blur' }
        ],
        password: [
          { required: true, message: this.$t('login.pleaseEnterPassword'), trigger: 'blur' },
          { required: true, validator: verifyLength, trigger: 'blur' },
          { required: true, validator: verifySpecialCode, trigger: 'blur' }
        ],
        role_ids: [
          { type: 'array', required: true, message: this.$t('backendUserAdd.moreOneRole'), trigger: 'blur' }
        ]
      },
      dialogVisible: false,
      accountBackendHeader,
      formRule: {},
      formData: [
      //     {
      //   name: '用户ID',
      //   nameKey: 'userId',
      //   type: 'input',
      //   field: 'code',
      //   attrs: {
      //     clearable: true
      //   }
      // },
        {
        name: '手机号码',
        nameKey: 'phone',
        type: 'input',
        field: 'phone',
        attrs: {
          clearable: true
        }
      }, {
        name: '姓名',
        nameKey: 'name',
        type: 'input',
        field: 'admin_name',
        attrs: {
          clearable: true
        }
      }, {
        name: '品牌方',
        nameKey: 'tenant',
        type: 'select',
        field: 'tenant_id',
        placeholder: '请选择品牌方',
        attrs: {
          filterable: true,
          clearable: true
        },
        options: []
      },
        {
          name: 'BU',
          nameKey: 'BU',
          type: 'select',
          field: 'bu_ids',
          placeholder: '请选择所属BU',
          attrs: {
            'collapse-tags': true,
            multiple: true,
            filterable: true,
            clearable: true
          },
          options: []
        }, {
          name: '岗位',
          nameKey: 'position',
          type: 'input',
          field: 'position',
          attrs: {
            clearable: true
          }
        },
        {
          name: '状态',
          nameKey: 'status',
          type: 'select',
          field: 'status',
          placeholder: '请选择状态',
          options: [{
            label: '正常',
            labelKey: 'normal',
            value: 1
          }, {
            label: '禁用',
            labelKey: 'disable',
            value: 0
          }],
          attrs: {
            clearable: true
          }
        }, {
          name: '开设时间',
          nameKey: 'openTime',
          type: 'daterange',
          field: 'openTime'
        }],
      queryParam: {
        meta_store: 0,
        bu_ids: [],
        code: '',
        phone: '',
        admin_name: '',
        tenant_id: '',
        status: '',
        openTime: [],
        start: '',
        end: '',
        order_field: '',
        order: ''
      },
      initQueryParam: {
        meta_store: 0,
        bu_ids: [],
        code: '',
        phone: '',
        admin_name: '',
        tenant_id: '',
        status: '',
        openTime: [],
        start: '',
        end: '',
        order_field: '',
        order: ''
      },
      dataSource: [],
      /* mixin里接口名称*/
      apiUrl: {
        list: 'adminList'
      },
      /* 表格操作按钮*/
      tableOperates: [
        {
          label: '解锁',
          labelKey: 'unlock',
          slot: 'unlock'
        },
        {
          label: '禁用',
          labelKey: 'enable',
          slot: 'enable'
        },
        {
          label: '编辑',
          labelKey: 'edit',
          slot: 'edit',
          method: (index, row) => {
            this.editFn(row)
          }
        },
        {
          label: '删除',
          labelKey: 'delete',
          slot: 'delete'
        }

      ],
      operatesProps: {
        width: '250px',
        label: '操作',
        labelKey: 'operation',
        fixed: 'right'
      },
      selectAccount: {},
      roleList: [],
      optionsIndexObj: {},
      disabledResetPassword: false
    }
  },
  computed: {
    ...mapGetters([
      'userInfo',
      'language'
    ])
  },
  watch: {
    '$i18n.locale'() {
      const verifyLength = (rule, value, callback) => {
        if (value.length < 8 || value.length > 16) {
          callback(new Error(this.$t('reaCommon.passwordLength')))
        } else {
          callback()
        }
      }
      const equalToPassword = (rule, value, callback) => {
        if (this.dialogForm.password !== value) {
          callback(new Error(this.$t('backendUserAdd.notSamePassword')))
        } else {
          callback()
        }
      }
      const verifySpecialCode = (rule, value, callback) => {
        // 1.全部包含：大写、小写、数字、特殊字符；
        const regex1 = '(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[\\W_])^.*$'
        // 2.无大写：小写、数字、特殊字符；
        const regex2 = '(?=.*[a-z])(?=.*[0-9])(?=.*[\\W_])^.*$'
        // 3.无小写：大写、数字、特殊字符；
        const regex3 = '(?=.*[A-Z])(?=.*[0-9])(?=.*[\\W_])^.*$'
        // 4.无数字：大写、小写、特殊字符；
        const regex4 = '(?=.*[A-Z])(?=.*[a-z])(?=.*[\\W_])^.*$'
        // 5.无特殊字符：大写、小写、数字；
        const regex5 = '(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])^.*$'
        const reg = '(' + regex1 + ')|(' + regex2 + ')|(' + regex3 + ')|(' + regex4 + ')|(' + regex5 + ')'
        const pwdRegex = new RegExp(reg)
        if (pwdRegex.test(value)) {
          callback()
        } else {
          callback(new Error(this.$t('reaCommon.passwordSpecialVerify')))
        }
      }
      this.searchLabelWidth = this.language === 'en' ? '130px' : '100px'
      this.dialogRules = {
        phone: [
          { required: true, message: this.$t('frontUserAdd.phonePlaceholder'), trigger: 'blur' },
          { validator: formCheckPhone, trigger: 'blur' }
        ],
        admin_name: [
          { required: true, message: this.$t('frontUserAdd.namePlaceholder'), trigger: 'blur' }
        ],
        tenant_id: [
          { required: true, message: this.$t('frontUserAdd.belongTenantPlaceholder'), trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'blur' }
        ],
        position: [
          { required: true, message: this.$t('frontUserAdd.positionPlaceholder'), trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: this.$t('backendUserAdd.pleaseConfirmPassword'), trigger: 'blur' },
          { required: true, validator: verifyLength, trigger: 'blur' },
          { required: true, validator: equalToPassword, trigger: 'blur' },
          { required: true, validator: verifySpecialCode, trigger: 'blur' }

        ],
        password: [
          { required: true, message: this.$t('login.pleaseEnterPassword'), trigger: 'blur' },
          { required: true, validator: verifyLength, trigger: 'blur' },
          { required: true, validator: verifySpecialCode, trigger: 'blur' }

        ],
        role_ids: [
          { type: 'array', required: true, message: this.$t('backendUserAdd.moreOneRole'), trigger: 'blur' }
        ]
      }
      setTimeout(() => {
        if (this.$refs['dialogForm']) {
          this.$refs['dialogForm'].clearValidate()
        }
      })
    }

  },
  created() {
    this.searchLabelWidth = this.language === 'en' ? '130px' : '100px'
    this.operatesProps.width = this.language === 'en' ? '290px' : '250px'
    // 非超级管理员不能看到品牌方筛选
    if (!this.userInfo.is_super) {
      this.formData.splice(3, 1)
      // this.accountBackendHeader.splice(3, 1)
      this.accountBackendHeader.map((v, index) => {
        if (v.labelKey === 'tenant') {
          this.accountBackendHeader.splice(index, 1)
        }
      })
      this.formData.map((v, index) => {
        this.optionsIndexObj[v.field] = index
      })
    } else {
      this.formData.map((v, index) => {
        this.optionsIndexObj[v.field] = index
      })
      this.getTenantAll()
    }
    // this.getAdminRoleAll()
  },
  methods: {
    selectChangeHandler(e) {
      if (e.name == 'tenant_id') {
        this.queryParam.bu_ids = []
        this.getBuList(e.value, 100)
      }
    },
    /**
     * 锁定/解锁
     * */
    unlockFn(row) {
      if (row.loading) {
        return
      }
      {
        row.loading = true
      }
      (row.freeze_status ? adminUnlock : adminLock)({
        id: row.id,
        status: row.status == 1 ? 0 : 1
      }).then((res) => {
        row.freeze_status = row.freeze_status == 1 ? 0 : 1
        this.$message.success(row.freeze_status == 1 ? this.$t('reaCommon.lockSuccess') : this.$t('reaCommon.unlockSuccess'))
      }).finally(() => {
        row.loading = false
      })
    },
    /**
     * 启用禁用
     * */
    enableFn(row) {
      if (row.loading) {
        return
      }
      row.loading = true
      adminDisable({ id: row.id, status: row.status == 1 ? 0 : 1, meta_store: 0 }).then((res) => {
        row.status = row.status == 1 ? 0 : 1
        this.$message.success(row.status == 1 ? this.$t('reaCommon.enableSuccess') : this.$t('reaCommon.disableSuccess'))
      }).finally(() => {
        row.loading = false
      })
    },
    /**
     * 获取角色列表
     * */
    getRoleList(val) {
      this.dialogForm.role_ids = []
      this.dialogForm.bu_ids = []
      this.getBuList(val)
      this.getAdminRoleAll(val)
    },
    /**
     * 获取bu列表
     * */
    getBuList(tenant_id, type) {
      if (!tenant_id) {
        this.buList = []
        return
      }
      const searchFormRef = this.$refs.searchForm
      const index = this.formData.findIndex(v => v.field == 'bu_ids')
      let queryItem = this.formData[index]
      if (type) {
        queryItem.attrs.loading = true
        if (searchFormRef) {
          searchFormRef.updateFormData(this.formData)
        }
      }
      buListAll({ tenant_id }).then((res) => {
        if (Array.isArray(res)) {
          res.map((v) => {
            v.value = v.id
            v.label = v.bu_name
            return v
          })
          this.buList = res
        }
        if (type) {
          queryItem.options = this.buList
          if (searchFormRef) {
            setTimeout(() => {
              queryItem.attrs.loading = false
              searchFormRef.updateFormData(this.formData)
            }, 800)
          }
          this.$forceUpdate()
        }
      })
    },
    getAdminRoleAll(tenant_id) {
      if (!tenant_id) {
        this.roleList = []
        return
      }
      adminRoleAll({ tenant_id }).then((res) => {
        if (Array.isArray(res)) {
          res.map((v) => {
            v.value = v.id
            v.label = v.role_name
          })
          this.roleList = res
        }
      })
    },
    /**
     * 排序
     * */
    sortChange(data) {

      if (data.order) {
        this.queryParam.order = data.order.substring(0, data.order.length - 6)
        this.queryParam.order_field = data.prop == 'tenant_name' ? 'tenant_id' : data.prop
      } else {
        this.queryParam.order_field = ''
        this.queryParam.order = ''
      }
      this.formHandler()
    },
    /**
     * 初始化密码
     * */
    initPassword() {
      adminReset({ id: this.selectAccount.id }).then((res) => {
        if (res) {
          this.disabledResetPassword = true
          this.$message.success(this.$t('reaCommon.resetPasswordSuccess'))
        }
      })
    },
    /**
     * 删除
     * */
    deleteThis(row) {
      if (row.loading) {
        return
      }
      row.loading = true
      adminDelete({ id: row.id, meta_store: 0 }).then((res) => {
        if (res) {
          this.$message.success(this.$t('reaCommon.deleteSuccess'))
          this.formHandler()
        }
      }).finally(() => {
        row.loading = false
      })
    },
    /**
     * 编辑
     * */
    editFn(selectAccount) {
      this.selectAccount = selectAccount
      this.type = 'edit'
      const tenant_id = this.selectAccount.tenant_id || ''
      this.getAdminRoleAll(tenant_id)
      this.getBuList(tenant_id)
      Object.keys(this.dialogForm).map((key) => {
        if (selectAccount[key]) {
          this.dialogForm[key] = selectAccount[key]
        }
      })
      this.dialogVisible = true
      this.disabledResetPassword = false
      if (this.$refs['dialogForm']) {
        this.$refs['dialogForm'].clearValidate()
      }
    },
    dialogClose() {
      this.dialogVisible = false
    },
    /**
     * 保存 创建/删除
     * */
    dialogConfirm() {
      if (this.type == 'edit') {
        this.dialogForm.id = this.selectAccount.id
      }
      const formData = JSON.parse(JSON.stringify(this.dialogForm))
      formData.meta_store = 0
      // console.log(formData, 222222)
      // formData.role_ids = formData.role_ids.join(',')
      this.$refs['dialogForm'].validate((valid) => {
        if (valid) {
          (this.type === 'create' ? adminCreate : adminEdit)(formData).then((res) => {
            if (res) {
              this.$message.success(this.$t('reaCommon.operationSuccess'))
              delete this.dialogForm.id
              this.dialogVisible = false
              this.formHandler()
            }
          })
        }
      })
    },
    dialogCancel() {
      this.dialogVisible = false
    },
    openDialog() {
      this.type = 'create'
      this.dialogForm = {
        bu_ids: [],
        phone: '',
        admin_name: '',
        tenant_id: this.userInfo.is_super ? '' : this.userInfo.tenant_id,
        status: 1,
        position: '',
        role_ids: [],
        password: 'Lm12345678',
        confirmPassword: 'Lm12345678'
      }
      this.getAdminRoleAll(this.dialogForm.tenant_id)
      this.getBuList(this.dialogForm.tenant_id)
      this.dialogVisible = true
      this.disabledResetPassword = false
      if (this.$refs['dialogForm']) {
        this.$refs['dialogForm'].clearValidate()
      }
    },
    formHandler() {
      if (Array.isArray(this.queryParam.openTime) && this.queryParam.openTime.length) {
        this.queryParam.start = this.queryParam.openTime[0]
        this.queryParam.end = this.queryParam.openTime[1]
      } else {
        this.queryParam.start = ''
        this.queryParam.end = ''
      }
      this.searchQuery()
    }
  }
}
</script>

<style scoped>
.table_box {
  margin-top: 23px;
  padding: 18px 25px 0;
}

.control_box {
  padding: 0 0 17px;
}

.dot {
  margin-right: 8px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #36C08E;
}

.dot.red {
  background-color: #E52828;
}
</style>
<style lang="scss">
.account_frontend_view {
  .el-button--primary.is-plain {
    background-color: white;
  }
}
</style>
