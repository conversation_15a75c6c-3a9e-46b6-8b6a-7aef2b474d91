<template>
  <div class="page_container account_frontend_view">
    <div class="table_box wbg" style="margin-top:0;">
      <div class="frontend_control_box control_box flex flex-y-center flex-x-start" style="font-size:16px;">
        <div>{{ $t('reaCommon.username') }}:{{ name }}</div>
        <div style="margin-left:100px;">{{ $t('reaCommon.phone') }}:{{ phone }}</div>
      </div>
      <div>
        <new-table
          :outer-height="270"
          :loading="loading"
          :table-header="deviceHeader"
          :table-data="dataSource"
          :pagination="pagination"
          @pageConfigHandler="pageHandler"
        />
      </div>
    </div>
  </div>
</template>
<script>

import NewTable from '@/components/Table'
import {deviceHeader} from '@/utils/header'
import ListMixin from '@/mixins/List'
import {mapGetters} from 'vuex'

export default {
  name: 'account-frontend-device-list',
  components: {
    NewTable
  },
  mixins: [ListMixin],
  data() {
    return {
      phone: '',
      name: '',
      deviceHeader,
      queryParam: {
        user_id: this.$route.query.id
      },
      initQueryParam: {
        user_id: this.$route.query.id
      },
      dataSource: [],
      /* mixin里接口名称*/
      apiUrl: {
        list: 'deviceList'
      }
    }
  },
  computed: {
    ...mapGetters([
      'userInfo',
      'language'
    ])
  },
  created() {
  },
  methods: {
    formHandler() {
      if (Array.isArray(this.queryParam.openTime) && this.queryParam.openTime.length) {
        this.queryParam.start = this.queryParam.openTime[0]
        this.queryParam.end = this.queryParam.openTime[1]
      } else {
        this.queryParam.start = ''
        this.queryParam.end = ''
      }
      this.searchQuery()
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.table_box {
  margin-top: 23px;
  padding: 18px 25px 0;
}

.control_box {
  padding: 0 0 17px;
}

.reset_password ::v-deep .el-input__inner {
  border: none;
}

.frontend_control_box ::v-deep .el-button--default {
  border-color: $themeColor;
  color: $themeColor;
}

.dot {
  margin-right: 8px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #36C08E;
}

.dot.red {
  background-color: #E52828;
}
</style>
