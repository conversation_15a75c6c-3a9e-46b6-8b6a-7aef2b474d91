<template>
  <div>&nbsp;</div>
</template>

<script>
import {mapActions, mapState} from 'vuex'

// 自定义组件name
const url = location.href
const matchArr = url.match(/ifr-(.*)/g)
let componentName = 'ifr'
if (Array.isArray(matchArr) && matchArr.length) {
  const iframeNameArr = matchArr[0].split('?')
  componentName = iframeNameArr && iframeNameArr.length ? iframeNameArr[0] : componentName
}
export default {
  name: componentName,
  data() {
    const {name} = this.$route
    return {
      PAGE_ID: name
    }
  },
  computed: {
    ...mapState({
      iframes: state => state.iframes.iframeObj
    })
  },
  created() {
    const {meta} = this.$route
    if (!this.iframes[this.PAGE_ID]) {
      const iframeMes = {
        enable: true,
        id: this.PAGE_ID,
        name: meta.title,
        show: false,
        src: meta.url
      }
      this.createNewPage(iframeMes)
    }
    const obj = {id: this.PAGE_ID}
    this.createdPage(obj)
  },
  // 切换进入页面时显示对应 iframe
  activated() {
    this.activatedPage(this.PAGE_ID)
  },
  // 切换离开页面时隐藏对应 iframe
  deactivated() {
    this.deactivatedPage(this.PAGE_ID)
  },
  // 切换离开页面时销毁对应 iframe
  beforeDestroy() {
    this.deactivatedPage(this.PAGE_ID)
  },
  methods: {
    ...mapActions({
      createdPage: 'iframes/createdPage',
      activatedPage: 'iframes/activatedPage',
      deactivatedPage: 'iframes/deactivatedPage',
      beforeDestroyPage: 'iframes/beforeDestroyPage',
      changeSrc: 'iframes/changeSrc',
      createNewPage: 'iframes/createNewPage'
    })
  }
}
</script>
