<template>
  <div class="check__scene--box" ref="mianscroll">
    <div>
      <el-steps class="steps__box" :active="stepActive" simple v-if="modeType != 200">
        <el-step v-for="(item, index) in stepsList" :key="index">
          <template slot="icon">
            <i v-if="item.status == 1" class="el-icon-success success"></i>
            <i v-else class="num">{{ index + 1 }}</i>
          </template>
          <template slot="title">
            <div>{{ item.title }}</div>
          </template>
        </el-step>
      </el-steps>
      <div ref="stepsBox"></div>
      <!--第一步-->
      <div class="flex check__image--box" v-if="stepActive == 1">
        <div class="flex-1 install__device flex__direction">
          <div class="text">拍照时间：{{ deviceInfo.install_upload_time }}</div>
          <div>
            <el-image
                v-if="deviceInfo.install_device_photo"
                :preview-src-list="[deviceInfo.install_device_photo]"
                class="check__image"
                :src="deviceInfo.install_device_photo"
                fit="contain"
            ></el-image>
            <el-empty v-else :description="$t('reaCommon.noData')"></el-empty>
          </div>
          <div class="check__button frontend_control_box" v-if="deviceInfo.install_device_photo">
            <el-button size="small" @click="backCloseViewFun()">返回上一页</el-button>
            <!--提交过或者不合格或者已完成 则禁用-->
            <template v-if="deviceInfo.images && deviceInfo.images.length">
              <el-button size="small" type="danger" :disabled="angleButton" @click="checkPass(100)">不合格</el-button>
              <el-button size="small" type="success" :disabled="angleButton" @click="checkPass(200)">合格</el-button>
            </template>
            <!--已经审核过-->
            <!--            安装状态 0未安装 1待验收 2验收不合格 3验收合格 4验收已完成-->
            <el-button size="small" type="primary" plain
                       v-if="deviceInfo.install_status != 2 && (deviceInfo.inspect_step == 2 || isFinished)"
                       @click="nextFun(1)"
            >下一步
            </el-button>
          </div>
        </div>
        <div class="install__image">
          <div class="text">验收照：</div>
          <div v-if="deviceInfo.images && deviceInfo.images.length">
            <el-image
                :preview-src-list="getPrivewImages(deviceInfo.images, index)"
                v-for="(item, index) in deviceInfo.images"
                :key="index"
                class="check__image--item"
                :src="item+'?imageMogr2/thumbnail/!30p'"
                fit="contain"
            ></el-image>
          </div>
          <el-empty v-else :description="$t('reaCommon.noData')"></el-empty>
        </div>
      </div>
      <!--第二步-->
      <div class="flex check__image--box check__correction" v-show="stepActive == 2"
           :style="{height: 'calc(100vh - '+(modeType == 200 ? '0px' : '82px')+' - 20px - 110px)', 'margin-top': modeType == 200 ? '20px' : '0'}"
      >
        <div class="flex-1 install__device flex__direction">
          <div class="flex">
            <!--原图-->
            <div class="flex-1 mr10" ref="rectifyBox">
              <div id="haibao_box"></div>
              <div class="device__head">
                <i class="mr15">设备编号：{{ deviceInfo.device_code }}</i>
                <i>设备名称：{{ deviceInfo.device_name }}</i>
                <span class="clear__canves el-icon-delete" @click="clearCanvas"> 清空画布</span>
              </div>
              <div class="check__image--origin" style="overflow: scroll">
                <Konva v-if="imgSrc && deviceInfo.device_type == 3" ref="konva" :img-src="imgSrc"
                       :originalScale="originalScale"
                       @changeKonvaRange="handleSubmit(true)" @imageLoad="changeImageInfo"
                />
                <template v-else>
                  <el-image
                      style="width: 100%;height: 95%"
                      v-if="deviceInfo.device_photo"
                      :preview-src-list="[deviceInfo.device_photo]"
                      :src="deviceInfo.device_photo"
                      fit="contain"
                  ></el-image>
                  <el-empty v-else :description="$t('reaCommon.noData')"></el-empty>
                </template>
              </div>
            </div>
            <!--矫正图-->
            <div style="width: 40%">
              <div class="device__head"><i>{{ deviceInfo.device_type == 4 ? '去畸图' : '矫正图' }}</i></div>
              <div class="check__image--origin transform__image">
                <div v-if="deviceInfo.device_type == 3" class="center-content"
                     :style="{'transform-origin': '0% 0%', transform:`scale(${scale /100})`}"
                >
                  <div class="rectify__setting-image"
                       ref="rectify__image"
                       :style="{width:transformSizeX + 'px',height:transformSizeY + 'px'}"
                  >
                    <img v-if="matrix3d" crossOrigin="anonymous" ref="inputImage" style="transform-origin: 0 0;"
                         :style="{transform:`matrix3d(${matrix3d})`}" :src="imgSrc"
                    >
                  </div>
                </div>
                <el-image
                    v-else
                    style="width: 100%;height: 95%"
                    class="check__image"
                    :preview-src-list="[deviceInfo.undistorted_image]"
                    :src="deviceInfo.undistorted_image+'?imageMogr2/thumbnail/!30p'"
                    fit="contain"
                ></el-image>
              </div>
            </div>
          </div>

          <div class="check__button frontend_control_box">
            <el-button size="small" @click="backCloseViewFun()">返回上一页</el-button>
            <template v-if="modeType == 100">
              <el-button size="small" type="primary" plain @click="nextFun(0)">上一步</el-button>
            </template>
            <template v-if="modeType == 200">
              <el-tooltip :manual="true" v-model="isRemark" class="item" effect="dark" placement="bottom">
                <div slot="content" class="tooltip__list">
                  <div @click="finisheIssuedFun(item)" v-for="(item, index) in resolutionList" :key="index">
                    {{ item.key_label }}
                  </div>
                  <div class="off" @click.stop="isRemark = false">取消</div>
                </div>
                <el-button size="small" @click.stop="isRemark = !isRemark">标记</el-button>
              </el-tooltip>
            </template>
            <el-button v-if="isEdit" size="small" type="primary" :disabled="isFinished" @click="finishedFun(100)">完成
            </el-button>
          </div>
        </div>
        <div class="install__image">
          <div class="text">场景：</div>
          <div class="overflow">
            <div class="scene__image add__scene fl flex-center" v-if="!isFinished && isEdit && taskType != 100"
                 @click="createScene()"
            >
              <div>
                <span class="el-icon-plus"></span>
                <div>创建场景</div>
              </div>
            </div>
            <div @contextmenu.prevent="openContextMenu(index, item)" @click="choseScene(item, index)"
                 :class="{'active': item.active}"
                 v-for="(item, index) in deviceInfo.correction_parameters" :key="index" class="scene__image fl"
            >
              <div class="image__inner">
                <!--                <div class="center-content"-->
                <!--                     v-if="deviceInfo.device_photo && item.correction_parameter && item.correction_parameter.matrix_3d"-->
                <!--                     :style="{'transform-origin': '0% 0%', transform:`scale(${item.image_scale})`}"-->
                <!--                >-->
                <!--                  <div class="rectify__setting-image"-->
                <!--                       :style="{width:item.correction_parameter.image_w + 'px',height:item.correction_parameter.image_h + 'px'}"-->
                <!--                  >-->
                <!--                    <img ref="image" style="transform-origin: 0 0;"-->
                <!--                         :style="{transform:`matrix3d(${item.correction_parameter.matrix_3d})`}"-->
                <!--                         :src="deviceInfo.device_photo"-->
                <!--                    >-->
                <!--                  </div>-->
                <!--                </div>-->
                <el-image
                    style="width: 100%;height: 100%"
                    :src="item.scene_unit_image+'?imageMogr2/thumbnail/!30p'"
                    fit="contain"
                ></el-image>
                <div class="chose__icon">
                  <span class="el-icon-circle-check"></span>
                </div>
              </div>
              <div class="scene__name overflow">{{ item.scene_unit_name || '-' }}</div>
            </div>

          </div>
        </div>

        <div class="more__device" @click="openOtherDevice()"
             v-if="deviceInfo.other_device && deviceInfo.other_device.length"
        >
          <span>{{ isOpenOther ? '收起其他设备' : '展开其他设备' }} </span>
          <span :class="[!isOpenOther ? 'el-icon-arrow-down' : 'el-icon-arrow-up']"></span>
        </div>
      </div>
    </div>
    <!--更多设备-->
    <div v-if="isOpenOther && deviceInfo.other_device && deviceInfo.other_device.length" class="more__device--box flex">
      <div class="more__device--item" v-for="(item, index) in deviceInfo.other_device" :key="index">
        <div class="dev__num overflow flex-y-center">
          <div class="round" v-if="item.device_group_id == deviceInfo.device_group_id"></div>
          <div>设备号：{{ item.device_code }}</div>
        </div>
        <div class="dev__image">
          <el-image
              :preview-src-list="[item.device_photo_image]"
              class="img"
              :src="item.device_photo_image+'?imageMogr2/thumbnail/!30p'"
              fit="contain"
          ></el-image>
        </div>
      </div>
    </div>

    <!--配置信息-->
    <el-drawer
        :title="dialogForm.scene_type_code ? '编辑场景' : '创建场景'"
        :visible.sync="dialogVisible"
        direction="rtl"
        :wrapperClosable="false"
        :size="300"
        :modal="false"
        :style="{top: drawerTop+'px', width: '300px',left: 'auto','box-shadow': '-8px 0px 12px 0px rgba(0,0,0,0.15)'}"
        :before-close="handleClose"
    >
      <div class="dialog_form">
        <!--冰柜类型且是更换不可修改，货架类型可矫正其他不可修改-->
        <el-form :disabled="isFinished || taskType == 100 && modeType == 100" ref="dialogForm" size="small"
                 :model="dialogForm" :rules="dialogRules"
                 :label-width="dialogLabelWidth" class="demo-ruleForm mr10"
        >
          <el-form-item :label="$t('reaCommon.sceneType')" prop="scene_type_code">
            <el-select filterable clearable v-model="dialogForm.scene_type_code"
                       :placeholder="$t('reaCommon.pleaseEnter')"
                       @change="changeScene()"
            >
              <el-option-group
                  v-for="group in sceneList"
                  :key="group.scene_type_code"
                  :label="group.scene_type_name"
              >
                <el-option
                    v-for="item in group.sub"
                    :key="item.scene_type_code"
                    :label="item.scene_type_name"
                    :value="item.scene_type_code"
                >
                </el-option>
              </el-option-group>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('reaCommon.sceneName')" prop="scene_unit_name">
            <el-input v-model="dialogForm.scene_unit_name" clearable
                      :placeholder="$t('reaCommon.pleaseEnter')"
                      @blur="dialogForm.scene_unit_name = $event.target.value.trim()"
            />
          </el-form-item>
          <el-form-item :label="$t('reaCommon.sceneLocation')" prop="scene_location">
            <el-select v-model="dialogForm.scene_location" clearable filterable
                       :placeholder="$t('reaCommon.pleaseEnter')"
            >
              <el-option
                  v-for="item in downListObj.store_scene_location"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <!--冰柜场景-->
          <template v-if="isAssetFreezer">
            <el-form-item :label="$t('reaCommon.openDirection')">
              <el-select v-model="dialogForm.door_direction" clearable filterable
                         :placeholder="$t('reaCommon.pleaseEnter')"
              >
                <el-option
                    v-for="item in downListObj.door_direction"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('reaCommon.assetNumber')">
              <el-input v-model="dialogForm.asset_code" clearable
                        :placeholder="$t('reaCommon.pleaseEnter')"
                        @blur="dialogForm.asset_code = $event.target.value.trim()"
              />
            </el-form-item>
          </template>

        </el-form>
        <!--关联场景-->
        <div>
          <div class="relation__item--ul" v-if="belongStoreScene.length && belongStep == 1">
            <div class="relation__item" v-if="!item.hidden" v-for="(item, index) in belongStoreScene" :key="index"
                 @click="choseRelation(item)"
            >
              <div class="relation__scene--name">{{ item.scene_name }}</div>
              <div class="relation__scene--img">
                <div class="el-icon-circle-check check__svg" v-if="item.active"></div>
                <div class="flex">
                  <el-image
                      v-for="(it, ind) in item.scene_units" :key="ind"
                      class="face"
                      :src="it.scene_unit_image+'?imageMogr2/thumbnail/!30p'"
                      fit="contain"
                  ></el-image>
                </div>
              </div>
            </div>
          </div>
          <div
              class="rectify__setting"
              v-if="deviceInfo.device_type == 3 && (belongStoreScene.length && belongStep == 2 || !belongStoreScene.length && belongStep == 1)"
          >
            <div class="flex">
              <div class="flex-1"><b class="mr10">调整矫正参数</b>
              </div>
            </div>
            <div class="mt20">
              <div class="rectify__title ">固定宽高：
                <el-switch v-model="fixedWidth">
                </el-switch>
              </div>
            </div>
            <div class="mt20">
              <div class="rectify__title ">图片宽度：
                <el-input-number v-model="transformSizeX" class="mr10" style="width: 100px;" size="mini" :min="0"
                                 :max="2000" :step="2" :step-strictly="true" @change="handleSubmit"
                />
                px
              </div>
              <el-slider v-model="transformSizeX" :show-tooltip="false" :min="0" :max="2000" :step="1"
                         @input="handleSubmit"
              />
            </div>
            <div>
              <div class="rectify__title ">图片高度：
                <el-input-number v-model="transformSizeY" class="mr10" style="width: 100px;" size="mini" :min="0"
                                 :max="2000" :step="2" :step-strictly="true" @change="handleSubmit"
                />
                px
              </div>
              <el-slider v-model="transformSizeY" :show-tooltip="false" :min="0" :max="2000" :step="1"
                         @input="handleSubmit"
              />
            </div>
            <div>
              <div class="rectify__title">原始图缩放比例：
                <el-input-number v-model="originalScale" class="mr10" style="width: 100px;" size="mini" :min="0"
                                 :max="100" :step="1" :step-strictly="true"
                />
                %
              </div>
              <el-slider v-model="originalScale" :show-tooltip="false" :min="0" :max="100" :step="1"/>
            </div>
            <div>
              <div class="rectify__title">矫正图缩放比例：
                <el-input-number v-model="scale" class="mr10" style="width: 100px;" size="mini" :min="0" :max="100"
                                 :step="1" :step-strictly="true"
                />
                %
              </div>
              <el-slider v-model="scale" :show-tooltip="false" :min="0" :max="100" :step="1"/>
            </div>
          </div>
          <!--            <div class="rectify__title">矫正参数：</div>-->
          <!--            <el-input style="margin-top: 10px;"-->
          <!--                      type="textarea"-->
          <!--                      :autosize="{ minRows: 4, maxRows: 100 }"-->
          <!--                      v-model="homographyValue"-->
          <!--            ></el-input>-->
          <div class="flex-center button__save">
            <template v-if="belongStoreScene.length">
              <el-button type="primary" plain size="small" v-if="belongStep == 1" @click="changeSet(100)">下一步
              </el-button>
              <el-button type="primary" plain size="small" v-if="belongStep == 2" @click="changeSet(200)">上一步
              </el-button>
            </template>
            <template v-if="!(taskType == 100 && modeType == 100 && deviceInfo.device_type == 4)">
              <el-button type="primary"
                         v-if="isEdit && !isFinished && (belongStoreScene.length && belongStep == 2 || !belongStoreScene.length && belongStep == 1)"
                         size="small" @click="submitFun" :loading="isSubmit"
              >保 存
              </el-button>
            </template>
          </div>
        </div>


      </div>
    </el-drawer>
    <!--版本冲突-->
    <el-dialog
        center
        title="确认新版本生效时间"
        :visible.sync="visibleVersion"
        width="40%"
        class="ruleform__version"
    >
      <el-form ref="dialogForm1" label-position="left" size="medium" :disabled="true"
               label-width="110px" class="demo-ruleForm"
      >
        <div class="flex">
          <div class="flex-1">
            <el-form-item label="生效开始时间：">
              <el-date-picker
                  size="small"
                  class="w100p"
                  v-model="deviceInfo.new_relation_time && deviceInfo.new_relation_time.start_time"
                  type="datetime"
                  placeholder="请选择"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="生效结束时间：">
              <el-date-picker
                  size="small"
                  class="w100p"
                  v-model="deviceInfo.new_relation_time && deviceInfo.new_relation_time.end_time"
                  type="datetime"
                  placeholder="请选择"
              >
              </el-date-picker>
            </el-form-item>
          </div>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visibleVersion = false" size="small">取 消</el-button>
        <el-button type="primary" @click="submitVersion" size="small">确 定</el-button>
      </span>
    </el-dialog>
    <!--矫正图-->
    <canvas ref="outputCanvas" style="display: none"></canvas>
    <!--审核不合格原因-->
    <NopassReason ref="feedBackRef" @dialogConfirm="dialogConfirm"></NopassReason>
    <el-dialog width="60%" :close-on-press-escape="false" :close-on-click-modal="false"  title="识别结果" :visible.sync="reportVisible" center
               :before-close="reportClose"
    >
      <reportResult ref="reportDetail"  @report="sendReport"></reportResult>
      <span slot="footer" class="dialog-footer" v-if="reportResult">
        <el-button @click="reportClose">取 消</el-button>
        <el-button type="primary" @click="finishedFun(200)">确认完成</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Konva from '@/components/Konva/index.vue'
import ReportResult from '@/components/ReportResult/index.vue'
// import cv from 'opencv.js'
import EleDialog from '@/components/Dialog/index.vue'
import {
  installTaskSave,
  installTaskViewDevice,
  sceneCreate,
  sceneDelete,
  installCorrectFinish,
  storeGetScenes, aiotRecognition, aiotRecognitionResult
} from '@/api/install'
import { sceneTypeTree, backendMenu, resolutionDropdown } from '@/api/common'
import { mapGetters } from 'vuex'
import { uuid } from 'vue-uuid'
import { getPrefix, getTimestamp } from '@/utils/validate'
import NopassReason from './components/nopass-reason.vue'
import Event from '@/mixins/Event'
import { inspectionViewDevice, inspectionDeviceSave, inspectionMarkIssue } from '@/api/check'
import Config from '@/config'
import { deepJSON } from '@/utils/tools'

export default {
  name: 'check-scene',
  components: {
    ReportResult,
    Konva,
    NopassReason,
    EleDialog
    // Upload
  },
  mixins: [Event],
  data() {
    return {
      isEdit: true,
      // 定义matrix3d矫正参数
      matrix3d1: [
        0.953539, -0.0162668, 0, -9.8e-06,
        0.0055762, 0.68863, 0, -0.000304,
        0, 0, 1, 0,
        -1366.62, -76.6518, 0, 1
      ],
      imageUrl: 'https://iot-dev-1255412942.cos.ap-shanghai.myqcloud.com/camera2/photo-666C8FE0F7FEE87-1724642491-447.jpeg',
      isAssetFreezer: false,
      modeType: 100,
      isFinished: false,
      angleButton: false,
      deviceInfo: {},
      imgSrc: '',
      sceneList: [],
      isSubmit: false,
      polygonPoints: [],
      transformSizeX: 1000,
      transformSizeY: 1000,
      maxWidth: 3000,
      maxHeight: 3000,
      scale: 50,
      originalScale: 70,
      isOpenOther: false,

      loading: false,
      activeIndex: 0,
      imgList: [],
      matrix3d: '',

      fixedWidth: true,
      homographyValue: '',
      imageInfo: null,
      imageId: '',
      debug: false,
      submitLoading: false,

      visibleVersion: false,
      drawerTop: 0,
      dialogVisible: false,
      dialogForm: {
        edit_info: '',
        door_direction: 1,
        asset_code: '',
        scene_unit_name: '',
        scene_code: '',
        scene_type_code: '',
        scene_location: 1,
        store_code: '',
        device_photo_id: '',
        device_id: ''
      },
      dialogRules: {
        scene_unit_name: [
          { required: true, message: this.$t('reaCommon.pleaseEnter'), trigger: 'blur' }
        ],
        scene_type_code: [
          { required: true, message: this.$t('reaCommon.pleaseSelect'), trigger: 'blur' }
        ],
        scene_location: [
          { required: true, message: this.$t('reaCommon.pleaseSelect'), trigger: 'blur' }
        ]
      },
      dialogLabelWidth: '95px',
      stepActive: 1,
      stepsList: [
        {
          title: '角度验收',
          status: 0
        },
        {
          title: '矫正创建场景',
          status: 0
        }
      ],
      storeList: [],
      downListObj: {},
      belongStep: 1,
      belongStoreScene: [],
      storeScene: [],
      resolutionList: [],
      isRemark: false,
      taskType: '',
      reportVisible: false,
      // reportInfo: {},
      timer: null,
      reportResult: ''

    }
  },
  watch: {
    dialogVisible() {
      if (this.dialogVisible) {
        this.belongStep = 1
      }
      this.$refs.konva && this.$refs.konva.dialogConfig(this.dialogVisible)
    },
    '$i18n.locale'() {
    }
  },
  computed: {
    visitedViews() {
      return this.$store.state.tagsView.visitedViews
    },
    ...mapGetters([
      'userInfo',
      'language'
    ])
  },
  mounted() {
    this.getResolutionDropdown()
    const { is_edit, from } = this.$route.query
    if (is_edit) {
      this.isEdit = is_edit == 1 ? true : false
    }
    this.modeType = this.$route.query.type || 100 // 200=设备巡检 100=安装验证
    this.taskType = from // 更换=100
    this.getSceneList()
    this.getBackendMenu()
    this.$nextTick(() => {
      this.drawerTop = this.$refs.stepsBox.offsetTop
      this.getDetail()
    })
  },
  methods: {
    /**
     * 清空画布
     * */
    clearCanvas(){
      this.$refs.konva && this.$refs.konva.clearCanvas()
    },
    /**
     * 关闭识别结果弹窗
     * */
    reportClose(){
      // 关闭识别结果，重新更新设备巡检数据
      const type = this.$route.query.type
      if(type == 200){
        this.getDetail()
      }
      clearInterval(this.timer)
      this.reportVisible = false
    },
    getPrivewImages(imgList, index) {
      if (!imgList) {
        return
      }
      const tempImgList = [...imgList]// 所有图片地址
      if (index == 0) return tempImgList
      // 调整图片顺序，把当前图片放在第一位
      const start = tempImgList.splice(index)
      const remain = tempImgList.splice(0, index)
      return start.concat(remain)// 将当前图片调整成点击缩略图的那张图片
    },
    /**
     * 处理方式-下拉框
     * */
    getResolutionDropdown() {
      // inspection
      resolutionDropdown({ page_size: 10000, page_num: 1, page: 'inspection' }).then(res => {
        this.resolutionList = res.list
      })
    },
    /**
     * 设备标记-问题
     * */
    finisheIssuedFun(item) {
      let params = {
        resolution_type: item.key,
        device_photo_id: this.deviceInfo.device_photo_id
      }
      this.$confirm('是否确认标记?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        inspectionMarkIssue(params).then(res => {
          this.$message.success('操作成功')
          this.isRemark = false
          this.backCloseViewFun()
        })
      })
    },
    /**
     * 配置面板上下一步
     * type 100=下一步 200=上一步
     * */
    changeSet(type) {
      this.isOpenOther = false
      if (type == 100) {
        this.belongStep = 2
      }
      if (type == 200) {
        this.belongStep = 1
      }
    },
    /**
     * 选择关联场景
     * */
    choseRelation(item) {
      // 更换任务无法修改
      if (this.taskType == 100) {
        return
      }
      this.belongStoreScene = this.belongStoreScene.map(v => {
        if (v.scene_code == item.scene_code) {
          v.active = !v.active
        } else {
          v.active = false
        }
        return v
      })
      this.$forceUpdate()
    },
    /**
     * 获取售点下所有场景
     * */
    getAllScene() {
      let store_code = this.deviceInfo.store_code || this.$route.query.store_code
      storeGetScenes({ store_code }).then(res => {
        this.storeScene = res
      })
    },
    /**
     * 改变场景
     * init 是否是初次加载
     * */
    changeScene(init) {
      this.belongStep = 1
      this.isAssetFreezer = false
      let belongStoreScene = []
      this.sceneList.filter(v => {
        const findItem = v.sub.find(val => this.dialogForm.scene_type_code == val.scene_type_code)
        if (!init && findItem) {
          this.dialogForm.chose_scene_name = findItem.scene_type_name
          this.dialogForm.scene_unit_name = findItem.scene_type_name
        }
        // 选中冰柜场景
        if (findItem && findItem.parent_code == 1) {
          this.isAssetFreezer = true
        }
        // 筛选场景组
        if (findItem) {
          this.storeScene.filter(store_v => {
            if (store_v.scene_type_code == findItem.scene_type_code) {
              // 点击编辑时选中场景组
              let obj = deepJSON(store_v)
              if (init) {
                if (obj.scene_code == this.dialogForm.scene_code) {
                  obj.active = true
                }
              }
              belongStoreScene.push(obj)
            }
          })
        }
      })
      this.belongStoreScene = belongStoreScene
      this.$forceUpdate()
    },
    /**
     * 展开其他设备
     * */
    openOtherDevice() {
      this.isOpenOther = !this.isOpenOther
      if (this.isOpenOther) {
        setTimeout(() => {
          window.scrollTo({
            top: document.body.scrollHeight,
            behavior: 'smooth'
          })
        }, 100)
      }
    },
    /**
     * 提交版本参数
     * */
    submitVersion() {
      const { device_id, device_photo_id } = this.deviceInfo
      let data = []
      this.deviceInfo.correction_parameters.filter(v => {
        v.device_id = device_id
        v.device_photo_id = device_photo_id
        v.store_code = this.$route.query.store_code
        data.push(v)
      })
      if (!data.length) {
        this.$message.error('请至少添加1个场景')
        return
      }
      inspectionDeviceSave({ data, device_photo_id}).then(res => {
        // this.$message.success('操作成功')
        this.visibleVersion = false
        // 查看识别结果是否正确
        this.sendReport()
        // this.backCloseViewFun()
      })
    },
    /**
     * 安装完成
     * type 100=第一次完成按钮（识别结果之前） 200=第二次完成按钮（识别结果之后）
     * */
    finishedFun(type) {
      if (!this.deviceInfo.correction_parameters.length) {
        this.$message.error('请至少添加1个场景')
        return
      }
      if (this.modeType == 200) {
        // 巡检，并且查看识别结果保存
        if(type == 200){
          this.$message.success('操作成功')
          this.backCloseViewFun()
          return
        }
        this.$confirm('是否确认保存数据并查看识别结果?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.submitVersion()
        })
        // this.visibleVersion = true
        return
      }
      // 查看识别结果
      if(type == 100){
        this.sendReport()
        return
      }
      this.$confirm('是否确认完成?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        installCorrectFinish({ install_device_id: this.$route.query.install_device_id }).then(res => {
          this.$message.success('操作成功')
          this.backCloseViewFun()
        })
      })
    },
    /**
     * 送识别
     * */
    sendReport(){
      clearInterval(this.timer)
      this.reportResult = ''
      this.$refs.reportDetail && this.$refs.reportDetail.initParams('')
      this.reportVisible = true
      aiotRecognition({device_photo_id: this.deviceInfo.device_photo_id}).then(res => {
        this.timer = setInterval(() => {
          this.getReport()
        }, 1000)
      }).catch(e => {
        this.reportVisible = false
      })
    },
    /**
     * 查看识别结果
     * */
    getReport(){
      const { device_photo_id, device_id, device_type } = this.deviceInfo
      aiotRecognitionResult({ device_photo_id, device_id }).then(res => {
        if (Object.keys(res).length) {
          clearInterval(this.timer)
          this.reportResult = res
          res.scene_type = device_type == 4 ? 1 : 2
          this.$refs.reportDetail && this.$refs.reportDetail.initParams(res)
        }
      })
    },
    /**
     * 重置参数
     * */
    initLonvaDefSize() {
      this.transformSizeX = 1000
      this.transformSizeY = 1000
      this.maxWidth = 3000
      this.maxHeight = 3000
      this.scale = 50
      this.originalScale = 70
      this.homographyValue = ''
      this.$forceUpdate()
    },
    /**
     * 获取场景列表接口
     * */
    getSceneList() {
      sceneTypeTree().then(res => {
        this.sceneList = res
      })
    },
    /**
     * 获取下拉列表数据
     * */
    getBackendMenu() {
      backendMenu({
        fields: ['store_scene_location', 'door_direction'],
        tenant_id: this.userInfo.tenant_id
      }).then(res => {
        // this.sceneLocation = res.store_scene_location
        this.downListObj = res

      })
    },
    /**
     * 上一步下一步
     * type 1=下一步 0=上一步
     * */
    nextFun(type) {
      this.isOpenOther = false
      this.dialogVisible = false
      if (type == 1) {
        this.stepActive = this.stepActive + 1
      }
      if (type == 0) {
        this.stepActive = this.stepActive - 1
      }
      if (this.stepActive == 2) {
        // 清空选中状态
        this.deviceInfo.correction_parameters = this.deviceInfo.correction_parameters.map(v => {
          v.active = false
          return v
        })
        this.imageLoad()
      }
    },
    /**
     * 右击删除
     * */
    openContextMenu(index, item) {
      if (!this.isEdit) {
        return
      }
      if (this.isFinished) {
        return
      }
      const arrLen = this.deviceInfo.correction_parameters.length
      // 没有创建场景按钮至少保留1个场景
      if(!(!this.isFinished && this.isEdit && this.taskType != 100)){
        if(arrLen <= 1){
          this.$message.error('至少保留1个场景')
          return
        }
      }
      this.$confirm('是否确认删除此场景?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const { install_device_id, store_code, from} = this.$route.query
        // 真实删除(设备巡检不真实删除)
        if (item.relation_id && this.modeType != 200) {
          let params = {
            task_type: from == 100 ? 1 : 0,
            scene_unit_code: item.scene_unit_code,
            relation_id: item.relation_id,
            install_device_id, // 设备安装记录id，巡检时不用传
            store_code // 售点编号
          }
          sceneDelete(params).then(res => {
            this.getDetail()
            this.$message.success(this.$t('reaCommon.operationSuccess'))
          })
        } else {
          // 重置绘图信息
          if (this.dialogForm && this.dialogForm.temp_relation_id == item.temp_relation_id) {
            this.imageLoad()
          }
          this.deviceInfo.correction_parameters.splice(index, 1)
          this.$message.success(this.$t('reaCommon.operationSuccess'))
        }
      })
    },
    /**
     * 选择某个场景
     * */
    choseScene(item, index) {
      this.sceneClear()
      // console.log(item.correction_parameter)
      this.deviceInfo.correction_parameters = this.deviceInfo.correction_parameters.map((v, ind) => {
        v.active = false
        if (ind == index) {
          v.active = true
          // v.correction_parameter.mark_params[0] = v.correction_parameter.mark_params[0] - 500
          // v.correction_parameter.mark_params[6] = v.correction_parameter.mark_params[6] - 500
          // v.correction_parameter.rectify_parameter.size[0] = v.correction_parameter.rectify_parameter.size[0] + 300
          // v.correction_parameter.rectify_parameter.size[1] = v.correction_parameter.rectify_parameter.size[1] + 300
          const { correction_parameter, relation_id, temp_relation_id } = v
          if (correction_parameter) {
            const { mark_params, rectify_parameter, matrix_3d } = correction_parameter
            if (mark_params) {
              if (rectify_parameter && rectify_parameter.size) {
                this.transformSizeX = rectify_parameter.size[0]
                this.transformSizeY = rectify_parameter.size[1]
              }
              this.imageLoad(mark_params)
              this.dialogForm.edit_info = { matrix_3d }
            }
          }

          Object.keys(this.dialogForm).filter(key => {
            if (v[key]) {
              this.dialogForm[key] = v[key]
            }
          })
          this.dialogForm.device_photo_id = this.deviceInfo.device_photo_id
          this.dialogForm.relation_id = relation_id
          this.dialogForm.scene_unit_code = v.scene_unit_code
          this.dialogForm.chose_scene_name = v.scene_name
          if (this.modeType == 200) {
            // this.dialogForm.scene_unit_code = v.scene_unit_code
            if (temp_relation_id) {
              this.dialogForm.temp_relation_id = temp_relation_id
            }
          }
          this.changeScene(true)
          this.dialogVisible = true
        }
        return v
      })
    },
    /**
     * 创建场景
     * */
    createScene() {
      // 冰柜类型只能添加1个
      const { device_type, correction_parameters } = this.deviceInfo
      if (device_type == 4 && correction_parameters.length >= 1) {
        this.$message.error('冰柜类型设备只能添加1个场景')
        return
      }
      this.$refs.konva && this.$refs.konva.clearPoint()
      this.deviceInfo.correction_parameters = this.deviceInfo.correction_parameters.map(v => {
        v.active = false
        return v
      })
      this.imageLoad()
      this.sceneClear()
      this.initLonvaDefSize()
      this.$refs.konva && this.$refs.konva.dialogConfig(true)
    },
    sceneClear() {
      this.belongStoreScene = []
      this.belongStep = 1
      this.isAssetFreezer = false
      this.dialogVisible = true
      this.dialogForm = {
        edit_info: '',
        door_direction: 1,
        asset_code: '',
        scene_unit_name: '',
        scene_code: '',
        scene_type_code: '',
        scene_location: 1,
        store_code: '',
        device_photo_id: '',
        device_id: ''
      }
      if (this.$refs.dialogForm) {
        this.$refs.dialogForm.clearValidate()
      }
    },
    /**
     * type 100=不合格 200=合格
     * */
    checkPass(type) {
      if (type == 100) {
        this.$refs.feedBackRef.initParams({ device_code: this.deviceInfo.device_code })
        return
      }
      this.$confirm('是否确认角度合格?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.checkPassSave(1)
      })
    },
    /**
     * 不合格原因提交
     * */
    dialogConfirm(e) {
      this.checkPassSave(0, e)
    },
    /**
     * 检验是否合格提交数据
     * */
    checkPassSave(is_pass, reson) {
      let params = {
        note: '',
        not_pass_reason: 0,
        install_device_id: this.$route.query.install_device_id,
        is_pass
      }
      if (reson) {
        params.note = reson.content
        params.not_pass_reason = reson.type
      }
      installTaskSave(params).then(res => {
        this.$message.success('操作成功')
        this.angleButton = true
        if (is_pass == 0) {
          // this.backCloseViewFun()
          return
        }
        this.deviceInfo.inspect_step = 2
        this.$forceUpdate()
        this.nextFun(1)
      })
    },
    /**
     * 编辑矫正参数对应更新
     * */
    updateCorrection(correction_parameters, isinit) {
      // if (this.deviceInfo && this.deviceInfo.device_type != 3) {
      //   return correction_parameters
      // }
      if (correction_parameters && correction_parameters.length) {
        return correction_parameters.map(v => {
          // if (Object.keys(v.correction_parameter).length) {
          if (isinit) {
            v.correction_parameter = JSON.parse(v.correction_parameter)
          }
          const { rectify_parameter, homography, size } = v.correction_parameter || {}
          if (rectify_parameter && rectify_parameter.size) {
            const [w, h] = [rectify_parameter.size[0], rectify_parameter.size[1]]
            v.image_scale = Math.min(108 / w, 120 / h)
            v.correction_parameter.image_w = w
            v.correction_parameter.image_h = h
          } else if (homography) {
            const matrix = this.calculateOriginalPointsAndCssMatrix(homography, size)
            // let matrix_3d = await this.applyPerspectiveTransform(point, 200, null, size)
            // 兼容metastore之前的数据格式
            let formObj = {
              image_w: size[0],
              image_h: size[1],
              mark_params: matrix.points,
              matrix_3d: matrix.cssMatrix,
              rectify_parameter: {
                homography,
                size
              }
            }
            v.correction_parameter = formObj
          }
          // }
          return v
        })
      }
      return []
    },
    /**
     * 获取详情数据
     * */
    getDetail() {
      const { type, install_device_id, scene_unit_photo_id, end_time, time, relation_id } = this.$route.query
      let call = null
      if (type == 200) {
        let params = {
          scene_unit_photo_id
        }
        if (relation_id == 0) {
          params.scene_unit_photo_id = 0
          params.device_photo_id = scene_unit_photo_id
        }
        call = inspectionViewDevice(params)
      } else {
        call = installTaskViewDevice({ install_device_id })
      }
      call.then(res => {
        // res.can_inspect = false
        // TODO
        // res.undistorted_image = "https://iot-dev-1255412942.cos.ap-shanghai.myqcloud.com/camera2/photo-865447065273150-1730348347-34.jpg"
        // res.new_relation_time = {
        //   start_time: '2021-02-02 12:12:12',
        //   end_time: '2023-02-02 12:12:12'
        // }
        const { other_device, device_group_id, issue_record_id, can_inspect} = res
        if (other_device && other_device.length) {
          other_device.sort((a, b) => {
            if (a.device_group_id == device_group_id) return -1 // device_group_id 为 2 的排在前面
            if (b.device_group_id == device_group_id) return 1
            return 0 // 其他情况按原始顺序
          })
          res.other_device = other_device
        }
        // 1=smart tag；2=smart tag； 3=货架 4=冰柜
        end_time && (res.relation_end_time = end_time)
        time && (res.time = time)
        // 设备巡检进来则直接可编辑
        if (type == 200) {
          res.inspect_step = 2
          res.install_status = 1
        }
        res.correction_parameters = this.updateCorrection(res.correction_parameters, true)
        const { inspect_step, install_status, correction_parameters, device_photo } = res
        this.isFinished = inspect_step == 3 ? true : false
        this.imgSrc = device_photo
        // install_status 整个任务的安装验收状态 0默认 1待验收 2验收不合格 3验收合格
        // inspect_step 验收步骤 0待验收 1角度验收，2矫正参数配置，3提交完成
        this.stepsList[0].status = ((inspect_step == 2 || inspect_step == 3) ? 1 : 0) // 如果已经提交过安装验收状态带包处理过第一步
        this.stepsList[1].status = (correction_parameters && correction_parameters.length ? 1 : 0) // 如果提交过矫正参数代表已经提交过
        this.stepActive = inspect_step >= 2 ? 2 : 1
        this.deviceInfo = res
        // 角度验收按钮是否禁用 步骤是第二步骤|验收不合格|验收合格
        this.angleButton = (install_status == 2) || (install_status == 3) || (inspect_step == 2 || inspect_step == 3)
        if (this.stepActive == 2) {
          this.imageLoad()
        }
        // 如果是问题件或者被锁定，则不允许编辑只能查看
        if (issue_record_id || Object.keys(res).includes('can_inspect') && !can_inspect) {
          this.isFinished = true
        }

        this.getAllScene()
        this.$forceUpdate()
      })
    },
    /**
     * 初始化图片
     * */
    imageLoad(polygonPoints) {
      this.matrix3d = ''
      this.$forceUpdate()
      this.$nextTick(() => {
        this.$refs.konva && this.$refs.konva.init(polygonPoints || [])
        this.originalScale = 70
      })
    }
    ,
    changeImageInfo(info) {
      this.imageInfo = info
    }
    ,
    /**
     * 提交矫正参数
     * */
    submitFun() {
      this.$refs['dialogForm'].validate(async(valid) => {
        if (valid) {
          const { device_id, device_photo_id, device_type, undistorted_image } = this.deviceInfo
          const { store_code, install_device_id, type } = this.$route.query
          let params = this.dialogForm
          if (device_type == 3) {
            if (!this.homographyValue) {
              this.$message.error('矫正参数不能为空')
              return
            }
          }
          // 冰柜类型直接使用去畸图作为场景图
          if (device_type == 4) {
            if (!undistorted_image) {
              this.$message.error('设备没有去畸图')
              return
            }
            params.scene_unit_image = undistorted_image
          }
          if (this.isSubmit) {
            return
          }
          this.isSubmit = true
          // 不是冰柜类型的则清空相关数据
          if (!this.isAssetFreezer) {
            params.door_direction = 0
            params.asset_code = ''
          }
          params.device_id = device_id
          params.device_photo_id = device_photo_id
          params.store_code = store_code
          if (this.belongStoreScene.length) {
            const storeScene = this.belongStoreScene.find(v => v.active)
            params.scene_code = storeScene ? storeScene.scene_code : ''
          }
          if (type == 100) {
            params.install_device_id = install_device_id // 巡检时不用传
          }

          // 创建的时候不要传
          // params.scene_code = uuid.v1() + '_' + getTimestamp()
          if (device_type == 3) {
            params.correction_parameter = {
              matrix_3d: this.matrix3d,
              mark_params: this.polygonPoints,
              rectify_parameter: JSON.parse(this.homographyValue)
            }
            // 判断矫正参数是否发生变化，变化则重新生成图片，冰柜类型的默认取去畸图
            if (!params.edit_info || (params.edit_info && params.edit_info.matrix_3d && params.edit_info && params.edit_info.matrix_3d) != this.matrix3d) {
              const resultUrl = await this.uploadImageFn()
              if (resultUrl) {
                params.scene_unit_image = resultUrl
              }
            }
          }
          // 创建的时候默认塞个场景名称如果勾选了组，则不传、原本属于A组，去掉勾选
          // if (!(params.relation_id || params.temp_relation_id) || this.belongStoreScene.length) {
          //   if (!params.scene_code) {
          //     params.scene_name = params.chose_scene_name || params.scene_unit_name
          //   }
          // }
          params.scene_name = params.chose_scene_name || params.scene_unit_name
          if (this.modeType == 200) {
            // if (device_type == 3) {
            // 编辑时
            if (params.relation_id || params.temp_relation_id) {
              this.deviceInfo.correction_parameters = this.deviceInfo.correction_parameters.map(v => {
                if (v.relation_id && (v.relation_id == params.relation_id) || params.temp_relation_id && (v.temp_relation_id == params.temp_relation_id)) {
                  v = Object.assign(v, params)
                }
                return v
              })
            } else {
              // console.log(5555)
              // TODO 不考虑创建 创建时---生成一个临时的relation_id 用于后期编辑temp_relation_id
              this.deviceInfo.correction_parameters.unshift(Object.assign({ temp_relation_id: uuid.v1() + '_' + getTimestamp() }, params))
            }
            this.deviceInfo.correction_parameters = this.updateCorrection(this.deviceInfo.correction_parameters)
            // }
            // console.log(device_type, this.modeType, params, this.deviceInfo, 22222)

            this.dialogVisible = false
            setTimeout(() => {
              this.isSubmit = false
            }, 800)
            this.$message.success(this.$t('reaCommon.operationSuccess'))
            this.$forceUpdate()
            // 创建时---是否生成一个临时的relation_id 用于后期编辑temp_relation_id
            return
          }
          delete params.edit_info
          // if (this.isSubmit) {
          //   return
          // }
          // this.isSubmit = true
          sceneCreate(params).then(res => {
            this.dialogVisible = false
            this.getDetail()
            this.$message.success(this.$t('reaCommon.operationSuccess'))
          }).finally(() => {
            setTimeout(() => {
              this.isSubmit = false
            }, 800)
          })
        }
      })
    },
    /**
     * 上传矫正图片
     * */
    uploadImageFn() {
      return new Promise((resolve, reject) => {
        this.applyPerspectiveTransform(this.polygonPoints, 100, call => {
          const canvas = this.$refs.outputCanvas
          const Key = getPrefix('scene_', '.png')
          canvas.toBlob((blob) => {
            this._cos.putObject(
                {
                  Bucket: Config.cosConfig.Bucket,
                  Region: Config.cosConfig.Region,
                  Key,
                  StorageClass: 'STANDARD', // 上传模式
                  Body: blob,
                  onProgress: (progressData) => {
                  }
                },
                (err, data) => {
                  if (data) {
                    resolve(`http://${data.Location}`)
                  } else {
                    reject(err)
                  }
                }
            )
          }, 'image/png')
        })
      })
    },
    /**
     * 根据透视变换矩阵计算原图中的四个点坐标，并生成 matrix3d 样式
     * @param {Array} homography - 3x3 矫正矩阵，来源于参数 'homography'
     * @param {Array} size - 矫正后的图像尺寸 [width, height]
     * @returns {Object} 返回原图中的四个点坐标和对应的 matrix3d 数组
     */
    calculateOriginalPointsAndCssMatrix(homography, size) {
      const transformSizeX = size[0]
      const transformSizeY = size[1]

      // 矫正图像的四个角点
      const correctedPoints = [
        0, 0, // 左上角
        transformSizeX, 0, // 右上角
        transformSizeX, transformSizeY, // 右下角
        0, transformSizeY // 左下角
      ]

      // 将 homography 转换为 OpenCV Mat
      const homographyMat = cv.matFromArray(3, 3, cv.CV_64FC1, [
        homography[0][0], homography[0][1], homography[0][2],
        homography[1][0], homography[1][1], homography[1][2],
        homography[2][0], homography[2][1], homography[2][2]
      ])

      // 计算 homography 矩阵的逆矩阵，用于计算原图点
      const inverseHomography = new cv.Mat()
      cv.invert(homographyMat, inverseHomography, cv.DECOMP_SVD)

      // 将矫正图像的点转换为 OpenCV Mat（每个点的形式为 [x, y]）
      const srcPoints = cv.matFromArray(4, 1, cv.CV_32FC2, correctedPoints)

      // 应用逆透视变换，计算出原图中的点坐标
      const dstPoints = new cv.Mat()
      cv.perspectiveTransform(srcPoints, dstPoints, inverseHomography)

      // 读取结果的点坐标
      const points = []
      for (let i = 0; i < dstPoints.rows; i++) {
        points.push(dstPoints.data32F[i * 2])     // x 坐标
        points.push(dstPoints.data32F[i * 2 + 1]) // y 坐标
      }

      // 将 homography 矩阵转换为 matrix3d 数组（正向矩阵）
      const cssMatrix = this.homographyToCssMatrix(homography)

      // 清理内存
      homographyMat.delete()
      inverseHomography.delete()
      srcPoints.delete()
      dstPoints.delete()

      return {
        points,    // 返回原图中的四个点坐标
        cssMatrix: cssMatrix.length ? cssMatrix.join(',') : ''  // 返回对应的 matrix3d 数组
      }
    },
    /**
     * 将 homography 矩阵转换为 CSS 的 matrix3d 数组
     * @param {Array} homography - 3x3 矫正矩阵
     * @returns {Array} CSS 需要的 4x4 matrix3d 数组
     */
    homographyToCssMatrix(homography) {
      // 将 homography 矩阵转换为 4x4 matrix3d
      const cssMatrix = [
        homography[0][0], homography[1][0], 0, homography[2][0], // 第1列
        homography[0][1], homography[1][1], 0, homography[2][1], // 第2列
        0, 0, 1, 0,              // 第3列 (Z轴不做变换)
        homography[0][2], homography[1][2], 0, homography[2][2]  // 第4列
      ]
      return cssMatrix
    },

    /**
     * 转化矫正图参数
     * from 100=保存矫正图片
     * */
    async applyPerspectiveTransform(points, from, call) {
      let transformSizeX = this.transformSizeX
      let transformSizeY = this.transformSizeY
      // 定义标定图像的四个点
      const srcPoints = cv.matFromArray(4, 1, cv.CV_32FC2, points)
      // 定义透视变换后的四个点
      const dstPoints = cv.matFromArray(4, 1, cv.CV_32FC2, [0, 0, transformSizeX, 0, transformSizeX, transformSizeY, 0, transformSizeY])
      // 获取透视变换矩阵
      const transformMatrix = cv.getPerspectiveTransform(srcPoints, dstPoints)
      if (from == 100) {
        // 从图像元素读取图像并转换为Mat
        const imgElement = this.$refs.inputImage
        const srcMat = cv.imread(imgElement)
        // 定义目标Mat和变换的尺寸
        const dstMat = new cv.Mat()
        const dsize = new cv.Size(transformSizeX, transformSizeY)
        // 应用透视变换
        cv.warpPerspective(srcMat, dstMat, transformMatrix, dsize, cv.INTER_LINEAR, cv.BORDER_CONSTANT, new cv.Scalar())
        // 将结果显示在画布上
        cv.imshow(this.$refs.outputCanvas, dstMat)
        // 将画布内容转换为Blob对象
        call && call()
        // 释放内存
        srcPoints.delete()
        dstPoints.delete()
        transformMatrix.delete()
        srcMat.delete()
        dstMat.delete()
        return
      }
      const r = transformMatrix.data64F
      const cvMatrix = [
        r[0], r[1], 0, r[2],
        r[3], r[4], 0, r[5],
        0, 0, 1, 0,
        r[6], r[7], 0, r[8]]
      const cssMatrix = [
        cvMatrix[0], cvMatrix[4], cvMatrix[8], cvMatrix[12],
        cvMatrix[1], cvMatrix[5], cvMatrix[9], cvMatrix[13],
        cvMatrix[2], cvMatrix[6], cvMatrix[10], cvMatrix[14],
        cvMatrix[3], cvMatrix[7], cvMatrix[11], cvMatrix[15]
      ]
      // 释放内存
      transformMatrix.delete()
      srcPoints.delete()
      dstPoints.delete()
      this.matrix3dArr = JSON.parse(JSON.stringify(r))
      this.handleImageChange(cssMatrix)
    }
    ,
    /**
     * 点选后提交
     * */
    handleSubmit(changePoint) {
      if (this.$refs.konva) {
        const polygonPoints = this.$refs.konva.getPoints()
        this.polygonPoints = polygonPoints
        if (polygonPoints.length === 8) {
          if (changePoint === true && !this.fixedWidth) {
            let width = Math.abs(parseInt(polygonPoints[4] - polygonPoints[0]))
            let height = Math.abs(parseInt(polygonPoints[5] - polygonPoints[1]))
            this.transformSizeX = width < height ? width : height
            this.transformSizeY = width < height ? height : width
          }
          this.applyPerspectiveTransform(polygonPoints)
        }
      }
    }
    ,
    handleImageChange(cssMatrix) {
      this.matrix3d = cssMatrix.length ? cssMatrix.join(',') : ''
      this.handleSave()
    }
    ,
    handleSave() {
      if (this.matrix3dArr) {
        const arr = this.matrix3dArr
        const params = {
          'homography': [
            [arr[0], arr[1], arr[2]],
            [arr[3], arr[4], arr[5]],
            [arr[6], arr[7], arr[8]]
          ],
          'size': [
            this.transformSizeX,
            this.transformSizeY
          ]
        }
        this.homographyValue = JSON.stringify(params)
      }
    }
    ,
    handleClose() {
      if (this.isFinished) {
        this.dialogVisible = false
        return
      }
      // this.$confirm('是否确认关闭?', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning'
      // }).then(() => {
      this.dialogVisible = false
      // this.deviceInfo.correction_parameters = this.deviceInfo.correction_parameters.map(v => {
      //   v.active = false
      //   return v
      // })
      // this.imageLoad()
      // })
    }
    ,
    dialogClose() {
      this.dialogVisible = false
    }

  }
}
</script>
<style>
.el-step__title {
  color: #fff !important;
}

.check__scene--box .el-step__icon.is-text {
  border: none;
  font-size: 20px;
  width: auto;
  height: auto;
}

.check__scene--box .el-drawer__header {
  margin-bottom: 10px !important;
}

.check__scene--box .el-steps--simple {
  background: #0D8CDD;
}

.check__scene--box .el-step__title.is-finish {
  color: #fff;
}

.check__scene--box .el-step.is-simple .el-step__arrow::before, .check__scene--box .el-step.is-simple .el-step__arrow::after {
  background: #fff;
}

</style>
<style lang="scss" scoped>
.clear__canves{
  font-size: 13px;
  color: #0A5FAC;
  position: absolute;
  bottom: -25px;
  right: 0;
  cursor: pointer;
  z-index: 22;
  padding: 5px 10px;
  margin-top: 10px;
}
.tooltip__list {
  & > div {
    border-bottom: 1px solid rgba(255, 255, 255, .5);
    font-size: 14px;
    text-align: center;
    height: 35px;
    line-height: 35px;
    padding: 0 30px;
    cursor: pointer;
  }

  .off {
    height: 40px;
    line-height: 40px;
    font-size: 16px;
  }
}

.check__scene--box {
  .button__save {
    padding: 10px 0;
  }

  .relation__item--ul {
    max-height: 40vh;
    overflow-y: scroll;
  }

  .relation__item {
    cursor: pointer;
    padding: 0 20px;
    margin-bottom: 10px;
    overflow-x: scroll;

    .relation__scene--name {
      color: #0D8CDD;
      font-size: 14px;
      margin-bottom: 8px;
    }

    .relation__scene--img {
      border-radius: 3px;
      padding: 5px 1px;
      background: rgba(50, 157, 255, 0.1);
      border: 1px dashed #0D8CDD;
      display: inline-block;
      position: relative;
      padding-left: 10px;
      //padding-bottom: 0;

      .check__svg {
        position: absolute;
        left: 0;
        top: 0;
        background: rgba(0, 0, 0, .5);
        color: #00BAF7;
        z-index: 22;
        width: 100%;
        height: 100%;
        text-align: center;
        font-size: 46px;
        line-height: 120px
      }

      .face {
        width: 100px;
        height: 120px;
        display: block;
        margin-right: 10px;
        background: #F5F7FA;
      }
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .rectify__title {
    font-size: 14px;
  }

  .center-content {
    position: absolute;
    width: 100%;
    height: 100%;
    text-align: center;
    vertical-align: middle;
  }

  .rectify__setting {
    padding: 20px;
    padding-bottom: 0;
  }

  .rectify__setting-image {
    display: inline-block;
    transform-origin: 50% 50%;
    position: relative;
    z-index: 1;
    overflow: hidden;
  }

  .association__image {
    width: 80px;
    height: 53px;
    display: block;
    margin: 0 5px;
    margin-bottom: -10px;
  }

  .frontend_control_box .el-button--default {
    border-color: #0471D4;;
    color: #0471D4;
  }

  .steps__box {
    margin: 18px auto;
    width: 650px;

    .num {
      width: 20px;
      height: 20px;
      text-align: center;
      font-size: 12px;
      line-height: 20px;
      background: #fff;
      color: #0D8CDD;
      border-radius: 50%;
      display: block;
    }

    .success {
      color: #fff;
    }
  }

  .check__image--box {
    position: relative;
    height: calc(100vh - 82px - 20px - 110px);
    padding: 20px;
    background: #fff;
    border-radius: 4px;
    margin: 0 20px;

    .install__device {
      .check__button {
        text-align: center;
        margin-top: 30px;
      }

      .text {
        font-size: 14px;
        color: #333;
        margin-bottom: 15px;
      }

      .check__image {
        background: #EDEDED;
        height: 50vh;
        width: 100%;
      }

    }

    .install__image {
      margin-left: 20px;
      width: 35%;
      overflow-y: scroll;

      .text {
        font-size: 14px;
        color: #333;
        margin-bottom: 15px;
      }

      .check__image--item {
        width: 137px;
        height: 183px;
        margin-right: 10px;
        background: #EDEDED;
        margin-bottom: 5px;
      }
    }
  }

  .check__correction {
    .check__image--origin {
      height: 53vh;

      .image {
        width: 100%;
        height: 100%;
        display: block;
        background: #EDEDED;
      }

      &.transform__image {
        position: relative;
        overflow: auto;
      }
    }

    .device__head {
      background: #C5E4FF;
      font-size: 16px;
      color: #333333;
      text-align: center;
      padding: 10px;
      position: relative;
    }

    .install__image {
      width: 245px
    }

    .add__scene {
      border: 1px dashed #979797;
      width: 108px;
      height: 120px !important;
      font-size: 12px;
      color: #666666;
      text-align: center;
      cursor: pointer;
      background: #EDEDED;

      span {
        font-size: 20px;
        margin-bottom: 10px;
      }
    }

    .scene__image {
      width: 108px;
      height: 140px;
      margin-right: 10px;
      margin-bottom: 10px;
      cursor: pointer;

      .scene__name {
        font-size: 12px;
        line-height: 20px;
      }

      .img {
        width: 100%;
        height: 100%;
        display: block;
        background: #EDEDED;
      }

      &:nth-child(2n) {
        margin-right: 0;
      }

      .chose__icon {
        display: none;
      }

      .image__inner {
        position: relative;
        background: #f2f2f2;
        height: calc(100% - 20px);
      }

      &.active {
        .img {
          border: 4px solid #0A5FAC;
        }

        .chose__icon {
          display: block;
          position: absolute;
          left: 0;
          top: 0;
          z-index: 2;
          background: rgba(0, 0, 0, .4);
          width: 100%;
          height: 100%;
          text-align: center;
          line-height: 120px;

          span {
            font-size: 35px;
            color: #00DCFF;
          }
        }
      }


    }
  }

  .more__device {
    position: absolute;
    width: 100%;
    height: 30px;
    line-height: 30px;
    font-size: 14px;
    color: #0D8CDD;
    text-align: center;
    background: #EDEDED;
    border-radius: 52px 52px 0px 0px;
    left: 0;
    bottom: 0;
    font-weight: 600;

    span {
      cursor: pointer;
    }
  }

  .more__device--box {
    padding: 0 20px;
    margin: 20px;
    margin-top: 0;
    padding-top: 0px;
    background: #EDEDED;
    //height: 140px;
    overflow-x: scroll;

    .more__device--item {
      cursor: pointer;
      margin-right: 10px;

      .dev__num {
        font-size: 12px;
        color: #333333;

        .round {
          width: 6px;
          height: 6px;
          display: inline-block;
          border-radius: 50%;
          background: #0471D4;
          margin-right: 5px;
        }
      }

      .dev__image {
        width: 220px;
        height: 124px;
        background: #dadada;
        margin-top: 7px;
        margin-bottom: 15px;

        .img {
          width: 100%;
          height: 100%;
          display: block;
        }
      }
    }

  }
}

</style>
