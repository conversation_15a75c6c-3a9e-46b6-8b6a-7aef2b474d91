<template>
  <div>
    <ele-dialog width="56%" :append-to-body="true" title="不合格原因" :visible.sync="dialogFeedback"
                center
                @dialogCancel="dialogCancel" @dialogConfirm="dialogConfirm" @dialogClose="dialogCancel"
    >
      <template slot="content">
        <div class="feedback__dialog" v-loading="loading">
          <el-form :disabled="dialogForm.id ? true : false" ref="dialogForm" size="medium" :model="dialogForm"
                   :rules="dialogRules"
                   :label-width="dialogFormLabelWidth" class="demo-ruleForm change_password_form"
          >
            <el-form-item label="整改原因：" prop="type">
              <el-radio-group v-model="dialogForm.type">
                <el-radio :label="item.id" v-for="(item, index) in qestionTypeList" :key="index">
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="整改描述：" >
              <div class="editor__box">
                <Toolbar
                    style="border-bottom: 1px solid #dcdfe6;"
                    :editor="editor"
                    :default-config="toolbarConfig"
                    mode="simple"
                />
                <Editor
                    v-model="dialogForm.content"
                    style="height: 300px; overflow-y: hidden;"
                    :default-config="editorConfig"
                    mode="simple"
                    @onCreated="onCreated"
                />
              </div>
            </el-form-item>
          </el-form>
        </div>
      </template>
    </ele-dialog>
  </div>
</template>
<script>
import '@wangeditor/editor/dist/css/style.css'
import EleDialog from '@/components/Dialog'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import Config from '@/config'
import { formCheckPhone, getPrefix, checkEditor } from '@/utils/validate'
import { feedbackCreate, feedbackDetail, feedbackTypeList } from '@/api/brand'
import { mapGetters } from 'vuex'
import { nopassReason } from '@/api/common'

export default {
  name: 'feedBackUp',
  components: {
    EleDialog,
    Editor,
    Toolbar
  },
  props: {},
  computed: {
    ...mapGetters([
      'userInfo',
      'language'
    ])
  },
  data() {
    return {
      dialogForm: {
        id: '',
        tenant_id: '',
        type: '',
        content: '',
        username: '',
        phone: ''
      },
      dialogRules: {
        type: [{ required: true, trigger: 'blur', message: this.$t('reaCommon.pleaseSelect') }],
        content: [
          {
            required: true,
            message: this.$t('reaCommon.pleaseEnter'),
            trigger: 'blur'
          },
          {
            validator: checkEditor, trigger: 'blur'
          }
        ],
        phone: [
          {
            required: true,
            message: this.$t('reaCommon.pleaseEnter'),
            trigger: 'blur'
          }
          // { validator: formCheckPhone, trigger: 'blur' }
        ]
      },
      qestionTypeList: [],
      accept: 'image/png, image/jpeg, image/jpg',
      dialogFormLabelWidth: '100px',
      editor: null,
      toolbarConfig: {
        toolbarKeys: [
          'headerSelect',
          'blockquote',
          'bold',
          'underline',
          'italic',
          'through',
          'color',
          'bgColor',
          'fontSize',
          'lineHeight',
          'bulletedList',
          'numberedList',
          'todo',
          'justifyLeft',
          'justifyRight',
          'justifyCenter',
          'divider',
          'insertLink',
          'uploadImage',
          'undo',
          'redo'
        ]
      },
      editorConfig: {
        placeholder: '请输入您要反馈的详细内容，上传图片会更好',
        MENU_CONF: {
          // 插入图片
          uploadImage: {
            customUpload: async(file, insertFn) => {
              const resultUrl = await this.uploadImageFn(file)
              insertFn(resultUrl)
            }
            // base64LimitSize: 1024 * 1024 * 10 // 10MB 小于该值就插入 base64 格式（而不上传），默认为 0
          }
        },
        autoFocus: false
      },
      dialogFeedback: false,
      loading: false
    }
  },
  mounted() {
    this.dialogFormLabelWidth = this.language === 'en' ? '180px' : '100px'
  },
  beforeDestroy() {
    this.editor && this.editor.destroy()
  },
  methods: {
    /**
     * 初始化编辑器
     * */
    onCreated(editor) {
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
      // if (this.type == 'add') {
      //   this.editor.enable()
      // } else {
      //   this.editor.disable()
      // }
    },
    /**
     * 编辑查看数据
     * */
    initParams(row) {
      this.dialogForm = {
        id: '',
        type: '',
        content: '',
        username: '',
        phone: ''
      }
      this.getQestionType(row && row.device_code)
      this.dialogFeedback = true
      this.$nextTick(() => {
        this.$refs.dialogForm && this.$refs.dialogForm.resetFields()
      })
      // if (row) {
      //   this.loading = true
      //   this.getDetail(row)
      //   setTimeout(() => {
      //     this.editor && this.editor.disable()
      //   }, 200)
      // }
    },
    getDetail(row) {
      feedbackDetail({ id: row.id }).then(res => {
        Object.keys(this.dialogForm).filter(v => {
          if (res[v]) {
            this.dialogForm[v] = res[v] || ''
          }
        })
        this.dialogForm.id = res.id
        this.$forceUpdate()
      }).finally(e => {
        setTimeout(() => {
          this.loading = false
        }, 500)
      })
    },
    /**
     * 获取问题类型列表
     * */
    getQestionType(device_code) {
      nopassReason({ device_code, store_code: this.$route.query.store_code }).then(res => {
        this.qestionTypeList = res.list || []
      })
    },
    /**
     * 创建问题
     * */
    dialogConfirm() {
      this.$refs['dialogForm'].validate((valid) => {
        if (valid) {
          this.$confirm('是否确认角度不合格?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$emit('dialogConfirm', this.dialogForm)
            this.dialogFeedback = false
          })
        }
      })
    },
    dialogCancel() {
      this.dialogFeedback = false
      setTimeout(() => {
        this.$emit('dialogCancel', false)
      }, 100)
    },
    /**
     * 上传图片
     * */
    uploadImageFn(res) {
      return new Promise((resolve, reject) => {
        const fileInfo = res
        if ((fileInfo.size / 1024 / 1024 > 512)) {
          this.$message.error(this.$t('reaCommon.fileSize512M'))
          res.onError(this.$t('reaCommon.fileSize512M'))
          return
        }
        const fileType = fileInfo.name.split('.').reverse()[0]
        if (!(fileType && this.accept.includes(fileType)) || !fileType) {
          this.$message.error(this.$t('reaCommon.fileFormat'))
          res.onError(this.$t('reaCommon.fileFormat'))
          return
        }
        const Key = getPrefix('checkfail_', '.' + fileType, this.fileRoutePrefix)
        this._cos.putObject(
            {
              Bucket: Config.cosConfig.Bucket,
              Region: Config.cosConfig.Region,
              Key,
              StorageClass: 'STANDARD', // 上传模式
              Body: fileInfo,
              onProgress: (progressData) => {
              }
            },
            (err, data) => {
              if (data) {
                resolve(`http://${data.Location}`)
              } else {
                reject(err)
              }
            }
        )
      })
    }

  }
}
</script>
<style>
.w-e-selected-image-container {
  width: 50%;
}
</style>
<style lang="scss" scoped>
.feedback__dialog {
  .remind {
    font-size: 12px;
    color: #1F69C4;
  }

  .editor__box {
    width: 85%;
    border: 1px solid #ccc;
  }
}

</style>
