<template>
  <div class="page_container account_frontend_view store__detail">
    <div class="flex">
      <div class="go__back" @click="backCloseViewFun()"><span class="el-icon-arrow-left"></span>返回</div>
      <div class="store__base flex-1 flex" v-if="storeBase.id">
        <div>{{ $t('reaCommon.storeCode') }}：<span>{{ storeBase.store_code || '-' }}</span></div>
        <div>{{ $t('reaCommon.storeName') }}：<span>{{ storeBase.store_name || '-' }}</span></div>
        <div>{{ $t('reaCommon.storeAddress') }}：<span>{{ storeBase.address || '-' }}</span></div>
        <div>已安装设备数：<span>{{ storeBase.device_count || '0' }}</span></div>
        <div>已覆盖场景数：<span>{{ storeBase.scene_count || '0' }}</span></div>
        <div
            v-if="$havePermission('0301', $route.meta.functionList) || $havePermission('0201', $route.meta.functionList)"
        >全店视频：<span
            class="cursor" @click="isVideo = true"
        >查看</span></div>
        <!--        <div>-->
        <!--          <div v-if="$havePermission('0301', $route.meta.functionList)">-->
        <!--          <span class="cursor el-icon-edit" @click="dialogVisible = true"> 编辑视频</span>-->
        <!--        </div>-->
      </div>
    </div>
    <div class="flex mt12 store__devlist--box" v-if="storeBase.id">
      <!--      <img class="task__status" src="@/assets/images/lock.png" v-if="!storeBase.can_inspect" />-->
      <div class="store_mes wbg wbg_shadow store__devlist">
        <div class="dev__item--tit flex-center">
          <div class="flex-1">
            设备列表
          </div>
          <div class="frontend_control_box">
            <el-button :disabled="isFinshedButton" type="primary" @click="checkEnd" size="small"
                       style="border-radius: 20px"
            >验收结束
            </el-button>
          </div>
        </div>
        <el-collapse v-model="activeNames" @change="handleChange">
          <el-collapse-item :name="key.toString()" v-for="(key, index) in Object.keys(groupObj).sort().reverse()"
                            :key="index"
          >
            <template slot="title">
              <div class="group__name">

                <div v-if="key == 0">
                  全店摄像头
                </div>
                <div v-else>
                  可口可乐冰柜({{ groupObj[key].index }})
                </div>
                <!--                <div>待验收数量：2</div>-->
              </div>
            </template>
            <el-table
                :row-class-name="getRowClassName"
                v-if="groupObj[key].list.length"
                min-height="300px"
                :data="groupObj[key].list"
                style="width: 100%;margin-bottom: 10px"
            >
              <el-table-column
                  type="index"
                  :label="$t('reaCommon.serialNumber')"
                  width="70"
              >
              </el-table-column>
              <el-table-column
                  width="240"
                  :label="$t('reaCommon.deviceCode')"
                  prop="device_code"
              >
                <template slot-scope="{row}">
                  {{ row.device_code }}
                  <div>
                    <el-tag v-if="row.old_device_code" type="info" size="small">已移除设备：{{
                        row.old_device_code
                      }}
                    </el-tag>
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                  :label="$t('reaCommon.deviceName')"
                  prop="device_name"
              >
              </el-table-column>
              <el-table-column
                  :label="$t('reaCommon.assetNumber')"
                  prop="asset_code"
              >
                <template slot-scope="{row}">
                  {{ row.asset_code || '-' }}
                </template>
              </el-table-column>
              <el-table-column
                  :label="$t('reaCommon.sceneConfig')"
                  prop="name"
              >
                <template slot-scope="{row}">
                  {{ row.scene_unit_count || 0 }}
                </template>
              </el-table-column>
              <!--待验收=1=整体未提交 验收合格=3=整体提交 角度不合格=2=验收不合格-->
              <el-table-column
                  label="验收状态"
                  prop="install_status_text"
              >
                <template slot-scope="{row}">
                  <el-tag v-if="row.install_status == 3" type="success">{{ row.install_status_text }}</el-tag>
                  <el-tag v-else-if="row.install_status == 2" type="danger">{{ row.install_status_text }}</el-tag>
                  <el-tag v-else-if="row.install_status_text">{{ row.install_status_text }}</el-tag>
                  <template v-else>-</template>
                </template>
              </el-table-column>
              <el-table-column
                  label="上传时间"
                  prop="upload_time"
              >
                <template slot-scope="{row}">
                  {{ row.upload_time || '-' }}
                </template>
              </el-table-column>
              <el-table-column
                  :label="$t('reaCommon.operation')"
                  width="85"
                  prop="desc"
              >
                <template slot-scope="{row}">
                  <template v-if="row.id">
                    <el-link class="table__oprate--item" v-if="row.install_status == 3" type="success"
                             :underline="false"
                    >已完成
                    </el-link>
                    <el-link class="table__oprate--item" v-else type="primary" :underline="false"
                             @click="checkScene(row)"
                    >
                      待验收
                    </el-link>
                  </template>
                  <template v-else>-</template>
                </template>
              </el-table-column>
              <el-table-column type="expand" width="30">
                <template slot-scope="{row}">
                  <div class="mb10" v-if="row.scene_unit_parameters.length">
                    <div class="dev__scene--title">场景图</div>
                    <div class="flex-wrap--div">
                      <template>
                        <div v-for="(it, ind) in row.scene_unit_parameters" :key="ind" class="dev__scene"
                             style="background: rgba(50, 157, 255, 0.10)"
                        >
                          <div class="name overflow">{{ it.scene_name }}</div>
                          <div class="image__inner">
                            <el-image
                                :initial-index="ind"
                                style="width: 100%;height: 100%"
                                :preview-src-list="getPrivewImages(row.scene_unit_parameters_urls, ind)"
                                class="check__image--item"
                                :src="it.scene_unit_image+'?imageMogr2/thumbnail/!30p'"
                                fit="contain"
                            >
                              <div class="image__slot" slot="error">
                                <i class="el-icon-picture-outline"></i>
                              </div>
                            </el-image>
                          </div>
                        </div>
                      </template>
                    </div>
                  </div>
                  <div>
                    <template v-if="row.images && row.images.length">
                      <div class="dev__scene--title">验收图片（设备特写、场景、冰柜插座等）：</div>
                      <div class="flex-wrap--div">
                        <div v-for="(it, ind) in row.image_urls" :key="ind" class="install__image">
                          <el-image
                              :preview-src-list="getPrivewImages(row.image_urls, ind)"
                              class="face install__url"
                              :src="it+'?imageMogr2/thumbnail/!30p'"
                              fit="contain"
                          ></el-image>
                        </div>
                        <!--                        <div class="install__image" v-if="row.install_source_image">-->
                        <!--                          <el-image-->
                        <!--                              :preview-src-list="[row.install_source_image]"-->
                        <!--                              class="face install__url"-->
                        <!--                              :src="row.install_source_image+'?imageMogr2/thumbnail/!30p'"-->
                        <!--                              fit="contain"-->
                        <!--                          ></el-image>-->
                        <!--                        </div>-->
                      </div>
                    </template>
                    <el-empty v-else :description="$t('reaCommon.noData')"></el-empty>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <div class="scene__image flex-wrap--div">
              <div
                  v-if="groupObj[key] && groupObj[key].freezer && groupObj[key].freezer.asset_card_image && groupObj[key].freezer.asset_card_image.length"
              >
                <div class="dev__scene--title">资产编号：</div>
                <div class="flex-wrap--div">
                  <div v-for="(it, ind) in groupObj[key].freezer.asset_card_image" :key="ind" class="install__image">
                    <el-image
                        :preview-src-list="getPrivewImages(groupObj[key].freezer.asset_card_image, ind)"
                        class="face install__url"
                        :src="it+'?imageMogr2/thumbnail/!30p'"
                        fit="contain"
                    ></el-image>
                  </div>
                </div>
              </div>
              <div
                  v-if="groupObj[key] && groupObj[key].freezer && groupObj[key].freezer.complete_cooler_image && groupObj[key].freezer.complete_cooler_image.length"
              >
                <div class="dev__scene--title">冰柜整体图：</div>
                <div class="flex-wrap--div">
                  <div v-for="(it, ind) in groupObj[key].freezer.complete_cooler_image" :key="ind"
                       class="install__image"
                  >
                    <el-image
                        :preview-src-list="getPrivewImages(groupObj[key].freezer.complete_cooler_image, ind)"
                        class="face install__url"
                        :src="it+'?imageMogr2/thumbnail/!30p'"
                        fit="contain"
                    ></el-image>
                  </div>
                </div>
              </div>
              <div
                  v-if="groupObj[key] && groupObj[key].freezer && groupObj[key].freezer.working_image && groupObj[key].freezer.working_image.length"
              >
                <div class="dev__scene--title">工作流程照（热缩管、插座、接线）：</div>
                <div class="flex-wrap--div">
                  <div v-for="(it, ind) in groupObj[key].freezer.working_image" :key="ind" class="install__image">
                    <el-image
                        :preview-src-list="getPrivewImages(groupObj[key].freezer.working_image, ind)"
                        class="face install__url"
                        :src="it+'?imageMogr2/thumbnail/!30p'"
                        fit="contain"
                    ></el-image>
                  </div>
                </div>
              </div>
            </div>
            <template v-if="!groupObj[key].list.length">
              <template
                  v-if="!(groupObj[key].freezer && (groupObj[key].freezer.asset_card_image && groupObj[key].freezer.asset_card_image.length || groupObj[key].freezer && groupObj[key].freezer.complete_cooler_image && groupObj[key].freezer.complete_cooler_image.length || groupObj[key].freezer && groupObj[key].freezer.working_image && groupObj[key].freezer.working_image.length))"
              >
                <el-empty description="暂无数据"></el-empty>
              </template>
            </template>
          </el-collapse-item>
        </el-collapse>
      </div>
      <div class="flex-1 flex__direction" ref="viewerBox" v-if="false">
        <!--        <div class="store_mes wbg wbg_shadow model__viewer&#45;&#45;box direction__flex">-->
        <!--          <div class="dev__item&#45;&#45;tit flex-center">-->
        <!--            <div class="flex-1">验收视频</div>-->
        <!--          </div>-->
        <!--          <template v-if="deviceInfo.video">-->
        <!--            <video width="100%" height="85%" controls style="margin-top: 15px">-->
        <!--              <source :src="deviceInfo.video" type="video/mp4">-->
        <!--              您的浏览器不支持视频标签。-->
        <!--            </video>-->
        <!--          </template>-->
        <!--          <el-empty v-else :description="$t('reaCommon.noData')" :image-size="100"></el-empty>-->
        <!--        </div>-->
        <!--        <div class="store_mes wbg wbg_shadow model__drawing direction__flex" style="overflow-y: scroll">-->
        <!--          <div class="dev__item&#45;&#45;tit flex-center">-->
        <!--            <div class="flex-1">-->
        <!--              <span>验收图片（设备特写、场景、冰柜插座等）</span>-->
        <!--              <div class="time">上传时间：{{ deviceInfo.upload_time }}</div>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--          <div class="flex__direction mt10" ref="imageBox">-->
        <!--            <div class="install__image" v-if="deviceInfo.images && deviceInfo.images.length">-->
        <!--              <el-image-->
        <!--                  v-for="(item, index) in deviceInfo.images"-->
        <!--                  :key="index"-->
        <!--                  class="install__url"-->
        <!--                  :src="item"-->
        <!--                  :preview-src-list="deviceInfo.images"-->
        <!--                  fit="contain"-->
        <!--              ></el-image>-->
        <!--            </div>-->
        <!--            <el-empty v-else :description="$t('reaCommon.noData')" :image-size="100"></el-empty>-->
        <!--          </div>-->

      </div>
    </div>
    <el-empty v-else description="暂无数据"></el-empty>

    <!--查看视频-->
    <el-dialog
        @close="offVideo"
        title="查看视频"
        :visible.sync="isVideo"
        width="70%"
    >
      <div class="flex" style="height: 500px">
        <template v-if="Array.isArray(storeBase.video)">
          <div class="mr15 video__item" v-for="(item, index) in storeBase.video" :key="index">
            <span class="el-icon-circle-close delete" @click.stop="deleteVideo(index)"></span>
            <video :ref="'videoPlayer'+index" :src="item" controls="controls">
              您的浏览器不支持video
            </video>
          </div>
        </template>
        <!--        <div>-->
        <!--          <div v-if="$havePermission('0301', $route.meta.functionList)">-->
        <!--          <span class="cursor el-icon-edit" @click="dialogVisible = true"> 编辑视频</span>-->
        <!--        </div>-->
        <div class="mr15 video__item flex-center add" @click="uplaodFile">
          <div>
            <i class="el-icon-upload"></i>
            <div>上传视频</div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
         <el-button @click="offVideo">取 消</el-button>
          <el-button type="primary" @click="offVideo">确 定</el-button>
      </span>
    </el-dialog>

    <!--上传视频-->
    <ele-dialog :title="$t('reaCommon.uploadFile')" @dialogClose="dialogCancel"
                :visible.sync="dialogVisible"
                center @dialogCancel="dialogCancel" @dialogConfirm="sbumitVideoFun()"
    >

      <template slot="content">
        <div class="dialog_form">
          <el-form ref="dialogForm" :model="uploadDialogForm" :rules="dialogRules" size="medium" label-width="0"
                   class="demo-ruleForm"
          >
            <el-form-item label="" prop="file_url">
              <Upload ref="uploadImage1"
                      accept=".mp4,.mov,.avi," class="flex-1"
                      fileRoutePrefix="store_scene"
                      @uploadSuccess="uploadSuccess" :isProgress="false"
              ></Upload>
            </el-form-item>
          </el-form>
        </div>
      </template>
    </ele-dialog>
  </div>

</template>
<script>

import Upload from '@/components/Upload'
import SceneAnnotate from '@/components/SceneAnnotate'
import { storeModelEdit } from '@/api/store'
import { devWithScene } from '@/api/device'
import { storeSceneList, storeVersionList } from '@/api/list'
import EleDialog from '@/components/Dialog/index.vue'
import { exchangeTaskFinish, exchangeTaskOne, installTaskEdit, installTaskFinish, installTaskOne } from '@/api/install'
import Link from '@/layout/components/Sidebar/Link.vue'
import Event from '@/mixins/Event'

export default {
  name: 'check-detail',
  components: {
    Link,
    Upload,
    EleDialog,
    SceneAnnotate
  },
  mixins: [Event],
  data() {
    return {
      activeNames: [],
      isFinshedButton: true,
      isVideo: false,
      storeBase: {},
      taskDetail: {},
      deviceInfo: [],

      versionCode: '',
      versionList: [],
      tableData: [{
        id: '12987122',
        name: '好滋好味鸡蛋仔',
        category: '江浙小吃、小吃零食',
        desc: '荷兰优质淡奶，奶香浓而不腻',
        address: '上海市普陀区真北路',
        shop: '王小虎夫妻店',
        shopId: '10333'
      }, {
        id: '12987123',
        name: '好滋好味鸡蛋仔',
        category: '江浙小吃、小吃零食',
        desc: '荷兰优质淡奶，奶香浓而不腻',
        address: '上海市普陀区真北路',
        shop: '王小虎夫妻店',
        shopId: '10333'
      }, {
        id: '12987125',
        name: '好滋好味鸡蛋仔',
        category: '江浙小吃、小吃零食',
        desc: '荷兰优质淡奶，奶香浓而不腻',
        address: '上海市普陀区真北路',
        shop: '王小虎夫妻店',
        shopId: '10333'
      }],
      storeScene: [],
      query: {},
      storeDetail: {},
      imageBoxW: 0,
      imageBoxH: 0,
      sceneList: [],
      deviceCount: 0,
      planCount: 0,
      loading: false,
      uploadDialogForm: {
        file_url: '',
        type: 100 // 100=3d文件 200=2d图片
      },
      dialogRules: {
        file_url: [
          { required: true, message: '请上传文件', trigger: 'blur' }
        ]
      },
      dialogVisible: false,
      isSubmit: false,
      groupObj: {}
    }
  },
  watch: {
    '$route'(to, from) {
      const whiteList = ['/install/check-scene', '/install/check-replace-scene']
      if (whiteList.includes(from.path)) {
        this.getDetail()
      }
    }
  },
  computed: {},
  created() {
    this.query = this.$route.query
    this.versionCode = this.query.version
  },
  mounted() {
    // this.getStoreVersion()
    this.initDomSize()
    this.$nextTick(() => {
      this.getDetail()
      // this.getSceneList()
      // this.getDevWithScene()
    })
  },
  methods: {
    /**
     * 删除视频
     * */
    deleteVideo(index) {
      this.$confirm('是否确认删除该视频?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.storeBase.video.splice(index, 1)
        this.updateVideo()
      })
    },
    /**
     * 更新视频资源
     * */
    updateVideo(newUrl) {
      let params = {
        task_type: this.query.from == 100 ? 1 : 0,
        install_task_id: this.query.id,
        video: this.storeBase.video
      }
      if (newUrl) {
        params.video = params.video.concat(newUrl)
      }
      installTaskEdit(params).then(res => {
        this.dialogVisible = false
        this.$message.success(this.$t('reaCommon.operationSuccess'))
        // this.storeBase.video = imageSrc
        if (newUrl) {
          this.storeBase.video = params.video
        }
        this.$forceUpdate()
      }).finally(() => {
        setTimeout(() => {
          this.isSubmit = false
        }, 800)
      })
    },
    getRowClassName({ row }) {
      if (!row.id) {
        return 'special__row'
      }
      return ''
    },
    /**
     * 上传文件成功
     * */
    uploadSuccess(e) {
      // 临时url
      // this.uploadDialogForm.url = e.imageSrc
      this.$refs.dialogForm.clearValidate('file_url')
    },
    offVideo() {
      if (Array.isArray(this.storeBase.video)) {
        this.storeBase.video.filter((v, index) => {
          let refKey = `videoPlayer${index}`
          if (this.$refs[refKey]) {
            this.$refs[refKey][0].pause()
          }
        })
      } else {
        if (this.$refs.videoPlayer) {
          this.$refs.videoPlayer.pause()
        }
      }
      this.isVideo = false
    },
    getPrivewImages(imgList, index) {
      if (!imgList) {
        return
      }
      const tempImgList = [...imgList]// 所有图片地址
      if (index == 0) return tempImgList
      // 调整图片顺序，把当前图片放在第一位
      const start = tempImgList.splice(index)
      const remain = tempImgList.splice(0, index)
      return start.concat(remain)// 将当前图片调整成点击缩略图的那张图片
    },
    handleChange(val) {
      console.log(val)
    },
    /**
     * 验收结束
     * */
    checkEnd() {
      this.$confirm('是否确认验收该任务?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params = { install_task_id: this.query.id }
        let call = this.query.from == 100 ? exchangeTaskFinish(params) : installTaskFinish(params)
        call.then(res => {
          this.$message.success(this.$t('reaCommon.operationSuccess'))
          this.backCloseViewFun()
        })
      })
    },
    /**
     * 编辑矫正参数对应更新
     * */
    updateCorrection(correction_parameters, isinit) {
      if (correction_parameters && correction_parameters.length) {
        return correction_parameters.map(v => {
          if (v.correction_parameter && Object.keys(v.correction_parameter).length) {
            if (isinit) {
              v.correction_parameter = JSON.parse(v.correction_parameter)
            }
            const { rectify_parameter } = v.correction_parameter || {}
            if (rectify_parameter && rectify_parameter.size) {
              const [w, h] = [rectify_parameter.size[0], rectify_parameter.size[1]]
              v.image_scale = Math.min(140 / w, 180 / h)
              v.correction_parameter.image_w = w
              v.correction_parameter.image_h = h
            }
          }
          return v
        })
      }
      return []
    },
    /**
     * 获取任务详情
     * 1=smart tag;2=smart tag;3=货架; 4=冰柜
     * */
    getDetail() {
      this.loading = true
      let params = { install_task_id: this.query.id, task_type: this.query.from == 100 ? 1 : 0 }
      // from 100=更换详情
      let call = this.query.from == 100 ? exchangeTaskOne(params) : installTaskOne(params)
      call.then(res => {
        // res.summery_info.can_inspect = false
        // res.summery_info.video = ['https://iot-dev-1255412942.cos.ap-shanghai.myqcloud.com/REA_video_1730367720901_r6cwqs.mp4']
        let video = res.summery_info && res.summery_info.video
        if(!video){
          res.summery_info.video = []
        } else if(!Array.isArray(video)){
          res.summery_info.video = [video]
        }
        if (res.device_info) {
          res.device_info = res.device_info.map(v => {
            if (v.type == 3) {
              if (v.scene_unit_parameters) {
                v.scene_unit_parameters = this.updateCorrection(v.scene_unit_parameters, true)
              }
            }
            if (v.scene_unit_parameters && v.type == 4) {
              v.asset_code = v.scene_unit_parameters.map(item => item.asset_code).join('、')
            }
            if (v.scene_unit_parameters) {
              v.scene_unit_parameters_urls = v.scene_unit_parameters.map(v => {
                return v.scene_unit_image
              })
            }
            let images = v.images || []
            let install_source_image = v.install_source_image || []
            v.image_urls = images.concat(install_source_image)
            return v
          })
        }
        const { summery_info, device_info, freezer_arr } = res
        let freezerObj = {}
        let shelfObj = {
          0: {
            id: 0,
            list: []
          }
        }
        if (freezer_arr) {
          freezer_arr.filter((v, index) => {
            freezerObj[v.id] = {
              id: v.id,
              index: freezer_arr.length - index,
              freezer: v,
              list: []
            }
          })
        }
        device_info.filter(v => {
          const cooler_id = v.install_device_cooler_id || 0
          if (cooler_id == 0) {
            shelfObj[cooler_id].list.push(v)
          } else {
            if (freezerObj[cooler_id]) {
              freezerObj[cooler_id].list.push(v)
            }
          }
        })
        this.groupObj = Object.assign(freezerObj, shelfObj)
        this.activeNames = Object.keys(this.groupObj)
        this.deviceInfo = device_info
        this.storeBase = summery_info
        if (device_info && device_info.length) {
          const item = device_info.find(v => v.install_status != 3)
          this.isFinshedButton = item ? true : false
        }
        if (summery_info && (summery_info.install_status == 3 || Object.keys(summery_info).includes('can_inspect') && !summery_info.can_inspect)) {
          this.isFinshedButton = true
        }
      }).finally(e => {
        setTimeout(e => {
          this.loading = false
        }, 500)
      })
    },
    /**
     * 查看场景图
     * */
    checkScene(row) {
      let from = this.query.from
      let query = {
        type: 100,
        from,
        install_device_id: row.id,
        store_code: this.storeBase.store_code
      }
      // window.location.href = `${location.origin}/#/install/check-scene?type=${query.type}&install_device_id=${query.install_device_id}&store_code=${query.store_code}`
      let path = from ? '/install/check-replace-scene' : '/install/check-scene'
      this.$router.push({
        path: path,
        query
      })
    },
    /**
     * 点击上传文件按钮
     * */
    uplaodFile(type) {
      this.dialogVisible = true
      this.uploadDialogForm.type = type
      this.uploadDialogForm.file_url = ''
      // this.$refs.dialogForm && this.$refs.dialogForm.clearValidate('file_url');
      if (this.$refs.uploadImage1) {
        this.$refs.uploadImage1.clearFun()
      }
      if (this.$refs['dialogForm']) {
        this.$refs['dialogForm'].clearValidate()
      }
      this.$forceUpdate()
    },
    /**
     * 文件上传提交
     * */
    sbumitVideoFun() {
      const { type } = this.uploadDialogForm
      const imageSrc = this.$refs.uploadImage1.imageSrc
      if (imageSrc) {
        this.uploadDialogForm.file_url = imageSrc
        this.$forceUpdate()
      }
      let defVideo = this.storeBase.video || []
      this.$refs['dialogForm'].validate((valid) => {
        if (valid) {
          if (this.isSubmit) {
            return
          }
          this.isSubmit = true
          this.updateVideo(imageSrc)
        }
      })
    },
    /**
     * 取消编辑
     * */
    dialogCancel() {
      this.dialogVisible = false
    },
    /**
     * 切换售点版本
     * */
    changeVersion(e) {
      let str = `${this.query.id}_${this.versionCode}`
      localStorage.versionCode = str
      let url = location.href
      const versionIndex = location.href.indexOf('&version')
      if (versionIndex !== -1) {
        url = url.substring(0, versionIndex)
      }
      location.href = url + '&version=' + this.versionCode
    },
    /**
     * 获取售点版本号
     * */
    getStoreVersion() {
      // if(localStorage.versionCode){
      //   const localVersionCode = localStorage.versionCode
      //   const arr = localVersionCode.split('_')
      //   if(arr.length && arr[0] == this.query.id){
      //     this.versionCode = arr[1] * 1
      //   } else {
      //     localStorage.removeItem('versionCode')
      //   }
      // }
      storeVersionList({ store_id: this.query.id, page_num: -1 }).then(res => {
        const { id, version_code } = this.storeDetail
        if (id) {
          const item = res.list.find(v => v.version_code == version_code)
          item && (this.storeDetail.version_name = item.version_name)
        }
        this.versionList = res.list
        this.$forceUpdate()
      })
    },
    /**
     * 获取初始化尺寸
     * */
    initDomSize() {
      const imageBox = this.$refs.imageBox
      if (imageBox) {
        this.imageBoxW = imageBox.offsetWidth
        this.imageBoxH = imageBox.offsetHeight
      }
    },
    /**
     * 获取场景列表
     * */
    getSceneList() {
      storeSceneList({ store_id: this.query.id, version_code: this.versionCode, page_size: 999 }).then(res => {
        this.sceneList = res.list
        let arr = this.sceneList.filter(v => v.is_bind_plan == 1)
        this.planCount = arr.length
        setTimeout(() => {
          this.$refs.planeFigure && this.$refs.planeFigure.initParams({ allScene: arr, isLock: true })
        }, 600)
      })
    },

    /**
     * 设备组包含场景列表
     * */
    getDevWithScene() {
      devWithScene({
        store_id: this.query.id,
        with_scene: 1,
        version_code: this.versionCode,
        page_size: 999
      }).then(res => {
        this.deviceCount = res.count
        this.storeScene = res.list
      })
    }

  }
}
</script>

<style>
.special__row {
  color: #FF0000;
}

.store__detail .el-table::before {
//height: 0!important;
}

.store__detail .el-collapse-item__wrap {
  border-bottom: none !important;
}

.store__detail .el-collapse-item__content {
  padding-bottom: 0 !important;
}
</style>
<style lang="scss" scoped>
@import "~@/styles/variables.scss";

$sceneBoxH: calc(100vh - 264px);
$drawingH: calc((100vh - 264px) / 2 - 70px);

.task__status {
  width: 50px;
  height: 50px;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 2;
}

.video__item {
  width: 200px;
  height: 200px;
  border: 1px solid #d9d9d9;
  position: relative;

  .delete {
    font-size: 20px;
    color: #999;
    position: absolute;
    right: 4%;
    top: 4%;
    z-index: 2;
    cursor: pointer;
  }

  &.add {
    cursor: pointer;
    border: 1px dashed #d9d9d9;
    text-align: center;
    font-size: 14px;
    color: #c0c4cc;

    i {
      font-size: 67px;
    }
  }

  video {
    width: 100%;
    height: 100%;
  }
}

.remove__device {
  font-size: 12px;
  color: #999;
}

.version__text {
  font-size: 13px;
  color: #8492a6;
  margin-left: 10px;
}

.store__detail {
  .scene__image {
    width: 100%;

    & > div {
      background: rgba(50, 157, 255, 0.10);
      margin-right: 10px;
      padding-left: 10px;
      padding-top: 5px;
      margin-bottom: 10px;
      border-radius: 3px;
    }

    .install__url {
      background: #f2f2f2;
      width: 80px !important;
      height: 80px !important;
    }

    .install__image {

    }
  }

  .group__name {
    font-size: 14px;
    color: $themeColor;
  }

  .image__inner {
    position: relative;
    background: #f2f2f2;
    height: 180px;

    .center-content {
      position: absolute;
      width: 100%;
      height: 100%;
      text-align: center;
      vertical-align: middle;
    }

    .rectify__setting-image {
      display: inline-block;
      transform-origin: 50% 50%;
      position: relative;
      z-index: 1;
      overflow: hidden;
    }
  }


  .go__back {
    font-size: 14px;
    color: $themeColor;
    margin-right: 30px;
    cursor: pointer;
  }

  .store__base {
    font-size: 14px;
    color: #333;

    & > div {
      margin-right: 30px;

      &:last-child {
        margin-right: 0;
      }
    }

    span {
      color: $themeColor;
    }
  }

  .install__image {
    //grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    //grid-gap: 10px;
    display: flex;
    flex-wrap: wrap;

    .install__url {
      width: 138px;
      height: 190px;
      display: block;
      border: 1px solid #f2f2f2;
      border-radius: 4px;
      margin-right: 10px;
      margin-bottom: 10px;
    }
  }

  .store_mes {
    padding: 20px;


    .store_big_title {
      font-size: 18px;
      font-weight: 500;
      color: #262626;
      line-height: 28px;
      padding-bottom: 5px;
    }

    .store_mes_col {
      font-size: 14px;
      font-weight: 400;
      color: #737373;
      line-height: 22px;

      & > div {
        margin-right: 4%;
      }

      span {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #000;
        line-height: 22px;
      }
    }
  }

  .store__devlist--box {
    position: relative;
    height: $sceneBoxH;
  }

  .store__devlist {
    width: 100%;
    //width: 55%;
    //padding-top: 0;
    overflow-y: scroll;
    height: 100%;
    //margin-right: 12px;

    .el-collapse {
      border-top: none !important;
    }

    .dev__item--tit {
      margin-bottom: 10px;
    }
  }

  .dev__item--tit {
    font-size: 16px;
    color: #000000;
    width: 100%;
    font-weight: 500;

    span {
      margin-right: 5%;
    }

    .time {
      font-size: 14px;
      color: #999;
      margin-top: 5px;
    }
  }

  .dev__scene--title {
    font-size: 14px;
    margin-bottom: 10px;
    color: #333;
  }

  .dev__scene {
    width: 168px;
    border-radius: 4px;
    padding: 14px;
    margin-right: 10px;
    margin-bottom: 10px;

    .name {
      font-size: 14px;
      color: #000000;
      font-weight: 600;
      line-height: 19px;
      margin-bottom: 10px;
    }

    .face {
      width: 100%;
      height: 180px;
      display: block;
    }

    //&:nth-child(3n) {
    //  margin-right: 0;
    //}
  }

  .model__viewer--box {
    /*height: calc(50% - 7px);*/
    .model {
      background: #000;
      margin-top: 10px;
    }
  }

  .model__drawing {
    margin-top: 12px;
    /*height: calc(50% - 7px);*/
    margin-top: 12px;
    box-sizing: content-box;

    img {
      width: 100%;
      height: $drawingH;
      display: block;
      object-fit: contain;
    }
  }

}
</style>
<style lang="scss">
.store__devlist {
  .image__slot {
    text-align: center;
    padding-top: 60px;
  }

  .el-table th.el-table__cell {
    background-color: #FAFAFA;
    color: #000000;
    font-size: 14px;
    border-bottom: none;
  }

  .el-table__expand-icon > .el-icon {
    color: #0055B5;
  }

}
</style>
