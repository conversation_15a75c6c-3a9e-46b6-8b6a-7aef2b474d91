<template>
  <div class="page_container account_frontend_view">
    <div class="form_box">
      <search-form :value="queryParam" :form-data="formData" :label-width="searchLabelWidth"
                   @nodeConfigHandler="formHandler" @resetQueryParam="resetQueryParam"
      />
    </div>
    <div class="table_box wbg">
      <div class="control_box flex flex-y-center flex-x-end">
        <div class="frontend_control_box">
          <el-button
              v-if="$havePermission('0601',$route.meta.functionList)"
              icon="el-icon-upload2" size="medium" @click.native="uploadVisible = true"
          >{{ $t('reaCommon.dataImport') }}
          </el-button>
          <el-button
              v-if="$havePermission('0701',$route.meta.functionList)"
              icon="el-icon-download" size="medium" @click.native="openFilterPop"
          >{{ $t('reaCommon.dataExport') }}
          </el-button>
          <el-button v-if="$havePermission('0101',$route.meta.functionList)" style="margin-left:15px;" size="medium"
                     icon="el-icon-plus" type="primary"
                     @click.native="openView({}, 300)"
          >{{ $t('reaCommon.add') }}
          </el-button>
        </div>
      </div>
      <div>
        <new-table
            :loading="loading"
            :table-header="issueHeader"
            :table-data="dataSource"
            :operates="tableOperates"
            :operates-props="operatesProps"
            :pagination="pagination"
            @pageConfigHandler="pageHandler"
        >
          <template v-slot:check="{items, index, row}">
            <div>
              <!--查看0201+编辑0301-->
              <el-link v-if="$havePermission('0301',$route.meta.functionList)"
                       class="table__oprate--item"
                       type="primary"
                       :class="{'dis99': row.issue_status == 1}"
                       :disabled="row.issue_status == 1 ? true : false"
                       @click="openView(row, 100)" :underline="false"
              >
                修改处理意见
              </el-link>
              <!--              && row.issue_status == 1-->
              <el-link v-if="$havePermission('0201',$route.meta.functionList)"
                       class="table__oprate--item"
                       type="primary"
                       @click="openView(row, 200)" :underline="false"
              >
                查看
              </el-link>
              <!--              <el-link v-else-if="$havePermission('0301',$route.meta.functionList)" class="table__oprate&#45;&#45;item dis99" :disabled="false" type="primary" :underline="false">-->
              <!--                处理-->
              <!--              </el-link>-->
              <!--              <el-link v-if="$havePermission('0201',$route.meta.functionList) && row.install_status == 3"-->
              <!--                       class="table__oprate&#45;&#45;item" :class="{'dis99': row.install_status == 0}"-->
              <!--                       :disabled="row.install_status == 0 ? true : false" type="primary"-->
              <!--                       @click="openView(row)" :underline="false"-->
              <!--              >-->
              <!--                查看-->
              <!--              </el-link>-->
              <!--              <el-link v-else-if="$havePermission('0301',$route.meta.functionList) && row.install_status != 3"-->
              <!--                       class="table__oprate&#45;&#45;item" :class="{'dis99': row.install_status == 0}"-->
              <!--                       :disabled="row.install_status == 0 ? true : false" type="primary"-->
              <!--                       @click="openView(row)" :underline="false"-->
              <!--              >-->
              <!--                {{ $t('reaCommon.checkView') }}-->
              <!--              </el-link>-->
              <!--              <el-link v-else class="table__oprate&#45;&#45;item dis99" :disabled="false" type="primary" :underline="false">-->
              <!--                {{ row.install_status == 3 ? '查看' : $t('reaCommon.checkView') }}-->
              <!--              </el-link>-->
            </div>
          </template>
        </new-table>
      </div>
    </div>
    <!--新增-->
    <div class="dialog_container">
      <ele-dialog title="新建任务"
                  :visible.sync="dialogVisible" center @dialogCancel="dialogClose" @dialogConfirm="dialogConfirm"
                  @dialogClose="dialogClose"
      >
        <template slot="content">
          <div class="dialog_form">
            <el-form ref="dialogForm" size="medium" :model="dialogForm" :rules="dialogRules"
                     :label-width="dialogLabelWidth" class="demo-ruleForm"
            >
              <el-form-item label="售点编号" prop="store_code">
                <el-input v-model.trim="dialogForm.store_code"
                          :placeholder="$t('reaCommon.pleaseEnter')" clearable
                />
              </el-form-item>
              <el-form-item label="供应商" prop="supplier_id">
                <el-select v-model="dialogForm.supplier_id" class="check_item_select"
                           :placeholder="$t('reaCommon.pleaseSelect')" filterable clearable
                >
                  <el-option
                      v-for="item in supplierData"
                      :key="item.id"
                      :label="item.supplier_name"
                      :value="item.id"
                  />
                </el-select>
              </el-form-item>

            </el-form>
          </div>
        </template>
      </ele-dialog>
    </div>
    <!--修改处理意见-->
    <div class="dialog_container">

      <ele-dialog :title="!dialogHandleForm.id ? '新增处理意见' : '修改处理意见'"
                  :visible.sync="dialogHandleVisible" center @dialogCancel="dialogHandleVisible = false"
                  @dialogConfirm="dialogConfirm"
                  @dialogClose="dialogHandleVisible = false"
      >
        <template slot="content">
          <div class="dialog_form">
            <el-form ref="dialogHandleForm" size="medium" :disabled="dialogHandleForm.type == 200 ? true : false"
                     :model="dialogHandleForm" :rules="dialogHandleRules"
                     label-width="150px" class="demo-ruleForm"
            >
              <el-form-item label="设备编号：" prop="device_code" v-if="dialogHandleForm.type == 300">
                <el-input v-model.trim="dialogHandleForm.device_code"
                          :placeholder="$t('reaCommon.pleaseEnter')" clearable
                />
              </el-form-item>
              <el-form-item label="处理方式：" prop="resolution_type">
                <el-select v-model="dialogHandleForm.resolution_type" class="check_item_select"
                           :placeholder="$t('reaCommon.pleaseSelect')" filterable clearable
                >
                  <el-option
                      v-for="item in resolutionList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="备注：">
                <el-input type="textarea" :rows="2" v-model.trim="dialogHandleForm.solution_note"
                          :placeholder="$t('reaCommon.pleaseEnter')" clearable
                />
              </el-form-item>
              <!--              <el-form-item label="是否创建更换任务：" prop="store_code">-->
              <!--                <el-radio-group v-model="dialogHandleForm.replace_task">-->
              <!--                  <el-radio :label="1">是</el-radio>-->
              <!--                  <el-radio :label="0">否</el-radio>-->
              <!--                </el-radio-group>-->
              <!--              </el-form-item>-->

            </el-form>
          </div>
        </template>
      </ele-dialog>
    </div>
    <!--倒入-->
    <div v-if="uploadVisible">
      <import
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          :action="'import_issue'"
          :visible.sync="uploadVisible"
          center
          @refresh="formHandler"
          @dialogCancel="uploadCancel"
          @dialogConfirm="uploadConfirm"
          @dialogClose="uploadCancel"
      />
    </div>
    <!--下载-->
    <template>
      <div v-if="downloadFilterPop">
        <!--        field-list="fieldList"-->
        <filter-field :filter-visible.sync="downloadFilterPop" @dialogCancel="filterCancel"
                      @dialogConfirm="filterConfirm" @dialogClose="filterClose"
        />
      </div>
      <download-progress :progress="progress" :download-visible.sync="downloadVisible"
                         @downloadDialogCancel="processCancel" @downloadDialogConfirm="processConfirm"
                         @downloadDialogClose="processClose"
      />
    </template>

  </div>
</template>
<script>
import SearchForm from '@/components/SearchForm'
import NewTable from '@/components/Table'
import EleDialog from '@/components/Dialog'
import { installHeader, issueHeader, replaceHeader } from '@/utils/header'
import ListMixin from '@/mixins/List'
import { supplierList, tenantAll, configInstallDropdown, resolutionDropdown } from '@/api/common'
import { mapGetters } from 'vuex'
import { addDevice, delDeviceGroup, editDevice } from '@/api/device'
import { getStoreListSelect } from '@/api/common'
import { installList } from '@/api/list'
import Import from '@/components/Import/tenantSelectImport.vue'
import { installTaskCreate, issueRecordCreate, issueRecordEdit } from '@/api/install'
import Link from '@/layout/components/Sidebar/Link.vue'
import DownloadProgress from '@/components/DownloadProgress/index.vue'
import FilterField from '@/components/FilterField/index.vue'
import { exportStart } from '@/api/download-api'

export default {
  name: 'device-index',
  components: {
    FilterField, DownloadProgress,
    Link,
    Import,
    SearchForm,
    NewTable,
    EleDialog
  },
  mixins: [ListMixin],
  data() {
    return {
      resolutionList: [],
      openLocalSearch: true,
      supplierData: [],
      uploadVisible: false,
      storeList: [],
      brandList: [],
      dialogHandleVisible: false,
      dialogVisible: false,
      dialogForm: {
        store_code: '',
        supplier_id: ''
      },
      dialogHandleForm: {
        device_code: '',
        id: '',
        resolution_type: '',
        solution_note: ''
      },
      dialogHandleRules: {
        device_code: [
          {
            required: true,
            message: this.$t('reaCommon.pleaseEnter'),
            trigger: 'change'
          }
        ],
        resolution_type: [
          {
            required: true,
            message: this.$t('reaCommon.pleaseSelect'),
            trigger: 'change'
          }
        ]
      },
      dialogRules: {
        supplier_id: [
          {
            required: true,
            message: this.$t('reaCommon.pleaseSelect'),
            trigger: 'change'
          }
        ],
        store_code: [
          {
            required: true,
            message: this.$t('reaCommon.pleaseEnter'),
            trigger: 'change'
          }
        ]
      },
      searchLabelWidth: '100px',
      dialogLabelWidth: '95px',
      issueHeader,
      formData: [
        {
          name: '售点编号或名称',
          nameKey: 'storeSearchKey',
          type: 'input',
          field: 'store_search_key',
          attrs: {
            clearable: true
          }
        }, {
          name: '设备编号',
          nameKey: 'deviceNumber',
          type: 'input',
          field: 'device_code',
          attrs: {
            clearable: true
          }
        }, {
          name: '问题件状态',
          nameKey: 'issueStatus',
          type: 'select',
          field: 'issue_status',
          placeholder: '请选择',
          attrs: {
            clearable: true,
            filterable: true
          },
          options: []
        }, {
          name: '处理方式',
          nameKey: 'methodStatus',
          type: 'select',
          field: 'resolution_type',
          placeholder: '请选择',
          attrs: {
            'collapse-tags': true,
            // 'multiple-limit': 3,
            multiple: true,
            clearable: true,
            filterable: true
          },
          options: []
        }
        // {
        //   name: '任务完成时间',
        //   nameKey: 'taskFinishTime',
        //   type: 'daterange',
        //   field: 'openTime'
        // }
        // , {
        //   name: '供应商',
        //   nameKey: 'supplier',
        //   type: 'select',
        //   field: 'supplier_id',
        //   placeholder: '请选择供应商',
        //   attrs: {
        //     clearable: true
        //   },
        //   options: []
        // }
      ],
      queryParam: {
        issue_status: '',
        resolution_type: '',
        finished_time_start: '',
        finished_time_end: '',
        supplier_id: '',
        openTime: [],
        install_status: '',
        store_search_key: '',
        store_code: '',
        device_code: ''
      },
      initQueryParam: {
        issue_status: '',
        resolution_type: '',
        finished_time_start: '',
        finished_time_end: '',
        supplier_id: '',
        openTime: [],
        install_status: '',
        store_search_key: '',
        store_code: '',
        device_code: ''
      },
      dataSource: [],
      /* mixin里接口名称*/
      apiUrl: {
        export: 'issueDownload',
        list: 'issueRecordList'
      },
      /* 表格操作按钮*/
      tableOperates: [
        {
          label: '验收',
          labelKey: 'checkView',
          slot: 'check'
        }
      ],
      operatesProps: {
        width: '200px',
        label: '操作',
        labelKey: 'operation'
      }
    }
  },
  computed: {
    ...mapGetters([
      'userInfo',
      'language'
    ])
  },
  watch: {
    '$i18n.locale'() {
      this.searchLabelWidth = this.language === 'en' ? '140px' : '140px'
      this.dialogLabelWidth = this.language === 'en' ? '180px' : '95px'
    }
  },
  created() {
    this.searchLabelWidth = this.language === 'en' ? '140px' : '140px'
    this.dialogLabelWidth = this.language === 'en' ? '180px' : '95px'
    // 记录筛选条件
    this.setLocalQuery(100)
    this.getIssueStatusDropdown()
    this.getResolutionDropdown()
  },
  methods: {
    /**
     * 问题件状态-下拉框
     * */
    getIssueStatusDropdown() {
      const index = this.formData.findIndex(v => v.field == 'issue_status')
      this.formData[index].options = [
        {
          value: '0',
          label: '待处理'
        },
        {
          value: '1',
          label: '已完成'
        }
      ]
    },
    /**
     * 处理方式-下拉框
     * */
    getResolutionDropdown() {
      resolutionDropdown({ page_size: 10000, page_num: 1 }).then(res => {
        res.list = res.list.map((v) => {
          v.value = v.key
          v.label = v.key_label
          return v
        })
        this.resolutionList = res.list
        const index = this.formData.findIndex(v => v.field == 'resolution_type')
        this.formData[index].options = res.list
      })
    },
    /**
     * 获取供应商列表
     * */
    getSupplierList() {
      supplierList({ page_size: 10000, page_num: 1 }).then(res => {
        this.supplierData = res.list
        let arr = []
        res.list.map((v) => {
          arr.push({ value: v.id, label: v.supplier_name })
        })
        const index = this.formData.findIndex(v => v.field == 'supplier_id')
        this.formData[index].options = arr
      })
    },
    /**
     * 跳转到详情
     * type 100=编辑 200=查看 300=创建
     * */
    openView(row, type) {
      // this.type = row.id ? 'edit' : 'add'
      this.dialogHandleForm = {
        id: row.id || '',
        type,
        device_code: row.device_code || '',
        solution_note: row.solution_note || '',
        resolution_type: row.resolution_type || ''
      }
      // if (row.id) {
      //   this.dialogForm.id = row.id
      // } else {
      // }
      this.dialogHandleVisible = true
      this.$nextTick(() => {
        if (this.$refs['dialogHandleForm']) {
          this.$refs['dialogHandleForm'].clearValidate()
        }
      })
      // this.dialogHandleVisible = true
      // 存储筛选条件
      // this.openItem('/install/check-detail', { id: row.id })
    },
    /**
     * 售点列表
     */
    getStoreListSelect() {
      getStoreListSelect().then((res) => {
        if (Array.isArray(res)) {
          res.map((v) => {
            v.value = v.code
            v.label = v.name
            return v
          })
          this.storeList = res
          this.formData[0].options = res
        }
      })
    },
    uploadCancel() {
      this.uploadVisible = false
    },
    uploadConfirm() {
      this.uploadCancel()
    },
    dialogClose() {
      this.dialogVisible = false
    },
    /**
     * 创建安装任务
     * */
    dialogConfirm() {
      if (this.dialogHandleForm.type == 200) {
        this.dialogHandleVisible = false
        return
      }
      if (this.submitting) {
        return
      }
      this.$refs['dialogHandleForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          let call = this.dialogHandleForm.type == 300 ? issueRecordCreate(this.dialogHandleForm) : issueRecordEdit(this.dialogHandleForm)
          call.then((res) => {
            this.$message.success(this.$t('reaCommon.operationSuccess'))
            this.dialogHandleVisible = false
            this.formHandler()
          }).finally(() => {
            setTimeout(() => {
              this.submitting = false
            }, 200)
          })
        }
      })
    },
    openDialog(row) {
      this.type = row.id ? 'edit' : 'add'
      this.dialogForm = {
        store_id: row.store_id || '',
        device_group_code: row.device_group_code || '',
        device_group_name: row.device_group_name || '',
        asset_number: row.device_group_name || ''
      }
      if (row.id) {
        this.dialogForm.id = row.id
        this.editStoreId = row.store_id || ''
      } else {
        this.editStoreId = ''
      }
      console.log(this.editStoreId)
      this.dialogVisible = true
      this.$nextTick(() => {
        if (this.$refs['dialogForm']) {
          this.$refs['dialogForm'].clearValidate()
        }
      })
    },
    deleteThis(row) {
      if (row.loading) {
        return
      }
      row.loading = true
      delDeviceGroup({ id: row.id }).then((res) => {
        this.formHandler()
        this.$message.success(this.$t('reaCommon.deleteSuccess'))
      }).finally(() => {
        row.loading = false
      })
    },
    formHandler() {
      if (Array.isArray(this.queryParam.openTime) && this.queryParam.openTime.length) {
        this.queryParam.finished_time_start = this.queryParam.openTime[0]
        this.queryParam.finished_time_end = this.queryParam.openTime[1]
      } else {
        this.queryParam.finished_time_start = ''
        this.queryParam.finished_time_end = ''
      }
      this.searchQuery()
    }
  }
}
</script>

<style lang="scss" scoped>
.dis99 {
  color: #AEAEAE !important;
}

@import "~@/styles/variables.scss";

.table_box {
  margin-top: 16px;
  padding: 18px 25px 0;
}

.control_box {
  padding: 0 0 17px;
}

.reset_password ::v-deep .el-input__inner {
  border: none;
}

.frontend_control_box ::v-deep .el-button--default {
  border-color: $themeColor;
  color: $themeColor;
}

.dot {
  margin-right: 8px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #36C08E;
}

.dot.red {
  background-color: #E52828;
}

.danger {
  color: #dc3545;
}

.freezer_code {
  color: $themeColor;
  cursor: pointer;
}

.freezer_code:hover {
  color: #262626;
}

.target_dialog_container ::v-deep .el-dialog__body {
  padding-bottom: 0 !important;
  padding-top: 0 !important;
}
</style>
