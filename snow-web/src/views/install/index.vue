<template>
  <div class="page_container account_frontend_view">
    <div class="form_box">
      <search-form :value="queryParam" :form-data="formData" :label-width="searchLabelWidth"
                   @nodeConfigHandler="formHandler" @resetQueryParam="resetQueryParam"
      />
    </div>
    <div class="table_box wbg">
      <div class="control_box flex flex-y-center flex-x-end">
        <div class="frontend_control_box">
          <el-button
              v-if="$havePermission('0601',$route.meta.functionList)"
              icon="el-icon-upload2" size="medium" @click.native="uploadVisible = true"
          >{{ $t('reaCommon.dataImport') }}
          </el-button>
          <el-button v-if="$havePermission('0101',$route.meta.functionList)" style="margin-left:15px;" size="medium"
                     icon="el-icon-plus" type="primary"
                     @click.native="openDialog({})"
          >{{ $t('reaCommon.add') }}
          </el-button>
        </div>
      </div>
      <div>
        <new-table
            :loading="loading"
            :table-header="installHeader"
            :table-data="dataSource"
            :operates="tableOperates"
            :operates-props="operatesProps"
            :pagination="pagination"
            @pageConfigHandler="pageHandler"
        >
          <template v-slot:taskStatus="{items, index, row}">
            <el-tag
                :type="row.install_status == 0 ? 'info' : row.install_status == 1 ? 'danger' : row.install_status == 2 ? 'warning' : 'success'"
            >{{ row.install_status_label }}
            </el-tag>
          </template>
          <!--          <template v-slot:finishTime="{row}">-->
          <!--            {{ row.install_finish_time ? moment(row.install_finish_time * 1000).format('YYYY-MM-DD HH:mm:ss') : '-' }}-->
          <!--          </template>-->
          <template v-slot:check="{items, index, row}">
            <div>
              <!--查看0201+编辑0301-->
              <el-link v-if="$havePermission('0201',$route.meta.functionList) && row.install_status == 3"
                       class="table__oprate--item" :class="{'dis99': row.install_status == 0}"
                       :disabled="row.install_status == 0 ? true : false" type="primary"
                       @click="openView(row)" :underline="false"
              >
                查看
              </el-link>
              <el-link v-else-if="$havePermission('0301',$route.meta.functionList) && row.install_status != 3"
                       class="table__oprate--item" :class="{'dis99': row.install_status == 0}"
                       :disabled="row.install_status == 0 || !row.can_inspect ? true : false" type="primary"
                       @click="openView(row)" :underline="false"
              >
                {{ $t('reaCommon.checkView') }}<span v-if="!row.can_inspect" class="lock__status">（被锁定）</span>
              </el-link>
              <el-link v-else class="table__oprate--item dis99" :disabled="false" type="primary" :underline="false">
                {{row.install_status == 3 ? '查看' : $t('reaCommon.checkView')}}
              </el-link>
            </div>
          </template>
        </new-table>
      </div>
    </div>
    <!--新增-->
    <div class="dialog_container">
      <ele-dialog title="新建任务"
                  :visible.sync="dialogVisible" center @dialogCancel="dialogClose" @dialogConfirm="dialogConfirm"
                  @dialogClose="dialogClose"
      >
        <template slot="content">
          <div class="dialog_form">
            <el-form ref="dialogForm" size="medium" :model="dialogForm" :rules="dialogRules"
                     :label-width="dialogLabelWidth" class="demo-ruleForm"
            >
              <el-form-item label="售点编号" prop="store_code">
                <el-input v-model.trim="dialogForm.store_code"
                          :placeholder="$t('reaCommon.pleaseEnter')" clearable
                />
              </el-form-item>
              <el-form-item label="供应商" prop="supplier_id">
                <el-select v-model="dialogForm.supplier_id" class="check_item_select"
                           :placeholder="$t('reaCommon.pleaseSelect')" filterable clearable
                >
                  <el-option
                      v-for="item in supplierData"
                      :key="item.id"
                      :label="item.supplier_name"
                      :value="item.id"
                  />
                </el-select>
              </el-form-item>

            </el-form>
          </div>
        </template>
      </ele-dialog>
    </div>
    <!--倒入-->
    <div v-if="uploadVisible">
      <import
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          :action="'import_install_task'"
          :visible.sync="uploadVisible"
          center
          @refresh="formHandler"
          @dialogCancel="uploadCancel"
          @dialogConfirm="uploadConfirm"
          @dialogClose="uploadCancel"
      />
    </div>

  </div>
</template>
<script>
import SearchForm from '@/components/SearchForm'
import NewTable from '@/components/Table'
import EleDialog from '@/components/Dialog'
import { installHeader } from '@/utils/header'
import ListMixin from '@/mixins/List'
import { supplierList, tenantAll, configInstallDropdown } from '@/api/common'
import { mapGetters } from 'vuex'
import { addDevice, delDeviceGroup, editDevice } from '@/api/device'
import { getStoreListSelect } from '@/api/common'
import { installList } from '@/api/list'
import Import from '@/components/Import/tenantSelectImport.vue'
import { installTaskCreate } from '@/api/install'
import Link from '@/layout/components/Sidebar/Link.vue'
import moment from 'moment'

export default {
  name: 'device-index',
  components: {
    Link,
    Import,
    SearchForm,
    NewTable,
    EleDialog
  },
  mixins: [ListMixin],
  data() {
    return {
      openLocalSearch: true,
      supplierData: [],
      uploadVisible: false,
      storeList: [],
      brandList: [],
      dialogVisible: false,
      dialogForm: {
        store_code: '',
        supplier_id: ''
      },
      dialogRules: {
        supplier_id: [
          {
            required: true,
            message: this.$t('reaCommon.pleaseSelect'),
            trigger: 'change'
          }
        ],
        store_code: [
          {
            required: true,
            message: this.$t('reaCommon.pleaseEnter'),
            trigger: 'change'
          }
        ]
      },
      searchLabelWidth: '100px',
      dialogLabelWidth: '95px',
      installHeader,
      formData: [
        {
          name: '售点编号或名称',
          nameKey: 'storeSearchKey',
          type: 'input',
          field: 'store_search_key',
          attrs: {
            clearable: true
          }
        }, {
          name: '设备编号',
          nameKey: 'deviceNumber',
          type: 'input',
          field: 'device_code',
          attrs: {
            clearable: true
          }
        }, {
          name: '任务状态',
          nameKey: 'taskStatus',
          type: 'select',
          field: 'install_status',
          placeholder: '请选择',
          attrs: {
            clearable: true,
            filterable: true
          },
          options: []
        }, {
          name: '任务完成时间',
          nameKey: 'taskFinishTime',
          type: 'daterange',
          field: 'openTime'
        }, {
          name: '供应商',
          nameKey: 'supplier',
          type: 'select',
          field: 'supplier_id',
          placeholder: '请选择供应商',
          attrs: {
            clearable: true
          },
          options: []
        }
      ],
      queryParam: {
        finished_time_start: '',
        finished_time_end: '',
        supplier_id: '',
        openTime: [],
        install_status: '',
        store_search_key: '',
        store_code: '',
        device_code: ''
      },
      initQueryParam: {
        finished_time_start: '',
        finished_time_end: '',
        supplier_id: '',
        openTime: [],
        install_status: '',
        store_search_key: '',
        store_code: '',
        device_code: ''
      },
      dataSource: [],
      /* mixin里接口名称*/
      apiUrl: {
        list: 'installList'
      },
      /* 表格操作按钮*/
      tableOperates: [
        {
          label: '验收',
          labelKey: 'checkView',
          slot: 'check'
        }
      ],
      operatesProps: {
        fixed: 'right',
        width: '120px',
        label: '操作',
        labelKey: 'operation'
      },
      moment

    }
  },
  computed: {
    ...mapGetters([
      'userInfo',
      'language'
    ])
  },
  watch: {
    '$i18n.locale'() {
      this.searchLabelWidth = this.language === 'en' ? '140px' : '140px'
      this.dialogLabelWidth = this.language === 'en' ? '180px' : '95px'
    }
  },
  created() {
    this.searchLabelWidth = this.language === 'en' ? '140px' : '140px'
    this.dialogLabelWidth = this.language === 'en' ? '180px' : '95px'
    // 记录筛选条件
    this.setLocalQuery(100)
    // this.getStoreListSelect()
    this.getStatusDropdown()
    this.getSupplierList()
  },
  methods: {
    /**
     * 安装任务状态-下拉框
     * */
    getStatusDropdown() {
      configInstallDropdown({ page_size: 10000, page_num: 1 }).then(res => {
        res.list.map((v) => {
          v.value = v.key
          v.label = v.key_label
          return v
        })
        const index = this.formData.findIndex(v => v.field == 'install_status')
        this.formData[index].options = [{ value: '', label: '全部' }].concat(res.list)
      })
    },
    /**
     * 获取供应商列表
     * */
    getSupplierList() {
      supplierList({ page_size: 10000, page_num: 1 }).then(res => {
        this.supplierData = res.list
        let arr = []
        res.list.map((v) => {
          arr.push({ value: v.id, label: v.supplier_name })
        })
        const index = this.formData.findIndex(v => v.field == 'supplier_id')
        this.formData[index].options = arr
      })
    },
    /**
     * 跳转到详情
     * */
    openView(row) {
      // 存储筛选条件
      this.openItem('/install/check-detail', { id: row.id })
    },
    /**
     * 售点列表
     */
    getStoreListSelect() {
      getStoreListSelect().then((res) => {
        if (Array.isArray(res)) {
          res.map((v) => {
            v.value = v.code
            v.label = v.name
            return v
          })
          this.storeList = res
          this.formData[0].options = res
        }
      })
    },
    uploadCancel() {
      this.uploadVisible = false
    },
    uploadConfirm() {
      this.uploadCancel()
    },
    dialogClose() {
      this.dialogVisible = false
    },
    /**
     * 创建安装任务
     * */
    dialogConfirm() {
      if (this.submitting) {
        return
      }
      this.$refs['dialogForm'].validate((valid) => {
        if (valid) {
          this.submitting = true
          installTaskCreate(this.dialogForm).then((res) => {
            this.$message.success(this.$t('reaCommon.operationSuccess'))
            this.dialogVisible = false
            this.formHandler()
          }).finally(() => {
            setTimeout(() => {
              this.submitting = false
            }, 200)
          })
        }
      })
    },
    openDialog(row) {
      this.type = row.id ? 'edit' : 'add'
      this.dialogForm = {
        store_id: row.store_id || '',
        device_group_code: row.device_group_code || '',
        device_group_name: row.device_group_name || '',
        asset_number: row.device_group_name || ''
      }
      if (row.id) {
        this.dialogForm.id = row.id
        this.editStoreId = row.store_id || ''
      } else {
        this.editStoreId = ''
      }
      console.log(this.editStoreId)
      this.dialogVisible = true
      this.$nextTick(() => {
        if (this.$refs['dialogForm']) {
          this.$refs['dialogForm'].clearValidate()
        }
      })
    },
    deleteThis(row) {
      if (row.loading) {
        return
      }
      row.loading = true
      delDeviceGroup({ id: row.id }).then((res) => {
        this.formHandler()
        this.$message.success(this.$t('reaCommon.deleteSuccess'))
      }).finally(() => {
        row.loading = false
      })
    },
    formHandler() {
      if (Array.isArray(this.queryParam.openTime) && this.queryParam.openTime.length) {
        this.queryParam.finished_time_start = this.queryParam.openTime[0]
        this.queryParam.finished_time_end = this.queryParam.openTime[1]
      } else {
        this.queryParam.finished_time_start = ''
        this.queryParam.finished_time_end = ''
      }
      this.searchQuery()
    }
  }
}
</script>

<style lang="scss" scoped>
.lock__status{color: #8F0202;font-size: 10px}
.dis99 {
  color: #AEAEAE !important;
}

@import "~@/styles/variables.scss";

.table_box {
  margin-top: 16px;
  padding: 18px 25px 0;
}

.control_box {
  padding: 0 0 17px;
}

.reset_password ::v-deep .el-input__inner {
  border: none;
}

.frontend_control_box ::v-deep .el-button--default {
  border-color: $themeColor;
  color: $themeColor;
}

.dot {
  margin-right: 8px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #36C08E;
}

.dot.red {
  background-color: #E52828;
}

.danger {
  color: #dc3545;
}

.freezer_code {
  color: $themeColor;
  cursor: pointer;
}

.freezer_code:hover {
  color: #262626;
}

.target_dialog_container ::v-deep .el-dialog__body {
  padding-bottom: 0 !important;
  padding-top: 0 !important;
}
</style>
