<template>
  <div>
    <div class="home-user"> {{ $t('home.hello') }}，{{ userInfo.admin_name }}</div>
    <div class="page_container">
      <div class="form_box">
        <search-form :value="queryParam" :form-data="formData" :label-width="searchLabelWidth" @nodeConfigHandler="formHandler" @selectChangeHandler="selectChangeHandler" @resetQueryParam="resetQueryParam" />
      </div>
      <div class="cards-container" v-loading="loading">
        <div class="flex-y-center home__search">
          <div class="flex-1 tit">数据概览</div>
        </div>
        <div class="flex">
            <div class="cards__item" >
              <div class="text flex">
                <div class="flex-1">QC任务数</div>
              </div>
              <div class="count">{{taskAtatistics.total || '0'}}</div>
            </div>
            <div class="cards__item">
              <div class="text">合格数</div>
              <div class="count">{{taskAtatistics.pass_count || '0'}}</div>
            </div>
            <div class="cards__item">
              <div class="text">不合格数</div>
              <div class="count">{{taskAtatistics.no_pass_count || '0'}}</div>
            </div>
            <div class="cards__item">
              <div class="text">未审核数</div>
              <div class="count">{{taskAtatistics.wait_count || '0'}}</div>
            </div>
        </div>
        <div class="mt20">
          <pieChart ref="pieChart"  height="300px"/>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import SearchForm from '@/components/SearchForm'
import { mapGetters } from 'vuex'
import pieChart from './components/pieChart.vue'
import moment from 'moment'
import { indexTaskAtatistics } from '@/api/home'
export default {
  name: 'home-index',
  components: {
    SearchForm,
    pieChart
  },
  data() {
    return {
      taskAtatistics: {},
      searchLabelWidth: '100px',
      queryParam: {
        dateRange: [],
      },
      initQueryParam: {
        dateRange: [],
      },
      formData: [{
        name: '选择时间',
        nameKey: 'selectTime',
        'value-format': 'yyyy-MM-dd',
        type: 'daterange',
        field: 'dateRange',
      }],
      loading: false,
      info: {},
      nameList: {},
      topList: [],
      salesList: [],
      flowList: []
    }
  },
  computed: {
    ...mapGetters([
      'userInfo'
    ])
  },
  created() {
    this.queryParam.dateRange = [moment().subtract(6, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
    this.getSale()
  },
  methods: {
    formHandler() {
      this.getSale()
    },
    selectChangeHandler(selectObj) {
    },
    resetQueryParam() {
      this.queryParam = JSON.parse(JSON.stringify(this.initQueryParam))
    },
    getSale() {
      this.loading = true
      indexTaskAtatistics({ start: this.queryParam.dateRange[0], end: this.queryParam.dateRange[1] }).then(res => {
        const {pass_count, no_pass_count, wait_count} = res
        this.taskAtatistics = res
        this.$nextTick(() => {
          const arr = [
            { count: pass_count, name: '合格数' },
            { count: no_pass_count, name: '不合格数' },
            { count: wait_count, name: '未审核数' },
          ]
          this.$refs.pieChart.initChart(arr)
        })
      }).finally(() => {
        this.loading = false
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.home-user{
  font-size: 32px;
  font-weight: 600;
  margin: 20px;
}
.home__search{
  .tit{
    font-size: 18px;
    font-weight: 500;
  }
  margin-bottom: 15px;
}
.cards__item{
  background: #f4f9fd;
  border-radius: 8px;
  padding: 15px;
  width: 25%;
  font-weight: 500;
  .text{
    font-size: 14px;
    color: #9da2a9;
  }
  .count{
    font-size: 32px;
    margin-top: 10px;
  }
  margin-right: 15px;
  img{
    width: 66px;
    height: 66px;
  }
  &:nth-child(4n){
    margin-right: 0;
  }
}
.cards-container{
  background-color: #FFFFFF;
  border-radius: 8px;
  padding: 20px 15px;
  margin-top: 25px;
  //min-height: 60vh;
  .title{
    height: 45px;
    line-height: 45px;
    align-items: center;
    padding: 0 20px;
    font-size: 14px;
    &.border{
      border-bottom: 1px solid #f1f1f1;
      margin-bottom: 20px;
    }
  }
  .sales-bar-charts{
    padding: 0 10px;
  }
  .rank-item{
    padding: 0 20px;
    margin-bottom: 15px;
    &:nth-child(1) .rank, &:nth-child(2) .rank, &:nth-child(3) .rank{
      background: linear-gradient(22deg, #003997 0%, #4CD6FF 100%);
      color: #FFFFFF;
    }
    .rank{
      width: 20px;
      height: 20px;
      line-height: 20px;
      background-color: #F0F2F5;
      border-radius: 50%;
      text-align: center;
      font-size: 12px;
      font-weight: 500;
      margin-right: 12px;
    }
    .name{
      font-size: 14px;
      color: #0063CE;
      font-weight: 400;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 0;
    }
    .count{
      font-size: 14px;
      color: rgba(0,0,0,0.65);
    }
  }
}


.page_container {
  margin:0 20px;
  padding: 0;
}
.cards-item{
  background: linear-gradient(180deg, #DDEDFF 0%, #FFFFFF 100%);
  box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.04);
  border-radius: 4px;
  padding: 10px 16px;
  display: flex;
  flex-direction: row;
  align-items: center;
}

//.border{
//  width: 100px;margin-left: 50px;
//  border-radius: 30px 0 30px 0;
//  height: 30px;
//  border-top: 2px solid #00bbf2;
//  border-bottom: 2px solid #00bbf2;
//  border-left: 2px solid #00bbf2;
//  //border-image: linear-gradient(90deg, #00bbf2 0%, rgba(255, 255, 255, 0) 70%) 2 2 2 2;
//}

</style>
