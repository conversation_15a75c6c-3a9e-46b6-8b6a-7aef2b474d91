<template>
  <div>
    <div class="flex" v-if="resultData.length">
      <div class="flex-1" id="chart-container" ref="chartContainer" style="position: relative" :class="className" :style="{ height: height, width: width }" />
      <div style="min-width: 250px">
        <div class="mb13 chart__item flex-y-center"  v-for="(item,index) in resultData" :key="index">
          <div class="round" :style="{borderColor:itemStyleMap[index % itemStyleMap.length]}"></div>
          <div class="f12 chart__name" >{{item.name}}</div>
          <div class="f12 chart__count">{{item.count}}</div>
        </div>
      </div>
     </div>
    <div v-else :style="{ height: height, width: width }">
      <el-empty :description="$t('reaCommon.noData')" style="padding-top: 0px" :image-size="100"></el-empty>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import otherImage from '@/assets/images/other的副本.png'
export default {
  name: 'pie-chart',
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '180px'
    },
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      chart: null,
      // itemStyleMap: ['#3340FF', '#3B90FF', '#48E3FF', '#0FFFDB'],
      itemStyleMap: ['#67C23A', '#F56C6C', '#909399', '#0FFFDB'],
      resultData: [],
      positionLeft: '',
      positionTop: '',
      showModal: false,
      skuList: [],
      imageUrls: [],
      skuTitle: '',
      otherImage,
    }
  },
  mounted() {
  },
  beforeDestroy() {
    this.clear()
  },
  methods: {
    stopBlur() {
      this.canNotClose = true
      setTimeout(() => {
        this.canNotClose = false
        this.$refs.elInput.focus()
      }, 100)
    },
    blurClick() {
      setTimeout(() => {
        if (this.canNotClose) {
          this.canNotClose = false
          return
        }
        this.showModal = false
      }, 10)
    },
    /**
     * 清空
     * */
    clear() {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    },
    /**
     * 初始化
     * */
    initChart(resultData) {
      resultData = resultData || []
      this.clear()
      this.resultData = resultData
      if (!resultData.length) {
        return
      }
      const legendData = []
      const indicatorList = resultData.map((item, index) => {
        legendData.push(item.name)
        const itemStyle = {
          color: this.itemStyleMap[index % this.itemStyleMap.length] && this.itemStyleMap[index % this.itemStyleMap.length] || '#3371FF',
          borderRadius: 0
        }
        return { name: item.name, value: Number(item.count), label: {textStyle: {color:itemStyle.color}}, itemStyle }
      })
      this.$nextTick(()=>{
        const chartDom = this.$refs.chartContainer
        this.chart = echarts.init(chartDom)
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: `<span style="color: #333">{b}: {c}${this.$t('store.ownCountTitle')}</span>`
          },
          series: [
            {
              name: this.title,
              type: 'pie',
              radius: ['40%', '70%'],
              center: ['50%', '45%'],
              color: this.itemStyleMap,
              data: indicatorList,
              label: {
                show: true,
                formatter: "{d|{d}%} \n {b|{b}}",
                rich: {
                  d: {
                    fontSize: 12,
                    lineHeight: 18,
                    height: 18,
                    fontWeight: 500,
                  },
                  b: {
                    color: "#91A7B8",
                    fontSize: 10,
                    lineHeight: 12,
                    align: 'left',
                  }
                },
                textStyle: {
                  color: "#91A7B8",
                  align: "left",
                  fontSize: 14,
                  lineHeight: 12,
                },
              },
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              },
              selectedMode: 'single',
              selectedOffset: 5,
              clockwise: true
            }
          ]
        }
        option && this.chart.setOption(option)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
#chart-container {
  border-radius: 5px;
  margin: 0 auto;
}

.sales-sku-title {
  border-bottom: 1px solid #00A6F8;
  align-items: center;
  font-size: 16px;
  padding: 0 5px;
  font-weight: bold;
}
.sales-sku-top {
  font-size: 16px;
  padding: 0 5px;
  padding-top: 10px;
  font-weight: bold;
}
.round{
  width: 14px;
  height: 14px;
  background: #FFFFFF;
  border: 4px solid #3340FF;
  display: inline-block;
  border-radius: 50%;
  vertical-align: middle;
  margin-right: 10px;
}
.f14{
  font-size: 14px;
}
.f12{
  font-size: 12px;
}
.chart__name{
  min-width: 60px;
}
.chart__count{
  font-size: 14px;
  font-weight: 500;
  color: #000;
}
</style>
