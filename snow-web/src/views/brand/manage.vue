<template>
  <div class="page_container account_frontend_view">
    <div class="form_box">
      <search-form :value="queryParam" :form-data="formData" :label-width="searchLabelWidth"
                   @nodeConfigHandler="formHandler" @resetQueryParam="resetQueryParam"/>
    </div>
    <div class="table_box wbg">
      <div class="control_box flex flex-y-center flex-x-start">
        <div class="flex-1"/>
        <div v-if="$havePermission('0101',$route.meta.functionList)">
          <el-button size="medium" icon="el-icon-plus" type="primary" @click.native="addBrand(100)">{{
              $t('reaCommon.add')
            }}
          </el-button>
        </div>
      </div>
      <div>
        <new-table
          :loading="loading"
          :table-header="brandHeader"
          :table-data="dataSource"
          :operates="tableOperates"
          :pagination="pagination"
          :operates-props="operatesProps"
          @pageConfigHandler="pageHandler"
        >
          <template v-slot:status="{row}">
            <div class="flex flex-y-center"><span class="dot" :class="{'red':row.status == 0}"/>{{
                row.status == 1 ?
                  $t('reaCommon.isEnabled') : $t('reaCommon.isNotEnabled')
              }}
            </div>
          </template>
          <template v-slot:edit="{row}">
            <span v-if="row.status == 0 && $havePermission('0301',$route.meta.functionList)" class="table__oprate--item"
                  @click="editFun(row,2)">{{ $t('reaCommon.edit') }}</span>
          </template>
          <template v-slot:enable="{row}">
            <span v-if="$havePermission('0401',$route.meta.functionList)" class="table__oprate--item"
                  @click="enableFn(row)">{{ row.status == 1 ? $t('reaCommon.disable') : $t('reaCommon.enable') }}</span>
          </template>
        </new-table>
      </div>
    </div>

    <!--新增/编辑品牌-->
    <div class="dialog_container">
      <ele-dialog :title="dialogForm.id ? $t('reaCommon.edit') : $t('reaCommon.add')" @dialogClose="dialogCancel"
                  :visible.sync="dialogVisible"
                  center @dialogCancel="dialogCancel" @dialogConfirm="sbumitFun()">
        <template slot="content">
          <div class="dialog_form">
            <el-form ref="dialogForm" size="medium" :model="dialogForm" :rules="dialogRules" label-width="155px"
                     class="demo-ruleForm">
              <el-form-item :label="$t('reaCommon.tenantName')" prop="tenant_name">
                <el-input v-model="dialogForm.tenant_name" clearable
                          :placeholder="$t('reaCommon.pleaseEnter')"
                          @blur="dialogForm.tenant_name = $event.target.value.trim()"/>
              </el-form-item>
            </el-form>
          </div>
        </template>
      </ele-dialog>
    </div>


  </div>
</template>
<script>
import EleDialog from '@/components/Dialog'
import SearchForm from '@/components/SearchForm'
import NewTable from '@/components/Table'
import {brandHeader} from '@/utils/header'
import ListMixin from '@/mixins/List'
import {mapGetters} from 'vuex'
import {changeTenantStatus} from '@/api/brand/index'
import {tenantCreate, tenantEdit} from "@/api/brand";

export default {
  name: 'brand-manage',
  components: {
    SearchForm,
    NewTable,
    EleDialog
  },
  mixins: [ListMixin],
  data() {
    return {
      isSubmit: false,
      dialogVisible: false,
      dialogForm: {
        id: '',
        tenant_name: '',
      },
      dialogRules: {
        tenant_name: [
          {required: true, message: this.$t('reaCommon.pleaseEnter'), trigger: 'blur'}
        ],
      },
      searchLabelWidth: '100px',
      type: 'create',
      brandList: [],
      brandHeader,
      formRule: {},
      formData: [{
        name: '创建日期',
        nameKey: 'createDate',
        type: 'daterange',
        field: 'createTime',
        attrs: {
          clearable: true
        }
      }, {
        name: '品牌名称/ID',
        nameKey: 'tenantNameOrId',
        type: 'input',
        field: 'tenant_name',
        attrs: {
          clearable: true
        },
        options: []
      }],
      queryParam: {
        tenant_name: '',
        config_module: [],
        createTime: [],
        start: '',
        end: ''
      },
      initQueryParam: {
        tenant_name: '',
        config_module: [],
        createTime: [],
        start: '',
        end: ''
      },
      dataSource: [],
      /* mixin里接口名称*/
      apiUrl: {
        list: 'brandList'
      },
      tableOperates: [
        {
          label: '禁用',
          labelKey: 'enable',
          slot: 'enable'
        },
        {
          label: '编辑',
          labelKey: 'edit',
          slot: 'edit',
          method: (index, row) => {
            this.editFun(row, 2)
          }
        }
      ],
      operatesProps: {
        width: '220px',
        label: '操作',
        labelKey: 'operation',
        fixed: 'right'
      },
      selectAccount: {},
      optionsIndexObj: {}
    }
  },
  computed: {
    visitedViews() {
      return this.$store.state.tagsView.visitedViews
    },
    ...mapGetters([
      'userInfo',
      'language'
    ])
  },
  watch: {
    '$i18n.locale'() {
      this.searchLabelWidth = this.language === 'en' ? '180px' : '100px'
    }
  },
  created() {
    this.searchLabelWidth = this.language === 'en' ? '180px' : '100px'
    this.formData.map((v, index) => {
      this.optionsIndexObj[v.field] = index
    })
  },
  methods: {
    /**
     * 添加品牌
     * */
    addBrand(type) {
      if (type == 100) {
        this.dialogForm = {
          id: '',
          tenant_name: '',
        }
      }
      if (this.$refs['dialogForm']) {
        this.$refs['dialogForm'].clearValidate()
      }
      this.dialogVisible = true
    },
    /**
     * 编辑品牌
     * */
    editFun(store) {
      Object.keys(this.dialogForm).filter(v => {
        this.dialogForm[v] = store[v]
      })
      this.addBrand()
    },
    /**
     * 取消编辑
     * */
    dialogCancel() {
      this.dialogVisible = false
    },
    /**
     * 品牌编辑/新增
     * */
    sbumitFun() {
      this.$refs['dialogForm'].validate((valid) => {
        if (valid) {
          if (this.isSubmit) {
            return
          }
          this.isSubmit = true
          let call = null
          if (this.dialogForm.id) {
            call = tenantEdit(this.dialogForm)
          } else {
            call = tenantCreate(this.dialogForm)
          }
          call.then(res => {
            this.dialogVisible = false
            this.$message.success(this.$t('reaCommon.operationSuccess'))
            this.formHandler()
          }).finally(() => {
            setTimeout(() => {
              this.isSubmit = false
            }, 800)
          })
        }
      })
    },
    /**
     * 启用/禁用
     * */
    enableFn(row) {
      if (row.loading) {
        return
      }
      row.loading = true
      changeTenantStatus({id: row.id, status: row.status == 1 ? 0 : 1}).then((res) => {
        row.status = row.status == 1 ? 0 : 1
        this.$message.success(row.status == 1 ? this.$t('reaCommon.enableSuccess') : this.$t('reaCommon.disableSuccess'))
      }).finally(() => {
        row.loading = false
      })
    },

    /**
     * 关闭当前选项卡
     * */
    closeView() {
      this.visitedViews.map((v) => {
        if (v.path === '/brand/add') {
          this.$store.dispatch('tagsView/delView', v)
        }
      })
    },
    /**
     * 搜索条件
     * */
    formHandler() {
      if (Array.isArray(this.queryParam.createTime) && this.queryParam.createTime.length) {
        this.queryParam.start = this.queryParam.createTime[0]
        this.queryParam.end = this.queryParam.createTime[1]
      } else {
        this.queryParam.start = ''
        this.queryParam.end = ''
      }
      this.searchQuery()
    }
  }
}
</script>

<style scoped>
.table_box {
  margin-top: 23px;
  padding: 18px 25px 0;
}

.control_box {
  padding: 0 0 17px;
}

.dot {
  margin-right: 8px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #36C08E;
}

.dot.red {
  background-color: #E52828;
}
</style>
<style lang="scss">
.account_frontend_view {
  .el-button--primary.is-plain {
    background-color: white;
  }
}
</style>
