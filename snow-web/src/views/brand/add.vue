<template>
  <div class="brand-add-view page_container module_config_view">
    <div>
      <div v-if="isShowModule" class="module_container">
        <div class="module_title">{{ $t('tenantAdd.pleaseSelectModule') }}</div>
        <div class="module_logo_box flex flex-y-start flex-x-start flex-wrap">
          <div v-for="(module,index) in moduleList" :key="index" class="module_one">
            <div class="module_one_view flex flex-column flex-x-center flex-y-center"
                 :class="{'selected':module.selected}" @click.stop="selectOwnTab(module)">
              <img class="img_box" :src="module.icon_default" alt="">
              <div class="module_col_tit">{{ $t(module.nameKey) }}</div>
              <div v-if="module.selected" class="module_selected_icon">
                <img :src="module_selected_icon" alt="" width="30" height="30">
              </div>
            </div>
          </div>
        </div>
        <div v-if="model != 1" class="bottom_btn_box edit_view_btn">
          <el-button class="button_default" size="medium" style="margin-right:10px;" @click.native="back()">
            {{ $t('reaCommon.back') }}
          </el-button>
          <el-button type="primary" class="button__default" size="medium" @click.stop="toStepView">
            {{ $t('reaCommon.nextStep') }}
          </el-button>
        </div>
      </div>
      <div v-else>
        <div v-loading="fullLoading || submitting" class="wbg step_container_view">
          <div v-if="model == 2" class="all_complete_btn edit_view_btn">
            <el-button class="button__default" type="primary" :loading="submitting" @click.native="submit">
              {{ $t('tenantAdd.completeConfiguration') }}
            </el-button>
          </div>
          <div class="plan__add--contain1 br2 flex flex-y-start flex-x-start">
            <div class="step_full_vertical_line"/>
            <!--            <div v-if="model == 2" class="el_step_container">-->
            <!--              <el-steps :active="checkActive" direction="vertical" align-center finish-status="success" class="plan__steps">-->
            <!--                <el-step v-for="(item, index) in setupStep" v-if="item.form.params['enable']" :key="index" :status="checkActive === item.activeIndex ? 'process' : (item.completed ? 'success' : 'wait')" :title="$t(item.nameKey)" @click.native="changeThisStep(item)" />-->
            <!--              </el-steps>-->
            <!--            </div>-->
            <div class="step_container">
              <div v-for="(item, index) in setupStepFilterList" :key="index"
                   :class="{'selected': checkActive === item.activeIndex}"
                   class="check_step flex flex-y-center flex-x-end" @click.stop="changeThisStep(item)">
                <div class="flex-1">{{ $t(item.nameKey) }}</div>
                <div v-if="model == 2" class="step_count_box"
                     :class="checkActive === item.activeIndex ? 'process' : (item.completed ? 'success' : 'wait')">
                  <img v-if="checkActive != item.activeIndex && item.completed" :src="successIcon" alt="" width="20"
                       height="20">
                  <span v-else>{{ index + 1 }}</span>
                </div>
              </div>
            </div>
            <div class="flex-1">
              <!--采集配置-->
              <div v-show="checkActive == setupStep[0].activeIndex">
                <div class="description">{{ $t(setupStep[0].nameKey) }}</div>
                <el-form
                  :ref="setupStep[0].form.ref"
                  :disabled="model == 1"
                  class="plan__add--form"
                  size="medium"
                  :rules="setupStep[0].form.rules"
                  :model="setupStep[0].form.params"
                >
                  <el-form-item :label="$t('tenantAdd.tenantName')" prop="tenant_name">
                    <el-input v-model="setupStep[0].form.params['tenant_name']"
                              :placeholder="$t('tenantAdd.tenantNamePlaceholder')" clearable
                              @blur="setupStep[0].form.params['tenant_name']=$event.target.value.trim()"/>
                  </el-form-item>
                  <el-form-item :label="$t('tenantAdd.collectWay')" prop="collection_type">
                    <el-select v-model="setupStep[0].form.params['collection_type']"
                               :placeholder="$t('reaCommon.pleaseSelect')" style="width:100%;">
                      <el-option label="APP" value="APP"/>
                    </el-select>
                  </el-form-item>
                  <el-form-item :label="$t('tenantAdd.regionType')" prop="is_oversea">
                    <el-select v-model="setupStep[0].form.params['is_oversea']"
                               :placeholder="$t('reaCommon.pleaseSelect')" style="width:100%;">
                      <el-option :label="$t('tenantAdd.regionChina')" value="0"/>
                      <el-option :label="$t('tenantAdd.regionOverseas')" value="1"/>
                    </el-select>
                  </el-form-item>
                  <el-form-item :label="$t('tenantAdd.acquisitionMode')" prop="collection_mode">
                    <el-checkbox-group v-model="setupStep[0].form.params['collection_mode']">
                      <el-checkbox :label="1">{{ $t('tenantAdd.manualMode') }}</el-checkbox>
                      <el-checkbox :label="2">{{ $t('tenantAdd.irMode') }}</el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                  <el-form-item :label="$t('tenantAdd.photoMode')" prop="photo_mode_list">
                    <el-checkbox-group v-model="setupStep[0].form.params['photo_mode_list']">
                      <el-checkbox v-for="(mode,index) in photoModeList" :key="index" :label="mode.value"
                                   :disabled="mode.disabled">{{ $t(`tenantAdd.${mode.labelKey}`) }}
                      </el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                  <!-- 是否开启分组模式 -->
                  <el-form-item :label="$t('tenantAdd.cameraGroup')" prop="camera_group_enabled">
                    <el-radio-group v-model="setupStep[0].form.params['camera_group_enabled']">
                      <el-radio label="1">{{ $t('reaCommon.yes') }}</el-radio>
                      <el-radio label="0">{{ $t('reaCommon.no') }}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <!-- 验证码配置 -->
                  <el-form-item :label="$t('tenantAdd.smsCodeEnabled')" prop="sms_code_enabled">
                    <el-radio-group v-model="setupStep[0].form.params['sms_code_enabled']">
                      <el-radio label="1">{{ $t('reaCommon.yes') }}（{{ $t('tenantAdd.cos') }}）</el-radio>
                      <el-radio label="0">{{ $t('reaCommon.no') }}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item :label="$t('tenantAdd.scene')" prop="scene">
                    <el-tree-select ref="treeSelect" v-model="setupStep[0].form.params['scene']" collapse-tags
                                    :styles="styles" :select-params="selectParams" :tree-params="treeParams"
                                    :disabled="model == 1"/>
                  </el-form-item>
                  <el-form-item :label="$t('tenantAdd.cloudStorage')" prop="storage_type">
                    <el-select v-model="setupStep[0].form.params['storage_type']" @change="changeStorage()"
                               :placeholder="$t('tenantAdd.cloudStoragePlaceholder')" style="width:100%;" clearable>
                      <el-option :label="$t('tenantAdd.retailEye')" value="RETAILEYE"/>
                      <el-option :label="$t('tenantAdd.cos')" value="COS"/>
                      <el-option :label="$t('tenantAdd.blob')" value="BLOB"/>
                    </el-select>
                  </el-form-item>
                  <!-- 输入腾讯云 -->
                  <div v-if="setupStep[0].form.params['storage_type'] === 'COS'">
                    <el-form-item label="SECRET ID" prop="secret_id">
                      <el-input v-model="setupStep[0].form.params['secret_id']"
                                :placeholder="`${$t('reaCommon.pleaseEnter')} SECRET ID`" clearable
                                @blur="setupStep[0].form.params['secret_id']=$event.target.value.trim()"/>
                    </el-form-item>
                    <el-form-item label="SECRET KEY" prop="secret_key">
                      <el-input v-model="setupStep[0].form.params['secret_key']"
                                :placeholder="`${$t('reaCommon.pleaseEnter')} SECRET KEY`" clearable
                                @blur="setupStep[0].form.params['secret_key']=$event.target.value.trim()"/>
                    </el-form-item>
                    <el-form-item label="REGION" prop="region">
                      <el-input v-model="setupStep[0].form.params['region']"
                                :placeholder="`${$t('reaCommon.pleaseEnter')} REGION`" clearable
                                @blur="setupStep[0].form.params['region']=$event.target.value.trim()"/>
                    </el-form-item>
                    <el-form-item label="BUCKET" prop="bucket">
                      <el-input v-model="setupStep[0].form.params['bucket']"
                                :placeholder="`${$t('reaCommon.pleaseEnter')} BUCKET`" clearable
                                @blur="setupStep[0].form.params['bucket']=$event.target.value.trim()"/>
                    </el-form-item>
                    <el-form-item label="BUCKET VIDEO" prop="bucket_video">
                      <el-input v-model="setupStep[0].form.params['bucket_video']"
                                :placeholder="`${$t('reaCommon.pleaseEnter')} BUCKET VIDEO`" clearable
                                @blur="setupStep[0].form.params['bucket_video']=$event.target.value.trim()"/>
                    </el-form-item>
                  </div>
                  <!-- 输入blob -->
                  <div v-if="setupStep[0].form.params['storage_type'] === 'BLOB'">
                    <el-form-item label="dns name" prop="dns_name">
                      <el-input v-model="setupStep[0].form.params['dns_name']"
                                :placeholder="`${$t('reaCommon.pleaseEnter')}`" clearable
                                @blur="setupStep[0].form.params['dns_name']=$event.target.value.trim()"/>
                    </el-form-item>
                    <el-form-item label="account name" prop="account_name">
                      <el-input v-model="setupStep[0].form.params['account_name']"
                                :placeholder="`${$t('reaCommon.pleaseEnter')}`" clearable
                                @blur="setupStep[0].form.params['account_name']=$event.target.value.trim()"/>
                    </el-form-item>
                    <el-form-item label="account key" prop="account_key">
                      <el-input v-model="setupStep[0].form.params['account_key']"
                                :placeholder="`${$t('reaCommon.pleaseEnter')}`" clearable
                                @blur="setupStep[0].form.params['account_key']=$event.target.value.trim()"/>
                    </el-form-item>
                    <el-form-item label="container image" prop="container_image">
                      <el-input v-model="setupStep[0].form.params['container_image']"
                                :placeholder="`${$t('reaCommon.pleaseEnter')}`" clearable
                                @blur="setupStep[0].form.params['container_image']=$event.target.value.trim()"/>
                    </el-form-item>
                    <el-form-item label="container video" prop="container_video">
                      <el-input v-model="setupStep[0].form.params['container_video']"
                                :placeholder="`${$t('reaCommon.pleaseEnter')}`" clearable
                                @blur="setupStep[0].form.params['container_video']=$event.target.value.trim()"/>
                    </el-form-item>
                  </div>
                  <el-form>
                    <div v-if="model != 1" class="flex flex-y-center flex-x-center edit_view_btn module_form_btn">
                      <el-button style="margin-right:10px;" @click.native="back()">{{
                          $t('reaCommon.back')
                        }}
                      </el-button>
                      <el-button style="margin-right:10px;" @click="changeStep(0)">{{
                          $t('reaCommon.previousStep')
                        }}
                      </el-button>
                      <el-button
                        v-if="!(model == 1 && setupStep[0].isLast)"
                        class="button__default"
                        type="primary"
                        :loading="setupStep[0].form.loading"
                        @click="changeStep(1,setupStep[0])"
                      >{{ setupStep[0].isLast ? $t('tenantAdd.completeConfiguration') : $t('reaCommon.nextStep') }}
                      </el-button>
                    </div>
                    <div v-else class="flex flex-y-center flex-x-center edit_view_btn module_form_btn">
                      <el-button style="margin-right:10px;" @click.native="back()">{{
                          $t('reaCommon.back')
                        }}
                      </el-button>
                    </div>
                  </el-form>

                </el-form>
              </div>
              <!--配置图像识别-->
              <div v-show="checkActive == setupStep[1].activeIndex">
                <div class="description">{{ $t(setupStep[1].nameKey) }}</div>
                <el-form
                  :ref="setupStep[1].form.ref"
                  :disabled="model == 1"
                  :rules="setupStep[1].form.rules"
                  class="plan__add--form"
                  size="medium"
                  :model="setupStep[1].form.params"
                >
                  <el-form-item :label="$t('tenantAdd.skuMasterDataIndustry')" prop="sku_project_id">
                    <el-select v-model="setupStep[1].form.params['sku_project_id']"
                               :placeholder="$t('tenantAdd.skuMasterDataIndustryPlaceholder')" style="width:100%;"
                               clearable filterable>
                      <el-option v-for="skuProject in skuProjectList" :key="skuProject.project_id"
                                 :label="skuProject.project_name" :value="skuProject.project_id"/>
                    </el-select>
                  </el-form-item>
                  <el-form-item :label="$t('tenantAdd.IRMasterDataSource')" prop="ir_sku_origin">
                    <el-radio-group v-model="setupStep[1].form.params['ir_sku_origin']">
                      <el-radio label="ir">{{ $t('tenantAdd.IRMasterData') }}</el-radio>
                      <el-radio label="rea">{{ $t('tenantAdd.REAMappingTable') }}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <!--配置平台-->
                  <el-form-item :label="$t('tenantAdd.detectPlatform')" prop="detect_platform">
                    <el-radio-group v-model="setupStep[1].form.params['detect_platform']">
                      <el-radio label="1">{{ $t('tenantAdd.basicDetectPlatform') }}</el-radio>
                      <el-radio label="2">{{ $t('tenantAdd.omniDetectPlatform') }}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <!--基础配置-->
                  <div v-if="setupStep[1].form.params['detect_platform'] == '1'">
                    <el-form-item :label="$t('tenantAdd.identifyConfig')" prop="option">
                      <el-checkbox-group v-model="setupStep[1].form.params['option']">
                        <el-checkbox label="with_price">{{ $t('tenantAdd.priceTag') }}</el-checkbox>
                        <el-checkbox label="with_material">POSM</el-checkbox>
                        <el-checkbox label="with_restrain_bottles_contains_bottles">{{
                            $t('tenantAdd.withBottles')
                          }}
                        </el-checkbox>
                        <el-checkbox label="with_display">{{ $t('tenantAdd.withDisplay') }}</el-checkbox>
                        <el-checkbox label="with_shelf_background_display">{{
                            $t('tenantAdd.withShelfBackgroundDisplay')
                          }}
                        </el-checkbox>
                        <el-checkbox label="model_monitor">{{ $t('tenantAdd.modelMonitor') }}</el-checkbox>
                      </el-checkbox-group>
                    </el-form-item>
                    <el-form-item :label="$t('tenantAdd.recognitionModel')" prop="project" key="project">
                      <el-select v-model="setupStep[1].form.params['project']"
                                 :placeholder="$t('tenantAdd.recognitionModelPlaceholder')" style="width:100%;"
                                 clearable>
                        <el-option v-for="project in projectList" :key="project.id" :label="project.name"
                                   :value="project.project"/>
                      </el-select>
                    </el-form-item>
                  </div>
                  <!--OMNI配置-->
                  <!--流程编号-->
                  <div v-if="setupStep[1].form.params['detect_platform'] == '2'">
                    <el-form-item :label="$t('tenantAdd.flowId')" prop="flow_id" key="flow_id">
                      <el-input :placeholder="$t('tenantAdd.pleaseFlowId')"
                                v-model="setupStep[1].form.params['flow_id']"></el-input>
                    </el-form-item>
                    <!--识别接口地址-->
                    <el-form-item prop="api_url" key="api_url" class="el-form-item-label">
                      <span slot="label" class="slot-label">{{ $t('tenantAdd.apiUrl') }}<span
                        style="color: #cccccc">（{{ $t('tenantAdd.linkHelp') }}）</span></span>
                      <el-input :placeholder="$t('tenantAdd.pleaseApiUrl')"
                                v-model="setupStep[1].form.params['api_url']"></el-input>
                    </el-form-item>
                    <!--签名密钥-->
                    <el-form-item :label="$t('tenantAdd.apiSecret')" prop="api_secret" key="api_secret">
                      <el-input :placeholder="$t('tenantAdd.pleaseApiSecret')"
                                v-model="setupStep[1].form.params['api_secret']"></el-input>
                    </el-form-item>
                  </div>

                  <el-form>
                    <div v-if="model != 1" class="flex flex-y-center flex-x-center edit_view_btn module_form_btn">
                      <el-button class="button__plain ele__form--reset" style="margin-right:10px;" @click="back()">
                        {{ $t('reaCommon.back') }}
                      </el-button>
                      <el-button class="button__plain ele__form--reset" style="margin-right:10px;"
                                 @click="changeStep(0)">{{ $t('reaCommon.previousStep') }}
                      </el-button>
                      <el-button
                        v-if="!(model == 1 && setupStep[1].isLast)"
                        class="button__default ele__form--reset"
                        type="primary"
                        :loading="setupStep[1].form.loading"
                        @click="changeStep(1,setupStep[1])"
                      >{{ setupStep[1].isLast ? $t('tenantAdd.completeConfiguration') : $t('reaCommon.nextStep') }}
                      </el-button>
                    </div>
                    <div v-else class="flex flex-y-center flex-x-center edit_view_btn module_form_btn">
                      <el-button style="margin-right:10px;" @click.native="back()">{{
                          $t('reaCommon.back')
                        }}
                      </el-button>
                    </div>
                  </el-form>
                </el-form>
              </div>
              <!--配置规则引擎-->
              <div v-show="checkActive == setupStep[2].activeIndex">
                <div class="description">{{ $t(setupStep[2].nameKey) }}</div>
                <el-form
                  :ref="setupStep[2].form.ref"
                  :disabled="model == 1"
                  :rules="setupStep[2].form.rules"
                  class="plan__add--form"
                  size="medium"
                  :model="setupStep[2].form.params"
                >
                  <el-form-item :label="$t('tenantAdd.successfulImage')" prop="success_image">
                    <el-switch v-model="setupStep[2].form.params['success_image']"/>
                  </el-form-item>
                  <el-form-item :label="$t('tenantAdd.checkProjectTackEffect')" prop="config_effective">
                    <el-radio-group v-model="setupStep[2].form.params['config_effective']">
                      <el-radio :label="1">{{ $t('tenantAdd.instant') }}</el-radio>
                      <el-radio :label="2">T+1</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item :label="$t('tenantAdd.openInspectionPlan')" prop="open_inspection_plan">
                    <el-switch v-model="setupStep[2].form.params['open_check_plan_menu']"/>
                  </el-form-item>
                  <el-form>
                    <div v-if="model != 1" class="flex flex-y-center flex-x-center edit_view_btn module_form_btn">
                      <el-button class="button__plain ele__form--reset" style="margin-right:10px;" @click="back()">
                        {{ $t('reaCommon.back') }}
                      </el-button>
                      <el-button class="button__plain ele__form--reset" style="margin-right:10px;"
                                 @click="changeStep(0)">{{ $t('reaCommon.previousStep') }}
                      </el-button>
                      <el-button
                        v-if="!(model == 1 && setupStep[2].isLast)"
                        class="button__default ele__form--reset"
                        type="primary"
                        :loading="setupStep[2].form.loading"
                        @click="changeStep(1,setupStep[2])"
                      >{{ setupStep[2].isLast ? $t('tenantAdd.completeConfiguration') : $t('reaCommon.nextStep') }}
                      </el-button>
                    </div>
                    <div v-else class="flex flex-y-center flex-x-center edit_view_btn module_form_btn">
                      <el-button style="margin-right:10px;" @click.native="back()">{{
                          $t('reaCommon.back')
                        }}
                      </el-button>
                    </div>
                  </el-form>
                </el-form>
              </div>
              <!--配置图像计划-->
              <div v-show="checkActive == setupStep[3].activeIndex">
                <div class="description">{{ $t(setupStep[3].nameKey) }}</div>
                <div>
                  <el-form
                    :ref="setupStep[3].form.ref"
                    :rules="setupStep[3].form.rules"
                    class="plan__add--form"
                    size="medium"
                    :model="setupStep[3].form.params"
                  >
                    <el-form-item :label="$t('reaCommon.visitType')" prop="visit_type">
                      <el-checkbox-group v-model="setupStep[3].form.params['visit_type']" :disabled="model == 1">
                        <el-checkbox :label="1">{{ $t('tenantAdd.planToVisit') }}</el-checkbox>
                        <el-checkbox :label="2">{{ $t('tenantAdd.wheneverWhereverVisit') }}</el-checkbox>
                      </el-checkbox-group>
                    </el-form-item>
                    <el-form-item :label="$t('reaCommon.doorImage')" prop="door_image">
                      <el-radio-group v-model="setupStep[3].form.params['door_image']" :disabled="model == 1">
                        <el-radio :label="1">{{ $t('tenantAdd.noPictures') }}</el-radio>
                        <el-radio :label="2">{{ $t('tenantAdd.takePictures') }}</el-radio>
                        <el-radio :label="3">{{ $t('tenantAdd.selectPictures') }}</el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item :label="$t('tenantAdd.doorVideo')" prop="door_video">
                      <el-radio-group v-model="setupStep[3].form.params['door_video']" :disabled="model == 1">
                        <el-radio :label="1">{{ $t('tenantAdd.noPictures') }}</el-radio>
                        <el-radio :label="2">{{ $t('tenantAdd.takePictures') }}</el-radio>
                        <el-radio :label="3">{{ $t('tenantAdd.selectPictures') }}</el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item :label="$t('tenantAdd.signIn')" prop="sign">
                      <el-switch v-model="setupStep[3].form.params['sign']" :disabled="model == 1"/>
                    </el-form-item>
                    <div v-if="setupStep[3].form.params['sign']">
                      <el-form-item :label="$t('tenantAdd.SignInEffectiveDistance')" prop="distance">
                        <div class="sign_in_input flex flex-y-center flex-x-start" style="position:relative;">
                          <div class="flex-1">
                            <el-input v-model="setupStep[3].form.params['distance']"
                                      :placeholder="$t('tenantAdd.SignInEffectiveDistancePlaceholder')" clearable
                                      :disabled="model == 1" onkeyup="value=value.replace(/[^\d]/g,'')"/>
                          </div>
                          <div style="position:absolute;right:9px;line-height: 40px;">{{ $t('tenantAdd.meters') }}</div>
                        </div>
                      </el-form-item>
                    </div>
                    <el-form-item :label="$t('tenantAdd.checkPlanEffect')" prop="config_effective">
                      <el-radio-group v-model="setupStep[3].form.params['config_effective']" :disabled="model == 1">
                        <el-radio :label="1">{{ $t('tenantAdd.instant') }}</el-radio>
                        <el-radio :label="2">T+1</el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item :label="$t('tenantAdd.organization')" class="check__add--button" prop="organize">
                      <el-button
                        style="width:100%;"
                        :class="{'hasVal':setupStep[3].form.params['organize'].length}"
                        class="button__import ele__form--reset"
                        :icon="setupStep[3].form.params['organize'].length ? '' : 'el-icon-plus'"
                        @click="toShowTree"
                      >{{
                          model == 1 ? $t('tenantAdd.checkOrganizationConfig') : (setupStep[3].form.params['organize'].length ? $t('tenantAdd.editOrganizationConfig') : $t('tenantAdd.organizationConfig'))
                        }}
                      </el-button>
                    </el-form-item>
                    <el-form>
                      <div v-if="model != 1" class="flex flex-y-center flex-x-center edit_view_btn module_form_btn">
                        <el-button class="button__plain ele__form--reset" style="margin-right:10px;" @click="back()">
                          {{ $t('reaCommon.back') }}
                        </el-button>
                        <el-button
                          class="button__default ele__form--reset"
                          style="margin-right:10px;"
                          @click="changeStep(0)"
                        >{{ $t('reaCommon.previousStep') }}
                        </el-button>
                        <el-button
                          v-if="!(model == 1 && setupStep[3].isLast)"
                          class="button__default ele__form--reset"
                          type="primary"
                          :loading="setupStep[3].form.loading"
                          @click.native="submit()"
                        >{{ $t('tenantAdd.completeConfiguration') }}
                        </el-button>
                      </div>
                      <div v-else class="flex flex-y-center flex-x-center edit_view_btn module_form_btn">
                        <el-button style="margin-right:10px;" @click.native="back()">{{
                            $t('reaCommon.back')
                          }}
                        </el-button>
                      </div>
                    </el-form>
                  </el-form>
                </div>
                <div class="dialog_container">
                  <ele-dialog width="80%" custom-class="tree_dialog" :modal-append-to-body="true" :append-to-body="true"
                              :title="$t('tenantAdd.organizationConfig')" :visible.sync="isShowTree" center
                              @dialogCancel="treeCancel" @dialogConfirm="treeConfirm" @dialogClose="treeClose">
                    <template slot="content">
                      <div style="overflow:scroll;min-height:60vh">
                        <div class="vue2-org-tree">
                          <div v-for="tree in organize" :key="tree.id">
                            <vue2-org-tree
                              :data="tree"
                              :horizontal="true"
                              :render-content="renderContent"
                              collapsable
                              @on-expand="onExpand"
                            />
                          </div>
                          <div class="dialog_container">
                            <ele-dialog :append-to-body="true" width="600px" :title="typeTitle"
                                        :visible.sync="dialogVisible" center @dialogCancel="dialogCancel"
                                        @dialogConfirm="dialogConfirm" @dialogClose="dialogClose">
                              <template slot="content">
                                <div class="dialog_form">
                                  <el-form ref="dialogForm" size="medium" :model="dialogForm" :rules="dialogRules"
                                           label-width="200px" class="demo-ruleForm">
                                    <el-form-item :label="$t('tenantAdd.departmentName')" prop="name">
                                      <el-input v-if="dialogVisible" v-model="dialogForm.name" v-focus clearable
                                                @keyup.enter.native="dialogConfirm"/>
                                    </el-form-item>
                                  </el-form>
                                </div>
                              </template>
                            </ele-dialog>
                          </div>
                        </div>
                        <div v-if="model != 1" style="padding-left:35px;padding-top:25px;padding-bottom:25px;">
                          <el-button
                            size="medium"
                            type="primary"
                            plain
                            icon="el-icon-plus"
                            @click="addLevel1()"
                          >{{ $t('tenantAdd.levelOneDepartment') }}
                          </el-button>
                        </div>
                      </div>
                    </template>
                  </ele-dialog>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import successIcon from '@/assets/images/<EMAIL>'
import Vue2OrgTree from 'vue2-org-tree'
// import { EleDialog } from 'ele-form'
import EleDialog from '@/components/Dialog'
import collect_default from '@/assets/images/<EMAIL>'
import recognition_default from '@/assets/images/<EMAIL>'
import rule_default from '@/assets/images/<EMAIL>'
import plan_default from '@/assets/images/<EMAIL>'
import module_selected_icon from '@/assets/images/<EMAIL>'
import {sceneSystemList, tenantProject} from '@/api/common'
import {recognitionProjectList, tenantCreate, tenantDetail, tenantEdit} from '@/api/brand'
import {isObject} from '@/utils/validate'
import {filterAsyncRouter} from '@/store/modules/permission'

const originPhotoModeList = [
  {
    name: {
      'zh-Hans': '普通模式',
      'zh-Hant': '普通模式',
      'en': 'Rear Camera mode'
    },
    active: false,
    isshow: true, // 如果这个取消了则手动修改默认号码为1
    disabled: true,
    value: 3
  },
  //  原本只开这个模式
  {
    name: {
      'zh-Hans': '光流模式',
      'zh-Hant': '光流模式',
      'en': 'OF/Optical Flow mode'
    },
    active: false,
    isshow: true,
    disabled: true,
    value: 1
  },
  {
    name: {
      'zh-Hans': 'AR模式',
      'zh-Hant': 'AR模式',
      'en': 'Augmented reality/AR mode'
    },
    active: false,
    isshow: true,
    disabled: true,
    value: 0
  },
  {
    name: {
      'zh-Hans': 'VR模式',
      'zh-Hant': 'VR模式',
      'en': 'VR mode'
    },
    active: false,
    isshow: true,
    disabled: true,
    value: 11
  },
  {
    name: {
      'zh-Hans': '视频分析',
      'zh-Hant': '視頻分析',
      'en': 'Video analysis'
    },
    active: false,
    isshow: true,
    disabled: true,
    value: 13
  },
  {
    name: {
      'zh-Hans': '自拍模式',
      'zh-Hant': '自拍模式',
      'en': 'Front Camera mode'
    },
    active: false,
    isshow: true,
    disabled: true,
    value: 2
  }
]

export default {
  name: 'brand-add',
  components: {
    Vue2OrgTree,
    EleDialog
  },
  directives: {
    // 注册一个局部的自定义指令 v-focus
    focus: {
      // 指令的定义
      inserted: function (el) {
        // 聚焦元素
        el.qurySelector('input').focus()
      }
    }
  },
  data() {
    return {
      originPhotoModeList,
      photoModeList: [{
        labelKey: 'rearCameraMode',
        value: 0,
        disabled: false
      }, {
        labelKey: 'flowMode',
        value: 1,
        disabled: true
      }, {
        labelKey: 'ARMode',
        value: 2,
        disabled: false
      }, {
        labelKey: 'VRMode',
        value: 3,
        disabled: false
      }, {
        labelKey: 'videoAnalysis',
        value: 4,
        disabled: false
      }, {
        labelKey: 'frontCameraMode',
        value: 5,
        disabled: false
      }],
      successIcon,
      fullLoading: false,
      module_selected_icon,
      // 模块组合规则
      // moduleRuleList: [
      //   [0, 3],
      //   [0, 1, 3]
      //   [0, 1, 2, 3],
      // ],
      model: 0,
      id: '',
      skuProjectList: [],
      projectList: [],
      isShowModule: true,
      moduleList: [{
        name: '售点采集',
        nameKey: 'tenantAdd.storeCollection',
        key: 'collection',
        id: 1,
        icon_default: collect_default,
        selected: true,
        addModuleList: [3],
        subModuleList: []
      }, {
        name: '图像识别',
        nameKey: 'tenantAdd.imageRecognition',
        key: 'image_recognition',
        id: 2,
        icon_default: recognition_default,
        selected: false,
        addModuleList: [0, 3],
        subModuleList: [2]
      }, {
        name: '规则引擎',
        nameKey: 'tenantAdd.ruleEngine',
        id: 3,
        key: 'rule_engine',
        icon_default: rule_default,
        selected: false,
        addModuleList: [0, 1, 3],
        subModuleList: []
      }, {
        name: '拜访计划',
        nameKey: 'route.visitPlan',
        id: 4,
        key: 'plan',
        icon_default: plan_default,
        selected: true,
        addModuleList: [0],
        subModuleList: []

      }],
      // step 参数
      checkActive: -1,
      descriptionTxt: '',
      // step form 参数
      styles: {
        width: '100%'
      },
      selectParams: {
        multiple: true,
        clearable: true,
        placeholder: this.$t('tenantAdd.scenePlaceholder')
      },
      treeParams: {
        filterable: false,
        'default-expand-all': true,
        'expand-on-click-node': false,
        'node-key': 'id',
        'show-checkbox': true,
        leafOnly: true,
        data: [],
        parentIds: [],
        props: {
          children: 'items',
          label: 'name',
          disabled: 'disabled',
          value: 'id'
        }
      },
      setupStep: [
        {
          name: '售点采集',
          nameKey: 'tenantAdd.storeCollection',
          key: 'collection',
          activeIndex: 0,
          isLast: false,
          completed: false,
          descriptionTxt: '品牌方只使用采集功能的情况下、仅可通过REA  APP拍照、在后台查看照片、仅支持随店随访。不需要配置检查项目、检查计划、拜访计划。REA APP端没有采集模式选择。',
          form: {
            params: {
              enable: false,
              tenant_name: '',
              collection_type: 'APP',
              scene: [],
              storage_type: '',
              secret_id: '',
              secret_key: '',
              region: '',
              bucket: '',
              bucket_video: '',
              collection_mode: [1, 2],
              photo_mode_list: [1],
              origin_photo_mode_list: [],
              dns_name: '',
              account_name: '',
              account_key: '',
              container_image: '',
              container_video: '',
              is_oversea: '',
              camera_group_enabled: '',
              sms_code_enabled: '',
              scenereport_time_enabled: ''
            },
            rules: {
              collection_mode: [
                {required: true, message: this.$t('tenantAdd.acquisitionModePlaceholder'), trigger: 'blur'}
              ],
              tenant_name: [
                {required: true, message: this.$t('tenantAdd.tenantNamePlaceholder'), trigger: 'blur'}
              ],
              collection_type: [
                {
                  required: true,
                  message: this.$t('reaCommon.pleaseSelect') + ' ' + this.$t('tenantAdd.collectWay'),
                  trigger: 'blur'
                }
              ],
              scene: [
                {required: true, message: this.$t('tenantAdd.scenePlaceholder'), trigger: 'blur'}
              ],
              storage_type: [
                {required: true, message: this.$t('tenantAdd.cloudStoragePlaceholder'), trigger: 'blur'}
              ],
              secret_id: [
                {required: true, message: this.$t('reaCommon.pleaseEnter') + ' SECRET ID', trigger: 'blur'}
              ],
              secret_key: [
                {required: true, message: this.$t('reaCommon.pleaseEnter') + ' SECRET KEY', trigger: 'blur'}
              ],
              region: [
                {required: true, message: this.$t('reaCommon.pleaseEnter') + ' REGION', trigger: 'blur'}
              ],
              bucket: [
                {required: true, message: this.$t('reaCommon.pleaseEnter') + ' BUCKET', trigger: 'blur'}
              ],
              bucket_video: [
                {required: true, message: this.$t('reaCommon.pleaseEnter') + ' BUCKET VIDEO', trigger: 'blur'}
              ],
              dns_name: [
                {required: true, message: this.$t('reaCommon.pleaseEnter'), trigger: 'blur'},
              ],
              account_name: [
                {required: true, message: this.$t('reaCommon.pleaseEnter'), trigger: 'blur'},
              ],
              account_key: [
                {required: true, message: this.$t('reaCommon.pleaseEnter'), trigger: 'blur'},
              ],
              container_image: [
                {required: true, message: this.$t('reaCommon.pleaseEnter'), trigger: 'blur'},
              ],
              container_video: [
                {required: true, message: this.$t('reaCommon.pleaseEnter'), trigger: 'blur'},
              ],
              is_oversea: [
                {required: true, message: this.$t('reaCommon.pleaseEnter'), trigger: 'blur'},
              ],
              camera_group_enabled: [
                {required: true, message: this.$t('reaCommon.pleaseEnter'), trigger: 'blur'},
              ],
              sms_code_enabled: [
                {required: true, message: this.$t('reaCommon.pleaseEnter'), trigger: 'blur'},
              ],

            },
            ref: 'collection',
            loading: false
          }
        },
        {
          name: '图像识别',
          nameKey: 'tenantAdd.imageRecognition',
          key: 'image_recognition',
          isLast: false,
          completed: false,
          descriptionTxt: '品牌方使用采集及图像识别功能的情况下、仅可通过REA  APP拍照、在后台查看照片及识别结果、仅支持随店随访。不需要配置检查项目、检查计划、拜访计划。REA APP端没有采集模式选择。',

          activeIndex: 1,
          form: {
            params: {
              enable: false,
              options: {},
              option: [],
              project: '',
              sku_project_id: '',
              ir_sku_origin: 'ir',
              detect_platform: '1',
              flow_id: '',
              api_url: '',
              api_secret: '',
            },
            rules: {
              project: [
                {required: true, message: this.$t('tenantAdd.recognitionModelPlaceholder'), trigger: 'blur'}
              ],
              sku_project_id: [
                {required: true, message: this.$t('tenantAdd.skuMasterDataIndustryPlaceholder'), trigger: 'blur'}
              ],
              ir_sku_origin: [
                {required: true, message: '请勾选IR主数据来源', trigger: 'blur'}
              ],
              detect_platform: [
                {required: true, message: this.$t('tenantAdd.pleaseDetectPlatform'), trigger: 'blur'}
              ],
              flow_id: [
                {required: true, message: this.$t('tenantAdd.pleaseFlowId'), trigger: 'blur'}
              ],
              api_url: [
                {required: true, message: this.$t('tenantAdd.pleaseApiUrl'), trigger: 'blur'}
              ],
              api_secret: [
                {required: true, message: this.$t('tenantAdd.pleaseApiSecret'), trigger: 'blur'}
              ]
            },
            ref: 'image_recognition',
            loading: false
          }
        },
        {
          name: '规则引擎',
          key: 'rule_engine',
          nameKey: 'tenantAdd.ruleEngine',
          isLast: false,
          descriptionTxt: '品牌方使用采集及图像识别、规则引擎功能的情况下、可通过REA  APP拍照、在后台查看照片、识别结果及是否符合规则、仅支持随店随访。需要配置检查项目。使用规则引擎的前提是必须使用采集、图像识别。',
          completed: false,
          activeIndex: 2,
          form: {
            params: {
              enable: false,
              success_image: true,
              config_effective: 2,
              open_check_plan_menu: true
            },
            rules: {},
            ref: 'rule_engine',
            loading: false
          }
        },
        {
          name: '拜访计划',
          nameKey: 'route.visitPlan',
          key: 'plan',
          isLast: true,
          descriptionTxt: '品牌方使用计划功能的情况下、可通过REA  APP看到采集计划。需要配置检查计划、拜访计划。',
          completed: false,
          activeIndex: 3,
          form: {
            params: {
              enable: false,
              visit_type: [1],
              door_image: 2,
              door_video: 2,
              sign: true,
              distance: '',
              config_effective: 2,
              organize: []
            },
            rules: {
              visit_type: [
                {type: 'array', required: true, message: this.$t('tenantAdd.chooseLeastOne'), trigger: 'blur'}
              ],
              door_image: [
                {required: true, message: '请选择门头照是否必拍', trigger: 'blur'}
              ],
              door_video: [
                {required: true, message: '请选择门视频是否必拍', trigger: 'blur'}
              ],
              sign: [
                {required: true, message: '请选择是否签到', trigger: 'blur'}
              ],
              distance: [
                {required: true, message: this.$t('tenantAdd.SignInEffectiveDistancePlaceholder'), trigger: 'blur'}
              ],
              config_effective: [
                {required: true, message: '请选择检查计划配置生效时间', trigger: 'blur'}
              ],
              organize: [
                {type: 'array', required: true, message: this.$t('tenantAdd.pleaseConfigOrganization'), trigger: 'blur'}
              ]
            },
            ref: 'plan',
            loading: false
          }
        }
      ],
      // tree 参数
      isShowTree: false,
      organize: [],
      type: 'lowerLevel',
      typeTitle: '',
      dialogVisible: false,
      dialogForm: {
        name: ''
      },
      dialogRules: {
        name: [{required: true, message: this.$t('tenantAdd.departmentNamePlaceholder'), trigger: 'blur'}]
      },
      selectNode: {},
      sameLevelList: [],
      selectedStep: {},
      submitting: false

    }
  },
  computed: {
    visitedViews() {
      return this.$store.state.tagsView.visitedViews
    },
    setupStepFilterList() {
      return this.setupStep.filter((item) => {
        return item.form.params['enable']
      })
    }
  },
  watch: {
    '$i18n.locale'() {
      this.selectParams = {
        multiple: true,
        clearable: true,
        placeholder: this.$t('tenantAdd.scenePlaceholder')
      }
      this.setFormRule()
    },
    checkActive(newVal) {
      console.log(newVal)
      const stepList = this.setupStep.filter((item) => {
        return item.form.params.enable
      })
      stepList.map((step) => {
        if (newVal === step.activeIndex) {
          this.selectedStep = step
          console.log(step)
        }
      })
    }
  },
  created() {
    this.id = this.$route.query.id
    this.model = this.$route.query.type || (this.id ? 1 : 2)
    if (this.model == 1) {
      this.isShowModule = false
    }
    if (this.id) {
      this.getDetail()
    }
    this.getRecognitionProjectList()
    this.getSkuProjectList()
    this.getSceneList()
    this.setFormRule()
  },
  methods: {
    /**
     * 切换云存储方式清空验证
     * */
    changeStorage() {
      this.$refs[this.setupStep[0].form.ref].clearValidate()
    },
    setFormRule() {
      this.setupStep[0].form.rules = {
        photo_mode_list: [
          {required: true, type: 'array', message: this.$t('tenantAdd.photoModePlaceholder'), trigger: 'change'}
        ],
        tenant_name: [
          {required: true, message: this.$t('tenantAdd.tenantNamePlaceholder'), trigger: 'blur'}
        ],
        collection_type: [
          {
            required: true,
            message: this.$t('reaCommon.pleaseSelect') + ' ' + this.$t('tenantAdd.collectWay'),
            trigger: 'blur'
          }
        ],
        scene: [
          {required: true, message: this.$t('tenantAdd.scenePlaceholder'), trigger: 'blur'}
        ],
        storage_type: [
          {required: true, message: this.$t('tenantAdd.cloudStoragePlaceholder'), trigger: 'blur'}
        ],
        secret_id: [
          {required: true, message: this.$t('reaCommon.pleaseEnter') + ' SECRET ID', trigger: 'blur'}
        ],
        secret_key: [
          {required: true, message: this.$t('reaCommon.pleaseEnter') + ' SECRET KEY', trigger: 'blur'}
        ],
        region: [
          {required: true, message: this.$t('reaCommon.pleaseEnter') + ' REGION', trigger: 'blur'}
        ],
        bucket: [
          {required: true, message: this.$t('reaCommon.pleaseEnter') + ' BUCKET', trigger: 'blur'}
        ],
        bucket_video: [
          {required: true, message: this.$t('reaCommon.pleaseEnter') + ' BUCKET VIDEO', trigger: 'blur'}
        ],
        collection_mode: [
          {required: true, message: this.$t('tenantAdd.acquisitionModePlaceholder'), trigger: 'blur'}
        ],
        dns_name: [
          {required: true, message: this.$t('reaCommon.pleaseEnter'), trigger: 'blur'},
        ],
        account_name: [
          {required: true, message: this.$t('reaCommon.pleaseEnter'), trigger: 'blur'},
        ],
        account_key: [
          {required: true, message: this.$t('reaCommon.pleaseEnter'), trigger: 'blur'},
        ],
        container_image: [
          {required: true, message: this.$t('reaCommon.pleaseEnter'), trigger: 'blur'},
        ],
        container_video: [
          {required: true, message: this.$t('reaCommon.pleaseEnter'), trigger: 'blur'},
        ],
        is_oversea: [
          {required: true, message: this.$t('reaCommon.pleaseEnter'), trigger: 'blur'},
        ],
        camera_group_enabled: [
          {required: true, message: this.$t('reaCommon.pleaseEnter'), trigger: 'blur'},
        ],
        sms_code_enabled: [
          {required: true, message: this.$t('reaCommon.pleaseEnter'), trigger: 'blur'},
        ],

      }
      this.setupStep[3].form.rules = {
        visit_type: [
          {type: 'array', required: true, message: this.$t('tenantAdd.chooseLeastOne'), trigger: 'blur'}
        ],
        door_image: [
          {required: true, message: '请选择门头照是否必拍', trigger: 'blur'}
        ],
        door_video: [
          {required: true, message: '请选择门视频是否必拍', trigger: 'blur'}
        ],
        sign: [
          {required: true, message: '请选择是否签到', trigger: 'blur'}
        ],
        distance: [
          {required: true, message: this.$t('tenantAdd.SignInEffectiveDistancePlaceholder'), trigger: 'blur'}
        ],
        config_effective: [
          {required: true, message: '请选择检查计划配置生效时间', trigger: 'blur'}
        ],
        organize: [
          {type: 'array', required: true, message: this.$t('tenantAdd.pleaseConfigOrganization'), trigger: 'blur'}
        ]
      }
      this.dialogRules = {
        name: [{required: true, message: this.$t('tenantAdd.departmentNamePlaceholder'), trigger: 'blur'}]
      }
      setTimeout(() => {
        if (this.$refs[this.setupStep[0].form.ref]) {
          this.$refs[this.setupStep[0].form.ref].clearValidate()
        }
        if (this.$refs[this.setupStep[1].form.ref]) {
          this.$refs[this.setupStep[1].form.ref].clearValidate()
        }
        if (this.$refs[this.setupStep[2].form.ref]) {
          this.$refs[this.setupStep[2].form.ref].clearValidate()
        }
        if (this.$refs[this.setupStep[3].form.ref]) {
          this.$refs[this.setupStep[3].form.ref].clearValidate()
        }
        if (this.$refs['dialogForm']) {
          this.$refs['dialogForm'].clearValidate()
        }
      })
    },
    changeThisStep(item) {
      if (this.checkActive === item.activeIndex) {
        return
      }
      if (this.model == 2) {
        if (this.$refs[this.selectedStep.form.ref]) {
          this.$refs[this.selectedStep.form.ref].validate((valid) => {
            if (valid) {
              this.selectedStep.completed = true
            } else {
              this.selectedStep.completed = false
            }
            this.$refs[this.selectedStep.form.ref].clearValidate()
            this.checkActive = item.activeIndex
          })
        }
      } else {
        this.checkActive = item.activeIndex
      }
    },
    getDetail() {
      this.fullLoading = true
      tenantDetail({id: this.id}).then((res) => {
        if (res.code == 1) {
          if (res.data && isObject(res.data.config)) {
            const detail = res.data.config
            Object.keys(detail).map((key) => {
              this.setupStep.map((v, index) => {
                if (key == v.key) {
                  if (key === 'image_recognition') {
                    detail[key]['options'] = isObject(detail[key]['options']) ? detail[key]['options'] : {}
                    if (isObject(detail[key]['options'])) {
                      detail[key]['option'] = Object.keys(detail[key]['options'])
                    }
                    if (!detail[key].hasOwnProperty('ir_sku_origin')) {
                      detail[key]['ir_sku_origin'] = 'ir'
                    }
                    if (!detail[key].hasOwnProperty('detect_platform')) {
                      detail[key]['detect_platform'] = '1'
                    } else {
                      detail[key].detect_platform = detail[key].detect_platform + ''
                    }
                    if (detail[key]['omni_config']) {
                      let {flow_id, api_url, api_secret} = detail[key]['omni_config']
                      detail[key]['flow_id'] = flow_id
                      detail[key]['api_url'] = api_url
                      detail[key]['api_secret'] = api_secret
                    }
                  }
                  if (key === 'rule_engine') {
                    if (!detail[key].hasOwnProperty('open_check_plan_menu')) {
                      detail[key]['open_check_plan_menu'] = true
                    }
                  }
                  if (key === 'collection') {
                    if (!detail[key].hasOwnProperty('photo_mode_list')) {
                      detail[key]['photo_mode_list'] = [1]
                    }
                  }
                  v.form.params = detail[key]
                  if (v.form.params.enable) {
                    this.moduleList[index].selected = true
                    v.completed = true
                  }
                }
              })
            })
            if (this.model == 1) {
              this.toStepView()
            }
          }
        }
      }).finally(() => {
        this.fullLoading = false
      })
    },
    toShowTree() {
      if (Array.isArray(this.setupStep[3].form.params.organize)) {
        this.organize = [...this.setupStep[3].form.params.organize]
      }
      this.toggleExpand(this.organize, true)
      this.isShowTree = true
    },
    addLevel1() {
      this.type = 'addLevel1'
      this.typeTitle = this.$t('tenantAdd.addLevelOneDepartment')
      this.dialogVisible = true
      if (this.$refs['dialogForm']) {
        this.$refs['dialogForm'].resetFields()
      }
    },
    addLevel1Data() {
      for (let i = 0; i < this.organize.length; i++) {
        if (this.organize[i].label === this.dialogForm.name) {
          this.$message.error(this.$t('tenantAdd.sameNameLevelOneDepartment'))
          return
        }
      }
      this.organize.push({
        id: this.getId(20),
        label: this.dialogForm.name
      })
      this.dialogVisible = false
    },
    treeCancel() {
      this.isShowTree = false
    },
    treeConfirm() {
      if (Array.isArray(this.organize)) {
        this.setupStep[3].form.params.organize = [...this.organize]
        this.$refs[this.setupStep[3].form.ref].clearValidate()
      }
      this.isShowTree = false
    },
    treeClose() {
      this.isShowTree = false
    },
    closeView() {
      const {path} = this.$route
      this.visitedViews.map((v) => {
        if (v.path === path) {
          this.$store.dispatch('tagsView/delView', v)
        }
      })
    },
    refreshSelectedTag() {
      let hasTag = false
      this.visitedViews.map((v) => {
        if (v.path === '/brand/manage') {
          hasTag = true
          this.$store.dispatch('tagsView/delCachedView', v).then(() => {
            const {fullPath} = v
            this.$nextTick(() => {
              this.$router.replace({
                path: '/redirect' + fullPath
              })
            })
          })
        }
      })
      if (!hasTag) {
        this.$router.push({
          path: '/brand/manage'
        })
      }
    },
    submit() {
      const stepList = this.setupStep.filter((item) => {
        return item.form.params.enable
      })
      for (let i = 0; i < stepList.length; i++) {
        let jumpOut = false
        if (this.$refs[stepList[i].form.ref]) {
          this.$refs[stepList[i].form.ref].validate((valid) => {
            if (valid) {
              stepList[i].completed = true
            } else {
              stepList[i].completed = false
              this.checkActive = stepList[i].activeIndex
              jumpOut = true
            }
          })
          if (jumpOut) {
            return
          }
        }
      }
      const config = {}
      this.setupStep.map((v) => {
        if (v.key === 'collection') {
          const childIdList = []
          v.form.params.scene.map((id) => {
            if (!this.treeParams.parentIds.includes(id)) {
              childIdList.push(id)
            }
          })
          v.form.params.scene = childIdList
          if (!v.form.params['photo_mode_list'].includes(1)) {
            v.form.params['photo_mode_list'].push(1)
          }
          v.form.params['origin_photo_mode_list'] = []
          this.originPhotoModeList.map((item, index) => {
            if (v.form.params['photo_mode_list'].includes(index)) {
              v.form.params['origin_photo_mode_list'].push(item)
            }
          })
          // 选择blob清空cos配置
          if (v.form.params.storage_type == 'BLOB') {
            v.form.params.bucket = ''
            v.form.params.bucket_video = ''
            v.form.params.region = ''
            v.form.params.secret_id = ''
            v.form.params.secret_key = ''
          } else {
            v.form.params.dns_name = ''
            v.form.params.account_name = ''
            v.form.params.account_key = ''
            v.form.params.container_image = ''
            v.form.params.container_video = ''
          }
        }
        if (v.key === 'image_recognition') {
          const options = {}
          v.form.params.option.map((attr) => {
            options[attr] = 1
          })
          v.form.params.options = options
        }
        if (v.key === 'rule_engine') {
          v.form.params.collection_mode = this.setupStep[0].form.params.collection_mode
        }
        config[v.key] = JSON.parse(JSON.stringify(v.form.params))
        if (v.key === 'image_recognition') {
          delete config[v.key].option
          let {flow_id, api_url, api_secret, project} = config[v.key]
          if (config[v.key].detect_platform == '1') {
            flow_id = ''
            api_url = ''
            api_secret = ''
          } else {
            project = ''
            config[v.key].options = {}
          }
          delete config[v.key].flow_id
          delete config[v.key].api_url
          delete config[v.key].api_secret
          config[v.key].project = project
          config[v.key].omni_config = {flow_id, api_url, api_secret}
        }
      })
      this.submitting = true
      const ajax = this.id ? tenantEdit : tenantCreate
      ajax({config: config, id: this.id || ''}).then((res) => {
        if (res.code == 1) {
          this.$message.success(this.$t('reaCommon.operationSuccess'))
          this.closeView()
          this.refreshSelectedTag()
          // this.$router.push({
          //   path: '/brand/manage'
          // })
        }
      }).finally(() => {
        this.submitting = false
      })
    },
    getId(length) {
      return Number(Math.random().toString().substr(3, length) + Date.now()).toString(36)
    },
    getRecognitionProjectList() {
      recognitionProjectList().then((res) => {
        if (res.code == 1 && Array.isArray(res.data)) {
          this.projectList = res.data
        }
      })
    },
    getSkuProjectList() {
      tenantProject().then((res) => {
        if (res.code == 1 && Array.isArray(res.data)) {
          this.skuProjectList = res.data
        }
      })
    },
    getSceneList() {
      sceneSystemList().then((res) => {
        if (res.code == 1 && Array.isArray(res.data)) {
          const parentIds = []
          res.data.map((v) => {
            v.id = this.getId(20) + '' + v.id
            parentIds.push(v.id)
          })
          this.treeParams.parentIds = parentIds
          this.treeParams.data = res.data
          if (this.$refs.treeSelect) {
            this.$refs.treeSelect.treeDataUpdateFun(res.data)
          }
        }
      })
    },
    back() {
      console.log('4听听丰富趣味')
      this.closeView()
      this.$router.push({
        path: '/brand/manage'
      })
    },
    changeStep(type, step) {
      // console.log(type, this.model, step.form.ref)
      if (step && step.isLast) {
        this.submit()
        return
      }
      const count = this.setupStep.filter((item) => {
        return item.form.params.enable
      }).length
      if (type) {
        if (this.model == 2) {
          this.$refs[step.form.ref].validate((valid) => {
            if (valid) {
              step.completed = true
            } else {
              step.completed = false
            }
            this.$refs[step.form.ref].clearValidate()
            if (this.checkActive < count) {
              this.checkActive++
            }
          })
        } else {
          if (this.checkActive < count) {
            this.checkActive++
          }
        }
      } else {
        if (this.checkActive > 0) {
          this.checkActive--
        } else {
          this.isShowModule = true
        }
      }
    },
    toStepView() {
      this.isShowModule = false
      let activeIndex = 0
      this.moduleList.map((v, k) => {
        if (v.selected) {
          this.setupStep[k].form.params.enable = true
          this.setupStep[k].activeIndex = activeIndex
          activeIndex++
        } else {
          this.setupStep[k].form.params.enable = false
          this.setupStep[k].activeIndex = -1
        }
      })
      const selectedModuleList = this.setupStep.filter((v) => {
        return v.form.params.enable
      })
      // 找出最后一步
      this.setupStep.map((m) => {
        if (m.key === selectedModuleList[selectedModuleList.length - 1].key) {
          m.isLast = true
        } else {
          m.isLast = false
        }
      })
      this.checkActive = 0
    },
    selectOwnTab(module) {
      if (this.model == 1) {
        return
      }
      // 模块之间关联训中 取消
      if (module.key === 'collection' && module.selected) {
        this.$message({
          message: this.$t('tenantAdd.collectModuleWarning'),
          type: 'warning'
        })
        return
      }
      if (module.key === 'plan' && module.selected) {
        this.$message({
          message: this.$t('tenantAdd.planModuleWarning'),
          type: 'warning'
        })
        return
      }
      if (module.selected) {
        this.moduleList.map((m, index) => {
          if (module.subModuleList.includes(index)) {
            m.selected = false
          }
        })
      } else {
        this.moduleList.map((m, index) => {
          if (module.addModuleList.includes(index)) {
            m.selected = true
          }
        })
      }
      module.selected = !module.selected
    },
    // 组织架构展开，合并
    toggleExpand(data, val) {
      const _this = this
      if (Array.isArray(data)) {
        data.forEach(function (item) {
          _this.$set(item, 'expand', val)
          if (item.children) {
            _this.toggleExpand(item.children, val)
          }
        })
      } else {
        this.$set(data, 'expand', val)
        if (data.children) {
          _this.toggleExpand(data.children, val)
        }
      }
    },
    collapse(list) {
      const that = this
      list.forEach(function (child) {
        if (child.expand) {
          child.expand = false
        }
        child.children && child.children.length && that.collapse(child.children)
      })
    },
    onExpand(e, data) {
      if ('expand' in data) {
        data.expand = !data.expand
        if (!data.expand && data.children && data.children.length) {
          this.collapse(data.children)
        }
      } else {
        this.$set(data, 'expand', true)
      }
    },
    // 编辑当前部门名称
    editNodeName() {
      if (Array.isArray(this.sameLevelList) && this.sameLevelList.length) {
        for (let i = 0; i < this.sameLevelList.length; i++) {
          if (this.sameLevelList[i]['label'] === this.dialogForm.name && this.sameLevelList[i].id !== this.selectNode.id) {
            this.$message.error(this.$t('tenantAdd.sameNameSameLevelDepartment'))
            return
          }
        }
        this.selectNode.label = this.dialogForm.name
        this.selectNode.change = 1
        this.dialogVisible = false
      }
    },
    // 删除当前节
    deleteNode() {
      if (Array.isArray(this.sameLevelList) && this.sameLevelList.length) {
        for (let i = 0; i < this.sameLevelList.length; i++) {
          if (this.sameLevelList[i]['id'] === this.selectNode.id) {
            this.sameLevelList.splice(i, 1)
            this.dialogVisible = false
          }
        }
      }
    },
    // 添加同级部门
    addSameLevelNode() {
      if (Array.isArray(this.sameLevelList) && this.sameLevelList.length) {
        for (let i = 0; i < this.sameLevelList.length; i++) {
          if (this.sameLevelList[i]['label'] === this.dialogForm.name) {
            this.$message.error(this.$t('tenantAdd.sameNameSameLevelDepartment'))
            return
          }
        }
        this.sameLevelList.push({
          id: (+new Date()),
          label: this.dialogForm.name
        })
        this.dialogVisible = false
      }
    },
    // 获取同级兄弟部门数组
    getSameLevel(data, id) {
      if (Array.isArray(data) && data.length) {
        for (let i = 0; i < data.length; i++) {
          if (data[i].id === id) {
            this.sameLevelList = data
            return
          }
          this.getSameLevel(data[i].children, id)
        }
      }
    },
    // 添加下级部门
    addLowerLevelNode() {
      if (this.selectNode.children && this.selectNode.children.length) {
        for (let i = 0; i < this.selectNode.children.length; i++) {
          if (this.selectNode.children[i]['label'] === this.dialogForm.name) {
            this.$message.error(this.$t('tenantAdd.sameNameChildDepartment'))
            return
          }
        }
        this.selectNode.children.push({
          id: (+new Date()),
          label: this.dialogForm.name
        })
        this.dialogVisible = false
      } else {
        this.$set(this.selectNode, 'expand', true)
        this.$set(this.selectNode, 'children', [{id: (+new Date()), label: this.dialogForm.name}])
        this.dialogVisible = false
      }
    },
    dialogCancel() {
      this.dialogVisible = false
    },
    dialogConfirm() {
      this.$refs['dialogForm'].validate((valid) => {
        if (valid) {
          switch (this.type) {
            case 'sameLevel':
              this.addSameLevelNode()
              break
            case 'lowerLevel':
              this.addLowerLevelNode()
              break
            case 'edit':
              this.editNodeName()
              break
            case 'addLevel1':
              this.addLevel1Data()
              break
          }
        }
      })
    },
    dialogClose() {
      // this.dialogVisible = false
    },
    renderContent(h, data) {
      return (
        <el-dropdown trigger='click' onCommand={(key) => this.handleCommand(key, data)}>
          <el-button class={this.model == 1 ? 'disabledTreeClass' : ''} style='cursor:pointer' size='medium'
                     disabled={this.model == 1}>{data.label}</el-button>
          <el-dropdown-menu slot='dropdown'>
            <el-dropdown-item command='sameLevel'>{this.$t('tenantAdd.addSameDepartment')}</el-dropdown-item>
            <el-dropdown-item command='lowerLevel'>{this.$t('tenantAdd.addChildDepartment')}</el-dropdown-item>
            <el-dropdown-item command='edit'>{this.$t('tenantAdd.changeDepartmentName')}</el-dropdown-item>
            <el-dropdown-item command='delete'>{this.$t('tenantAdd.deleteDepartment')}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      )
    },
    handleCommand(key, node) {
      this.type = key
      this.selectNode = node
      switch (key) {
        case 'sameLevel':
          this.typeTitle = `[${node.label}] ${this.$t('tenantAdd.addSameDepartment')}`
          this.dialogVisible = true
          if (this.$refs['dialogForm']) {
            this.$refs['dialogForm'].resetFields()
          }
          this.getSameLevel(this.organize, this.selectNode.id)
          break
        case 'lowerLevel':
          this.typeTitle = `[${node.label}] ${this.$t('tenantAdd.addChildDepartment')}`
          this.dialogVisible = true
          if (this.$refs['dialogForm']) {
            this.$refs['dialogForm'].resetFields()
          }
          break
        case 'edit':
          this.typeTitle = `[${node.label}] ${this.$t('tenantAdd.changeName')}`
          this.dialogVisible = true
          this.dialogForm.name = this.selectNode.label
          this.getSameLevel(this.organize, this.selectNode.id)
          break
        case 'delete':
          this.getSameLevel(this.organize, this.selectNode.id)
          if (node.children && node.children.length) {
            this.$message.error(this.$t('tenantAdd.haveChild'))
          } else {
            this.$confirm(`${this.$t('reaCommon.confirmDelete')} [${node.label}]?`, this.$t('reaCommon.warning'), {
              confirmButtonText: this.$t('reaCommon.confirm'),
              cancelButtonText: this.$t('reaCommon.cancel'),
              type: 'warning'
            }).then(() => {
              this.deleteNode()
            })
          }
          break
      }
    }
  }
}
</script>
<style lang="scss">
@import "~@/styles/variables.scss";

.module_config_view {
  .disabledTreeClass {
    color: #777 !important;
  }

  .cursor {
    cursor: pointer;
  }

  .text__align--center {
    text-align: center;
  }

  .plan__steps {
    width: 228px;

    .el-step__line {
      display: none !important;
    }

    .el-step.is-vertical {
      -webkit-flex-direction: row-reverse;
      -moz-flex-direction: row-reverse;
      -ms-flex-direction: row-reverse;
      -o-flex-direction: row-reverse;
      flex-direction: row-reverse;
      flex-direction: row-reverse;
    }

    .el-step__main {
      margin-bottom: 28px;
    }

    .el-step__icon {
      width: 20px;
      height: 20px;
      font-size: 12px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #b7b7b7;
      border: 1px solid #b7b7b7;
    }

    .is-success {
      &.el-step__head {
        border-color: $themeColor;
      }

      .el-step__icon {
        border-color: $themeColor;
        background-color: $themeColor;
      }

      .el-step__icon-inner {
        color: #fff;
      }
    }

    .el-step__title {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #B7B7B7;
      line-height: 20px;
      cursor: pointer;
    }

    .el-step__title.is-success {
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: $themeColor;
      line-height: 20px;
    }

    .el-step__title.is-process {
      color: #262626;
    }

    .el-step__icon-inner {
      color: #b7b7b7;
    }

    .is-process {
      color: $themeColor;
      border-color: $themeColor;

      .el-step__icon {
        border-color: $themeColor;
        box-sizing: border-box;
      }

      .el-step__icon-inner {
        color: $themeColor;
      }
    }

    .el-step.is-vertical .el-step__line {
      top: 1px;
      left: 8px;
      width: 4px;
      bottom: -1px;
      background-color: #f2f2f2;

      .el-step__line-inner {
        border-width: 2px !important;
        border-color: #f2f2f2 !important;
      }
    }

    .el-step.is-vertical .is-success .el-step__line {
      background-color: $themeColor;

      .el-step__line-inner {
        border-color: $themeColor !important;
      }
    }

    .el-step.is-vertical .is-process .el-step__line {
      background-color: $themeColor;

      .el-step__line-inner {
        border-color: $themeColor !important;
      }
    }
  }

  .plan__add--contain1 {
    min-height: 75vh;
    position: relative;
    padding: 24px;

    .step_full_vertical_line {
      position: absolute;
      left: 250px;
      width: 2px;
      top: 24px;
      bottom: 0;
      background: #e6e6e6;
    }

    .plan__sale--tree {
      background: #fff;
      z-index: 2;
      top: 45px !important;

      .el-tree {
        padding: 20px 0;
        box-shadow: 0px 6px 21px 0px rgba(0, 0, 0, 0.11);
        max-height: 300px;
        overflow-y: scroll;
      }
    }

    /*设计要求修改字体大小 start*/
    .plan__add--sale {
      .distribution__point .title {
        margin-bottom: 23px !important;
      }

      .el-form-item {
        margin-bottom: 30px;
      }
    }

    .button__next {
      margin-top: 88px;
    }

    .el-form-item__content {
      line-height: 40px;
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #262626;

      .el-input--medium .el-input__inner, .el-range-editor--medium.el-input__inner {
        height: 40px;
        line-height: 40px;
        font-size: 14px;
      }

      .el-range-editor--medium .el-range-input {
        font-size: 14px;
      }

      .el-range-editor--medium .el-range-separator {
        line-height: 34px;
      }
    }

    /*设计要求修改字体大小 end*/

    .distribution__point {
      .title {
        font-size: 20px;
        color: $themeColor;
        margin-bottom: 15px;
      }
    }

    .distribution__button {
      text-align: center;
      margin-top: 80px;
    }

    .plan__add--form {
      margin-left: 128px;
      width: 60%;
      padding-bottom: 69px;

      .absolute {
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
      }

      .el-range-editor.el-input__inner {
        width: 100%;
      }

      .el-form-item__label {
        font-weight: 500;
        font-size: 14px;
        line-height: 40px;
        font-family: PingFangSC-Medium, PingFang SC;
        color: #262626;
      }

      .plan__sale--des {
        font-size: 13px;

        span {
          color: $themeColor;
        }
      }
    }

  }

  .dialog_container {
    line-height: 20px;
  }

  .button__import {
    border: 1px dashed #DADADA;
    border-radius: 4px;
    text-align: center;
  }

  .hasVal {
    color: $themeColor !important;
    border-color: $themeColor !important;
  }
}
</style>
<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.module_title {
  font-size: 20px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #262626;
  line-height: 28px;
  padding: 8px 0 11px;
}

.module_container {
  padding-left: 22px;
}

.module_logo_box {

}

.module_one {
  margin-top: 16px;
  height: 196px;
  width: 262px;
  position: relative;
  margin-right: 25px;

}

.module_one_view {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  cursor: pointer;
  background: #FFFFFF;
  box-shadow: 0px 4px 18px 0px rgba(0, 0, 0, 0.1);
  border-radius: 6px;
}

.module_one_view:hover {
  box-shadow: 0px 4px 18px 0px rgba(0, 0, 0, 0.4);
}

.module_one_view.selected {
}

.module_selected_icon {
  position: absolute;
  right: 0;
  top: 0;
  width: 32px;
  height: 32px;
  background: #0471D4;
  border-radius: 0px 6px 0px 6px;
  line-height: 32px;
  text-align: center;
}

.module_col_tit {
  font-size: 18px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #0471D4;
  line-height: 20px;
  padding-top: 15px;
}

.img_box {
  width: 30%;
  height: auto;
}

.bottom_btn_box {
  text-align: center;
  padding-top: 154px;
  padding-bottom: 80px;
}

.description {
  font-size: 20px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  line-height: 28px;
  text-indent: 128px;
  padding-bottom: 22px;
}

.sign_in_input::v-deep .el-input__inner {
  padding-right: 60px;
}

.sign_in_input::v-deep .el-input__suffix {
  right: 25px;
}

.step_container {
  width: 228px;

  .check_step {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #262626;
    line-height: 20px;
    padding: 10px 24px;
    cursor: pointer;
    position: relative;
    z-index: 1;
    border-right: 4px solid transparent;

    &.selected {
      background: #CDE5FA;
      border-right: 4px solid $themeColor;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: $themeColor;
    }
  }
}

.module_form_btn {
  padding-top: 47px;
}

.all_complete_btn {
  border-bottom: 1px solid #E7EAEC;
  text-align: right;
  margin: 0 24px;
  padding: 15px 0;
}

.step_container_view {
  box-shadow: 0px 8px 10px 0px rgba(0, 0, 0, 0.02);
  border-radius: 8px;
}

.step_count_box {
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  border-radius: 50%;
  font-size: 12px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #DDDDDD;

  &.process {
    background-color: $themeColor;
    color: white;
  }

  &.wait {
    background: #FFFFFF;
    border: 1px solid #DDDDDD;
  }

  &.success {

  }
}
</style>
