<template>
  <div class="forgot-container">
    <div class="logo_header flex flex-y-center flex-x-start">
      <img :src="lingmouLogo" alt="" class="lingmou_logo">
      <div class="header_title">{{ $t('login.retailEyeSystemName') }}</div>
    </div>
    <div class="forgot_sub_title">
      {{ (resetPassword || token) ? $t('reaCommon.updatePassword') : $t('reaCommon.forgotPassword') }}
    </div>
    <div class="forgot_main">
      <div v-if="step == 1" class="step_one" :class="{'en':language == 'en'}">
        <div class="forgot_main_title">{{ $t('reaCommon.pleaseEnterPhoneReceiveCode') }}</div>
        <div>
          <el-form ref="stepOneForm" size="medium" :model="stepOneForm" :rules="stepOneRules" class="el_form_box">
            <el-form-item label="" prop="phone">
              <el-input v-model="stepOneForm.phone" :disabled="resetPassword == 1"
                        :placeholder="$t('reaCommon.phonePlaceholder')" type="text" clearable/>
            </el-form-item>
            <el-form-item label="" prop="code">
              <div class="flex flex-y-center flex-x-start">
                <el-input v-model="stepOneForm.code" type="text"
                          :placeholder="$t('reaCommon.pleaseEnterVerificationCode')" clearable/>
                <el-button style="margin-left:6px;text-align: center;" class="cn_code_btn"
                           :class="{'en':language == 'en'}" :disabled="!sending" :loading="isGetting"
                           @click.native="toGetCode">{{ codeText }}
                </el-button>
              </div>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" style="width:100%;" @click.native="changeStep">{{
                  $t('reaCommon.nextStep')
                }}
              </el-button>
              <div style="text-align:right;padding-top:20px;">
                <el-button type="text" icon="el-icon-arrow-left" @click.stop="back">{{
                    $t('reaCommon.back')
                  }}
                </el-button>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div v-show="step == 2" class="step_one" :class="{'en':language == 'en'}">
        <div class="forgot_main_title">{{ $t('reaCommon.passwordRequirements') }}</div>
        <div>
          <el-form ref="stepTwoForm" size="medium" :model="stepTwoForm" :rules="stepTwoRules" class="el_form_box">
            <el-form-item prop="password">
              <el-input v-model="stepTwoForm.password" :placeholder="$t('reaCommon.pleaseEnterNewPassword')"
                        type="password" show-password/>
            </el-form-item>
            <el-form-item prop="confirm">
              <el-input v-model="stepTwoForm.confirm" type="password"
                        :placeholder="$t('reaCommon.pleaseConfirmNewPassword')" show-password/>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" style="width:100%;" :disabled="isSubmitting" @click.native="submit">
                {{ $t('reaCommon.complete') }}
              </el-button>
              <div style="text-align:right;padding-top:20px;">
                <el-button type="text" icon="el-icon-arrow-left" @click.stop="back">{{
                    $t('reaCommon.back')
                  }}
                </el-button>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

import {mapGetters} from 'vuex'
import lingmouLogo from '@/assets/images/<EMAIL>'
import {sendCode, submitForgotPassword, toVerificationCode} from '@/api/user'

export default {
  name: 'login',
  data() {
    const verifyLength = (rule, value, callback) => {
      if (value.length < 8 || value.length > 16) {
        callback(new Error(this.$t('reaCommon.passwordLength')))
      } else {
        callback()
      }
    }
    const verifySpecialCode = (rule, value, callback) => {
      // 1.全部包含：大写、小写、数字、特殊字符；
      const regex1 = '(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[\\W_])^.*$'
      // 2.无大写：小写、数字、特殊字符；
      const regex2 = '(?=.*[a-z])(?=.*[0-9])(?=.*[\\W_])^.*$'
      // 3.无小写：大写、数字、特殊字符；
      const regex3 = '(?=.*[A-Z])(?=.*[0-9])(?=.*[\\W_])^.*$'
      // 4.无数字：大写、小写、特殊字符；
      const regex4 = '(?=.*[A-Z])(?=.*[a-z])(?=.*[\\W_])^.*$'
      // 5.无特殊字符：大写、小写、数字；
      const regex5 = '(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])^.*$'
      const reg = '(' + regex1 + ')|(' + regex2 + ')|(' + regex3 + ')|(' + regex4 + ')|(' + regex5 + ')'
      const pwdRegex = new RegExp(reg)
      if (pwdRegex.test(value)) {
        callback()
      } else {
        callback(new Error(this.$t('reaCommon.passwordSpecialVerify')))
      }
    }
    const equalToPassword = (rule, value, callback) => {
      if (this.stepTwoForm.password !== value) {
        callback(new Error(this.$t('reaCommon.notSamePassword')))
      } else {
        callback()
      }
    }
    return {
      resetPassword: this.$route.query.reset_password || '',
      isGetting: false,
      codeText: this.$t('reaCommon.verificationCode'),
      second: 60,
      sending: true,
      step: 1,
      stepOneForm: {
        phone: this.$route.query.phone || '',
        code: ''
      },
      stepOneRules: {
        phone: [
          {required: true, trigger: 'blur', message: this.$t('reaCommon.phonePlaceholder')}
        ],
        code: [{required: true, trigger: 'blur', message: this.$t('reaCommon.pleaseEnterVerificationCode')}]
      },
      stepTwoForm: {
        password: '',
        confirm: ''
      },
      stepTwoRules: {
        password: [
          {required: true, trigger: 'blur', message: this.$t('reaCommon.pleaseEnterNewPassword')},
          {required: true, validator: verifyLength, trigger: 'blur'},
          {required: true, validator: verifySpecialCode, trigger: 'blur'}
        ],
        confirm: [
          {required: true, trigger: 'blur', message: this.$t('reaCommon.pleaseConfirmNewPassword')},
          {required: true, validator: verifyLength, trigger: 'blur'},
          {required: true, validator: verifySpecialCode, trigger: 'blur'},
          {required: true, validator: equalToPassword, trigger: 'blur'}
        ]
      },
      lingmouLogo,
      loading: false,
      passwordType: 'password',
      redirect: undefined,
      isSubmitting: false
    }
  },
  computed: {
    ...mapGetters([
      'language',
      'token'
    ])
  },
  watch: {
    '$i18n.locale'() {
      this.setRules()
    }
  },
  mounted() {
    if (window.history && window.history.pushState) {
      window.history.pushState(null, null, document.URL)
      window.addEventListener('popstate', this.backFn, false)
    }
    this.setRules()
  },
  destroyed() {
    // 销毁监听
    window.removeEventListener('popstate', this.backFn, false)
  },
  methods: {
    backFn() {
      // console.log(e)
      if (this.resetPassword) {
        this.$confirm(this.$t('reaCommon.passwordNotChange'), this.$t('reaCommon.warning'), {
          confirmButtonText: this.$t('reaCommon.confirm'),
          cancelButtonText: this.$t('reaCommon.cancel'),
          type: 'warning'
        }).then(() => {
          window.history.pushState(null, null, document.URL)
        }).catch(() => {
          this.$router.replace('/')
        })
      } else {
        this.$router.replace('/')
      }
    },
    back() {
      if (this.resetPassword) {
        this.$confirm(this.$t('reaCommon.passwordNotChange'), this.$t('reaCommon.warning'), {
          confirmButtonText: this.$t('reaCommon.confirm'),
          cancelButtonText: this.$t('reaCommon.cancel'),
          type: 'warning'
        }).then(() => {
        }).catch(() => {
          this.$router.replace('/')
        })
      } else {
        this.$router.replace('/')
      }
    },
    submit() {
      if (this.isSubmitting) {
        return
      }
      this.$refs['stepTwoForm'].validate((valid) => {
        if (valid) {
          submitForgotPassword({
            phone: this.stepOneForm.phone,
            code: this.stepOneForm.code,
            password: this.stepTwoForm.password,
            confirm: this.stepTwoForm.confirm
          }).then((res) => {
            if (res.code == 1) {
              this.$message.success(this.$t('reaCommon.operationSuccess'))
              this.$store.dispatch('tagsView/delAllViews')
              this.$store.dispatch('user/logout').then(() => {
                this.$router.replace('/')
              })
            }
          }).catch((err) => {
            if (err && err.sub_code == -1) {
              this.step = 1
              this.stepOneForm.code = ''
            }
          }).finally(() => {
            setTimeout(() => {
              this.isSubmitting = false
            }, 200)
          })
        }
      })
    },
    toGetCode() {
      if (this.sending) {
        this.$refs.stepOneForm.validateField('phone', (valid) => {
          if (!valid) {
            this.isGetting = true
            sendCode({phone: this.stepOneForm.phone}).then((res) => {
              if (res.code == 1) {
                this.timeDown()
                this.sending = false
              }
            }).finally(() => {
              this.isGetting = false
            })
          }
        })
      }
    },
    // 60秒倒计时
    timeDown() {
      this.codeText = this.second
      const result = setInterval(() => {
        --this.second
        this.codeText = this.second
        if (this.second < 1) {
          clearInterval(result)
          this.sending = true
          // this.disabled = false;
          this.second = 60
          this.codeText = this.$t('reaCommon.verificationCode')
        }
      }, 1000)
    },
    changeStep() {
      this.$refs['stepOneForm'].validate((valid) => {
        if (valid) {
          toVerificationCode(this.stepOneForm).then((res) => {
            if (res.code == 1) {
              this.step = 2
              this.stepTwoForm = {
                password: '',
                confirm: ''
              }
              setTimeout(() => {
                if (this.$refs['stepTwoForm']) {
                  this.$refs['stepTwoForm'].clearValidate()
                }
              })
            }
          })
        }
      })
    },
    setRules() {
      const verifyLength = (rule, value, callback) => {
        if (value.length < 8 || value.length > 16) {
          callback(new Error(this.$t('reaCommon.passwordLength')))
        } else {
          callback()
        }
      }
      const verifySpecialCode = (rule, value, callback) => {
        // 1.全部包含：大写、小写、数字、特殊字符；
        const regex1 = '(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[\\W_])^.*$'
        // 2.无大写：小写、数字、特殊字符；
        const regex2 = '(?=.*[a-z])(?=.*[0-9])(?=.*[\\W_])^.*$'
        // 3.无小写：大写、数字、特殊字符；
        const regex3 = '(?=.*[A-Z])(?=.*[0-9])(?=.*[\\W_])^.*$'
        // 4.无数字：大写、小写、特殊字符；
        const regex4 = '(?=.*[A-Z])(?=.*[a-z])(?=.*[\\W_])^.*$'
        // 5.无特殊字符：大写、小写、数字；
        const regex5 = '(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])^.*$'
        const reg = '(' + regex1 + ')|(' + regex2 + ')|(' + regex3 + ')|(' + regex4 + ')|(' + regex5 + ')'
        const pwdRegex = new RegExp(reg)
        if (pwdRegex.test(value)) {
          callback()
        } else {
          callback(new Error(this.$t('reaCommon.passwordSpecialVerify')))
        }
      }
      const equalToPassword = (rule, value, callback) => {
        if (this.stepTwoForm.password !== value) {
          callback(new Error(this.$t('reaCommon.notSamePassword')))
        } else {
          callback()
        }
      }
      const validatePhone = (rule, value, callback) => {
        if (value) {
          if (/^1[3-9]\d{9}$/.test(value)) {
            callback()
          } else {
            callback(new Error(this.$t('reaCommon.phoneError')))
          }
        } else {
          callback()
        }
      }
      this.stepOneRules = {
        phone: [
          {required: true, trigger: 'blur', message: this.$t('reaCommon.phonePlaceholder')},
          {required: true, trigger: 'blur', validator: validatePhone}
        ],
        code: [{required: true, trigger: 'blur', message: this.$t('reaCommon.pleaseEnterVerificationCode')}]
      }

      this.stepTwoRules = {
        password: [
          {required: true, trigger: 'blur', message: this.$t('reaCommon.pleaseEnterNewPassword')},
          {required: true, validator: verifyLength, trigger: 'blur'},
          {required: true, validator: verifySpecialCode, trigger: 'blur'}
        ],
        confirm: [
          {required: true, trigger: 'blur', message: this.$t('reaCommon.pleaseConfirmNewPassword')},
          {required: true, validator: verifyLength, trigger: 'blur'},
          {required: true, validator: verifySpecialCode, trigger: 'blur'},
          {required: true, validator: equalToPassword, trigger: 'blur'}
        ]
      }
      setTimeout(() => {
        if (this.$refs['stepOneForm']) {
          this.$refs['stepOneForm'].clearValidate()
        }
        if (this.$refs['stepTwoForm']) {
          this.$refs['stepTwoForm'].clearValidate()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.logo_header {
  height: 88px;
  background-color: $themeColor;
  padding-left: 32px;
}

.lingmou_logo {
  height: 30px;
}

.header_title {
  margin-left: 22px;
  font-size: 24px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 36px;
}

.forgot_sub_title {
  font-size: 36px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 50px;
  padding: 50px 0;
  text-align: center;
}

.forgot_main {
  margin: 0 130px;
  background-color: white;
}

.forgot_main_title {
  text-align: center;
  font-size: 18px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #666666;
  line-height: 25px;
  padding-bottom: 29px;
  margin: 0 -130px;
}

.step_one {
  width: 402px;
  margin: 0 auto;
  padding-top: 33px;
  padding-bottom: 33px;
}

.step_one.en {
  width: 502px;
}

.el_form_box ::v-deep .el-form-item {
  margin-bottom: 34px;
}

.el_form_box ::v-deep .el-input__inner {
  height: 48px;
  line-height: 48px;
  font-size: 14px;
}

.el_form_box ::v-deep .el-button--primary {
  height: 48px;
}

.el_form_box ::v-deep .el-button--primary span {
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 20px;
}

.el_form_box ::v-deep .el-button--default {
  border-color: $themeColor;
  color: $themeColor;
  height: 48px;
  padding: 10px 0 !important;
}

.cn_code_btn {
  width: 130px;
}

.cn_code_btn.en {
  width: 250px;
}

.cn_code_btn.is-disabled {
  color: #c0c4cc !important;
  cursor: not-allowed;
  background-image: none;
  background-color: #fff !important;
  border-color: #ebeef5 !important;

}

</style>
