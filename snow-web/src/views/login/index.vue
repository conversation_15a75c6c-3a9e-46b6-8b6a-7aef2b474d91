<template>
  <div class="login_box">
    <div class="login_container" :class="{'show':already}">
      <img :src="lingmouLogo" alt="" class="lingmou_logo">
      <img :src="rightTopLogo" alt="" class="right_top_logo">
      <div class="">
        <img :src="banner" alt="" class="left_banner">
        <div class="right_box">
          <div class="flex flex-y-center flex-x-start" style="width:612px;">
            <div class="welcome_txt">{{ $t('login.welcomeToUse') }}<span v-if="language === 'en'"
                                                                         style="visibility: hidden">a</span></div>
            <div class="lingmou_txt" :class="{'ml8':language !== 'en'}">{{ $t('login.retailEyeSystemName') }}</div>
          </div>
          <div class="el_form_box" :class="{'en':language == 'en'}">
            <el-form ref="loginForm" size="medium" :model="loginForm" :rules="loginRules" class="login-form"
                     auto-complete="on" label-position="left">
              <el-form-item prop="username">
                <el-form-item prop="phone">
                  <el-input
                    ref="phone"
                    v-model="loginForm.phone"
                    :placeholder="$t('reaCommon.phonePlaceholder')"
                    name="phone"
                    type="text"
                    tabindex="1"
                    auto-complete="on"
                    clearable
                    @keyup.enter.native="handleLogin"
                  />
                </el-form-item>

                <el-form-item prop="password">
                  <el-input
                    :key="passwordType"
                    ref="password"
                    v-model="loginForm.password"
                    clearable
                    :type="passwordType"
                    :placeholder="$t('login.pleaseEnterPassword')"
                    name="password"
                    tabindex="2"
                    auto-complete="on"
                    @keyup.enter.native="handleLogin"
                  />
                </el-form-item>

                <el-form-item v-if="status" prop="code">
                  <div class="flex flex-y-center flex-x-start">
                    <el-input v-model="loginForm.code" type="text"
                              :placeholder="$t('reaCommon.pleaseEnterVerificationCode')" clearable/>
                    <el-button style="margin-left:6px;text-align: center;" class="cn_code_btn"
                               :class="{'en':language == 'en'}" :disabled="!sending" :loading="isGetting"
                               @click.native="toGetCode">{{ codeText }}
                    </el-button>
                  </div>
                </el-form-item>

                <el-button :loading="loading" type="primary" style="width:100%;" @click.native.prevent="handleLogin">
                  {{ $t('login.login') }}
                </el-button>
                <div class="flex flex-y-center flex-x-end login_lang_change">
                  <div class="forgot_password_box" v-if="status">
                    <el-button type="text" size="small" @click.native="toResetPassword">
                      {{ $t('reaCommon.forgotPassword') }}
                    </el-button>
                  </div>
                  <lang-select view="login"/>
                </div>

              </el-form-item>
            </el-form>

          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {sendCode, checkTwoFactors} from '@/api/user'
import LangSelect from '@/components/LangSelect'
import banner from '@/assets/images/<EMAIL>'
import lingmouLogo from '@/assets/images/<EMAIL>'
import rightTopLogo from '@/assets/images/<EMAIL>'
import {mapGetters} from 'vuex'

export default {
  name: 'login',
  components: {
    LangSelect
  },
  data() {
    return {
      isGetting: false,
      codeText: this.$t('reaCommon.verificationCode'),
      second: 60,
      sending: true,
      status: false,
      lingmouLogo,
      rightTopLogo,
      banner,
      already: false,
      loginForm: {
        phone: '',
        password: '',
        // phone: '14011112222',
        // password: '123456!q',
        code: ''
      },
      loginRules: {
        phone: [
          {required: true, trigger: 'blur', message: this.$t('reaCommon.phonePlaceholder')}
        ],
        password: [{required: true, trigger: 'blur', message: this.$t('login.pleaseEnterPassword')}],
        code: [{required: true, trigger: 'blur', message: this.$t('reaCommon.pleaseEnterVerificationCode')}]
      },
      loading: false,
      passwordType: 'password',
      redirect: undefined
    }
  },
  computed: {
    ...mapGetters([
      'language'
    ])
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    },
    '$i18n.locale'() {
      this.setLoginRules()
    }
  },
  mounted() {
    // this.getLoginConfig()
    this.setLoginRules()
    this.$nextTick(() => {
      setTimeout(() => {
        this.already = true
      }, 100)
    })
  },
  methods: {
    toGetCode() {
      if (this.sending) {
        this.$refs.loginForm.validateField('phone', (valid) => {
          if (!valid) {
            this.isGetting = true
            sendCode({phone: this.loginForm.phone}).then((res) => {
              if (res.code == 1) {
                this.timeDown()
                this.sending = false
              }
            }).finally(() => {
              this.isGetting = false
            })
          }
        })
      }
    },
    // 60秒倒计时
    timeDown() {
      this.codeText = this.second
      const result = setInterval(() => {
        --this.second
        this.codeText = this.second
        if (this.second < 1) {
          clearInterval(result)
          this.sending = true
          // this.disabled = false;
          this.second = 60
          this.codeText = this.$t('reaCommon.verificationCode')
        }
      }, 1000)
    },
    getLoginConfig() {
      checkTwoFactors({}).then((res) => {
        if (res.code == 1) {
          this.status = res.data.status
        }
      })
    },
    toResetPassword() {
      this.$router.push({path: '/forgot-password'})
    },
    setLoginRules() {
      this.loginRules = {
        phone: [
          {required: true, trigger: 'blur', message: this.$t('reaCommon.phonePlaceholder')}
        ],
        password: [{required: true, trigger: 'blur', message: this.$t('login.pleaseEnterPassword')}],
        code: [{required: true, trigger: 'blur', message: this.$t('reaCommon.pleaseEnterVerificationCode')}]
      }
      this.codeText = this.$t('reaCommon.verificationCode')
      setTimeout(() => {
        if (this.$refs['loginForm']) {
          this.$refs['loginForm'].clearValidate()
        }
      })
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          this.$store.dispatch('user/login', this.loginForm).then((data) => {
            // 清除搜索条件
            localStorage.removeItem('queryParams')
            // this.$router.push({ path: this.redirect || '/' })
            // this.$router.push({ path: '/' })
            if (data.reset_password) {
              this.$router.push({
                path: '/forgot-password', query: {
                  reset_password: 1,
                  phone: this.loginForm.phone
                }
              })
            } else {
              this.$router.push({path: '/'})
            }
            this.loading = false
          }).catch(() => {
            this.loading = false
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
$bg: #fff;
$dark_gray: #889aa4;
$light_gray: #eee;
@import "~@/styles/variables.scss";
.login_box {
  height: 100vh;
  width: 100%;
  background: linear-gradient(215deg, #33C3FF 0%, #0070D6 100%);
  position: relative;
  overflow: hidden;
}

.login_container {
  width: 1040px;
  height: 646px;
  background: #FFFFFF;
  border-radius: 28px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: opacity .3s;
  opacity: 0;

  .lingmou_logo {
    height: 38px;
    width: 156px;
    position: absolute;
    top: 33px;
    left: 36px;
  }

  .right_top_logo {
    width: 77px;
    height: 87px;
    position: absolute;
    right: 56px;
    top: -88px;
  }

  .login-form {
    position: relative;
    width: 100%;
    margin: 0 auto;
    overflow: hidden;
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-size: 30px;
      font-family: PingFang SC;
      font-weight: 500;
      color: #0471D4;
    }

    .vertical_line {
      width: 1px;
      height: 14px;
      background: #3C85C6;
      opacity: 0.48;
      margin: 0 15px;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }
}

.login_container.show {
  opacity: 1;
}

.left_banner {
  width: 527px;
  height: 521px;
  position: absolute;
  top: 200px;
  left: -141px;
}

.right_box {
  padding-left: 407px;
  padding-top: 148px;
}

.logo_img {
  width: 228px;
  height: 57px;
  margin-top: 4.26vh;
  margin-left: 3.7vh;
}

.banner_img {
  width: 567px;
  height: 584px;
  margin-top: 17vh;
}

.welcome_txt {
  height: 40px;
  font-size: 28px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #262626;
  line-height: 40px;
}

.lingmou_txt {
  height: 50px;
  font-size: 36px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #0471D4;
  line-height: 50px;
}

.lingmou_txt.ml8 {
  margin-left: 8px;
}

.el_form_box {
  padding-left: 6px;
  width: 408px;
  padding-top: 48px;
}

.el_form_box.en {
  width: 508px;
}

.el_form_box ::v-deep .el-form-item {
  margin-bottom: 34px;
}

.el_form_box ::v-deep .el-input__inner {
  height: 48px;
  line-height: 48px;
  font-size: 14px;
}

.el_form_box ::v-deep .el-button--primary {
  height: 48px;
}

.el_form_box ::v-deep .el-button--primary span {
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 20px;
}

.login_lang_change {
  margin-top: 12px;
}

.login_lang_change::v-deep .language_title {
  font-size: 12px !important;
}

.cn_code_btn {
  width: 130px;
}

.cn_code_btn.en {
  width: 250px;
}

.cn_code_btn.is-disabled {
  color: #c0c4cc !important;
  cursor: not-allowed;
  background-image: none;
  background-color: #fff !important;
  border-color: #ebeef5 !important;

}

.el_form_box ::v-deep .el-button--default {
  border-color: $themeColor;
  color: $themeColor;
  height: 48px;
  padding: 10px 0 !important;
  text-align: center;
}

.forgot_password_box {
  margin-right: 14px;
}

.forgot_password_box ::v-deep .el-button--text {
  color: #262626 !important;
}
</style>
