import Vue from 'vue'
import {message} from '@/utils/resetMessage'
import common from '@/utils/common'
import Config from './config'
import 'normalize.css/normalize.css' // A modern alternative to CSS resets

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import 'element-ui/lib/theme-chalk/icon.css'

import '@/styles/index.scss' // global css
import '@/styles/vue2-org-tree.scss'
// import '@google/model-viewer/dist/model-viewer.min.js'

import App from './App'
import store from './store'
import router, {resetRouter} from './router'
import i18n from './lang' // internationalization

import '@/icons' // icon
import '@/permission' // permission control
import ElTreeSelect from 'el-tree-select'
import {getUserMenu, setUserMenu} from '@/utils/auth'

console.info(`%c version: v1.4.1.1`, 'background: green; color: #fff')

Vue.use(ElTreeSelect)
Vue.use(ElementUI, {
  i18n: (key, value) => i18n.t(key, value)
})
Vue.use(common)

import Logo from "@/components/Logo/index.vue"
Vue.component('Logo', Logo)

Vue.config.productionTip = false
Vue.prototype.$message = message
const cosSDK = require('cos-js-sdk-v5')
const cos = new cosSDK({
  SecretId: Config.cosConfig.SecretId,
  SecretKey: Config.cosConfig.SecretKey
})
Vue.prototype._cos = cos;
new Vue({
  el: '#app',
  router,
  store,
  i18n,
  computed: {
    visitedViews() {
      return this.$store.state.tagsView.visitedViews
    }
  },
  mounted() {
    window.addEventListener('message', async (rs) => {
      const {cmd, params} = rs.data
      if (cmd === 'openMenu' && params) {
        const {url, iframeName, title} = params
        if (url && title && iframeName) {
          const storageRoutes = getUserMenu()
          let hasMenu = false
          storageRoutes.map((route) => {
            if (route.module_id === iframeName) {
              hasMenu = true
              this.visitedViews.map((v) => {
                if (v.path === `/iframe-${iframeName}`) {
                  this.$store.dispatch('tagsView/delView', v)
                  this.$store.dispatch('tagsView/delCachedView', v)
                }
              })
              route.name = title
              route.url = url
            }
          })
          const iframeRoute = {
            icon: '',
            name: title,
            module_id: iframeName,
            url,
            is_iframe: 1,
            hidden: true
          }
          let accessRoutes = []
          if (!storageRoutes.length) {
            accessRoutes = await store.dispatch('permission/generateRoutes')
          } else {
            if (!hasMenu) {
              storageRoutes.push(iframeRoute)
              setUserMenu(storageRoutes)
            }
            accessRoutes = await store.dispatch('permission/setRoutes', storageRoutes)
          }
          resetRouter()
          router.addRoutes(accessRoutes)
          this.$router.push({
            path: `/iframe-${iframeName}`
          })
        }
      } else if (cmd === 'closeMenu') {
        const {iframeName, refresh, nextPage} = params
        this.visitedViews.map((v) => {
          if (v.path === `/iframe-${iframeName}`) {
            this.$store.dispatch('tagsView/delView', v)
            this.$store.dispatch('tagsView/delCachedView', v)
          }
        })
        const accessRoutes = await store.dispatch('permission/generateRoutes')
        accessRoutes.map((route) => {
          if (route && Array.isArray(route.children)) {
            route.children.map((child) => {
              if (child.meta.title === nextPage) {
                this.$router.push({
                  path: child.path
                })
              }
            })
          }
        })
        if (refresh) {
          const iframe = document.getElementsByName(nextPage) && document.getElementsByName(nextPage)[0]
          if (iframe) {
            iframe.contentWindow.postMessage({refresh}, '*')
          }
        }
      }
    })
  },
  render: h => h(App)
})
