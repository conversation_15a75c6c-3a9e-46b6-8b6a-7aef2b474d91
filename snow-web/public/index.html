<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title><%= webpackConfig.name %></title>
    <style>
      html,body{padding: 0;margin: 0;}.retaileye_loading-mask{position: fixed;width: 100%;height: 100vh;background: #020C16;z-index: 1}.retaileye_loading {position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);height: 40px;text-align: center;animation: retaileye_fadein 2s 1;}@keyframes retaileye_fadein{0% {opacity: 0;}50% {opacity: 0.5;}100% {opacity: 1;}}.retaileye_loading-ani {display: flex;align-items: center;height: 50px;}.retaileye_loading-text{color: #00fcff;margin-top: 10px;}.retaileye_item {height: 50px;width: 5px;background: #00fcff;margin: 0px 3px;border-radius: 10px;animation: retaileye_loading 1s infinite;}@keyframes retaileye_loading {0% {height: 0px;}50% {height: 50px;}100% {height: 0px;}}.retaileye_item:nth-child(2) {animation-delay: 0.1s;}.retaileye_item:nth-child(3) {animation-delay: 0.2s;}.retaileye_item:nth-child(4) {animation-delay: 0.3s;}.retaileye_item:nth-child(5) {animation-delay: 0.4s;}.retaileye_item:nth-child(6) {animation-delay: 0.5s;}.retaileye_item:nth-child(7) {animation-delay: 0.6s;}.retaileye_item:nth-child(8) {animation-delay: 0.7s;}
    </style>
</head>
<body>
<div id="loading" class="retaileye_loading-mask">
    <div class="retaileye_loading">
        <div class="retaileye_loading-ani">
            <div class="retaileye_item"></div>
            <div class="retaileye_item"></div>
            <div class="retaileye_item"></div>
            <div class="retaileye_item"></div>
            <div class="retaileye_item"></div>
            <div class="retaileye_item"></div>
            <div class="retaileye_item"></div>
            <div class="retaileye_item"></div>
        </div>
        <div class="retaileye_loading-text">Loading...</div>
    </div>
</div>
<script>
    let config = {
        env: 'dev',
        isOpenCut: true, // 是否需要切图
        isOpenQC: true, // 是否需要QC
        apiRoute: {
            'dev': '',
            // 'dev': 'http://************:8102',
            'test': 'http://snow-api.lingmou.ai:8081',
            'pre': 'https://snow-api-pre.lingmouai.com',
            'pro': 'https://snow-api.lingmouai.com',
        }
    }
    window.ENV = config.env
    window.HOST_URL = config.apiRoute[config.env]
    window.isOpenCut = config.isOpenCut
    window.isOpenQC = config.isOpenQC
</script>
<noscript>
    <strong>We're sorry but <%= webpackConfig.name %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
</noscript>
<div id="app"></div>
<!-- built files will be auto injected -->
</body>

</html>
