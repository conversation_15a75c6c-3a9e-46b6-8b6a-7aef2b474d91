{"name": "vue-admin-template", "version": "4.4.0", "description": "A vue admin template with Element UI & axios & iconfont & permission control & lint", "author": "Pan <<EMAIL>>", "scripts": {"dev": "cross-env NODE_OPTIONS=\"--max-old-space-size=8192 --openssl-legacy-provider\" vue-cli-service serve --mode dev", "test": "vue-cli-service serve --mode test", "pre": "vue-cli-service serve --mode pre", "pro": "vue-cli-service serve --mode pro", "build-dev": "cross-env NODE_OPTIONS=\"--max-old-space-size=8192 --openssl-legacy-provider\" vue-cli-service build --mode dev", "build-test": "cross-env NODE_OPTIONS=\"--max-old-space-size=8192 --openssl-legacy-provider\" vue-cli-service build --mode test", "build-pre": "cross-env NODE_OPTIONS=\"--max-old-space-size=8192 --openssl-legacy-provider\" vue-cli-service build --mode pre", "build-pro": "cross-env NODE_OPTIONS=\"--max-old-space-size=8192 --openssl-legacy-provider\" vue-cli-service build --mode pro", "preview-dev": "node build/index.js --mode dev --preview", "preview-test": "node build/index.js --mode test --preview", "preview-pre": "node build/index.js --mode pre --preview", "preview-pro": "node build/index.js --mode pro --preview", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit"}, "dependencies": {"@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "axios": "0.18.1", "compression-webpack-plugin": "^5.0.1", "core-js": "3.6.5", "cos-js-sdk-v5": "^1.8.3", "cross-env": "^7.0.3", "crypto-js": "^4.1.1", "dayjs": "^1.11.7", "echarts": "^5.4.2", "el-tree-select": "^3.1.13", "ele-form": "^1.1.5", "element-ui": "2.15.6", "fabric": "^5.3.0", "increase-memory-limit": "^1.0.7", "konva": "^9.3.18", "moment": "^2.30.1", "node-sass": "^4.13.0", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path": "^0.12.7", "path-to-regexp": "2.4.0", "sass": "^1.26.8", "sass-loader": "^7.3.1", "storejs": "^1.1.0", "v-viewer": "^1.6.4", "vue": "2.6.10", "vue-amap": "^0.5.10", "vue-fullscreen": "^2.6.1", "vue-i18n": "7.3.2", "vue-router": "3.0.6", "vue-uuid": "^3.0.0", "vue-virtual-scroll-list": "^2.3.5", "vue2-org-tree": "^1.3.6", "vuex": "3.1.0"}, "devDependencies": {"@babel/core": "^7.21.8", "@babel/plugin-transform-runtime": "^7.21.4", "@babel/preset-env": "^7.21.5", "@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "babel-plugin-transform-remove-console": "^6.9.4", "babel-polyfill": "^6.26.0", "browserslist": "^4.21.5", "caniuse-lite": "^1.0.30001486", "chalk": "2.4.2", "connect": "3.6.6", "es6-promise": "^4.2.8", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "mockjs": "1.0.1-beta3", "runjs": "4.3.2", "sass": "1.26.8", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-template-compiler": "2.6.10"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "license": "MIT"}